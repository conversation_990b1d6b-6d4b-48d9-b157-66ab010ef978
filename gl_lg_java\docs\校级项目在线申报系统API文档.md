# 校级项目在线申报系统API文档

## 概述

校级项目在线申报系统提供完整的CRUD功能和MinIO文件管理，支持项目类别管理、项目申报管理和文件上传下载功能。

## 系统架构

### 核心组件

1. **项目类别管理** - 管理校级项目的分类信息
2. **项目申报管理** - 处理项目申报的完整流程
3. **文件管理系统** - 基于MinIO的文件存储和管理
4. **预签名URL** - 支持安全的文件上传和下载

### 数据库表结构

- `t_rchx_school_project_categories` - 校级项目类别表
- `t_rchx_school_project_applications` - 校级项目申报表
- `t_rchx_school_project_files` - 校级项目文件表

## API接口文档

### 1. 校级项目类别管理

#### 1.1 分页查询项目类别

```http
GET /api/school-project/categories/page
```

**参数：**
- `current` (int): 页码，默认1
- `size` (int): 页大小，默认10
- `categoryName` (string): 类别名称（可选）
- `isEnabled` (boolean): 是否启用（可选）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "categoryCode": "SCHOOL_TEACHING",
        "categoryName": "教学改革项目",
        "fundingRangeMin": 1.00,
        "fundingRangeMax": 5.00,
        "applicationCount": 10,
        "approvedCount": 8
      }
    ],
    "total": 50,
    "current": 1,
    "size": 10
  }
}
```

#### 1.2 查询启用的项目类别

```http
GET /api/school-project/categories/enabled
```

#### 1.3 创建项目类别

```http
POST /api/school-project/categories
```

**请求体：**
```json
{
  "categoryCode": "SCHOOL_RESEARCH",
  "categoryName": "科研启动项目",
  "fundingRangeMin": 2.00,
  "fundingRangeMax": 10.00,
  "applicationRequirements": "需提交研究方案、文献综述、预期成果等",
  "evaluationCriteria": "学术价值、创新性、可行性",
  "projectDurationMonths": 24,
  "description": "支持青年教师开展科学研究工作",
  "sortOrder": 2,
  "isEnabled": true
}
```

#### 1.4 更新项目类别

```http
PUT /api/school-project/categories/{id}
```

#### 1.5 删除项目类别

```http
DELETE /api/school-project/categories/{id}
```

#### 1.6 批量删除项目类别

```http
DELETE /api/school-project/categories/batch
```

**请求体：**
```json
[1, 2, 3]
```

### 2. 文件管理

#### 2.1 上传文件

```http
POST /api/school-project/files/upload
Content-Type: multipart/form-data
```

**参数：**
- `applicationId` (long): 申报ID
- `applicationCode` (string): 申报编号
- `fileCategory` (string): 文件类别
  - `APPLICATION` - 申报书
  - `BUDGET` - 预算书
  - `RESUME` - 个人简历
  - `ACHIEVEMENT` - 成果证明
  - `PLAN` - 实施方案
  - `OTHER` - 其他
- `fileDescription` (string): 文件描述（可选）
- `isRequired` (boolean): 是否必需文件，默认false
- `uploadBy` (string): 上传人职工号
- `uploadByName` (string): 上传人姓名
- `file` (file): 上传的文件

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "applicationId": 1,
    "applicationCode": "SCHOOL2024001",
    "fileCategory": "APPLICATION",
    "fileName": "项目申报书_20250104_abc123.pdf",
    "fileOriginalName": "项目申报书.pdf",
    "filePath": "SCHOOL2024001/APPLICATION/20250104/abc123_项目申报书.pdf",
    "fileSize": 1024000,
    "fileType": "application/pdf",
    "bucketName": "rchx-school-project",
    "objectName": "SCHOOL2024001/APPLICATION/20250104/abc123_项目申报书.pdf",
    "uploadTime": "2025-01-04T10:30:00"
  }
}
```

#### 2.2 下载文件

```http
GET /api/school-project/files/download/{fileId}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "fileId": 1,
    "fileName": "项目申报书_20250104_abc123.pdf",
    "originalFileName": "项目申报书.pdf",
    "fileSize": 1024000,
    "fileType": "application/pdf",
    "presignedUrl": "http://localhost:9000/rchx-school-project/SCHOOL2024001/APPLICATION/20250104/abc123_项目申报书.pdf?X-Amz-Algorithm=...",
    "urlExpiry": 3600,
    "downloadCount": 5
  }
}
```

#### 2.3 直接下载文件流

```http
GET /api/school-project/files/stream/{fileId}
```

直接返回文件流，浏览器会自动下载文件。

#### 2.4 获取预签名下载URL

```http
GET /api/school-project/files/presigned-url/{fileId}?expiry=3600
```

**参数：**
- `expiry` (int): 过期时间（秒），可选

#### 2.5 获取预签名上传URL

```http
GET /api/school-project/files/presigned-upload-url?bucketName=rchx-school-project&objectName=test.pdf&expiry=3600
```

**参数：**
- `bucketName` (string): 存储桶名称
- `objectName` (string): 对象名称
- `expiry` (int): 过期时间（秒），可选

#### 2.6 删除文件

```http
DELETE /api/school-project/files/{fileId}?deleteBy=202401001
```

#### 2.7 批量删除文件

```http
DELETE /api/school-project/files/batch?deleteBy=202401001
```

**请求体：**
```json
[1, 2, 3]
```

#### 2.8 分页查询文件信息

```http
GET /api/school-project/files/page
```

**参数：**
- `current` (int): 页码
- `size` (int): 页大小
- `applicationId` (long): 申报ID（可选）
- `applicationCode` (string): 申报编号（可选）
- `fileCategory` (string): 文件类别（可选）
- `uploadBy` (string): 上传人（可选）
- `isRequired` (boolean): 是否必需文件（可选）

#### 2.9 根据申报ID查询文件列表

```http
GET /api/school-project/files/application/{applicationId}
```

#### 2.10 根据申报编号查询文件列表

```http
GET /api/school-project/files/application-code/{applicationCode}
```

#### 2.11 获取文件统计信息

```http
GET /api/school-project/files/statistics
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalFiles": 150,
    "totalSize": 1073741824,
    "requiredFiles": 80,
    "totalDownloads": 500,
    "totalUploaders": 25,
    "totalApplications": 30
  }
}
```

#### 2.12 检查申报的必需文件是否完整

```http
GET /api/school-project/files/check-required/{applicationId}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "hasApplication": 1,
    "hasBudget": 1,
    "hasResume": 0,
    "totalFiles": 5
  }
}
```

## 配置说明

### MinIO配置

在 `application.yml` 中添加以下配置：

```yaml
minio:
  endpoint: http://localhost:9000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: rchx-default
  school-project-bucket-name: rchx-school-project
  url-prefix: http://localhost:9000
  presigned-url-expiry: 3600
  max-file-size: 104857600  # 100MB
  allowed-file-types:
    - application/pdf
    - application/msword
    - application/vnd.openxmlformats-officedocument.wordprocessingml.document
    - application/vnd.ms-excel
    - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    - image/jpeg
    - image/png
    - text/plain
```

### 文件上传配置

```yaml
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 500MB
      file-size-threshold: 2KB
```

## 使用示例

### 前端JavaScript示例

#### 文件上传

```javascript
// 文件上传
async function uploadFile(file, applicationId, applicationCode, fileCategory) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('applicationId', applicationId);
  formData.append('applicationCode', applicationCode);
  formData.append('fileCategory', fileCategory);
  formData.append('uploadBy', '202401001');
  formData.append('uploadByName', '张三');
  
  const response = await fetch('/api/school-project/files/upload', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
}

// 文件下载
async function downloadFile(fileId) {
  const response = await fetch(`/api/school-project/files/download/${fileId}`);
  const result = await response.json();
  
  if (result.code === 200) {
    // 使用预签名URL下载
    window.open(result.data.presignedUrl);
  }
}
```

## 错误码说明

- `200` - 成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `500` - 服务器内部错误

## 注意事项

1. **文件大小限制**：默认最大100MB，可在配置中调整
2. **文件类型限制**：只允许配置中指定的文件类型
3. **预签名URL**：有时效性，默认1小时过期
4. **文件存储路径**：格式为 `申报编号/文件类别/日期/UUID_原始文件名`
5. **文件删除**：采用逻辑删除，不会立即从MinIO中删除物理文件
6. **并发上传**：支持多文件并发上传
7. **断点续传**：可通过预签名URL实现断点续传功能

## 部署说明

1. **MinIO服务器**：需要先部署MinIO服务器
2. **存储桶创建**：系统会自动创建所需的存储桶
3. **网络配置**：确保应用服务器能访问MinIO服务器
4. **权限配置**：配置MinIO的访问权限和策略
