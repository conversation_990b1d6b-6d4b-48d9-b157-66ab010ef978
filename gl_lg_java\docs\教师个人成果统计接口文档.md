# 教师个人成果统计接口文档

## 📋 概述

本文档提供教师个人成果统计接口，用于统计教师个人在专利成果、科技项目、科技论文三个维度的成果数量。这些接口基于当前登录用户的职工号自动统计个人成果，确保数据安全和权限控制。

## 🔐 权限说明

- **权限要求**: 教师及以上权限
- **数据范围**: 只能查看当前登录用户的个人统计数据
- **自动识别**: 系统自动从token中获取当前用户职工号，无需手动传参

---

## 📊 1. 教师个人成果统计接口

### 1.1 个人专利成果数量统计

#### 接口信息
- **接口地址**: `GET /api/zlcgjbxx/personal-count`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计当前登录教师的个人专利成果数量。统计范围：该教师作为第一发明人的所有专利成果。

#### 请求参数
无需传参，系统自动从token中获取当前用户职工号。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "zgh": "T001234",
        "count": 15,
        "type": "专利成果"
    }
}
```

#### 响应字段说明
- `zgh`: 教师职工号
- `count`: 个人专利成果数量
- `type`: 成果类型标识

### 1.2 个人科技项目数量统计

#### 接口信息
- **接口地址**: `GET /api/kjxmjbxx/personal-count`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计当前登录教师的个人科技项目数量。统计范围：该教师作为项目负责人的所有科技项目。

#### 请求参数
无需传参，系统自动从token中获取当前用户职工号。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "zgh": "T001234",
        "count": 8,
        "type": "科技项目"
    }
}
```

#### 响应字段说明
- `zgh`: 教师职工号
- `count`: 个人科技项目数量
- `type`: 成果类型标识

### 1.3 个人科技论文数量统计

#### 接口信息
- **接口地址**: `GET /api/kjlwjbxx/personal-count`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计当前登录教师的个人科技论文数量。统计范围：该教师作为第一作者或通讯作者的所有科技论文。

#### 请求参数
无需传参，系统自动从token中获取当前用户职工号。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "zgh": "T001234",
        "count": 23,
        "type": "科技论文"
    }
}
```

#### 响应字段说明
- `zgh`: 教师职工号
- `count`: 个人科技论文数量
- `type`: 成果类型标识

---

## 📝 2. 前端使用示例

### 2.1 Vue.js 组件示例

```javascript
// 教师个人成果统计组件
export default {
    data() {
        return {
            personalStats: {
                patents: 0,      // 专利成果数量
                projects: 0,     // 科技项目数量
                papers: 0        // 科技论文数量
            },
            loading: false,
            userInfo: {
                zgh: '',
                name: ''
            }
        };
    },
    
    async mounted() {
        await this.loadPersonalStats();
    },
    
    methods: {
        async loadPersonalStats() {
            this.loading = true;
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };
            
            try {
                // 并行加载所有个人统计数据
                const [patentsRes, projectsRes, papersRes] = await Promise.all([
                    fetch('/api/zlcgjbxx/personal-count', { headers }),
                    fetch('/api/kjxmjbxx/personal-count', { headers }),
                    fetch('/api/kjlwjbxx/personal-count', { headers })
                ]);
                
                const patentsData = await patentsRes.json();
                const projectsData = await projectsRes.json();
                const papersData = await papersRes.json();
                
                if (patentsData.code === 200) {
                    this.personalStats.patents = patentsData.data.count;
                    this.userInfo.zgh = patentsData.data.zgh;
                }
                
                if (projectsData.code === 200) {
                    this.personalStats.projects = projectsData.data.count;
                }
                
                if (papersData.code === 200) {
                    this.personalStats.papers = papersData.data.count;
                }
                
                console.log('个人成果统计加载完成:', this.personalStats);
                
                // 可以用于图表展示
                this.renderPersonalChart();
            } catch (error) {
                console.error('加载个人成果统计失败:', error);
                this.$message.error('加载个人成果统计失败');
            } finally {
                this.loading = false;
            }
        },
        
        renderPersonalChart() {
            // 使用 ECharts 渲染个人成果统计图表
            const chartData = [
                { name: '专利成果', value: this.personalStats.patents },
                { name: '科技项目', value: this.personalStats.projects },
                { name: '科技论文', value: this.personalStats.papers }
            ];
            
            // 这里可以调用 ECharts 渲染饼图或柱状图
            console.log('个人成果图表数据:', chartData);
        },
        
        getTotalCount() {
            return this.personalStats.patents + 
                   this.personalStats.projects + 
                   this.personalStats.papers;
        }
    }
};
```

### 个人统计接口
- `GET /api/zlcgjbxx/personal-count` - 个人专利成果数量统计
- `GET /api/kjxmjbxx/personal-count` - 个人科技项目数量统计
- `GET /api/kjlwjbxx/personal-count` - 个人科技论文数量统计

### 统计维度说明
| 成果类型 | 统计字段 | 统计条件 |
|---------|---------|---------|
| 专利成果 | dyfmrzgh | 第一发明人职工号 = 当前用户职工号 |
| 科技项目 | xmfzrh | 项目负责人号 = 当前用户职工号 |
| 科技论文 | dyzzbh OR txzzbh | 第一作者编号 = 当前用户职工号 OR 通讯作者编号 = 当前用户职工号 |

---

## 🎯 5. 应用场景

### 5.1 教师个人主页
- 在教师个人主页展示成果概览
- 提供快速的成果数量统计
- 支持成果分布图表展示

### 5.2 绩效考核系统
- 为绩效考核提供基础数据
- 支持成果数量的快速统计
- 可与其他考核指标结合使用

### 5.3 科研管理系统
- 教师科研成果总览
- 支持成果趋势分析
- 为科研规划提供数据支持

### 5.4 移动端应用
- 轻量级的个人成果查询
- 快速响应的统计接口
- 适合移动端展示

---

## 📞 6. 技术支持

如有问题，请联系后端开发团队。

## 📝 7. 更新日志

- **2025-07-21**: 新增教师个人专利成果数量统计接口
- **2025-07-21**: 新增教师个人科技项目数量统计接口
- **2025-07-21**: 新增教师个人科技论文数量统计接口
- **2025-07-21**: 实现基于token的自动用户识别
- **2025-07-21**: 提供完整的前端使用示例和组件模板
- **2025-07-21**: 优化查询性能，响应速度大幅提升
