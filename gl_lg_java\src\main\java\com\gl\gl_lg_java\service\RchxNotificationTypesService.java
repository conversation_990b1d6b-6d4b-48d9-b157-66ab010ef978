package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxNotificationTypes;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_notification_types(通知类型表)】的数据库操作Service
 * @createDate 2025-07-24 13:00:00
 */
public interface RchxNotificationTypesService extends IService<RchxNotificationTypes> {

    /**
     * 获取所有通知类型
     * @return 通知类型列表
     */
    List<RchxNotificationTypes> getAllNotificationTypes();

    /**
     * 根据类型编码获取通知类型
     * @param typeCode 类型编码
     * @return 通知类型
     */
    RchxNotificationTypes getNotificationTypeByCode(String typeCode);
}
