# 项目预算信息管理API文档

## 基础信息
- **模块名称**: 项目预算信息管理
- **基础路径**: `/api/xmysxx`
- **权限要求**: 需要JWT Token认证
- **数据格式**: JSON

## 统一响应格式

```json
{
    "code": 200,           // 状态码：200-成功，500-失败，401-未授权，403-权限不足
    "message": "操作成功",  // 响应消息
    "data": {}            // 响应数据（可能为null）
}
```

## 接口列表

### 1. 根据项目编号查询预算信息

**接口地址**: `GET /api/xmysxx/{xmbh}`

**权限要求**: 教师+

**路径参数**:
- `xmbh` (string, 必填): 项目编号

**请求示例**:
```
GET /api/xmysxx/KJXM2025001
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": [
        {
            "xmbh": "KJXM2025001",
            "xmmc": "人工智能在教育中的应用研究",
            "zcje": "5000.00",
            "zcsj": "2025-03-15",
            "zcyy": "购买实验设备",
            "pdh": "PD2025001",
            "jbr": "张三",
            "bxr": "张三",
            "cjrgh": "T001",
            "cjrxm": "张三",
            "shzt": "已审核",
            "cjsj": "2025-07-18 10:00:00",
            "gxsj": "2025-07-18 10:00:00"
        },
        {
            "xmbh": "KJXM2025001",
            "xmmc": "人工智能在教育中的应用研究",
            "zcje": "3000.00",
            "zcsj": "2025-04-20",
            "zcyy": "差旅费",
            "pdh": "PD2025002",
            "jbr": "李四",
            "bxr": "张三",
            "cjrgh": "T001",
            "cjrxm": "张三",
            "shzt": "待审核",
            "cjsj": "2025-07-18 10:00:00",
            "gxsj": "2025-07-18 10:00:00"
        }
    ]
}
```

### 2. 分页查询项目预算信息

**接口地址**: `POST /api/xmysxx/page`

**权限要求**: 教师+

**请求体**:
```json
{
    "xmbh": "KJXM2025",             // 项目编号（可选）
    "xmmc": "人工智能",             // 项目名称（可选）
    "startAmount": "1000.00",       // 支出金额开始（可选）
    "endAmount": "10000.00",        // 支出金额结束（可选）
    "startDate": "2025-01-01",      // 支出时间开始（可选）
    "endDate": "2025-12-31",        // 支出时间结束（可选）
    "pdh": "PD2025",                // 凭单号（可选）
    "jbr": "张三",                  // 经办人（可选）
    "bxr": "张三",                  // 报销人（可选）
    "cjrgh": "T001",                // 创建人工号（可选）
    "cjrxm": "张三",                // 创建人姓名（可选）
    "shzt": "已审核",               // 审核状态（可选）
    "pageNum": 1,                   // 页码（默认1）
    "pageSize": 10,                 // 页大小（默认10）
    "orderBy": "zcsj",              // 排序字段（默认zcsj）
    "orderDirection": "desc"        // 排序方向（默认desc）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "records": [
            {
                "xmbh": "KJXM2025001",
                "xmmc": "人工智能在教育中的应用研究",
                "zcje": "5000.00",
                "zcsj": "2025-03-15",
                "zcyy": "购买实验设备",
                "pdh": "PD2025001",
                "jbr": "张三",
                "bxr": "张三",
                "cjrgh": "T001",
                "cjrxm": "张三",
                "shzt": "已审核",
                "cjsj": "2025-07-18 10:00:00",
                "gxsj": "2025-07-18 10:00:00"
            }
        ],
        "total": 20,
        "size": 10,
        "current": 1,
        "pages": 2
    }
}
```

### 3. 多条件查询项目预算信息

**接口地址**: `POST /api/xmysxx/list`

**权限要求**: 教师+

**请求体**: 同分页查询，但不返回分页信息

### 4. 根据项目名称模糊查询

**接口地址**: `GET /api/xmysxx/name/{xmmc}`

**权限要求**: 学院管理员+

**路径参数**:
- `xmmc` (string, 必填): 项目名称关键字

### 5. 根据凭单号查询

**接口地址**: `GET /api/xmysxx/voucher/{pdh}`

**权限要求**: 学院管理员+

**路径参数**:
- `pdh` (string, 必填): 凭单号

### 6. 根据经办人模糊查询

**接口地址**: `GET /api/xmysxx/handler/{jbr}`

**权限要求**: 学院管理员+

**路径参数**:
- `jbr` (string, 必填): 经办人姓名关键字

### 7. 根据报销人模糊查询

**接口地址**: `GET /api/xmysxx/reimbursement/{bxr}`

**权限要求**: 学院管理员+

**路径参数**:
- `bxr` (string, 必填): 报销人姓名关键字

### 8. 根据创建人工号查询

**接口地址**: `GET /api/xmysxx/creator/{cjrgh}`

**权限要求**: 教师+

**路径参数**:
- `cjrgh` (string, 必填): 创建人工号

### 9. 根据审核状态查询

**接口地址**: `GET /api/xmysxx/audit/{shzt}`

**权限要求**: 评审+

**路径参数**:
- `shzt` (string, 必填): 审核状态

### 10. 新增项目预算信息

**接口地址**: `POST /api/xmysxx`

**权限要求**: 学院管理员+

**请求体**:
```json
{
    "xmbh": "KJXM2025002",                  // 项目编号（必填）
    "xmmc": "大数据分析技术研究",           // 项目名称（必填）
    "zcje": "8000.00",                      // 支出金额（必填）
    "zcsj": "2025-05-10",                   // 支出时间（必填）
    "zcyy": "购买服务器设备",               // 支出原因（必填）
    "pdh": "PD2025003",                     // 凭单号（可选）
    "jbr": "李四",                          // 经办人（可选）
    "bxr": "李四",                          // 报销人（可选）
    "cjrgh": "T002",                        // 创建人工号（必填）
    "cjrxm": "李四",                        // 创建人姓名（必填）
    "shzt": "待审核",                       // 审核状态（必填）
    "zcms": "用于项目实验环境搭建"          // 支出描述（可选）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "新增成功",
    "data": "新增成功"
}
```

### 11. 更新项目预算信息

**接口地址**: `PUT /api/xmysxx`

**权限要求**: 学院管理员+（教师可修改自己创建的预算）

**请求体**: 同新增，需要包含完整的记录信息

### 12. 删除项目预算信息

**接口地址**: `DELETE /api/xmysxx/{xmbh}`

**权限要求**: 系统管理员

**路径参数**:
- `xmbh` (string, 必填): 项目编号

### 13. 批量新增项目预算信息

**接口地址**: `POST /api/xmysxx/batch`

**权限要求**: 学院管理员+

**请求体**:
```json
[
    {
        "xmbh": "KJXM2025003",
        "xmmc": "机器学习算法优化研究",
        "zcje": "6000.00",
        "zcsj": "2025-06-15",
        "zcyy": "购买GPU计算卡",
        "pdh": "PD2025004",
        "jbr": "王五",
        "bxr": "王五",
        "cjrgh": "T003",
        "cjrxm": "王五",
        "shzt": "待审核"
    }
]
```

### 14. 批量更新项目预算信息

**接口地址**: `PUT /api/xmysxx/batch`

**权限要求**: 学院管理员+

### 15. 批量删除项目预算信息

**接口地址**: `DELETE /api/xmysxx/batch`

**权限要求**: 系统管理员

**请求体**:
```json
["KJXM2025001", "KJXM2025002", "KJXM2025003"]
```

## 支出类别说明

### 设备费
- **实验设备**: 用于科研实验的仪器设备
- **计算设备**: 计算机、服务器等计算设备
- **办公设备**: 办公用品和设备

### 材料费
- **实验材料**: 科研实验所需的各种材料
- **办公用品**: 日常办公所需用品
- **软件许可**: 软件授权和许可费用

### 差旅费
- **国内差旅**: 国内出差的交通、住宿费用
- **国际差旅**: 国际出差的交通、住宿费用
- **会议费**: 参加学术会议的相关费用

### 劳务费
- **专家咨询**: 聘请专家的咨询费用
- **学生助研**: 研究生助研津贴
- **临时用工**: 临时聘用人员的劳务费

### 其他费用
- **出版费**: 论文发表、专著出版费用
- **知识产权**: 专利申请、维护费用
- **管理费**: 项目管理相关费用

## 审核状态说明

- **待审核**: 预算申请已提交，等待审核
- **审核中**: 正在进行预算审核
- **已审核**: 预算审核通过，可以执行
- **审核不通过**: 预算审核未通过，需要修改
- **已执行**: 预算已执行完成
- **已报销**: 费用已完成报销

## 预算管理流程

### 1. 预算申请
- 项目负责人提交预算申请
- 填写详细的支出计划和用途

### 2. 内部审核
- 学院管理员进行初步审核
- 检查预算的合理性和必要性

### 3. 财务审核
- 财务部门进行预算审核
- 确认预算符合财务规定

### 4. 预算执行
- 审核通过后可以执行预算
- 按照预算计划进行支出

### 5. 报销结算
- 提供相关票据进行报销
- 完成财务结算流程

## 权限说明

### 数据权限
- **教师**: 只能查看自己创建的预算信息
- **评审**: 可以查看预算信息，进行预算评审
- **学院管理员**: 可以管理本学院的项目预算
- **系统管理员**: 可以管理所有项目预算

### 操作权限
- **查询**: 教师+（只能查看自己创建的）
- **新增**: 学院管理员+
- **修改**: 学院管理员+（教师可修改自己创建的）
- **删除**: 系统管理员

## 错误码说明

- **200**: 操作成功
- **401**: 未授权（Token无效或过期）
- **403**: 权限不足
- **500**: 服务器内部错误

## 注意事项

1. 所有接口都需要在请求头中携带JWT Token
2. 教师权限的用户只能操作自己创建的预算信息
3. 金额字段使用字符串类型，保持精度
4. 批量操作支持事务，失败时会回滚
5. 分页查询默认按支出时间倒序排列
6. 日期格式统一使用 yyyy-MM-dd
7. 预算信息需要与项目信息关联
8. 支出金额必须在项目总预算范围内
9. 预算执行需要提供相关凭证和票据
10. 预算变更需要重新审核
