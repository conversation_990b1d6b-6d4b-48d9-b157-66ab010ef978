# 科技项目基本信息管理API文档

## 基础信息
- **模块名称**: 科技项目基本信息管理
- **基础路径**: `/api/kjxmjbxx`
- **权限要求**: 需要JWT Token认证
- **数据格式**: JSON

## 统一响应格式

```json
{
    "code": 200,           // 状态码：200-成功，500-失败，401-未授权，403-权限不足
    "message": "操作成功",  // 响应消息
    "data": {}            // 响应数据（可能为null）
}
```

## 接口列表

### 1. 根据项目主键查询

**接口地址**: `GET /api/kjxmjbxx/{xmid}`

**权限要求**: 教师+

**路径参数**:
- `xmid` (string, 必填): 项目主键

**请求示例**:
```
GET /api/kjxmjbxx/XM2025001
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "xmid": "XM2025001",
        "xmbh": "KJXM2025001",
        "xmmc": "人工智能在教育中的应用研究",
        "xnbh": "XN2025001",
        "dwh": "001",
        "dwmc": "计算机学院",
        "xmfzrh": "T001",
        "fzrxm": "张三",
        "xmly": "国家自然科学基金",
        "xmxz": "基础研究",
        "xmlb": "重点项目",
        "xmjb": "国家级",
        "xmzxzt": "进行中",
        "shzt": "已审核",
        "lxrq": "2025-01-01",
        "ksrq": "2025-01-15",
        "jhwcrq": "2027-12-31",
        "jxrq": null
    }
}
```

### 2. 分页查询科技项目信息

**接口地址**: `POST /api/kjxmjbxx/page`

**权限要求**: 教师+

**请求体**:
```json
{
    "xmid": "XM2025",               // 项目主键（可选）
    "xmbh": "KJXM2025",             // 项目编号（可选）
    "xmmc": "人工智能",             // 项目名称（可选）
    "xnbh": "XN2025",               // 校内编号（可选）
    "dwmc": "计算机学院",           // 单位名称（可选）
    "xmfzrh": "T001",               // 项目负责人号（可选）
    "fzrxm": "张三",                // 负责人姓名（可选）
    "xmly": "国家自然科学基金",     // 项目来源（可选）
    "xmxz": "基础研究",             // 项目性质（可选）
    "xmlb": "重点项目",             // 项目类别（可选）
    "xmjb": "国家级",               // 项目级别（可选）
    "xmzxzt": "进行中",             // 项目执行状态（可选）
    "shzt": "已审核",               // 审核状态（可选）
    "lxStartDate": "2025-01-01",    // 立项开始日期（可选）
    "lxEndDate": "2025-12-31",      // 立项结束日期（可选）
    "ksStartDate": "2025-01-01",    // 开始日期范围-开始（可选）
    "ksEndDate": "2025-12-31",      // 开始日期范围-结束（可选）
    "pageNum": 1,                   // 页码（默认1）
    "pageSize": 10,                 // 页大小（默认10）
    "orderBy": "lxrq",              // 排序字段（默认lxrq）
    "orderDirection": "desc"        // 排序方向（默认desc）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "records": [
            {
                "xmid": "XM2025001",
                "xmbh": "KJXM2025001",
                "xmmc": "人工智能在教育中的应用研究",
                "xnbh": "XN2025001",
                "dwh": "001",
                "dwmc": "计算机学院",
                "xmfzrh": "T001",
                "fzrxm": "张三",
                "xmly": "国家自然科学基金",
                "xmxz": "基础研究",
                "xmlb": "重点项目",
                "xmjb": "国家级",
                "xmzxzt": "进行中",
                "shzt": "已审核",
                "lxrq": "2025-01-01",
                "ksrq": "2025-01-15",
                "jhwcrq": "2027-12-31",
                "jxrq": null
            }
        ],
        "total": 30,
        "size": 10,
        "current": 1,
        "pages": 3
    }
}
```

### 3. 多条件查询科技项目信息

**接口地址**: `POST /api/kjxmjbxx/list`

**权限要求**: 教师+

**请求体**: 同分页查询，但不返回分页信息

### 4. 根据项目名称模糊查询

**接口地址**: `GET /api/kjxmjbxx/name/{xmmc}`

**权限要求**: 学院管理员+

**路径参数**:
- `xmmc` (string, 必填): 项目名称关键字

### 5. 根据项目负责人号查询

**接口地址**: `GET /api/kjxmjbxx/leader/{xmfzrh}`

**权限要求**: 教师+

**路径参数**:
- `xmfzrh` (string, 必填): 项目负责人号

### 6. 根据项目执行状态查询

**接口地址**: `GET /api/kjxmjbxx/status/{xmzxzt}`

**权限要求**: 学院管理员+

**路径参数**:
- `xmzxzt` (string, 必填): 项目执行状态

### 7. 根据审核状态查询

**接口地址**: `GET /api/kjxmjbxx/audit/{shzt}`

**权限要求**: 评审+

**路径参数**:
- `shzt` (string, 必填): 审核状态

### 8. 新增科技项目信息

**接口地址**: `POST /api/kjxmjbxx`

**权限要求**: 学院管理员+

**请求体**:
```json
{
    "xmid": "XM2025002",                        // 项目主键（必填）
    "xmbh": "KJXM2025002",                      // 项目编号（必填）
    "xmmc": "大数据分析技术研究",               // 项目名称（必填）
    "xnbh": "XN2025002",                        // 校内编号（可选）
    "dwh": "001",                               // 单位号（可选）
    "dwmc": "计算机学院",                       // 单位名称（可选）
    "xmfzrh": "T002",                           // 项目负责人号（必填）
    "fzrxm": "李四",                            // 负责人姓名（必填）
    "xmly": "省科技厅",                         // 项目来源（可选）
    "xmxz": "应用研究",                         // 项目性质（可选）
    "xmlb": "一般项目",                         // 项目类别（可选）
    "xmjb": "省级",                             // 项目级别（可选）
    "xmzxzt": "申请中",                         // 项目执行状态（必填）
    "shzt": "待审核",                           // 审核状态（必填）
    "lxrq": "2025-02-01",                       // 立项日期（可选）
    "ksrq": "2025-02-15",                       // 开始日期（可选）
    "jhwcrq": "2027-02-14",                     // 计划完成日期（可选）
    "jxrq": null                                // 结项日期（可选）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "新增成功",
    "data": "新增成功"
}
```

### 9. 更新科技项目信息

**接口地址**: `PUT /api/kjxmjbxx`

**权限要求**: 学院管理员+（教师可修改自己负责的项目）

**请求体**: 同新增，但xmid不可修改

### 10. 删除科技项目信息

**接口地址**: `DELETE /api/kjxmjbxx/{xmid}`

**权限要求**: 系统管理员

**路径参数**:
- `xmid` (string, 必填): 项目主键

### 11. 批量新增科技项目信息

**接口地址**: `POST /api/kjxmjbxx/batch`

**权限要求**: 学院管理员+

### 12. 批量更新科技项目信息

**接口地址**: `PUT /api/kjxmjbxx/batch`

**权限要求**: 学院管理员+

### 13. 批量删除科技项目信息

**接口地址**: `DELETE /api/kjxmjbxx/batch`

**权限要求**: 系统管理员

**请求体**:
```json
["XM2025001", "XM2025002", "XM2025003"]
```

## 项目来源说明

- **国家自然科学基金**: 国家自然科学基金委员会资助
- **国家社会科学基金**: 全国哲学社会科学工作办公室资助
- **省科技厅**: 省级科技部门资助
- **市科技局**: 市级科技部门资助
- **企业委托**: 企业委托研发项目
- **国际合作**: 国际合作研究项目

## 项目性质说明

- **基础研究**: 以认识自然现象、揭示自然规律为目的的研究
- **应用研究**: 以解决实际问题为目的的研究
- **试验发展**: 利用研究成果进行的系统性工作

## 项目级别说明

- **国家级**: 国家部委资助的项目
- **省级**: 省级部门资助的项目
- **市级**: 市级部门资助的项目
- **校级**: 学校内部资助的项目

## 项目执行状态说明

- **申请中**: 项目申请已提交，等待审批
- **进行中**: 项目已立项，正在执行
- **已完成**: 项目已按计划完成
- **已结项**: 项目已通过验收结项
- **已终止**: 项目因故终止执行

## 权限说明

### 数据权限
- **教师**: 只能查看自己负责的项目
- **评审**: 可以查看项目信息，进行项目评审
- **学院管理员**: 可以管理本学院的项目
- **系统管理员**: 可以管理所有项目

### 操作权限
- **查询**: 教师+
- **新增**: 学院管理员+
- **修改**: 学院管理员+（教师可修改自己负责的项目）
- **删除**: 系统管理员

## 错误码说明

- **200**: 操作成功
- **401**: 未授权（Token无效或过期）
- **403**: 权限不足
- **500**: 服务器内部错误

## 注意事项

1. 所有接口都需要在请求头中携带JWT Token
2. 教师权限的用户只能操作自己负责的项目
3. 项目主键和项目编号必须唯一
4. 批量操作支持事务，失败时会回滚
5. 分页查询默认按立项日期倒序排列
6. 日期格式统一使用 yyyy-MM-dd
7. 项目状态变更需要按照流程进行
