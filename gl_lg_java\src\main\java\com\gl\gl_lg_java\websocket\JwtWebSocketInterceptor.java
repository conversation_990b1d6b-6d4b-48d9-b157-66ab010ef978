package com.gl.gl_lg_java.websocket;

import com.gl.gl_lg_java.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.net.URI;
import java.util.Map;

/**
 * JWT WebSocket握手拦截器
 * 用于验证WebSocket连接的JWT Token
 */
@Component
@Slf4j
public class JwtWebSocketInterceptor implements HandshakeInterceptor {

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        try {
            // 从URL参数中获取token
            String token = getTokenFromRequest(request);
            
            if (token == null || token.isEmpty()) {
                log.warn("WebSocket连接被拒绝: 缺少token参数, URI: {}", request.getURI());
                return false;
            }

            // 验证token
            if (!jwtUtil.validateToken(token)) {
                log.warn("WebSocket连接被拒绝: token无效, URI: {}", request.getURI());
                return false;
            }

            // 从token中获取用户信息
            String zgh = jwtUtil.getZghFromToken(token);
            String permission = jwtUtil.getQxFromToken(token);

            if (zgh == null || zgh.isEmpty()) {
                log.warn("WebSocket连接被拒绝: 无法从token获取职工号, URI: {}", request.getURI());
                return false;
            }

            // 将用户信息存储到WebSocket会话属性中
            attributes.put("zgh", zgh);
            attributes.put("permission", permission);
            attributes.put("token", token);

            log.info("WebSocket握手成功: zgh={}, permission={}, URI={}", zgh, permission, request.getURI());
            return true;

        } catch (Exception e) {
            log.error("WebSocket握手异常: {}, URI: {}", e.getMessage(), request.getURI());
            return false;
        }
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        if (exception != null) {
            log.error("WebSocket握手后异常: {}, URI: {}", exception.getMessage(), request.getURI());
        } else {
            log.debug("WebSocket握手完成: URI: {}", request.getURI());
        }
    }

    /**
     * 从请求中获取token
     * 支持URL参数: ws://localhost:8080/ws/teacher-notifications?token=xxx
     */
    private String getTokenFromRequest(ServerHttpRequest request) {
        try {
            URI uri = request.getURI();
            String query = uri.getQuery();
            
            if (query != null && !query.isEmpty()) {
                String[] params = query.split("&");
                for (String param : params) {
                    String[] keyValue = param.split("=");
                    if (keyValue.length == 2 && "token".equals(keyValue[0])) {
                        return keyValue[1];
                    }
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("解析token参数异常: {}", e.getMessage());
            return null;
        }
    }
}
