package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxKjxmjbxx;
import com.gl.gl_lg_java.dto.KjxmjbxxQueryDTO;
import com.gl.gl_lg_java.dto.KjxmStatisticsDTO;
import com.gl.gl_lg_java.mapper.RchxKjxmjbxxMapper;
import com.gl.gl_lg_java.service.RchxKjxmjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_kjxmjbxx(科技项目基本信息)】的数据库操作Service实现
 * @createDate 2025-07-18 11:11:41
 */
@Service
@Slf4j
public class RchxKjxmjbxxServiceImpl extends ServiceImpl<RchxKjxmjbxxMapper, RchxKjxmjbxx>
        implements RchxKjxmjbxxService {

    @Autowired
    private RchxKjxmjbxxMapper kjxmjbxxMapper;

    @Override
    public RchxKjxmjbxx getByXmid(String xmid) {
        return kjxmjbxxMapper.findByXmid(xmid);
    }

    @Override
    public RchxKjxmjbxx getByXmbh(String xmbh) {
        return kjxmjbxxMapper.findByXmbh(xmbh);
    }

    @Override
    public List<RchxKjxmjbxx> listByXmmcLike(String xmmc) {
        return kjxmjbxxMapper.findByXmmcLike(xmmc);
    }

    @Override
    public List<RchxKjxmjbxx> listByXmfzrh(String xmfzrh) {
        return kjxmjbxxMapper.findByXmfzrh(xmfzrh);
    }

    @Override
    public List<RchxKjxmjbxx> listByFzrxmLike(String fzrxm) {
        return kjxmjbxxMapper.findByFzrxmLike(fzrxm);
    }

    @Override
    public List<RchxKjxmjbxx> listByXmzxzt(String xmzxzt) {
        return kjxmjbxxMapper.findByXmzxzt(xmzxzt);
    }

    @Override
    public List<RchxKjxmjbxx> listByShzt(String shzt) {
        return kjxmjbxxMapper.findByShzt(shzt);
    }

    @Override
    public List<RchxKjxmjbxx> listByMultiConditions(KjxmjbxxQueryDTO queryDTO) {
        // 使用QueryWrapper替代复杂动态SQL
        QueryWrapper<RchxKjxmjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getXmid())) {
            queryWrapper.eq("xmid", queryDTO.getXmid());
        }
        if (StringUtils.hasText(queryDTO.getXmbh())) {
            queryWrapper.like("xmbh", queryDTO.getXmbh());
        }
        if (StringUtils.hasText(queryDTO.getXmmc())) {
            queryWrapper.like("xmmc", queryDTO.getXmmc());
        }
        if (StringUtils.hasText(queryDTO.getXnbh())) {
            queryWrapper.like("xnbh", queryDTO.getXnbh());
        }
        if (StringUtils.hasText(queryDTO.getDwmc())) {
            queryWrapper.like("dwmc", queryDTO.getDwmc());
        }
        if (StringUtils.hasText(queryDTO.getXmfzrh())) {
            queryWrapper.eq("xmfzrh", queryDTO.getXmfzrh());
        }
        if (StringUtils.hasText(queryDTO.getFzrxm())) {
            queryWrapper.like("fzrxm", queryDTO.getFzrxm());
        }
        if (StringUtils.hasText(queryDTO.getXmly())) {
            queryWrapper.eq("xmly", queryDTO.getXmly());
        }
        if (StringUtils.hasText(queryDTO.getXmxz())) {
            queryWrapper.eq("xmxz", queryDTO.getXmxz());
        }
        if (StringUtils.hasText(queryDTO.getXmlb())) {
            queryWrapper.eq("xmlb", queryDTO.getXmlb());
        }
        if (StringUtils.hasText(queryDTO.getXmjb())) {
            queryWrapper.eq("xmjb", queryDTO.getXmjb());
        }
        if (StringUtils.hasText(queryDTO.getXmzxzt())) {
            queryWrapper.eq("xmzxzt", queryDTO.getXmzxzt());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getLxStartDate())) {
            queryWrapper.ge("lxrq", queryDTO.getLxStartDate());
        }
        if (StringUtils.hasText(queryDTO.getLxEndDate())) {
            queryWrapper.le("lxrq", queryDTO.getLxEndDate());
        }
        if (StringUtils.hasText(queryDTO.getKsStartDate())) {
            queryWrapper.ge("ksrq", queryDTO.getKsStartDate());
        }
        if (StringUtils.hasText(queryDTO.getKsEndDate())) {
            queryWrapper.le("ksrq", queryDTO.getKsEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("lxrq");

        return kjxmjbxxMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<RchxKjxmjbxx> pageByMultiConditions(KjxmjbxxQueryDTO queryDTO) {
        // 使用MyBatis-Plus的分页功能替代复杂动态SQL
        Page<RchxKjxmjbxx> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<RchxKjxmjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getXmid())) {
            queryWrapper.eq("xmid", queryDTO.getXmid());
        }
        if (StringUtils.hasText(queryDTO.getXmbh())) {
            queryWrapper.like("xmbh", queryDTO.getXmbh());
        }
        if (StringUtils.hasText(queryDTO.getXmmc())) {
            queryWrapper.like("xmmc", queryDTO.getXmmc());
        }
        if (StringUtils.hasText(queryDTO.getXnbh())) {
            queryWrapper.like("xnbh", queryDTO.getXnbh());
        }
        if (StringUtils.hasText(queryDTO.getDwmc())) {
            queryWrapper.like("dwmc", queryDTO.getDwmc());
        }
        if (StringUtils.hasText(queryDTO.getXmfzrh())) {
            queryWrapper.eq("xmfzrh", queryDTO.getXmfzrh());
        }
        if (StringUtils.hasText(queryDTO.getFzrxm())) {
            queryWrapper.like("fzrxm", queryDTO.getFzrxm());
        }
        if (StringUtils.hasText(queryDTO.getXmly())) {
            queryWrapper.eq("xmly", queryDTO.getXmly());
        }
        if (StringUtils.hasText(queryDTO.getXmxz())) {
            queryWrapper.eq("xmxz", queryDTO.getXmxz());
        }
        if (StringUtils.hasText(queryDTO.getXmlb())) {
            queryWrapper.eq("xmlb", queryDTO.getXmlb());
        }
        if (StringUtils.hasText(queryDTO.getXmjb())) {
            queryWrapper.eq("xmjb", queryDTO.getXmjb());
        }
        if (StringUtils.hasText(queryDTO.getXmzxzt())) {
            queryWrapper.eq("xmzxzt", queryDTO.getXmzxzt());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getLxStartDate())) {
            queryWrapper.ge("lxrq", queryDTO.getLxStartDate());
        }
        if (StringUtils.hasText(queryDTO.getLxEndDate())) {
            queryWrapper.le("lxrq", queryDTO.getLxEndDate());
        }
        if (StringUtils.hasText(queryDTO.getKsStartDate())) {
            queryWrapper.ge("ksrq", queryDTO.getKsStartDate());
        }
        if (StringUtils.hasText(queryDTO.getKsEndDate())) {
            queryWrapper.le("ksrq", queryDTO.getKsEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("lxrq");

        return kjxmjbxxMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveKjxmjbxx(RchxKjxmjbxx kjxmjbxx) {
        try {
            return save(kjxmjbxx);
        } catch (Exception e) {
            log.error("新增科技项目信息失败: {}", e.getMessage());
            throw new RuntimeException("新增科技项目信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByXmid(RchxKjxmjbxx kjxmjbxx) {
        try {
            return updateById(kjxmjbxx);
        } catch (Exception e) {
            log.error("更新科技项目信息失败: {}", e.getMessage());
            throw new RuntimeException("更新科技项目信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByXmid(String xmid) {
        try {
            return removeById(xmid);
        } catch (Exception e) {
            log.error("删除科技项目信息失败: {}", e.getMessage());
            throw new RuntimeException("删除科技项目信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByXmids(List<String> xmids) {
        try {
            return removeByIds(xmids);
        } catch (Exception e) {
            log.error("批量删除科技项目信息失败: {}", e.getMessage());
            throw new RuntimeException("批量删除科技项目信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchKjxmjbxx(List<RchxKjxmjbxx> kjxmjbxxList) {
        try {
            return saveBatch(kjxmjbxxList);
        } catch (Exception e) {
            log.error("批量新增科技项目信息失败: {}", e.getMessage());
            throw new RuntimeException("批量新增科技项目信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchKjxmjbxx(List<RchxKjxmjbxx> kjxmjbxxList) {
        try {
            return updateBatchById(kjxmjbxxList);
        } catch (Exception e) {
            log.error("批量更新科技项目信息失败: {}", e.getMessage());
            throw new RuntimeException("批量更新科技项目信息失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByXmjb() {
        try {
            log.info("查询项目级别统计（使用数据库聚合查询）");
            Map<String, Integer> xmjbCounts = convertToIntegerMap(kjxmjbxxMapper.getStatsByXmjb());
            log.info("项目级别统计结果: {}", xmjbCounts);
            return xmjbCounts;
        } catch (Exception e) {
            log.error("查询项目级别统计失败: {}", e.getMessage());
            throw new RuntimeException("查询项目级别统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByXmzxzt() {
        try {
            log.info("查询执行状态统计（使用数据库聚合查询）");
            Map<String, Integer> xmzxztCounts = convertToIntegerMap(kjxmjbxxMapper.getStatsByXmzxzt());
            log.info("执行状态统计结果: {}", xmzxztCounts);
            return xmzxztCounts;
        } catch (Exception e) {
            log.error("查询执行状态统计失败: {}", e.getMessage());
            throw new RuntimeException("查询执行状态统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByShzt() {
        try {
            log.info("查询审核状态统计（使用数据库聚合查询）");
            Map<String, Integer> shztCounts = convertToIntegerMap(kjxmjbxxMapper.getStatsByShzt());
            log.info("审核状态统计结果: {}", shztCounts);
            return shztCounts;
        } catch (Exception e) {
            log.error("查询审核状态统计失败: {}", e.getMessage());
            throw new RuntimeException("查询审核状态统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getXmjbOptions() {
        try {
            log.info("快速获取项目级别选项列表（不统计数量）");
            List<String> xmjbList = kjxmjbxxMapper.findDistinctXmjb();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String xmjb : xmjbList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", xmjb);
                option.put("label", xmjb);
                options.add(option);
            }

            log.info("项目级别选项列表快速获取成功，共{}个级别", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取项目级别选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取项目级别选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getXmzxztOptions() {
        try {
            log.info("快速获取执行状态选项列表（不统计数量）");
            List<String> xmzxztList = kjxmjbxxMapper.findDistinctXmzxzt();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String xmzxzt : xmzxztList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", xmzxzt);
                option.put("label", xmzxzt);
                options.add(option);
            }

            log.info("执行状态选项列表快速获取成功，共{}个状态", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取执行状态选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取执行状态选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getShztOptions() {
        try {
            log.info("快速获取审核状态选项列表（不统计数量）");
            List<String> shztList = kjxmjbxxMapper.findDistinctShzt();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String shzt : shztList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", shzt);
                option.put("label", shzt);
                options.add(option);
            }

            log.info("审核状态选项列表快速获取成功，共{}个状态", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取审核状态选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取审核状态选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public Long getPersonalCount(String zgh) {
        try {
            log.info("查询教师个人科技项目数量，职工号: {}", zgh);
            Long count = kjxmjbxxMapper.getPersonalCountByZgh(zgh);
            log.info("教师个人科技项目数量查询结果: {}", count);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("查询教师个人科技项目数量失败: {}", e.getMessage());
            throw new RuntimeException("查询教师个人科技项目数量失败: " + e.getMessage());
        }
    }

    /**
     * 转换统计结果为Integer类型的Map
     */
    private Map<String, Integer> convertToIntegerMap(List<Map<String, Object>> statsList) {
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> stats : statsList) {
            String fieldValue = (String) stats.get("fieldValue");
            Object countObj = stats.get("count");
            Integer count = 0;
            if (countObj instanceof Number) {
                count = ((Number) countObj).intValue();
            }
            result.put(fieldValue, count);
        }
        return result;
    }

    @Override
    public KjxmStatisticsDTO.ComprehensiveStatistics getComprehensiveStatistics() {
        try {
            log.info("开始获取科技项目综合统计数据");

            KjxmStatisticsDTO.ComprehensiveStatistics statistics = new KjxmStatisticsDTO.ComprehensiveStatistics();

            // 获取单位统计
            List<KjxmStatisticsDTO.UnitStatistics> unitStats = kjxmjbxxMapper.getUnitStatistics();
            statistics.setUnitStatistics(unitStats);
            log.debug("单位统计数据获取完成，共{}个单位", unitStats.size());

            // 获取项目类别统计
            List<KjxmStatisticsDTO.CategoryStatistics> categoryStats = kjxmjbxxMapper.getCategoryStatistics();
            statistics.setCategoryStatistics(categoryStats);
            log.debug("项目类别统计数据获取完成，共{}个类别", categoryStats.size());

            // 获取负责人TOP10
            List<KjxmStatisticsDTO.LeaderStatistics> topLeaders = kjxmjbxxMapper.getTopLeaders();
            statistics.setTopLeaders(topLeaders);
            log.debug("负责人TOP10数据获取完成，共{}个负责人", topLeaders.size());

            // 获取总体统计
            Map<String, Object> overallStats = kjxmjbxxMapper.getOverallStatistics();
            if (overallStats != null) {
                statistics.setTotalProjects(((Number) overallStats.get("totalProjects")).longValue());
                statistics.setTotalUnits(((Number) overallStats.get("totalUnits")).longValue());
                statistics.setTotalCategories(((Number) overallStats.get("totalCategories")).longValue());

                Object totalFundingObj = overallStats.get("totalFunding");
                if (totalFundingObj instanceof BigDecimal) {
                    statistics.setTotalFunding((BigDecimal) totalFundingObj);
                } else if (totalFundingObj instanceof Number) {
                    statistics.setTotalFunding(new BigDecimal(totalFundingObj.toString()));
                } else {
                    statistics.setTotalFunding(BigDecimal.ZERO);
                }
            }

            log.info("科技项目综合统计数据获取完成: 项目总数={}, 单位总数={}, 类别总数={}, 总经费={}",
                    statistics.getTotalProjects(), statistics.getTotalUnits(),
                    statistics.getTotalCategories(), statistics.getTotalFunding());

            return statistics;

        } catch (Exception e) {
            log.error("获取科技项目综合统计数据失败", e);
            throw new RuntimeException("获取统计数据失败: " + e.getMessage());
        }
    }

    // ========== 人才匹配算法专用方法实现 ==========

    @Override
    public boolean hasNationalNaturalScienceFund(String teacherZgh) {
        try {
            QueryWrapper<RchxKjxmjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("xmfzrh", teacherZgh)
                    .and(wrapper -> wrapper.like("xmmc", "国家自然科学基金").or().like("xmlb", "国家自然科学基金"))
                    .like("xmlb", "面上项目");

            return count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查教师{}国家自然科学基金面上项目失败: {}", teacherZgh, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean hasNationalSocialScienceFund(String teacherZgh) {
        try {
            QueryWrapper<RchxKjxmjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("xmfzrh", teacherZgh)
                    .and(wrapper -> wrapper.like("xmmc", "国家社会科学基金").or().like("xmlb", "国家社会科学基金"))
                    .like("xmlb", "年度项目");

            return count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查教师{}国家社会科学基金失败: {}", teacherZgh, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean hasGuangxiYouthFund(String teacherZgh) {
        try {
            QueryWrapper<RchxKjxmjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("xmfzrh", teacherZgh)
                    .and(wrapper -> wrapper.like("xmmc", "广西自然科学基金").or().like("xmlb", "广西自然科学基金"))
                    .like("xmlb", "杰出青年基金");

            return count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查教师{}广西杰出青年基金失败: {}", teacherZgh, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean hasAdequateResearchFunding(String teacherZgh) {
        try {
            QueryWrapper<RchxKjxmjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("xmfzrh", teacherZgh)
                    .and(wrapper -> wrapper.like("xmlb", "省部级").or().like("xmjb", "省部级"));

            List<RchxKjxmjbxx> projects = list(queryWrapper);

            // 计算总经费（简化处理，实际应该解析经费字段）
            double totalFunding = projects.size() * 50000; // 假设每个省部级项目平均5万元

            // 自然科学类≥20万，社会科学类≥10万（简化标准）
            return totalFunding >= 200000;
        } catch (Exception e) {
            log.error("检查教师{}科研经费失败: {}", teacherZgh, e.getMessage());
            return false;
        }
    }

    @Override
    public Map<String, Integer> countProjectsByLevel(String teacherZgh) {
        Map<String, Integer> result = new HashMap<>();
        try {
            QueryWrapper<RchxKjxmjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("xmfzrh", teacherZgh);

            List<RchxKjxmjbxx> projects = list(queryWrapper);

            int nationalCount = 0;
            int provincialCount = 0;
            int municipalCount = 0;
            int otherCount = 0;

            for (RchxKjxmjbxx project : projects) {
                String level = project.getXmjb();
                if (level != null) {
                    if (level.contains("国家")) {
                        nationalCount++;
                    } else if (level.contains("省") || level.contains("部")) {
                        provincialCount++;
                    } else if (level.contains("市") || level.contains("厅") || level.contains("局")) {
                        municipalCount++;
                    } else {
                        otherCount++;
                    }
                }
            }

            result.put("national", nationalCount);
            result.put("provincial", provincialCount);
            result.put("municipal", municipalCount);
            result.put("other", otherCount);
            result.put("total", projects.size());

        } catch (Exception e) {
            log.error("统计教师{}项目数量失败: {}", teacherZgh, e.getMessage());
            result.put("national", 0);
            result.put("provincial", 0);
            result.put("municipal", 0);
            result.put("other", 0);
            result.put("total", 0);
        }

        return result;
    }

    @Override
    public int countByTeacher(String teacherZgh) {
        try {
            QueryWrapper<RchxKjxmjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("xmfzrh", teacherZgh);

            return Math.toIntExact(count(queryWrapper));
        } catch (Exception e) {
            log.error("统计教师{}项目总数失败: {}", teacherZgh, e.getMessage());
            return 0;
        }
    }
}
