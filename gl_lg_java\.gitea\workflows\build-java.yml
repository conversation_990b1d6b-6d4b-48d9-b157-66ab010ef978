name: Build Java Project

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
      
    - name: 检查环境和目录结构
      run: |
        echo "=== 环境信息 ==="
        echo "当前时间: $(date)"
        echo "当前目录: $(pwd)"
        echo "目录结构:"
        ls -la
        echo "Java版本:"
        java -version
        echo "Maven版本:"
        mvn -version
        
    - name: 进入Java项目目录并构建
      run: |
        echo "=== 进入Java项目目录 ==="
        cd gl_lg_java
        echo "Java项目目录内容:"
        ls -la
        
        echo "=== 开始编译 ==="
        mvn clean compile
        
        echo "=== 运行测试 ==="
        mvn test || echo "测试失败，继续构建"
        
        echo "=== 打包JAR ==="
        mvn package -DskipTests
        
        echo "=== 查看构建结果 ==="
        ls -la target/
        
    - name: 重命名和准备JAR文件
      run: |
        cd gl_lg_java/target
        JAR_FILE=$(ls *.jar | grep -v original | head -1)
        if [ -n "$JAR_FILE" ]; then
          NEW_NAME="gl-lg-java-$(date +%Y%m%d-%H%M%S).jar"
          cp "$JAR_FILE" "$NEW_NAME"
          echo "JAR文件: $NEW_NAME"
          ls -la *.jar
          # 复制到根目录方便上传
          cp *.jar ../../
        else
          echo "未找到JAR文件"
          exit 1
        fi
        
    - name: 上传JAR文件
      uses: actions/upload-artifact@v3
      with:
        name: java-jar-package
        path: "*.jar"
        retention-days: 30
        
    - name: 显示最终结果
      run: |
        echo "=== 构建完成 ==="
        echo "构建时间: $(date)"
        echo "根目录JAR文件:"
        ls -la *.jar
        echo "文件大小:"
        du -h *.jar
