package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectFiles;
import com.gl.gl_lg_java.service.RchxProjectFilesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 项目文件表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@RestController
@RequestMapping("/api/project-files")
public class RchxProjectFilesController {

    @Autowired
    private RchxProjectFilesService projectFilesService;

    /**
     * 分页查询项目文件
     */
    @GetMapping("/page")
    public Result<IPage<RchxProjectFiles>> page(@RequestParam(defaultValue = "1") Integer current,
                                               @RequestParam(defaultValue = "10") Integer size,
                                               @RequestParam(required = false) String businessType,
                                               @RequestParam(required = false) Long businessId,
                                               @RequestParam(required = false) String projectCode,
                                               @RequestParam(required = false) String fileCategory,
                                               @RequestParam(required = false) String uploadBy) {
        try {
            Page<RchxProjectFiles> page = new Page<>(current, size);
            IPage<RchxProjectFiles> result = projectFilesService.pageQuery(page, businessType, businessId, projectCode, fileCategory, uploadBy);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目文件失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目文件
     */
    @GetMapping("/{id}")
    public Result<RchxProjectFiles> getById(@PathVariable Long id) {
        try {
            RchxProjectFiles file = projectFilesService.getById(id);
            if (file == null || file.getIsDeleted()) {
                return Result.error("文件不存在");
            }
            return Result.success(file);
        } catch (Exception e) {
            log.error("查询项目文件失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据业务信息获取文件列表
     */
    @GetMapping("/business/{businessType}/{businessId}")
    public Result<List<RchxProjectFiles>> getByBusiness(@PathVariable String businessType, 
                                                       @PathVariable Long businessId) {
        try {
            List<RchxProjectFiles> files = projectFilesService.getByBusiness(businessType, businessId);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据业务信息查询文件失败: businessType={}, businessId={}", businessType, businessId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目编号获取文件列表
     */
    @GetMapping("/project/{projectCode}")
    public Result<List<RchxProjectFiles>> getByProjectCode(@PathVariable String projectCode) {
        try {
            List<RchxProjectFiles> files = projectFilesService.getByProjectCode(projectCode);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据项目编号查询文件失败: projectCode={}", projectCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件分类获取文件列表
     */
    @GetMapping("/category/{businessType}/{businessId}/{fileCategory}")
    public Result<List<RchxProjectFiles>> getByCategory(@PathVariable String businessType,
                                                       @PathVariable Long businessId,
                                                       @PathVariable String fileCategory) {
        try {
            List<RchxProjectFiles> files = projectFilesService.getByCategory(businessType, businessId, fileCategory);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据文件分类查询文件失败: businessType={}, businessId={}, fileCategory={}", 
                     businessType, businessId, fileCategory, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public Result<RchxProjectFiles> uploadFile(@RequestParam("file") MultipartFile file,
                                              @RequestParam String businessType,
                                              @RequestParam Long businessId,
                                              @RequestParam(required = false) String projectCode,
                                              @RequestParam String fileCategory,
                                              @RequestParam(required = false) String description,
                                              @RequestParam String uploadBy) {
        try {
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            
            RchxProjectFiles projectFile = projectFilesService.uploadFile(file, businessType, businessId, 
                                                                         projectCode, fileCategory, description, uploadBy);
            return Result.success(projectFile);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteFile(@PathVariable Long id, @RequestParam String deleteBy) {
        try {
            boolean success = projectFilesService.deleteFile(id, deleteBy);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除文件失败: id={}, deleteBy={}", id, deleteBy, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除文件
     */
    @DeleteMapping("/batch")
    public Result<String> batchDeleteFiles(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) request.get("ids");
            String deleteBy = (String) request.get("deleteBy");
            
            if (ids == null || ids.isEmpty()) {
                return Result.error("文件ID列表不能为空");
            }
            
            boolean success = projectFilesService.batchDeleteFiles(ids, deleteBy);
            return success ? Result.success("批量删除成功") : Result.error("批量删除失败");
        } catch (Exception e) {
            log.error("批量删除文件失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件下载URL
     */
    @GetMapping("/{id}/download-url")
    public Result<String> getDownloadUrl(@PathVariable Long id) {
        try {
            String downloadUrl = projectFilesService.getDownloadUrl(id);
            if (downloadUrl == null) {
                return Result.error("文件不存在或已删除");
            }
            return Result.success(downloadUrl);
        } catch (Exception e) {
            log.error("获取文件下载URL失败: id={}", id, e);
            return Result.error("获取下载URL失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件统计信息
     */
    @GetMapping("/statistics")
    public Result<Object> getFileStatistics(@RequestParam(required = false) String businessType,
                                           @RequestParam(required = false) String projectCode) {
        try {
            Object statistics = projectFilesService.getFileStatistics(businessType, projectCode);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取文件统计信息失败: businessType={}, projectCode={}", businessType, projectCode, e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }
}
