package com.gl.gl_lg_java.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.gl_lg_java.annotation.RequirePermission;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.util.PermissionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;

/**
 * 权限拦截器
 */
@Component
@Slf4j
public class PermissionInterceptor implements HandlerInterceptor {
    
    @Autowired
    private PermissionUtil permissionUtil;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 只处理方法处理器
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        
        // 检查方法上的权限注解
        RequirePermission methodAnnotation = method.getAnnotation(RequirePermission.class);
        // 检查类上的权限注解
        RequirePermission classAnnotation = handlerMethod.getBeanType().getAnnotation(RequirePermission.class);
        
        RequirePermission requirePermission = methodAnnotation != null ? methodAnnotation : classAnnotation;
        
        // 如果没有权限注解，直接放行
        if (requirePermission == null) {
            return true;
        }
        
        // 获取当前用户权限
        String userPermission = (String) request.getAttribute("currentUserQx");
        if (userPermission == null) {
            log.warn("用户权限信息为空: {}", request.getRequestURI());
            writeErrorResponse(response, "用户权限信息异常");
            return false;
        }
        
        // 验证权限
        boolean hasPermission;
        if (requirePermission.exact()) {
            // 精确匹配权限
            hasPermission = permissionUtil.isPermission(userPermission, requirePermission.value());
        } else {
            // 权限级别匹配
            hasPermission = permissionUtil.hasPermission(userPermission, requirePermission.value());
        }
        
        if (!hasPermission) {
            log.warn("用户权限不足: 用户权限={}, 需要权限={}, 请求路径={}", 
                    userPermission, requirePermission.value(), request.getRequestURI());
            writeErrorResponse(response, "权限不足，无法访问该资源");
            return false;
        }
        
        return true;
    }
    
    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json;charset=UTF-8");
        
        Result<String> result = Result.error(403, message);
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonResponse = objectMapper.writeValueAsString(result);
        
        response.getWriter().write(jsonResponse);
    }
}
