# 轻量化人才适配度分析实现方案

## 🎯 方案概述

基于现有数据表，零数据库改动，实时计算人才项目适配度分析。直接利用教职工、获奖成果、项目等现有数据，通过算法实现智能匹配推荐。

## 📊 核心算法设计

### 四维度评分体系

#### 1. 学术影响力 (Academic Influence)
**数据来源**: `t_rchx_hjcgjbxx` 获奖成果表
**计算因子**:
- `hjjb` (获奖级别): 国家级(30分) > 省级(20分) > 市级(10分)
- `hjrq` (获奖时间): 近2年(1.5倍) > 近5年(1.0倍) > 5年前(0.5倍)
- `zzzs` (总作者数): ≤3人(1.0倍) > 3人(0.8倍)

**计算公式**:
```
学术影响力 = Σ(获奖级别分 × 时间衰减因子 × 作者贡献因子)
```

#### 2. 领域深耕度 (Field Expertise)  
**数据来源**: `t_rchx_hjcgjbxx` 获奖成果表
**计算因子**:
- `xkml` (学科门类): 完全匹配(20分) > 相关(10分) > 不匹配(0分)
- `yjxk` (一级学科): 完全匹配(15分) > 相关(8分) > 不匹配(0分)
- `hjcgmc` (成果名称): 关键词匹配度 × 10分

**计算公式**:
```
领域深耕度 = Σ(学科匹配分 + 关键词匹配分) + 领域成果数量 × 5
```

#### 3. 团队适配值 (Team Compatibility)
**数据来源**: `t_rchx_jzgjbxx` 教职工表 + `t_rchx_hjcgjbxx` 获奖成果表
**计算因子**:
- `zcdj` (职称等级): 正高级(40分) > 副高级(30分) > 中级(20分) > 初级(10分)
- `zgxl` (最高学历): 博士(30分) > 硕士(20分) > 本科(10分)
- `sfhz` (合作经验): 每个合作项目+5分

**计算公式**:
```
团队适配值 = 职称得分 + 学历得分 + 合作经验得分
```

#### 4. 成果落地性 (Achievement Practicality)
**数据来源**: `t_rchx_hjcgjbxx` 获奖成果表
**计算因子**:
- `shzt` (审核状态): 已审核(20分) > 待审核(10分) > 其他(0分)
- `bjdw` (颁奖单位): 国家级(15分) > 省级(10分) > 其他(5分)
- `cgxs` (成果形式): 实用性评分

**计算公式**:
```
成果落地性 = Σ(审核状态分 + 颁奖单位分 + 成果形式分)
```

## 🏗️ 实现架构

### 1. DTO层设计
```
ProjectRequirementDTO     # 项目需求配置
TalentMatchingResultDTO   # 分析结果
TeacherCapabilityDTO      # 教师能力分析
WeightConfigDTO           # 权重配置
```

### 2. Service层设计
```
TalentMatchingService     # 核心匹配服务
├── analyzeTalentMatching()           # 主分析方法
├── calculateAcademicInfluence()      # 学术影响力计算
├── calculateFieldExpertise()         # 领域深耕度计算
├── calculateTeamCompatibility()      # 团队适配值计算
└── calculateAchievementPracticality() # 成果落地性计算
```

### 3. Controller层设计
```
TalentMatchingController  # 适配度分析控制器
├── POST /api/talent-matching/analyze        # 执行分析
├── GET /api/talent-matching/teacher/{id}    # 教师能力分析
└── GET /api/talent-matching/templates       # 获取预设模板
```

## 📋 实施步骤

### 第一步: 创建DTO类 (预计30分钟)
1. `ProjectRequirementDTO` - 项目需求配置
2. `TalentMatchingResultDTO` - 分析结果
3. `TeacherCapabilityDTO` - 教师能力详情
4. `WeightConfigDTO` - 权重配置

### 第二步: 实现核心Service (预计2小时)
1. `TalentMatchingService` - 主服务类
2. 四个维度的计算方法
3. 综合评分和排序逻辑
4. 风险预警和推荐理由生成

### 第三步: 添加Controller接口 (预计30分钟)
1. 分析接口
2. 教师详情接口
3. 权限控制和异常处理

### 第四步: 前端界面开发 (预计4小时)
1. 项目配置面板
2. 权重调节界面
3. 结果展示页面
4. 详细分析弹窗

## 🎛️ 前端交互设计

### 配置面板
```vue
<template>
  <div class="talent-matching-config">
    <!-- 项目信息 -->
    <el-card title="项目信息">
      <el-form :model="projectConfig">
        <el-form-item label="项目名称">
          <el-input v-model="projectConfig.name" />
        </el-form-item>
        <el-form-item label="研究领域">
          <el-select v-model="projectConfig.fields" multiple>
            <el-option label="计算机科学与技术" value="计算机科学与技术" />
            <el-option label="测绘科学与技术" value="测绘科学与技术" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 权重配置 -->
    <el-card title="权重配置">
      <div class="weight-config">
        <div class="weight-item">
          <label>学术影响力</label>
          <el-slider v-model="weights.academic" :max="100" />
          <span>{{ weights.academic }}%</span>
        </div>
        <!-- 其他权重配置... -->
      </div>
    </el-card>
    
    <!-- 硬条件筛选 -->
    <el-card title="基础要求">
      <el-form :model="requirements">
        <el-form-item label="最低职称">
          <el-select v-model="requirements.minTitle">
            <el-option label="正高级" value="正高级" />
            <el-option label="副高级" value="副高级" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
```

### 结果展示
```vue
<template>
  <div class="matching-results">
    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-statistic title="候选人总数" :value="results.total" />
      <el-statistic title="符合条件" :value="results.qualified" />
      <el-statistic title="强烈推荐" :value="results.highlyRecommended" />
    </div>
    
    <!-- 候选人列表 -->
    <el-table :data="results.candidates" class="candidates-table">
      <el-table-column prop="ranking" label="排名" width="80" />
      <el-table-column prop="teacherName" label="姓名" width="120" />
      <el-table-column prop="department" label="部门" width="150" />
      <el-table-column prop="titleLevel" label="职称" width="100" />
      <el-table-column label="适配度" width="200">
        <template #default="{ row }">
          <el-progress 
            :percentage="row.totalScore" 
            :color="getScoreColor(row.totalScore)"
            :format="() => `${row.totalScore}分`"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button size="small" @click="showDetail(row)">
            详细分析
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
```

## 🔧 技术要点

### 1. 性能优化
- 使用MyBatis-Plus的批量查询
- 合理使用数据库索引
- 结果缓存机制

### 2. 算法优化
- 权重归一化处理
- 分数区间标准化
- 异常值处理

### 3. 用户体验
- 实时权重调节反馈
- 分析进度提示
- 结果可视化展示

## 📈 预期效果

1. **零数据库改动**: 完全基于现有表结构
2. **实时计算**: 无需数据同步，结果实时准确
3. **灵活配置**: 权重可调，适应不同项目需求
4. **智能推荐**: 多维度分析，提供决策支持
5. **易于维护**: 代码结构清晰，扩展性强

## 🚀 开始实施

接下来将按照以上步骤逐步实现：
1. 创建DTO类
2. 实现Service层
3. 添加Controller
4. 开发前端界面

每个步骤完成后都会进行测试验证，确保功能正常运行。
