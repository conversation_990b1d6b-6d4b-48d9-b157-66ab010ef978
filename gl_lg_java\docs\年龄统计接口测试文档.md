# 年龄统计接口测试文档

## 接口信息

**接口地址：** `GET /api/jzgjbxx/stats/age`

**请求方式：** GET

**权限要求：** 教师权限及以上

**请求头：**
```
Authorization: Bearer {token}
```

## 接口功能
统计教职工按年龄段分布情况，分为三个年龄段：
- **35岁以下**
- **35-45岁** 
- **45岁以上**
- **年龄未知**（出生日期为空或格式错误）

## 测试步骤

### 1. 获取Token
首先需要登录获取token：
```bash
# 使用PowerShell
powershell -Command "Invoke-WebRequest -Uri 'http://localhost:8080/api/auth/login' -Method POST -Headers @{'Content-Type'='application/json'} -InFile 'test_login.json' | Select-Object -ExpandProperty Content"
```

### 2. 调用年龄统计接口
```bash
# 使用PowerShell（替换{token}为实际token）
powershell -Command "Invoke-WebRequest -Uri 'http://localhost:8080/api/jzgjbxx/stats/age' -Method GET -Headers @{'Authorization'='Bearer {token}'} | Select-Object -ExpandProperty Content"
```

## 响应示例

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "35岁以下": 15,
    "35-45岁": 28,
    "45岁以上": 42,
    "年龄未知": 3
  }
}
```

### 失败响应
```json
{
  "code": 401,
  "message": "Token无效或已过期，请重新登录",
  "data": null
}
```

```json
{
  "code": 403,
  "message": "权限不足",
  "data": null
}
```

```json
{
  "code": 500,
  "message": "查询失败: 数据库连接异常",
  "data": null
}
```

## 年龄计算逻辑

### 年龄段划分
- **35岁以下**: age < 35
- **35-45岁**: 35 ≤ age ≤ 45  
- **45岁以上**: age > 45
- **年龄未知**: 出生日期为空或无法解析

### 支持的出生日期格式
- `yyyy-MM-dd` (如: 1990-05-15)
- `yyyy/MM/dd` (如: 1990/05/15)
- `yyyy.MM.dd` (如: 1990.05.15)
- `yyyy-M-d` (如: 1990-5-15)
- `yyyy/M/d` (如: 1990/5/15)
- `yyyy.M.d` (如: 1990.5.15)
- `yyyy` (如: 1990)

### 年龄计算方式
```
年龄 = 当前年份 - 出生年份
```

## 测试用例

### 测试用例1：正常查询
**描述：** 使用有效token查询年龄统计
**预期结果：** 返回各年龄段的统计数据

### 测试用例2：无token访问
**描述：** 不携带Authorization头访问接口
**预期结果：** 返回401未授权错误

### 测试用例3：无效token
**描述：** 使用过期或错误的token访问
**预期结果：** 返回401 token无效错误

### 测试用例4：权限不足
**描述：** 使用权限不足的用户token访问
**预期结果：** 返回403权限不足错误

## 前端对接示例

### Vue.js 示例
```javascript
// 获取年龄统计数据
async getAgeStats() {
  try {
    const response = await this.$http.get('/api/jzgjbxx/stats/age', {
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });
    
    if (response.data.code === 200) {
      this.ageStats = response.data.data;
      console.log('年龄统计:', this.ageStats);
    } else {
      this.$message.error(response.data.message);
    }
  } catch (error) {
    this.$message.error('获取年龄统计失败');
  }
}
```

### 图表展示示例（ECharts）
```javascript
// 将统计数据转换为图表格式
formatAgeStatsForChart(ageStats) {
  const chartData = {
    labels: Object.keys(ageStats),
    data: Object.values(ageStats)
  };
  
  // 饼图配置
  const pieOption = {
    title: {
      text: '教职工年龄分布统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '年龄分布',
      type: 'pie',
      radius: '50%',
      data: chartData.labels.map((label, index) => ({
        name: label,
        value: chartData.data[index]
      }))
    }]
  };
  
  return pieOption;
}
```

## 相关接口对比

| 接口 | 地址 | 功能 | 返回格式 |
|------|------|------|----------|
| 职称统计（详细） | `/api/jzgjbxx/stats/zc` | 职称统计含教师详情 | `{statistics: {}, details: {}}` |
| 职称统计（简单） | `/api/jzgjbxx/stats/zc-count` | 仅职称统计数据 | `{"教授": 10, "副教授": 20}` |
| 职称等级统计 | `/api/jzgjbxx/stats/zcdj` | 职称等级统计 | `{"正高级": 15, "副高级": 25}` |
| **年龄统计** | `/api/jzgjbxx/stats/age` | 年龄段统计 | `{"35岁以下": 15, "35-45岁": 28}` |

## 注意事项

1. **数据准确性**：统计基于数据库中的出生日期字段（csrq），如果该字段为空或格式错误，会归类到"年龄未知"
2. **年龄计算**：使用当前年份减去出生年份的简单计算方式
3. **权限控制**：需要至少"教师"权限才能访问
4. **性能考虑**：该接口会查询所有有出生日期的教师记录，数据量大时可能较慢
5. **缓存建议**：前端可考虑缓存统计结果，避免频繁请求

## 错误排查

### 常见问题
1. **401错误**：检查token是否有效，是否已过期
2. **403错误**：检查用户权限是否足够
3. **500错误**：检查数据库连接，查看服务器日志
4. **数据异常**：检查出生日期字段格式是否正确

### 日志查看
服务器端会记录详细的执行日志：
```
2025-07-23 16:30:00.123 INFO  - 查询年龄段统计（基于出生日期计算）
2025-07-23 16:30:00.456 INFO  - 年龄段统计结果: {35岁以下=15, 35-45岁=28, 45岁以上=42, 年龄未知=3}
```

---

**文档版本：** v1.0  
**更新时间：** 2025-07-23  
**维护人员：** 后端开发团队
