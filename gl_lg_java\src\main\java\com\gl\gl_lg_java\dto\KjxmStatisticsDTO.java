package com.gl.gl_lg_java.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 科技项目统计数据传输对象
 */
@Data
public class KjxmStatisticsDTO {

    /**
     * 单位统计信息
     */
    @Data
    public static class UnitStatistics {
        private String dwmc;        // 单位名称
        private Long projectCount;  // 项目数量
        private BigDecimal totalFunding; // 总经费
    }

    /**
     * 项目类别统计信息
     */
    @Data
    public static class CategoryStatistics {
        private String xmlb;        // 项目类别
        private Long projectCount;  // 项目数量
        private BigDecimal totalFunding; // 总经费
    }

    /**
     * 负责人统计信息
     */
    @Data
    public static class LeaderStatistics {
        private String fzrxm;       // 负责人姓名
        private Long projectCount;  // 项目数量
        private BigDecimal totalFunding; // 总经费
        private String dwmc;        // 所属单位
    }

    /**
     * 综合统计结果
     */
    @Data
    public static class ComprehensiveStatistics {
        // 单位统计 - 所有单位及其项目数量
        private List<UnitStatistics> unitStatistics;
        
        // 项目类别统计 - 所有类别及其项目数量
        private List<CategoryStatistics> categoryStatistics;
        
        // 负责人TOP10 - 项目数量最多的前10个负责人
        private List<LeaderStatistics> topLeaders;
        
        // 总体统计
        private Long totalProjects;      // 项目总数
        private Long totalUnits;         // 单位总数
        private Long totalCategories;    // 类别总数
        private BigDecimal totalFunding; // 总经费
    }
}
