package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectCollection;
import com.gl.gl_lg_java.service.RchxProjectCollectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目选题征集表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@RestController
@RequestMapping("/api/project-collection")
public class RchxProjectCollectionController {

    @Autowired
    private RchxProjectCollectionService projectCollectionService;

    /**
     * 分页查询项目征集
     */
    @GetMapping("/page")
    public Result<IPage<RchxProjectCollection>> page(@RequestParam(defaultValue = "1") Integer current,
                                                    @RequestParam(defaultValue = "10") Integer size,
                                                    @RequestParam(required = false) Integer categoryId,
                                                    @RequestParam(required = false) Integer typeId,
                                                    @RequestParam(required = false) Integer deptId,
                                                    @RequestParam(required = false) String status,
                                                    @RequestParam(required = false) String collectionName) {
        try {
            Page<RchxProjectCollection> page = new Page<>(current, size);
            IPage<RchxProjectCollection> result = projectCollectionService.pageQuery(page, categoryId, typeId, deptId, status, collectionName);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目征集失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前有效的征集
     */
    @GetMapping("/active")
    public Result<List<RchxProjectCollection>> getActiveCollections() {
        try {
            List<RchxProjectCollection> collections = projectCollectionService.getActiveCollections();
            return Result.success(collections);
        } catch (Exception e) {
            log.error("获取有效征集失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目征集
     */
    @GetMapping("/{id}")
    public Result<RchxProjectCollection> getById(@PathVariable Long id) {
        try {
            RchxProjectCollection collection = projectCollectionService.getById(id);
            if (collection == null) {
                return Result.error("项目征集不存在");
            }
            return Result.success(collection);
        } catch (Exception e) {
            log.error("查询项目征集失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据征集编号查询项目征集
     */
    @GetMapping("/code/{collectionCode}")
    public Result<RchxProjectCollection> getByCollectionCode(@PathVariable String collectionCode) {
        try {
            RchxProjectCollection collection = projectCollectionService.getByCollectionCode(collectionCode);
            if (collection == null) {
                return Result.error("项目征集不存在");
            }
            return Result.success(collection);
        } catch (Exception e) {
            log.error("根据编号查询项目征集失败: collectionCode={}", collectionCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目征集
     */
    @PostMapping
    public Result<String> save(@RequestBody RchxProjectCollection collection) {
        try {
            // 检查编号是否重复
            if (projectCollectionService.existsByCollectionCode(collection.getCollectionCode(), null)) {
                return Result.error("征集编号已存在");
            }
            
            boolean success = projectCollectionService.save(collection);
            return success ? Result.success("新增成功") : Result.error("新增失败");
        } catch (Exception e) {
            log.error("新增项目征集失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目征集
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Long id, @RequestBody RchxProjectCollection collection) {
        try {
            // 检查编号是否重复
            if (projectCollectionService.existsByCollectionCode(collection.getCollectionCode(), id)) {
                return Result.error("征集编号已存在");
            }
            
            collection.setId(id);
            boolean success = projectCollectionService.updateById(collection);
            return success ? Result.success("更新成功") : Result.error("更新失败");
        } catch (Exception e) {
            log.error("更新项目征集失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目征集
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        try {
            boolean success = projectCollectionService.removeById(id);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除项目征集失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 提交征集
     */
    @PutMapping("/{id}/submit")
    public Result<String> submitCollection(@PathVariable Long id, @RequestParam String submitBy) {
        try {
            boolean success = projectCollectionService.submitCollection(id, submitBy);
            return success ? Result.success("提交成功") : Result.error("提交失败");
        } catch (Exception e) {
            log.error("提交征集失败: id={}, submitBy={}", id, submitBy, e);
            return Result.error("提交失败: " + e.getMessage());
        }
    }

    /**
     * 审核征集
     */
    @PutMapping("/{id}/review")
    public Result<String> reviewCollection(@PathVariable Long id, 
                                          @RequestParam boolean approved,
                                          @RequestParam String reviewerZgh,
                                          @RequestParam(required = false) String reviewComments) {
        try {
            boolean success = projectCollectionService.reviewCollection(id, approved, reviewerZgh, reviewComments);
            return success ? Result.success("审核成功") : Result.error("审核失败");
        } catch (Exception e) {
            log.error("审核征集失败: id={}, approved={}, reviewerZgh={}", id, approved, reviewerZgh, e);
            return Result.error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 发布征集
     */
    @PutMapping("/{id}/publish")
    public Result<String> publishCollection(@PathVariable Long id) {
        try {
            boolean success = projectCollectionService.publishCollection(id);
            return success ? Result.success("发布成功") : Result.error("发布失败");
        } catch (Exception e) {
            log.error("发布征集失败: id={}", id, e);
            return Result.error("发布失败: " + e.getMessage());
        }
    }

    /**
     * 关闭征集
     */
    @PutMapping("/{id}/close")
    public Result<String> closeCollection(@PathVariable Long id) {
        try {
            boolean success = projectCollectionService.closeCollection(id);
            return success ? Result.success("关闭成功") : Result.error("关闭失败");
        } catch (Exception e) {
            log.error("关闭征集失败: id={}", id, e);
            return Result.error("关闭失败: " + e.getMessage());
        }
    }
}
