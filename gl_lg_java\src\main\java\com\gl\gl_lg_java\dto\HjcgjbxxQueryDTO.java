package com.gl.gl_lg_java.dto;

import lombok.Data;

/**
 * 获奖成果基本信息查询DTO
 */
@Data
public class HjcgjbxxQueryDTO {
    
    /**
     * 获奖成果编号
     */
    private String hjcgbh;
    
    /**
     * 获奖成果名称
     */
    private String hjcgmc;
    
    /**
     * 奖励名称
     */
    private String jlmc;
    
    /**
     * 第一完成人姓名
     */
    private String dywcrxm;
    
    /**
     * 第一完成人职工号
     */
    private String dywcrzgh;
    
    /**
     * 获奖级别
     */
    private String hjjb;

    /**
     * 奖励等级
     */
    private String jldj;

    /**
     * 审核状态
     */
    private String shzt;
    
    /**
     * 单位名称
     */
    private String dwmc;
    
    /**
     * 获奖日期开始
     */
    private String startDate;
    
    /**
     * 获奖日期结束
     */
    private String endDate;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String orderBy = "hjrq";
    
    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "desc";
}
