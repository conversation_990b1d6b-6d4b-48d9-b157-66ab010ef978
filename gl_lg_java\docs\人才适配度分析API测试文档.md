# 人才适配度分析API测试文档

## 🎯 功能概述

基于现有教职工、获奖成果、项目数据，实时计算人才项目适配度，提供智能推荐和详细分析。

## 📋 API接口列表

### 1. 执行人才适配度分析
**接口地址**: `POST /api/talent-matching/analyze`
**权限要求**: 学院管理员
**功能**: 根据项目需求分析最适合的教师候选人

**请求示例**:
```json
{
  "projectName": "人工智能在教育中的应用研究",
  "projectType": "应用研究",
  "projectDescription": "研究AI技术在在线教育中的创新应用",
  "requiredFields": ["计算机科学与技术", "教育学"],
  "requiredXkml": ["工学", "教育学"],
  "requiredYjxk": ["计算机科学与技术"],
  "minTitleLevel": "副高级",
  "minEducation": "硕士",
  "projectKeywords": ["人工智能", "机器学习", "教育技术"],
  "academicWeight": 30,
  "fieldWeight": 35,
  "teamWeight": 25,
  "practicalWeight": 10,
  "maxResults": 20,
  "minScoreThreshold": 70
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "teacherId": "T001",
      "teacherName": "张三",
      "department": "计算机学院",
      "title": "教授",
      "titleLevel": "正高级",
      "education": "博士研究生",
      "academicScore": 85.5,
      "fieldScore": 92.0,
      "teamScore": 78.0,
      "practicalScore": 65.0,
      "totalScore": 82.3,
      "ranking": 1,
      "recommendationLevel": "HIGHLY_RECOMMENDED",
      "recommendationReasons": [
        "具有正高级职称，学术地位突出，适合担任项目负责人",
        "获得2项国家级奖励，学术影响力显著",
        "在计算机科学与技术领域有丰富研究经验，与项目需求高度匹配"
      ],
      "riskWarnings": [],
      "totalAwards": 8,
      "recentAwards": 3,
      "totalProjects": 5,
      "collaborativeProjects": 6,
      "researchFields": ["计算机科学与技术", "人工智能"]
    }
  ]
}
```

### 2. 获取教师详细能力分析
**接口地址**: `GET /api/talent-matching/teacher-analysis/{teacherId}`
**权限要求**: 教师
**功能**: 获取指定教师的详细能力分析报告

**请求示例**:
```
GET /api/talent-matching/teacher-analysis/T001
Authorization: Bearer YOUR_JWT_TOKEN
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "teacherId": "T001",
    "teacherName": "张三",
    "department": "计算机学院",
    "title": "教授",
    "titleLevel": "正高级",
    "education": "博士研究生",
    "academicInfluenceScore": 85.5,
    "fieldExpertiseScore": 78.0,
    "teamCompatibilityScore": 82.0,
    "achievementPracticalityScore": 70.0,
    "overallScore": 78.9,
    "awardStatistics": {
      "totalAwards": 8,
      "nationalAwards": 2,
      "provincialAwards": 4,
      "recentAwards": 3,
      "awardTypes": ["科技进步奖", "教学成果奖"]
    },
    "projectStatistics": {
      "totalProjects": 5,
      "leadingProjects": 5,
      "ongoingProjects": 2,
      "completedProjects": 3
    },
    "collaborationStatistics": {
      "collaborativeAwards": 6,
      "collaborationCount": 6,
      "collaborationRate": 75.0
    },
    "fieldDistributions": [
      {
        "fieldName": "计算机科学与技术",
        "achievementCount": 5,
        "percentage": 62.5
      },
      {
        "fieldName": "人工智能",
        "achievementCount": 3,
        "percentage": 37.5
      }
    ],
    "capabilityTags": [
      "学术影响力强",
      "国家级获奖专家",
      "团队协作能力强",
      "科研活跃"
    ]
  }
}
```

### 3. 获取预设项目模板
**接口地址**: `GET /api/talent-matching/templates`
**权限要求**: 教师
**功能**: 获取系统预设的项目需求模板

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "projectName": "基础研究项目模板",
      "projectType": "基础研究",
      "minTitleLevel": "副高级",
      "minEducation": "硕士",
      "academicWeight": 40,
      "fieldWeight": 30,
      "teamWeight": 20,
      "practicalWeight": 10
    },
    {
      "projectName": "应用研究项目模板",
      "projectType": "应用研究",
      "minTitleLevel": "副高级",
      "minEducation": "硕士",
      "academicWeight": 25,
      "fieldWeight": 35,
      "teamWeight": 25,
      "practicalWeight": 15
    },
    {
      "projectName": "产业化项目模板",
      "projectType": "产业化",
      "minTitleLevel": "中级",
      "minEducation": "本科",
      "academicWeight": 15,
      "fieldWeight": 25,
      "teamWeight": 30,
      "practicalWeight": 30
    }
  ]
}
```

### 4. 快速匹配分析
**接口地址**: `POST /api/talent-matching/quick-analyze`
**权限要求**: 学院管理员
**功能**: 使用预设模板快速进行人才匹配

**请求示例**:
```
POST /api/talent-matching/quick-analyze?templateType=应用研究&projectName=智能教育平台开发&projectDescription=开发基于AI的个性化学习平台
```

### 5. 批量教师能力评估
**接口地址**: `POST /api/talent-matching/batch-evaluate`
**权限要求**: 学院管理员
**功能**: 批量评估多个教师的能力

**请求示例**:
```json
{
  "teacherIds": ["T001", "T002", "T003", "T004", "T005"]
}
```

## 🧪 测试步骤

### 步骤1: 获取JWT Token
```bash
# 先登录获取token
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

### 步骤2: 测试预设模板接口
```bash
curl -X GET http://localhost:8080/api/talent-matching/templates \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 步骤3: 测试教师能力分析
```bash
curl -X GET http://localhost:8080/api/talent-matching/teacher-analysis/T001 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 步骤4: 测试人才适配度分析
```bash
curl -X POST http://localhost:8080/api/talent-matching/analyze \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "projectName": "测试项目",
    "projectType": "应用研究",
    "minTitleLevel": "副高级",
    "minEducation": "硕士",
    "academicWeight": 25,
    "fieldWeight": 35,
    "teamWeight": 25,
    "practicalWeight": 15,
    "maxResults": 10
  }'
```

### 步骤5: 测试快速分析
```bash
curl -X POST "http://localhost:8080/api/talent-matching/quick-analyze?templateType=应用研究&projectName=AI教育项目" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 📊 评分算法说明

### 四维度评分体系

1. **学术影响力 (0-100分)**
   - 国家级奖励: 30分/项
   - 省级奖励: 20分/项  
   - 市级奖励: 10分/项
   - 时间衰减: 近2年×1.5, 近5年×1.0, 5年前×0.5
   - 作者贡献: ≤3人×1.0, >3人×0.8

2. **领域深耕度 (0-100分)**
   - 学科门类完全匹配: 20分
   - 一级学科完全匹配: 15分
   - 关键词匹配: 5分/个
   - 领域成果数量: 2分/项

3. **团队适配值 (0-100分)**
   - 正高级职称: 40分
   - 副高级职称: 30分
   - 中级职称: 20分
   - 博士学历: 30分
   - 硕士学历: 20分
   - 合作经验: 5分/项

4. **成果落地性 (0-100分)**
   - 已审核状态: 20分
   - 国家级颁奖单位: 15分
   - 省级颁奖单位: 10分
   - 应用型成果: 10分

### 综合得分计算
```
总分 = (学术影响力 × 学术权重 + 领域深耕度 × 领域权重 + 团队适配值 × 团队权重 + 成果落地性 × 落地权重) ÷ 100
```

## 🎯 使用建议

1. **权重配置建议**:
   - 基础研究: 学术40%, 领域30%, 团队20%, 落地10%
   - 应用研究: 学术25%, 领域35%, 团队25%, 落地15%
   - 产业化项目: 学术15%, 领域25%, 团队30%, 落地30%

2. **筛选条件建议**:
   - 设置合理的最低分数阈值(建议60-70分)
   - 根据项目重要性调整职称和学历要求
   - 考虑项目时间安排，排除工作负荷过重的教师

3. **结果解读**:
   - 85分以上: 强烈推荐
   - 70-84分: 推荐
   - 60-69分: 有条件推荐
   - 60分以下: 不推荐

## ⚠️ 注意事项

1. 确保数据库中有足够的测试数据
2. 权重总和必须等于100%
3. 注意权限控制，学院管理员才能执行分析
4. 大批量分析时注意性能影响
5. 定期更新算法参数以提高准确性
