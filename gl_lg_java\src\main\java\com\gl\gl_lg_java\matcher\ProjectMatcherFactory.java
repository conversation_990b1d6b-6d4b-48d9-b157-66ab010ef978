package com.gl.gl_lg_java.matcher;

import com.gl.gl_lg_java.matcher.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 项目匹配器工厂类
 */
@Component
@Slf4j
public class ProjectMatcherFactory {

    private final Map<String, ProjectMatcher> matchers = new HashMap<>();

    @Autowired
    private InnovationTeamMatcher innovationTeamMatcher;

    @Autowired
    private ServiceManagementMatcher serviceManagementMatcher;

    @Autowired
    private LeaderTalentMatcher leaderTalentMatcher;

    @Autowired
    private PingfengScholarType1Matcher pingfengScholarType1Matcher;

    @Autowired
    private PingfengScholarType2Matcher pingfengScholarType2Matcher;

    @Autowired
    private PingfengScholarType3Matcher pingfengScholarType3Matcher;

    @Autowired
    private YoungTalentMatcher youngTalentMatcher;

    @Autowired
    private SocialServiceMatcher socialServiceMatcher;

    @PostConstruct
    public void initMatchers() {
        log.info("初始化项目匹配器...");

        // 创新团队支持计划
        matchers.put("CXTD", innovationTeamMatcher);

        // 服务管理人才支持计划
        matchers.put("FWGL", serviceManagementMatcher);

        // 领军人才引育计划
        matchers.put("LJRC", leaderTalentMatcher);

        // 屏风学者引育计划 - Ⅰ类
        matchers.put("PFXZ1", pingfengScholarType1Matcher);

        // 屏风学者引育计划 - Ⅱ类
        matchers.put("PFXZ2", pingfengScholarType2Matcher);

        // 屏风学者引育计划 - Ⅲ类
        matchers.put("PFXZ3", pingfengScholarType3Matcher);

        // 青年人才未来工程
        matchers.put("QNRC", youngTalentMatcher);

        // 社会服务人才培育计划
        matchers.put("SHFW", socialServiceMatcher);

        log.info("项目匹配器初始化完成，共加载{}个匹配器", matchers.size());
    }

    /**
     * 获取项目匹配器
     * 
     * @param projectCode 项目代码
     * @return 项目匹配器
     */
    public ProjectMatcher getMatcher(String projectCode) {
        ProjectMatcher matcher = matchers.get(projectCode);
        if (matcher == null) {
            throw new IllegalArgumentException("不支持的项目类型: " + projectCode);
        }
        return matcher;
    }

    /**
     * 获取所有支持的项目代码
     * 
     * @return 项目代码集合
     */
    public Map<String, String> getSupportedProjects() {
        Map<String, String> projects = new HashMap<>();
        for (Map.Entry<String, ProjectMatcher> entry : matchers.entrySet()) {
            projects.put(entry.getKey(), entry.getValue().getProjectName());
        }
        return projects;
    }

    /**
     * 检查是否支持指定项目
     * 
     * @param projectCode 项目代码
     * @return 是否支持
     */
    public boolean isSupported(String projectCode) {
        return matchers.containsKey(projectCode);
    }
}
