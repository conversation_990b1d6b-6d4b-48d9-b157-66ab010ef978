package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxProjectMidlateManagement;
import com.gl.gl_lg_java.mapper.RchxProjectMidlateManagementMapper;
import com.gl.gl_lg_java.service.RchxProjectMidlateManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目中后期管理Service实现类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Slf4j
public class RchxProjectMidlateManagementServiceImpl extends ServiceImpl<RchxProjectMidlateManagementMapper, RchxProjectMidlateManagement>
        implements RchxProjectMidlateManagementService {

    @Autowired
    private RchxProjectMidlateManagementMapper projectMidlateManagementMapper;

    @Override
    public IPage<Map<String, Object>> pageQueryWithDetails(Page<RchxProjectMidlateManagement> page,
                                                           String projectCode,
                                                           String projectName,
                                                           String projectStatus,
                                                           Integer categoryId,
                                                           Integer typeId,
                                                           Integer deptId,
                                                           String projectLeaderZgh,
                                                           String riskLevel) {
        try {
            return projectMidlateManagementMapper.selectPageWithDetails(
                    page, projectCode, projectName, projectStatus, 
                    categoryId, typeId, deptId, projectLeaderZgh, riskLevel);
        } catch (Exception e) {
            log.error("分页查询项目中后期管理信息失败", e);
            throw new RuntimeException("分页查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateManagement> getByProjectLeader(String projectLeaderZgh) {
        try {
            return projectMidlateManagementMapper.selectByProjectLeader(projectLeaderZgh);
        } catch (Exception e) {
            log.error("根据项目负责人查询项目列表失败: projectLeaderZgh={}", projectLeaderZgh, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateManagement> getByDepartment(Integer deptId) {
        try {
            return projectMidlateManagementMapper.selectByDepartment(deptId);
        } catch (Exception e) {
            log.error("根据部门查询项目列表失败: deptId={}", deptId, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateManagement> getByStatus(String projectStatus) {
        try {
            return projectMidlateManagementMapper.selectByStatus(projectStatus);
        } catch (Exception e) {
            log.error("根据项目状态查询项目列表失败: projectStatus={}", projectStatus, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateProgress(Long id, BigDecimal progressPercentage, String progressDescription, String updateBy) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int result = projectMidlateManagementMapper.updateProgress(
                    id, progressPercentage, progressDescription, now, updateBy, updateBy);
            
            if (result > 0) {
                log.info("更新项目进度成功: id={}, progress={}", id, progressPercentage);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新项目进度失败: id={}, progress={}", id, progressPercentage, e);
            throw new RuntimeException("更新项目进度失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateStatus(Long id, String projectStatus, String updateBy) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int result = projectMidlateManagementMapper.updateStatus(id, projectStatus, now, updateBy, updateBy);
            
            if (result > 0) {
                log.info("更新项目状态成功: id={}, status={}", id, projectStatus);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新项目状态失败: id={}, status={}", id, projectStatus, e);
            throw new RuntimeException("更新项目状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateRiskLevel(Long id, String riskLevel, String riskDescription, String updateBy) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int result = projectMidlateManagementMapper.updateRiskLevel(
                    id, riskLevel, riskDescription, now, updateBy, updateBy);
            
            if (result > 0) {
                log.info("更新项目风险等级成功: id={}, riskLevel={}", id, riskLevel);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新项目风险等级失败: id={}, riskLevel={}", id, riskLevel, e);
            throw new RuntimeException("更新项目风险等级失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProjectStatistics() {
        try {
            return projectMidlateManagementMapper.getProjectStatistics();
        } catch (Exception e) {
            log.error("获取项目统计信息失败", e);
            throw new RuntimeException("获取项目统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getDepartmentStatistics() {
        try {
            return projectMidlateManagementMapper.getDepartmentStatistics();
        } catch (Exception e) {
            log.error("获取部门项目统计失败", e);
            throw new RuntimeException("获取部门项目统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateManagement> getHighRiskProjects() {
        try {
            return projectMidlateManagementMapper.getHighRiskProjects();
        } catch (Exception e) {
            log.error("获取风险项目列表失败", e);
            throw new RuntimeException("获取风险项目列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateManagement> getProjectsNearDeadline(Integer days) {
        try {
            return projectMidlateManagementMapper.getProjectsNearDeadline(days);
        } catch (Exception e) {
            log.error("获取即将到期的项目失败: days={}", days, e);
            throw new RuntimeException("获取即将到期的项目失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean createProject(RchxProjectMidlateManagement projectMidlateManagement) {
        try {
            // 检查项目编号是否已存在
            if (existsByProjectCode(projectMidlateManagement.getProjectCode())) {
                throw new RuntimeException("项目编号已存在: " + projectMidlateManagement.getProjectCode());
            }

            // 设置默认值
            if (projectMidlateManagement.getProjectStatus() == null) {
                projectMidlateManagement.setProjectStatus("ONGOING");
            }
            if (projectMidlateManagement.getProgressPercentage() == null) {
                projectMidlateManagement.setProgressPercentage(BigDecimal.ZERO);
            }
            if (projectMidlateManagement.getRiskLevel() == null) {
                projectMidlateManagement.setRiskLevel("LOW");
            }
            if (projectMidlateManagement.getIsActive() == null) {
                projectMidlateManagement.setIsActive(true);
            }

            boolean result = save(projectMidlateManagement);
            if (result) {
                log.info("创建项目中后期管理记录成功: projectCode={}", projectMidlateManagement.getProjectCode());
            }
            return result;
        } catch (Exception e) {
            log.error("创建项目中后期管理记录失败: projectCode={}", 
                     projectMidlateManagement.getProjectCode(), e);
            throw new RuntimeException("创建项目失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateProject(RchxProjectMidlateManagement projectMidlateManagement) {
        try {
            projectMidlateManagement.setLastUpdateTime(LocalDateTime.now());
            boolean result = updateById(projectMidlateManagement);
            if (result) {
                log.info("更新项目中后期管理记录成功: id={}", projectMidlateManagement.getId());
            }
            return result;
        } catch (Exception e) {
            log.error("更新项目中后期管理记录失败: id={}", projectMidlateManagement.getId(), e);
            throw new RuntimeException("更新项目失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteProject(Long id, String updateBy) {
        try {
            RchxProjectMidlateManagement project = new RchxProjectMidlateManagement();
            project.setId(id);
            project.setIsActive(false);
            project.setLastUpdateTime(LocalDateTime.now());
            project.setLastUpdateBy(updateBy);
            project.setUpdateBy(updateBy);
            
            boolean result = updateById(project);
            if (result) {
                log.info("删除项目中后期管理记录成功: id={}", id);
            }
            return result;
        } catch (Exception e) {
            log.error("删除项目中后期管理记录失败: id={}", id, e);
            throw new RuntimeException("删除项目失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean batchDeleteProjects(List<Long> ids, String updateBy) {
        try {
            for (Long id : ids) {
                deleteProject(id, updateBy);
            }
            log.info("批量删除项目中后期管理记录成功: ids={}", ids);
            return true;
        } catch (Exception e) {
            log.error("批量删除项目中后期管理记录失败: ids={}", ids, e);
            throw new RuntimeException("批量删除项目失败: " + e.getMessage());
        }
    }

    @Override
    public RchxProjectMidlateManagement getByProjectCode(String projectCode) {
        try {
            QueryWrapper<RchxProjectMidlateManagement> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_code", projectCode)
                       .eq("is_active", true);
            return getOne(queryWrapper);
        } catch (Exception e) {
            log.error("根据项目编号查询项目信息失败: projectCode={}", projectCode, e);
            throw new RuntimeException("查询项目信息失败: " + e.getMessage());
        }
    }

    @Override
    public boolean existsByProjectCode(String projectCode) {
        try {
            QueryWrapper<RchxProjectMidlateManagement> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_code", projectCode)
                       .eq("is_active", true);
            return count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查项目编号是否存在失败: projectCode={}", projectCode, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean toggleProjectStatus(Long id, Boolean isActive, String updateBy) {
        try {
            RchxProjectMidlateManagement project = new RchxProjectMidlateManagement();
            project.setId(id);
            project.setIsActive(isActive);
            project.setLastUpdateTime(LocalDateTime.now());
            project.setLastUpdateBy(updateBy);
            project.setUpdateBy(updateBy);
            
            boolean result = updateById(project);
            if (result) {
                log.info("切换项目状态成功: id={}, isActive={}", id, isActive);
            }
            return result;
        } catch (Exception e) {
            log.error("切换项目状态失败: id={}, isActive={}", id, isActive, e);
            throw new RuntimeException("切换项目状态失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProjectOverview(Long id) {
        try {
            RchxProjectMidlateManagement project = getById(id);
            if (project == null) {
                throw new RuntimeException("项目不存在: " + id);
            }

            Map<String, Object> overview = new HashMap<>();
            overview.put("project", project);
            
            // TODO: 添加考核统计和文件统计
            // overview.put("assessmentStats", assessmentService.getStatsByProjectId(id));
            // overview.put("fileStats", fileService.getStatsByProjectCode(project.getProjectCode()));
            
            return overview;
        } catch (Exception e) {
            log.error("获取项目概览信息失败: id={}", id, e);
            throw new RuntimeException("获取项目概览信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> exportProjectList(String projectStatus, Integer deptId, String projectLeaderZgh) {
        try {
            Page<RchxProjectMidlateManagement> page = new Page<>(1, Integer.MAX_VALUE);
            IPage<Map<String, Object>> result = pageQueryWithDetails(
                    page, null, null, projectStatus, null, null, deptId, projectLeaderZgh, null);
            return result.getRecords();
        } catch (Exception e) {
            log.error("导出项目列表失败", e);
            throw new RuntimeException("导出项目列表失败: " + e.getMessage());
        }
    }
}
