-- =====================================================
-- 项目中后期管理系统数据库表结构
-- 创建时间: 2025-01-04
-- 说明: 专门用于项目中后期阶段的管理和考核，支持MinIO文件存储
-- =====================================================

USE glrchx;

-- =====================================================
-- 1. 项目中后期管理主表
-- =====================================================

-- 1.1 项目中后期管理表
DROP TABLE IF EXISTS `t_rchx_project_midlate_management`;
CREATE TABLE `t_rchx_project_midlate_management` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '管理ID',
  `project_code` varchar(50) NOT NULL COMMENT '项目编号',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `category_id` int NOT NULL COMMENT '项目大类ID',
  `type_id` int NOT NULL COMMENT '项目类别ID',
  `dept_id` int NOT NULL COMMENT '所属部门ID',
  `project_leader_zgh` varchar(50) NOT NULL COMMENT '项目负责人职工号',
  `project_leader_name` varchar(50) NOT NULL COMMENT '项目负责人姓名',
  `project_leader_phone` varchar(20) COMMENT '项目负责人电话',
  `project_leader_email` varchar(100) COMMENT '项目负责人邮箱',
  `project_leader_title` varchar(100) COMMENT '项目负责人职称',
  `project_start_date` date NOT NULL COMMENT '项目开始时间',
  `project_end_date` date NOT NULL COMMENT '项目结束时间',
  `project_duration_months` int COMMENT '项目周期(月)',
  `project_budget` decimal(15,2) COMMENT '项目预算(万元)',
  `project_description` longtext COMMENT '项目描述',
  `team_members` text COMMENT '团队成员信息',
  `project_status` varchar(20) NOT NULL DEFAULT 'ONGOING' COMMENT '项目状态(ONGOING进行中,MIDTERM_REVIEW中期检查,FINAL_REVIEW结项评审,COMPLETED已完成,SUSPENDED暂停,TERMINATED终止)',
  `progress_percentage` decimal(5,2) DEFAULT '0.00' COMMENT '项目进度百分比',
  `progress_description` text COMMENT '进度说明',
  `current_phase` varchar(50) COMMENT '当前阶段',
  `next_milestone` varchar(200) COMMENT '下一个里程碑',
  `milestone_date` date COMMENT '里程碑日期',
  `risk_level` varchar(20) DEFAULT 'LOW' COMMENT '风险等级(LOW低,MEDIUM中,HIGH高,CRITICAL严重)',
  `risk_description` text COMMENT '风险描述',
  `management_notes` text COMMENT '管理备注',
  `last_update_time` datetime COMMENT '最后更新时间',
  `last_update_by` varchar(50) COMMENT '最后更新人职工号',
  `source_application_id` bigint COMMENT '来源申报ID',
  `source_inspection_id` bigint COMMENT '来源中检ID',
  `source_completion_id` bigint COMMENT '来源结项ID',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活(0否,1是)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人职工号',
  `update_by` varchar(50) COMMENT '更新人职工号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_category_type` (`category_id`, `type_id`),
  KEY `idx_dept_status` (`dept_id`, `project_status`),
  KEY `idx_leader` (`project_leader_zgh`),
  KEY `idx_project_dates` (`project_start_date`, `project_end_date`),
  KEY `idx_progress` (`progress_percentage`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_midlate_category` FOREIGN KEY (`category_id`) REFERENCES `t_rchx_project_categories` (`id`),
  CONSTRAINT `fk_midlate_type` FOREIGN KEY (`type_id`) REFERENCES `t_rchx_project_types` (`id`),
  CONSTRAINT `fk_midlate_dept` FOREIGN KEY (`dept_id`) REFERENCES `t_rchx_departments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目中后期管理主表';

-- =====================================================
-- 2. 项目考核管理表
-- =====================================================

-- 2.1 项目考核表
DROP TABLE IF EXISTS `t_rchx_project_assessments`;
CREATE TABLE `t_rchx_project_assessments` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '考核ID',
  `assessment_code` varchar(50) NOT NULL COMMENT '考核编号',
  `project_id` bigint NOT NULL COMMENT '项目管理ID',
  `project_code` varchar(50) NOT NULL COMMENT '项目编号',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `project_leader_zgh` varchar(50) NOT NULL COMMENT '项目负责人职工号',
  `project_leader_name` varchar(50) NOT NULL COMMENT '项目负责人姓名',
  `assessment_type` varchar(50) NOT NULL COMMENT '考核类型(MIDTERM中期考核,FINAL结项考核,SPECIAL专项考核,ANNUAL年度考核,QUARTERLY季度考核)',
  `assessment_name` varchar(200) NOT NULL COMMENT '考核名称',
  `assessment_description` text COMMENT '考核描述',
  `assessment_criteria` text COMMENT '考核标准',
  `assessment_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '考核状态(PENDING待考核,ONGOING考核中,COMPLETED已完成,CANCELLED已取消,OVERDUE已逾期)',
  `assessment_date` datetime NOT NULL COMMENT '考核时间',
  `assessment_deadline` datetime COMMENT '考核截止时间',
  `assessment_location` varchar(200) COMMENT '考核地点',
  `assessment_method` varchar(50) COMMENT '考核方式(ONLINE线上,OFFLINE线下,HYBRID混合)',
  `assessor_group` text COMMENT '考核专家组',
  `assessment_score` decimal(5,2) COMMENT '考核得分',
  `max_score` decimal(5,2) DEFAULT '100.00' COMMENT '满分',
  `pass_score` decimal(5,2) DEFAULT '60.00' COMMENT '及格分',
  `assessment_result` varchar(20) COMMENT '考核结果(EXCELLENT优秀,GOOD良好,PASS合格,FAIL不合格)',
  `assessment_comments` text COMMENT '考核意见',
  `improvement_suggestions` text COMMENT '改进建议',
  `follow_up_actions` text COMMENT '后续行动',
  `assessor_zgh` varchar(50) COMMENT '主要考核人职工号',
  `assessor_name` varchar(50) COMMENT '主要考核人姓名',
  `assessment_start_time` datetime COMMENT '考核开始时间',
  `assessment_end_time` datetime COMMENT '考核结束时间',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开(0否,1是)',
  `priority_level` varchar(20) DEFAULT 'NORMAL' COMMENT '优先级(HIGH高,NORMAL普通,LOW低)',
  `reminder_sent` tinyint(1) DEFAULT '0' COMMENT '是否已发送提醒(0否,1是)',
  `last_reminder_time` datetime COMMENT '最后提醒时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人职工号',
  `update_by` varchar(50) COMMENT '更新人职工号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_assessment_code` (`assessment_code`),
  KEY `idx_project_assessment` (`project_id`, `assessment_type`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_leader` (`project_leader_zgh`),
  KEY `idx_assessment_status_date` (`assessment_status`, `assessment_date`),
  KEY `idx_assessment_type` (`assessment_type`),
  KEY `idx_assessment_result` (`assessment_result`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_assessment_project` FOREIGN KEY (`project_id`) REFERENCES `t_rchx_project_midlate_management` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目考核管理表';

-- =====================================================
-- 3. 项目中后期文件管理表
-- =====================================================

-- 3.1 项目中后期文件表
DROP TABLE IF EXISTS `t_rchx_project_midlate_files`;
CREATE TABLE `t_rchx_project_midlate_files` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型(PROJECT_MANAGEMENT项目管理,ASSESSMENT考核管理)',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `project_code` varchar(50) NOT NULL COMMENT '项目编号',
  `file_category` varchar(50) NOT NULL COMMENT '文件类别(PROGRESS_REPORT进度报告,ASSESSMENT_MATERIAL考核材料,MEETING_MINUTES会议纪要,MILESTONE_DOCUMENT里程碑文档,RISK_ANALYSIS风险分析,OTHER其他)',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT 'MinIO文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL COMMENT '文件MIME类型',
  `file_extension` varchar(10) NOT NULL COMMENT '文件扩展名',
  `bucket_name` varchar(100) NOT NULL DEFAULT 'rchx-midlate' COMMENT 'MinIO存储桶名称',
  `object_name` varchar(500) NOT NULL COMMENT 'MinIO对象名称',
  `file_md5` varchar(32) COMMENT '文件MD5值',
  `file_description` text COMMENT '文件描述',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必需文件(0否,1是)',
  `is_confidential` tinyint(1) DEFAULT '0' COMMENT '是否机密文件(0否,1是)',
  `version_number` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `upload_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `upload_by` varchar(50) NOT NULL COMMENT '上传人职工号',
  `upload_by_name` varchar(50) NOT NULL COMMENT '上传人姓名',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `last_download_time` datetime COMMENT '最后下载时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0否,1是)',
  `delete_time` datetime COMMENT '删除时间',
  `delete_by` varchar(50) COMMENT '删除人职工号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_project_category` (`project_code`, `file_category`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_bucket_object` (`bucket_name`, `object_name`),
  KEY `idx_deleted` (`is_deleted`),
  KEY `idx_confidential` (`is_confidential`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目中后期文件管理表';

-- =====================================================
-- 4. 索引优化
-- =====================================================

-- 4.1 复合索引优化
CREATE INDEX idx_midlate_complex_query ON t_rchx_project_midlate_management
(dept_id, project_status, category_id, project_start_date DESC);

CREATE INDEX idx_midlate_leader_status ON t_rchx_project_midlate_management
(project_leader_zgh, project_status, create_time DESC);

CREATE INDEX idx_assessment_complex_query ON t_rchx_project_assessments
(project_id, assessment_type, assessment_status, assessment_date DESC);

CREATE INDEX idx_files_midlate_complex ON t_rchx_project_midlate_files
(project_code, business_type, file_category, is_deleted, upload_time DESC);

-- =====================================================
-- 5. 视图定义
-- =====================================================

-- 5.1 项目中后期管理概览视图
CREATE OR REPLACE VIEW v_project_midlate_overview AS
SELECT
    pml.id,
    pml.project_code,
    pml.project_name,
    pc.category_name AS project_category,
    pt.type_name AS project_type,
    d.dept_name AS department,
    pml.project_leader_name,
    pml.project_leader_phone,
    pml.project_leader_email,
    pml.project_leader_title,
    pml.project_start_date,
    pml.project_end_date,
    pml.project_duration_months,
    pml.project_budget,
    pml.project_status,
    pml.progress_percentage,
    pml.current_phase,
    pml.next_milestone,
    pml.milestone_date,
    pml.risk_level,
    pml.create_time,
    -- 统计考核信息
    COALESCE(assessment_stats.total_assessments, 0) AS total_assessments,
    COALESCE(assessment_stats.completed_assessments, 0) AS completed_assessments,
    COALESCE(assessment_stats.pending_assessments, 0) AS pending_assessments,
    COALESCE(assessment_stats.avg_score, 0) AS avg_assessment_score,
    -- 统计文件信息
    COALESCE(file_stats.total_files, 0) AS total_files,
    COALESCE(file_stats.confidential_files, 0) AS confidential_files
FROM t_rchx_project_midlate_management pml
LEFT JOIN t_rchx_project_categories pc ON pml.category_id = pc.id
LEFT JOIN t_rchx_project_types pt ON pml.type_id = pt.id
LEFT JOIN t_rchx_departments d ON pml.dept_id = d.id
LEFT JOIN (
    SELECT
        project_id,
        COUNT(*) AS total_assessments,
        SUM(CASE WHEN assessment_status = 'COMPLETED' THEN 1 ELSE 0 END) AS completed_assessments,
        SUM(CASE WHEN assessment_status = 'PENDING' THEN 1 ELSE 0 END) AS pending_assessments,
        AVG(CASE WHEN assessment_score IS NOT NULL THEN assessment_score ELSE NULL END) AS avg_score
    FROM t_rchx_project_assessments
    GROUP BY project_id
) assessment_stats ON pml.id = assessment_stats.project_id
LEFT JOIN (
    SELECT
        project_code,
        COUNT(*) AS total_files,
        SUM(CASE WHEN is_confidential = 1 THEN 1 ELSE 0 END) AS confidential_files
    FROM t_rchx_project_midlate_files
    WHERE is_deleted = 0
    GROUP BY project_code
) file_stats ON pml.project_code = file_stats.project_code
WHERE pml.is_active = 1;

-- =====================================================
-- 6. 示例数据插入
-- =====================================================

-- 6.1 插入项目中后期管理示例数据
-- 注意：这里假设基础字典表已有数据，实际使用时需要根据实际ID调整
INSERT INTO t_rchx_project_midlate_management (
    project_code, project_name, category_id, type_id, dept_id,
    project_leader_zgh, project_leader_name, project_leader_phone, project_leader_email, project_leader_title,
    project_start_date, project_end_date, project_duration_months, project_budget,
    project_description, team_members, project_status, progress_percentage,
    current_phase, next_milestone, milestone_date, risk_level, risk_description,
    create_by
) VALUES
('PROJ2024001', '基于人工智能的智能制造关键技术研究', 1, 1, 1,
'202401001', '张教授', '13800138001', '<EMAIL>', '教授',
'2024-01-01', '2026-12-31', 36, 500.00,
'本项目旨在研究基于人工智能的智能制造关键技术，包括智能感知、智能决策、智能控制等方面。',
'项目负责人：张教授；核心成员：李副教授、王讲师；研究生：5名',
'ONGOING', 65.00, '技术验证阶段', '中期检查', '2025-06-30', 'LOW',
'项目进展顺利，技术路线清晰，团队配合良好。',
'202401001'
),
('PROJ2024002', '新能源汽车电池管理系统优化研究', 1, 2, 2,
'202401002', '李博士', '13800138002', '<EMAIL>', '副教授',
'2024-03-01', '2027-02-28', 36, 80.00,
'研究新能源汽车电池管理系统的优化算法，提高电池使用效率和安全性。',
'项目负责人：李博士；团队成员：王工程师、陈研究员；硕士生：3名',
'MIDTERM_REVIEW', 45.00, '中期检查阶段', '技术报告提交', '2025-03-15', 'MEDIUM',
'部分技术指标需要进一步优化，预算执行正常。',
'202401002'
),
('PROJ2024003', '区块链技术在供应链管理中的应用', 2, 3, 3,
'202401003', '王教授', '13800138003', '<EMAIL>', '教授',
'2023-09-01', '2025-08-31', 24, 120.00,
'探索区块链技术在供应链管理中的创新应用模式和实施方案。',
'项目负责人：王教授；技术团队：5人；合作企业：2家',
'FINAL_REVIEW', 85.00, '结项准备阶段', '结项答辩', '2025-08-15', 'LOW',
'项目即将结项，各项指标基本达成，准备结项材料。',
'202401003'
);

-- 6.2 插入项目考核管理示例数据
INSERT INTO t_rchx_project_assessments (
    assessment_code, project_id, project_code, project_name,
    project_leader_zgh, project_leader_name, assessment_type, assessment_name,
    assessment_description, assessment_status, assessment_date, assessment_deadline,
    assessment_method, assessment_score, assessment_result, assessment_comments,
    assessor_zgh, assessor_name, create_by
) VALUES
('ASSESS2024001', 1, 'PROJ2024001', '基于人工智能的智能制造关键技术研究',
'202401001', '张教授', 'MIDTERM', '中期考核评估',
'对项目中期进展、技术路线、预算执行等方面进行全面评估。',
'COMPLETED', '2024-06-15 14:00:00', '2024-06-15 17:00:00',
'OFFLINE', 88.50, 'GOOD', '项目进展良好，技术路线合理，建议加强产业化应用研究。',
'202401010', '评审专家组', '202401010'
),
('ASSESS2024002', 2, 'PROJ2024002', '新能源汽车电池管理系统优化研究',
'202401002', '李博士', 'MIDTERM', '中期检查',
'检查项目执行情况，评估技术方案可行性和进度安排。',
'PENDING', '2025-03-20 09:00:00', '2025-03-20 12:00:00',
'HYBRID', NULL, NULL, NULL,
'202401011', '技术评审组', '202401011'
),
('ASSESS2024003', 3, 'PROJ2024003', '区块链技术在供应链管理中的应用',
'202401003', '王教授', 'FINAL', '结项考核',
'对项目完成情况、成果质量、目标达成度进行综合评价。',
'ONGOING', '2025-08-10 14:00:00', '2025-08-10 17:00:00',
'OFFLINE', NULL, NULL, NULL,
'202401012', '结项评审委员会', '202401012'
),
('ASSESS2024004', 1, 'PROJ2024001', '基于人工智能的智能制造关键技术研究',
'202401001', '张教授', 'QUARTERLY', '第三季度考核',
'季度进展检查，重点关注技术突破和里程碑完成情况。',
'COMPLETED', '2024-09-30 10:00:00', '2024-09-30 12:00:00',
'ONLINE', 92.00, 'EXCELLENT', '技术突破显著，超额完成季度目标，团队协作优秀。',
'202401010', '项目管理办公室', '202401010'
);

-- =====================================================
-- 7. 常用查询语句示例
-- =====================================================

-- 7.1 查询项目列表（包含用户要求的所有字段）
/*
SELECT
    project_code AS '项目编号',
    project_name AS '项目名称',
    project_leader_name AS '项目负责人',
    department AS '所属部门',
    project_start_date AS '开始时间',
    project_end_date AS '结束时间',
    project_status AS '项目状态',
    progress_percentage AS '完成进度',
    total_assessments AS '考核次数',
    avg_assessment_score AS '平均得分'
FROM v_project_midlate_overview
ORDER BY project_start_date DESC;
*/

-- 7.2 查询考核管理信息（包含用户要求的所有字段）
/*
SELECT
    project_name AS '项目名称',
    project_leader_name AS '项目负责人',
    assessment_type AS '考核类型',
    assessment_status AS '考核状态',
    assessment_date AS '考核时间',
    assessment_score AS '考核得分',
    assessment_result AS '考核结果',
    assessment_comments AS '考核意见'
FROM t_rchx_project_assessments
ORDER BY assessment_date DESC;
*/

-- 7.3 查询即将到期的考核任务
/*
SELECT
    assessment_code,
    project_name,
    project_leader_name,
    assessment_type,
    assessment_date,
    assessment_deadline,
    DATEDIFF(assessment_deadline, NOW()) AS days_remaining
FROM t_rchx_project_assessments
WHERE assessment_status IN ('PENDING', 'ONGOING')
    AND assessment_deadline > NOW()
    AND DATEDIFF(assessment_deadline, NOW()) <= 7
ORDER BY assessment_deadline ASC;
*/

-- 7.4 查询各部门项目管理统计
/*
SELECT
    d.dept_name AS '部门名称',
    COUNT(*) AS '项目总数',
    SUM(CASE WHEN pml.project_status = 'ONGOING' THEN 1 ELSE 0 END) AS '进行中项目',
    SUM(CASE WHEN pml.project_status = 'COMPLETED' THEN 1 ELSE 0 END) AS '已完成项目',
    AVG(pml.progress_percentage) AS '平均进度',
    SUM(pml.project_budget) AS '总预算',
    COUNT(DISTINCT pml.project_leader_zgh) AS '项目负责人数'
FROM t_rchx_project_midlate_management pml
LEFT JOIN t_rchx_departments d ON pml.dept_id = d.id
WHERE pml.is_active = 1
GROUP BY pml.dept_id, d.dept_name
ORDER BY COUNT(*) DESC;
*/

-- 7.5 查询项目负责人考核情况统计
/*
SELECT
    project_leader_name AS '项目负责人',
    project_leader_zgh AS '职工号',
    COUNT(DISTINCT pa.project_id) AS '负责项目数',
    COUNT(pa.id) AS '参与考核次数',
    AVG(pa.assessment_score) AS '平均考核得分',
    SUM(CASE WHEN pa.assessment_result = 'EXCELLENT' THEN 1 ELSE 0 END) AS '优秀次数',
    SUM(CASE WHEN pa.assessment_result = 'GOOD' THEN 1 ELSE 0 END) AS '良好次数'
FROM t_rchx_project_assessments pa
WHERE pa.assessment_score IS NOT NULL
GROUP BY pa.project_leader_zgh, pa.project_leader_name
HAVING COUNT(pa.id) > 0
ORDER BY AVG(pa.assessment_score) DESC;
*/

-- =====================================================
-- 8. 存储过程
-- =====================================================

-- 8.1 自动生成考核编号的存储过程
DELIMITER //
CREATE PROCEDURE sp_generate_assessment_code(
    IN p_assessment_type VARCHAR(50),
    OUT p_assessment_code VARCHAR(50)
)
BEGIN
    DECLARE v_year VARCHAR(4);
    DECLARE v_prefix VARCHAR(10);
    DECLARE v_sequence INT DEFAULT 1;
    DECLARE v_max_code VARCHAR(50);

    -- 获取当前年份
    SET v_year = YEAR(NOW());

    -- 根据考核类型设置前缀
    CASE p_assessment_type
        WHEN 'MIDTERM' THEN SET v_prefix = 'MID';
        WHEN 'FINAL' THEN SET v_prefix = 'FINAL';
        WHEN 'SPECIAL' THEN SET v_prefix = 'SPEC';
        WHEN 'ANNUAL' THEN SET v_prefix = 'ANNUAL';
        WHEN 'QUARTERLY' THEN SET v_prefix = 'QUAR';
        ELSE SET v_prefix = 'ASSESS';
    END CASE;

    -- 查找当年最大编号
    SELECT MAX(assessment_code) INTO v_max_code
    FROM t_rchx_project_assessments
    WHERE assessment_code LIKE CONCAT(v_prefix, v_year, '%');

    -- 计算下一个序号
    IF v_max_code IS NOT NULL THEN
        SET v_sequence = CAST(SUBSTRING(v_max_code, -3) AS UNSIGNED) + 1;
    END IF;

    -- 生成新的考核编号
    SET p_assessment_code = CONCAT(v_prefix, v_year, LPAD(v_sequence, 3, '0'));
END //
DELIMITER ;

-- 8.2 项目进度更新存储过程
DELIMITER //
CREATE PROCEDURE sp_update_project_progress(
    IN p_project_id BIGINT,
    IN p_progress_percentage DECIMAL(5,2),
    IN p_progress_description TEXT,
    IN p_update_by VARCHAR(50)
)
BEGIN
    DECLARE v_project_status VARCHAR(20);

    -- 根据进度百分比自动判断项目状态
    IF p_progress_percentage >= 100 THEN
        SET v_project_status = 'COMPLETED';
    ELSEIF p_progress_percentage >= 80 THEN
        SET v_project_status = 'FINAL_REVIEW';
    ELSEIF p_progress_percentage >= 50 THEN
        SET v_project_status = 'MIDTERM_REVIEW';
    ELSE
        SET v_project_status = 'ONGOING';
    END IF;

    -- 更新项目进度和状态
    UPDATE t_rchx_project_midlate_management
    SET
        progress_percentage = p_progress_percentage,
        progress_description = p_progress_description,
        project_status = v_project_status,
        last_update_time = NOW(),
        last_update_by = p_update_by,
        update_by = p_update_by
    WHERE id = p_project_id;

END //
DELIMITER ;

-- =====================================================
-- 9. 触发器
-- =====================================================

-- 9.1 考核状态自动更新触发器
DELIMITER //
CREATE TRIGGER tr_assessment_status_update
    BEFORE UPDATE ON t_rchx_project_assessments
    FOR EACH ROW
BEGIN
    -- 当考核得分被设置时，自动更新考核状态和结果
    IF OLD.assessment_score IS NULL AND NEW.assessment_score IS NOT NULL THEN
        SET NEW.assessment_status = 'COMPLETED';
        SET NEW.assessment_end_time = NOW();

        -- 根据得分自动判断考核结果
        IF NEW.assessment_score >= 90 THEN
            SET NEW.assessment_result = 'EXCELLENT';
        ELSEIF NEW.assessment_score >= 80 THEN
            SET NEW.assessment_result = 'GOOD';
        ELSEIF NEW.assessment_score >= NEW.pass_score THEN
            SET NEW.assessment_result = 'PASS';
        ELSE
            SET NEW.assessment_result = 'FAIL';
        END IF;
    END IF;

    -- 当考核状态变为ONGOING时，设置开始时间
    IF OLD.assessment_status != 'ONGOING' AND NEW.assessment_status = 'ONGOING' THEN
        SET NEW.assessment_start_time = NOW();
    END IF;
END //
DELIMITER ;

-- =====================================================
-- 10. 权限设置建议
-- =====================================================

/*
-- 创建项目管理员角色
CREATE ROLE 'project_midlate_admin';
GRANT SELECT, INSERT, UPDATE, DELETE ON glrchx.t_rchx_project_midlate_management TO 'project_midlate_admin';
GRANT SELECT, INSERT, UPDATE, DELETE ON glrchx.t_rchx_project_assessments TO 'project_midlate_admin';
GRANT SELECT, INSERT, UPDATE, DELETE ON glrchx.t_rchx_project_midlate_files TO 'project_midlate_admin';

-- 创建项目负责人角色
CREATE ROLE 'project_leader';
GRANT SELECT, UPDATE ON glrchx.t_rchx_project_midlate_management TO 'project_leader';
GRANT SELECT ON glrchx.t_rchx_project_assessments TO 'project_leader';
GRANT SELECT, INSERT, UPDATE ON glrchx.t_rchx_project_midlate_files TO 'project_leader';

-- 创建考核专家角色
CREATE ROLE 'assessment_expert';
GRANT SELECT ON glrchx.t_rchx_project_midlate_management TO 'assessment_expert';
GRANT SELECT, UPDATE ON glrchx.t_rchx_project_assessments TO 'assessment_expert';
GRANT SELECT ON glrchx.v_project_midlate_overview TO 'assessment_expert';
*/

-- =====================================================
-- 说明文档
-- =====================================================

/*
项目中后期管理系统数据库设计说明

1. 核心表结构：
   - t_rchx_project_midlate_management: 项目中后期管理主表
   - t_rchx_project_assessments: 项目考核管理表
   - t_rchx_project_midlate_files: 项目中后期文件管理表

2. 主要功能：
   - 项目列表管理：项目编号、项目名称、项目负责人、所属部门、开始时间、结束时间、项目状态
   - 考核管理：项目名称、项目负责人、考核类型、考核状态、考核时间、考核得分
   - 完整的项目中后期阶段管理流程
   - 集成MinIO文件存储系统
   - 支持多种考核类型和评分机制

3. 核心字段覆盖：
   项目列表：
   - 项目编号：project_code
   - 项目名称：project_name
   - 项目负责人：project_leader_name
   - 所属部门：dept_id (关联部门表)
   - 开始时间：project_start_date
   - 结束时间：project_end_date
   - 项目状态：project_status

   考核管理：
   - 项目名称：project_name
   - 项目负责人：project_leader_name
   - 考核类型：assessment_type
   - 考核状态：assessment_status
   - 考核时间：assessment_date
   - 考核得分：assessment_score

4. 文件存储：
   - 使用MinIO对象存储
   - 支持进度报告、考核材料、会议纪要等文件分类
   - 提供文件版本管理和权限控制
   - 支持文件下载统计

5. 扩展功能：
   - 项目进度跟踪和风险管理
   - 自动生成考核编号
   - 考核状态自动更新
   - 项目里程碑管理
   - 统计报表支持
*/
