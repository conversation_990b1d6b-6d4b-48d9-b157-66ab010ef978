package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.dto.JzgjbxxQueryDTO;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_rchx_jzgjbxx(教职工基本信息)】的数据库操作Service
* @createDate 2025-07-18 11:11:41
*/
public interface RchxJzgjbxxService extends IService<RchxJzgjbxx> {

    /**
     * 根据职工号查询
     */
    RchxJzgjbxx getByZgh(String zgh);

    /**
     * 根据账号查询（支持职工号、手机号、身份证号、邮箱）
     */
    RchxJzgjbxx getByAccount(String account);

    /**
     * 根据姓名模糊查询
     */
    List<RchxJzgjbxx> listByXmLike(String xm);

    /**
     * 根据部门查询
     */
    List<RchxJzgjbxx> listByBm(String bm);

    /**
     * 根据部门模糊查询
     */
    List<RchxJzgjbxx> listByBmLike(String bm);

    /**
     * 根据职称查询
     */
    List<RchxJzgjbxx> listByZc(String zc);

    /**
     * 根据权限查询
     */
    List<RchxJzgjbxx> listByQx(String qx);

    /**
     * 根据性别查询
     */
    List<RchxJzgjbxx> listByXb(String xb);

    /**
     * 根据学历查询
     */
    List<RchxJzgjbxx> listByZgxl(String zgxl);

    /**
     * 根据学位查询
     */
    List<RchxJzgjbxx> listByZgxw(String zgxw);

    /**
     * 多条件查询
     */
    List<RchxJzgjbxx> listByMultiConditions(JzgjbxxQueryDTO queryDTO);

    /**
     * 分页多条件查询
     */
    IPage<RchxJzgjbxx> pageByMultiConditions(JzgjbxxQueryDTO queryDTO);

    /**
     * 获取部门选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getDepartmentOptions();

    /**
     * 获取当前状态选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getDqztOptions();

    /**
     * 按职称统计（包含具体教师信息）
     */
    Map<String, Object> getStatsByZc();

    /**
     * 获取职称选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getZcOptions();

    /**
     * 按职称等级统计
     */
    Map<String, Integer> getStatsByZcdj();

    /**
     * 按职称等级统计（包含具体教师信息）
     */
    Map<String, Object> getStatsByZcdjWithTeachers();

    /**
     * 按职称统计（仅返回统计数据）
     */
    Map<String, Integer> getStatsByZcCount();

    /**
     * 按年龄段统计
     */
    Map<String, Integer> getStatsByAge();

    /**
     * 按最高学历层次统计
     */
    Map<String, Integer> getStatsByZgxlcc();

    /**
     * 获取职称等级选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getZcdjOptions();

    /**
     * 获取最高学历层次选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getZgxlccOptions();

    /**
     * 获取年龄段选项列表（用于前端筛选）
     */
    List<Map<String, Object>> getAgeRangeOptions();

    /**
     * 获取教职工总数统计
     */
    Map<String, Object> getTotalCount();

    /**
     * 新增教职工信息
     */
    boolean saveJzgjbxx(RchxJzgjbxx jzgjbxx);

    /**
     * 根据职工号更新教职工信息
     */
    boolean updateByZgh(RchxJzgjbxx jzgjbxx);

    /**
     * 根据职工号删除教职工信息
     */
    boolean removeByZgh(String zgh);

    /**
     * 批量删除教职工信息
     */
    boolean removeBatchByZghs(List<String> zghs);

    /**
     * 批量新增教职工信息
     */
    boolean saveBatchJzgjbxx(List<RchxJzgjbxx> jzgjbxxList);

    /**
     * 批量更新教职工信息
     */
    boolean updateBatchJzgjbxx(List<RchxJzgjbxx> jzgjbxxList);

    /**
     * 按年龄段分页查询教师信息
     * @param ageRange 年龄段：35岁以下、35-45岁、45岁以上，为空则查询所有
     * @param pageNum 页码
     * @param pageSize 每页大小
     */
    IPage<RchxJzgjbxx> getPageByAgeRange(String ageRange, Integer pageNum, Integer pageSize);
}
