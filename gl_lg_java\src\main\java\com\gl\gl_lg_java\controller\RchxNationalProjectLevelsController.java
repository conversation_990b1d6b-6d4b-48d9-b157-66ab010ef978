package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxNationalProjectLevels;
import com.gl.gl_lg_java.mapper.RchxNationalProjectLevelsMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.gl.gl_lg_java.service.RchxNationalProjectLevelsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 国家/省部级项目级别管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@RestController
@RequestMapping("/api/national-project-levels")
@Slf4j
public class RchxNationalProjectLevelsController {

    @Autowired
    private RchxNationalProjectLevelsService levelsService;

    // Mapper已移除，直接使用Service层

    /**
     * 分页查询项目级别
     */
    @PostMapping("/page")
    public Result<IPage<RchxNationalProjectLevels>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String levelType,
            @RequestParam(required = false) String levelName,
            @RequestParam(required = false) Boolean isEnabled) {

        try {
            Page<RchxNationalProjectLevels> page = new Page<>(current, size);

            // 使用QueryWrapper构建查询条件
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<RchxNationalProjectLevels> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();

            if (levelType != null && !levelType.isEmpty()) {
                queryWrapper.eq("level_type", levelType);
            }
            if (levelName != null && !levelName.isEmpty()) {
                queryWrapper.like("level_name", levelName);
            }
            if (isEnabled != null) {
                queryWrapper.eq("is_enabled", isEnabled);
            }

            queryWrapper.orderByDesc("level_weight").orderByAsc("sort_order");

            IPage<RchxNationalProjectLevels> result = levelsService.page(page, queryWrapper);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目级别失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取启用的项目级别
     */
    @GetMapping("/enabled")
    public Result<List<RchxNationalProjectLevels>> getEnabledLevels() {
        try {
            List<RchxNationalProjectLevels> levels = levelsService.listEnabledLevels();
            return Result.success(levels);
        } catch (Exception e) {
            log.error("获取启用的项目级别失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据级别类型查询
     */
    @GetMapping("/type/{levelType}")
    public Result<List<RchxNationalProjectLevels>> getByLevelType(@PathVariable String levelType) {
        try {
            List<RchxNationalProjectLevels> levels = levelsService.listByLevelType(levelType);
            return Result.success(levels);
        } catch (Exception e) {
            log.error("根据级别类型查询失败: {}", levelType, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据资助金额查询匹配的级别
     */
    @GetMapping("/funding/{amount}")
    public Result<List<RchxNationalProjectLevels>> getByFundingAmount(@PathVariable BigDecimal amount) {
        try {
            List<RchxNationalProjectLevels> levels = levelsService.listByFundingAmount(amount);
            return Result.success(levels);
        } catch (Exception e) {
            log.error("根据资助金额查询级别失败: {}", amount, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目级别
     */
    @GetMapping("/{id}")
    public Result<RchxNationalProjectLevels> getById(@PathVariable Integer id) {
        try {
            RchxNationalProjectLevels level = levelsService.getById(id);
            if (level != null) {
                return Result.success(level);
            } else {
                return Result.error("项目级别不存在");
            }
        } catch (Exception e) {
            log.error("根据ID查询项目级别失败: {}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据级别编码查询
     */
    @GetMapping("/code/{levelCode}")
    public Result<RchxNationalProjectLevels> getByLevelCode(@PathVariable String levelCode) {
        try {
            RchxNationalProjectLevels level = levelsService.getByLevelCode(levelCode);
            if (level != null) {
                return Result.success(level);
            } else {
                return Result.error("项目级别不存在");
            }
        } catch (Exception e) {
            log.error("根据级别编码查询失败: {}", levelCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目级别
     */
    @PostMapping
    public Result<String> create(@RequestBody RchxNationalProjectLevels level) {
        try {
            // 验证级别编码唯一性
            if (!levelsService.isLevelCodeUnique(level.getLevelCode(), null)) {
                return Result.error("级别编码已存在");
            }

            // 设置默认值
            if (level.getSortOrder() == null) {
                level.setSortOrder(levelsService.getMaxSortOrder() + 1);
            }
            if (level.getLevelWeight() == null) {
                level.setLevelWeight(levelsService.getRecommendedWeight(level.getLevelType()));
            }
            if (level.getIsEnabled() == null) {
                level.setIsEnabled(true);
            }

            boolean success = levelsService.save(level);
            if (success) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增项目级别失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目级别
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Integer id, @RequestBody RchxNationalProjectLevels level) {
        try {
            // 验证级别编码唯一性
            if (!levelsService.isLevelCodeUnique(level.getLevelCode(), id)) {
                return Result.error("级别编码已存在");
            }

            level.setId(id);
            boolean success = levelsService.updateById(level);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新项目级别失败: {}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目级别
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Integer id) {
        try {
            boolean success = levelsService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除项目级别失败: {}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除项目级别
     */
    @DeleteMapping("/batch")
    public Result<String> batchDelete(@RequestBody List<Integer> ids) {
        try {
            boolean success = levelsService.removeByIds(ids);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除项目级别失败: {}", ids, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用项目级别
     */
    @PutMapping("/{id}/enabled")
    public Result<String> updateEnabledStatus(@PathVariable Integer id, @RequestParam Boolean enabled) {
        try {
            boolean success = levelsService.updateEnabledStatus(id, enabled);
            if (success) {
                return Result.success(enabled ? "启用成功" : "禁用成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("更新项目级别启用状态失败: id={}, enabled={}", id, enabled, e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量启用/禁用项目级别
     */
    @PutMapping("/batch/enabled")
    public Result<String> batchUpdateEnabledStatus(@RequestBody List<Integer> ids, @RequestParam Boolean enabled) {
        try {
            boolean success = levelsService.batchUpdateEnabledStatus(ids, enabled);
            if (success) {
                return Result.success("批量操作成功");
            } else {
                return Result.error("批量操作失败");
            }
        } catch (Exception e) {
            log.error("批量更新项目级别启用状态失败: ids={}, enabled={}", ids, enabled, e);
            return Result.error("批量操作失败: " + e.getMessage());
        }
    }

    /**
     * 调整项目级别排序
     */
    @PutMapping("/{id}/sort")
    public Result<String> updateSortOrder(@PathVariable Integer id, @RequestParam Integer sortOrder) {
        try {
            boolean success = levelsService.updateSortOrder(id, sortOrder);
            if (success) {
                return Result.success("排序调整成功");
            } else {
                return Result.error("排序调整失败");
            }
        } catch (Exception e) {
            log.error("调整项目级别排序失败: id={}, sortOrder={}", id, sortOrder, e);
            return Result.error("排序调整失败: " + e.getMessage());
        }
    }

    /**
     * 统计各级别类型的数量
     */
    @GetMapping("/statistics/type")
    public Result<List<Map<String, Object>>> countByLevelType() {
        try {
            List<Map<String, Object>> statistics = levelsService.countByLevelType();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("统计级别类型数量失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }
}
