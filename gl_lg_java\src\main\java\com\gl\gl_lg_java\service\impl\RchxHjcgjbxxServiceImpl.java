package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxHjcgjbxx;
import com.gl.gl_lg_java.dto.HjcgjbxxQueryDTO;
import com.gl.gl_lg_java.mapper.RchxHjcgjbxxMapper;
import com.gl.gl_lg_java.service.RchxHjcgjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_hjcgjbxx(获奖成果基本信息)】的数据库操作Service实现
 * @createDate 2025-07-18 11:11:41
 */
@Service
@Slf4j
public class RchxHjcgjbxxServiceImpl extends ServiceImpl<RchxHjcgjbxxMapper, RchxHjcgjbxx>
        implements RchxHjcgjbxxService {

    @Autowired
    private RchxHjcgjbxxMapper hjcgjbxxMapper;

    @Override
    public RchxHjcgjbxx getByHjcgbh(String hjcgbh) {
        return hjcgjbxxMapper.findByHjcgbh(hjcgbh);
    }

    @Override
    public List<RchxHjcgjbxx> listByHjcgmcLike(String hjcgmc) {
        return hjcgjbxxMapper.findByHjcgmcLike(hjcgmc);
    }

    @Override
    public List<RchxHjcgjbxx> listByDywcrzgh(String dywcrzgh) {
        return hjcgjbxxMapper.findByDywcrzgh(dywcrzgh);
    }

    @Override
    public List<RchxHjcgjbxx> listByDywcrxmLike(String dywcrxm) {
        return hjcgjbxxMapper.findByDywcrxmLike(dywcrxm);
    }

    @Override
    public List<RchxHjcgjbxx> listByJlmcLike(String jlmc) {
        return hjcgjbxxMapper.findByJlmcLike(jlmc);
    }

    @Override
    public List<RchxHjcgjbxx> listByShzt(String shzt) {
        return hjcgjbxxMapper.findByShzt(shzt);
    }

    @Override
    public List<RchxHjcgjbxx> listByHjjb(String hjjb) {
        return hjcgjbxxMapper.findByHjjb(hjjb);
    }

    @Override
    public List<RchxHjcgjbxx> listByJldj(String jldj) {
        return hjcgjbxxMapper.findByJldj(jldj);
    }

    @Override
    public List<RchxHjcgjbxx> listByHjrqBetween(String startDate, String endDate) {
        return hjcgjbxxMapper.findByHjrqBetween(startDate, endDate);
    }

    @Override
    public List<RchxHjcgjbxx> listByMultiConditions(HjcgjbxxQueryDTO queryDTO) {
        // 使用QueryWrapper替代复杂动态SQL
        QueryWrapper<RchxHjcgjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getHjcgbh())) {
            queryWrapper.like("hjcgbh", queryDTO.getHjcgbh());
        }
        if (StringUtils.hasText(queryDTO.getHjcgmc())) {
            queryWrapper.like("hjcgmc", queryDTO.getHjcgmc());
        }
        if (StringUtils.hasText(queryDTO.getJlmc())) {
            queryWrapper.like("jlmc", queryDTO.getJlmc());
        }
        if (StringUtils.hasText(queryDTO.getDywcrxm())) {
            queryWrapper.like("dywcrxm", queryDTO.getDywcrxm());
        }
        if (StringUtils.hasText(queryDTO.getDywcrzgh())) {
            queryWrapper.eq("dywcrzgh", queryDTO.getDywcrzgh());
        }
        if (StringUtils.hasText(queryDTO.getHjjb())) {
            queryWrapper.eq("hjjb", queryDTO.getHjjb());
        }
        if (StringUtils.hasText(queryDTO.getJldj())) {
            queryWrapper.eq("jldj", queryDTO.getJldj());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getDwmc())) {
            queryWrapper.like("dwmc", queryDTO.getDwmc());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("hjrq", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("hjrq", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("hjrq");

        return hjcgjbxxMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<RchxHjcgjbxx> pageByMultiConditions(HjcgjbxxQueryDTO queryDTO) {
        // 使用MyBatis-Plus的分页功能替代复杂动态SQL
        Page<RchxHjcgjbxx> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<RchxHjcgjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getHjcgbh())) {
            queryWrapper.like("hjcgbh", queryDTO.getHjcgbh());
        }
        if (StringUtils.hasText(queryDTO.getHjcgmc())) {
            queryWrapper.like("hjcgmc", queryDTO.getHjcgmc());
        }
        if (StringUtils.hasText(queryDTO.getJlmc())) {
            queryWrapper.like("jlmc", queryDTO.getJlmc());
        }
        if (StringUtils.hasText(queryDTO.getDywcrxm())) {
            queryWrapper.like("dywcrxm", queryDTO.getDywcrxm());
        }
        if (StringUtils.hasText(queryDTO.getDywcrzgh())) {
            queryWrapper.eq("dywcrzgh", queryDTO.getDywcrzgh());
        }
        if (StringUtils.hasText(queryDTO.getHjjb())) {
            queryWrapper.eq("hjjb", queryDTO.getHjjb());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getDwmc())) {
            queryWrapper.like("dwmc", queryDTO.getDwmc());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("hjrq", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("hjrq", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("hjrq");

        return hjcgjbxxMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveHjcgjbxx(RchxHjcgjbxx hjcgjbxx) {
        try {
            return save(hjcgjbxx);
        } catch (Exception e) {
            log.error("新增获奖成果信息失败: {}", e.getMessage());
            throw new RuntimeException("新增获奖成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByHjcgbh(RchxHjcgjbxx hjcgjbxx) {
        try {
            return updateById(hjcgjbxx);
        } catch (Exception e) {
            log.error("更新获奖成果信息失败: {}", e.getMessage());
            throw new RuntimeException("更新获奖成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByHjcgbh(String hjcgbh) {
        try {
            return removeById(hjcgbh);
        } catch (Exception e) {
            log.error("删除获奖成果信息失败: {}", e.getMessage());
            throw new RuntimeException("删除获奖成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByHjcgbhs(List<String> hjcgbhs) {
        try {
            return removeByIds(hjcgbhs);
        } catch (Exception e) {
            log.error("批量删除获奖成果信息失败: {}", e.getMessage());
            throw new RuntimeException("批量删除获奖成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchHjcgjbxx(List<RchxHjcgjbxx> hjcgjbxxList) {
        try {
            return saveBatch(hjcgjbxxList);
        } catch (Exception e) {
            log.error("批量新增获奖成果信息失败: {}", e.getMessage());
            throw new RuntimeException("批量新增获奖成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchHjcgjbxx(List<RchxHjcgjbxx> hjcgjbxxList) {
        try {
            return updateBatchById(hjcgjbxxList);
        } catch (Exception e) {
            log.error("批量更新获奖成果信息失败: {}", e.getMessage());
            throw new RuntimeException("批量更新获奖成果信息失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getTotalCount() {
        try {
            log.info("开始获取获奖成果总数统计（使用数据库聚合查询）");

            // 使用数据库聚合查询获取总数
            Long totalCount = hjcgjbxxMapper.getTotalCount();

            // 使用数据库聚合查询获取各维度统计
            Map<String, Integer> shztStats = convertToIntegerMap(hjcgjbxxMapper.getStatsByShzt());
            Map<String, Integer> hjjbStats = convertToIntegerMap(hjcgjbxxMapper.getStatsByHjjb());
            Map<String, Integer> jldjStats = convertToIntegerMap(hjcgjbxxMapper.getStatsByJldj());
            Map<String, Integer> cgxsStats = convertToIntegerMap(hjcgjbxxMapper.getStatsByCgxs());

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("timestamp", System.currentTimeMillis());
            result.put("shztStats", shztStats);
            result.put("hjjbStats", hjjbStats);
            result.put("jldjStats", jldjStats);
            result.put("cgxsStats", cgxsStats);

            log.info("获奖成果总数统计获取成功，总数: {}（使用数据库聚合查询）", totalCount);
            return result;
        } catch (Exception e) {
            log.error("获取获奖成果总数统计失败: {}", e.getMessage());
            throw new RuntimeException("获取获奖成果总数统计失败: " + e.getMessage());
        }
    }

    /**
     * 将数据库查询结果转换为Map<String, Integer>格式
     */
    private Map<String, Integer> convertToIntegerMap(List<Map<String, Object>> dbResults) {
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> row : dbResults) {
            String fieldValue = (String) row.get("fieldValue");
            Long count = (Long) row.get("count");
            result.put(fieldValue, count.intValue());
        }
        return result;
    }

    @Override
    public Map<String, Integer> getStatsByShzt() {
        try {
            log.info("查询审核状态统计（使用数据库聚合查询）");
            Map<String, Integer> shztCounts = convertToIntegerMap(hjcgjbxxMapper.getStatsByShzt());
            log.info("审核状态统计结果: {}", shztCounts);
            return shztCounts;
        } catch (Exception e) {
            log.error("查询审核状态统计失败: {}", e.getMessage());
            throw new RuntimeException("查询审核状态统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByJysmc() {
        try {
            log.info("查询教研室名称统计（使用数据库聚合查询）");
            Map<String, Integer> jysmcCounts = convertToIntegerMap(hjcgjbxxMapper.getStatsByJysmc());
            log.info("教研室名称统计结果: {}", jysmcCounts);
            return jysmcCounts;
        } catch (Exception e) {
            log.error("查询教研室名称统计失败: {}", e.getMessage());
            throw new RuntimeException("查询教研室名称统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByBjdw() {
        try {
            log.info("查询颁奖单位统计（使用数据库聚合查询）");
            Map<String, Integer> bjdwCounts = convertToIntegerMap(hjcgjbxxMapper.getStatsByBjdw());
            log.info("颁奖单位统计结果: {}", bjdwCounts);
            return bjdwCounts;
        } catch (Exception e) {
            log.error("查询颁奖单位统计失败: {}", e.getMessage());
            throw new RuntimeException("查询颁奖单位统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByCgxs() {
        try {
            log.info("查询成果形式统计（使用数据库聚合查询）");
            Map<String, Integer> cgxsCounts = convertToIntegerMap(hjcgjbxxMapper.getStatsByCgxs());
            log.info("成果形式统计结果: {}", cgxsCounts);
            return cgxsCounts;
        } catch (Exception e) {
            log.error("查询成果形式统计失败: {}", e.getMessage());
            throw new RuntimeException("查询成果形式统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByHjjb() {
        try {
            log.info("查询获奖级别统计（使用数据库聚合查询）");
            Map<String, Integer> hjjbCounts = convertToIntegerMap(hjcgjbxxMapper.getStatsByHjjb());
            log.info("获奖级别统计结果: {}", hjjbCounts);
            return hjjbCounts;
        } catch (Exception e) {
            log.error("查询获奖级别统计失败: {}", e.getMessage());
            throw new RuntimeException("查询获奖级别统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByJldj() {
        try {
            log.info("查询奖励等级统计（使用数据库聚合查询）");
            Map<String, Integer> jldjCounts = convertToIntegerMap(hjcgjbxxMapper.getStatsByJldj());
            log.info("奖励等级统计结果: {}", jldjCounts);
            return jldjCounts;
        } catch (Exception e) {
            log.error("查询奖励等级统计失败: {}", e.getMessage());
            throw new RuntimeException("查询奖励等级统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByDwmc() {
        try {
            log.info("查询单位名称统计（使用数据库聚合查询）");
            Map<String, Integer> dwmcCounts = convertToIntegerMap(hjcgjbxxMapper.getStatsByDwmc());
            log.info("单位名称统计结果: {}", dwmcCounts);
            return dwmcCounts;
        } catch (Exception e) {
            log.error("查询单位名称统计失败: {}", e.getMessage());
            throw new RuntimeException("查询单位名称统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getJldjOptions() {
        try {
            log.info("获取奖励等级选项列表（不统计数量）");
            List<String> jldjList = hjcgjbxxMapper.findDistinctJldj();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String jldj : jldjList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", jldj);
                option.put("label", jldj);
                options.add(option);
            }

            log.info("奖励等级选项列表获取成功，共{}个等级", options.size());
            return options;
        } catch (Exception e) {
            log.error("获取奖励等级选项列表失败: {}", e.getMessage());
            throw new RuntimeException("获取奖励等级选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getHjjbOptions() {
        try {
            log.info("获取获奖级别选项列表（不统计数量）");
            List<String> hjjbList = hjcgjbxxMapper.findDistinctHjjb();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String hjjb : hjjbList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", hjjb);
                option.put("label", hjjb);
                options.add(option);
            }

            log.info("获奖级别选项列表获取成功，共{}个级别", options.size());
            return options;
        } catch (Exception e) {
            log.error("获取获奖级别选项列表失败: {}", e.getMessage());
            throw new RuntimeException("获取获奖级别选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getShztOptions() {
        try {
            log.info("获取审核状态选项列表（不统计数量）");
            List<String> shztList = hjcgjbxxMapper.findDistinctShzt();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String shzt : shztList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", shzt);
                option.put("label", shzt);
                options.add(option);
            }

            log.info("审核状态选项列表获取成功，共{}个状态", options.size());
            return options;
        } catch (Exception e) {
            log.error("获取审核状态选项列表失败: {}", e.getMessage());
            throw new RuntimeException("获取审核状态选项列表失败: " + e.getMessage());
        }
    }

    // ========== 人才匹配算法专用方法实现 ==========

    @Override
    public boolean hasProvincialSecondPrizeOrAbove(String teacherZgh) {
        try {
            QueryWrapper<RchxHjcgjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dywcrzgh", teacherZgh)
                    .and(wrapper -> wrapper.like("bjdw", "省").or().like("bjdw", "部").or().like("bjdw", "国家"))
                    .and(wrapper -> wrapper.like("jldj", "一等奖").or().like("jldj", "二等奖").or().like("jldj", "特等奖"));

            return count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查教师{}省部级二等奖及以上失败: {}", teacherZgh, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean hasProvincialThirdPrizeOrAbove(String teacherZgh) {
        try {
            QueryWrapper<RchxHjcgjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dywcrzgh", teacherZgh)
                    .and(wrapper -> wrapper.like("bjdw", "省").or().like("bjdw", "部").or().like("bjdw", "国家"))
                    .and(wrapper -> wrapper.like("jldj", "一等奖").or().like("jldj", "二等奖").or().like("jldj", "三等奖").or()
                            .like("jldj", "特等奖"));

            return count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查教师{}省部级三等奖及以上失败: {}", teacherZgh, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean hasNationalAwards(String teacherZgh) {
        try {
            QueryWrapper<RchxHjcgjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dywcrzgh", teacherZgh)
                    .like("bjdw", "国家");

            return count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查教师{}国家级奖励失败: {}", teacherZgh, e.getMessage());
            return false;
        }
    }

    @Override
    public Map<String, Integer> countAwardsByLevel(String teacherZgh) {
        Map<String, Integer> result = new HashMap<>();
        try {
            QueryWrapper<RchxHjcgjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dywcrzgh", teacherZgh);

            List<RchxHjcgjbxx> awards = list(queryWrapper);

            int nationalCount = 0;
            int provincialCount = 0;
            int municipalCount = 0;
            int otherCount = 0;

            for (RchxHjcgjbxx award : awards) {
                String bjdw = award.getBjdw();
                if (bjdw != null) {
                    if (bjdw.contains("国家")) {
                        nationalCount++;
                    } else if (bjdw.contains("省") || bjdw.contains("部")) {
                        provincialCount++;
                    } else if (bjdw.contains("市") || bjdw.contains("厅") || bjdw.contains("局")) {
                        municipalCount++;
                    } else {
                        otherCount++;
                    }
                }
            }

            result.put("national", nationalCount);
            result.put("provincial", provincialCount);
            result.put("municipal", municipalCount);
            result.put("other", otherCount);
            result.put("total", awards.size());

        } catch (Exception e) {
            log.error("统计教师{}获奖数量失败: {}", teacherZgh, e.getMessage());
            result.put("national", 0);
            result.put("provincial", 0);
            result.put("municipal", 0);
            result.put("other", 0);
            result.put("total", 0);
        }

        return result;
    }
}
