# Vue前端权限管理文档

## 权限系统概述

系统共有四种权限，每种权限有独立的功能范围：

| 权限名称 | 权限描述 | 前端功能 |
|---------|---------|---------|
| 教师 | 普通教师 | 查看个人信息、修改个人资料 |
| 评审 | 评审人员 | 评审相关操作（不包含教师功能） |
| 学院管理员 | 学院管理员 | 管理学院教职工（不包含评审功能） |
| 系统管理员 | 系统管理员 | 所有功能（包含以上所有权限） |

## 1. 权限工具类

```javascript
// utils/permission.js - 权限工具类
export default {
  /**
   * 检查用户是否有指定权限
   * @param {string} userPermission 用户权限
   * @param {string} requiredPermission 需要的权限
   * @returns {boolean}
   */
  hasPermission(userPermission, requiredPermission) {
    // 系统管理员拥有所有权限
    if (userPermission === '系统管理员') {
      return true
    }

    // 其他权限只能访问自己对应的功能
    return userPermission === requiredPermission
  },
  
  /**
   * 检查是否是教师
   */
  isTeacher(userPermission) {
    return userPermission === '教师'
  },
  
  /**
   * 检查是否是评审
   */
  isReviewer(userPermission) {
    return userPermission === '评审'
  },
  
  /**
   * 检查是否是学院管理员
   */
  isCollegeAdmin(userPermission) {
    return userPermission === '学院管理员'
  },
  
  /**
   * 检查是否是系统管理员
   */
  isSystemAdmin(userPermission) {
    return userPermission === '系统管理员'
  },
  
  /**
   * 检查是否是管理员（学院管理员或系统管理员）
   */
  isAdmin(userPermission) {
    return ['学院管理员', '系统管理员'].includes(userPermission)
  },
  
  /**
   * 检查是否可以管理用户
   */
  canManageUsers(userPermission) {
    return userPermission === '学院管理员' || userPermission === '系统管理员'
  },

  /**
   * 检查是否可以评审
   */
  canReview(userPermission) {
    return userPermission === '评审' || userPermission === '系统管理员'
  }
}
```

## 2. Vuex权限模块

```javascript
// store/modules/permission.js
import permissionUtil from '@/utils/permission'

const state = {
  // 当前用户权限
  userPermission: '',
  
  // 权限列表
  permissions: ['教师', '评审', '学院管理员', '系统管理员']
}

const getters = {
  // 是否是教师
  isTeacher: state => permissionUtil.isTeacher(state.userPermission),
  
  // 是否是评审
  isReviewer: state => permissionUtil.isReviewer(state.userPermission),
  
  // 是否是学院管理员
  isCollegeAdmin: state => permissionUtil.isCollegeAdmin(state.userPermission),
  
  // 是否是系统管理员
  isSystemAdmin: state => permissionUtil.isSystemAdmin(state.userPermission),
  
  // 是否是管理员
  isAdmin: state => permissionUtil.isAdmin(state.userPermission),
  
  // 是否可以管理用户
  canManageUsers: state => permissionUtil.canManageUsers(state.userPermission),

  // 是否可以评审
  canReview: state => permissionUtil.canReview(state.userPermission),

  // 权限检查方法
  hasPermission: state => (requiredPermission) => {
    return permissionUtil.hasPermission(state.userPermission, requiredPermission)
  }
}

const mutations = {
  SET_USER_PERMISSION(state, permission) {
    state.userPermission = permission
  }
}

const actions = {
  setUserPermission({ commit }, permission) {
    commit('SET_USER_PERMISSION', permission)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
```

## 3. 权限指令

```javascript
// directives/permission.js - 权限指令
import store from '@/store'
import permissionUtil from '@/utils/permission'

export default {
  inserted(el, binding) {
    const { value } = binding
    const userPermission = store.getters['user/userInfo'].qx
    
    if (value) {
      const { permission, exact = false } = typeof value === 'string' 
        ? { permission: value } 
        : value
      
      const hasPermission = permissionUtil.hasPermission(userPermission, permission, exact)
      
      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}

// 在main.js中注册
import permission from '@/directives/permission'
Vue.directive('permission', permission)
```

## 4. 路由权限控制

```javascript
// router/permission.js - 路由权限控制
import router from './index'
import store from '@/store'
import { Message } from 'element-ui'
import permissionUtil from '@/utils/permission'

// 白名单路由（不需要权限）
const whiteList = ['/login', '/404', '/403']

router.beforeEach(async (to, from, next) => {
  const token = store.getters['user/token']
  
  if (token) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      const userInfo = store.getters['user/userInfo']
      
      if (userInfo.qx) {
        // 检查路由权限
        if (to.meta && to.meta.requiredPermission) {
          const hasPermission = permissionUtil.hasPermission(
            userInfo.qx,
            to.meta.requiredPermission
          )
          
          if (hasPermission) {
            next()
          } else {
            Message.error('权限不足，无法访问该页面')
            next('/403')
          }
        } else {
          next()
        }
      } else {
        try {
          // 获取用户信息
          await store.dispatch('user/getUserInfo')
          next()
        } catch (error) {
          await store.dispatch('user/logout')
          Message.error('获取用户信息失败，请重新登录')
          next('/login')
        }
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next('/login')
    }
  }
})
```

## 5. 路由配置示例

```javascript
// router/index.js
const routes = [
  {
    path: '/teacher',
    name: 'TeacherProfile',
    component: () => import('@/views/teacher/profile.vue'),
    meta: {
      title: '个人信息',
      requiredPermission: '教师' // 只有教师可以访问
    }
  },
  {
    path: '/review',
    name: 'ReviewManagement',
    component: () => import('@/views/review/index.vue'),
    meta: {
      title: '评审管理',
      requiredPermission: '评审' // 只有评审可以访问
    }
  },
  {
    path: '/college-admin',
    name: 'CollegeAdmin',
    component: () => import('@/views/admin/college.vue'),
    meta: {
      title: '学院管理',
      requiredPermission: '学院管理员' // 只有学院管理员可以访问
    }
  },
  {
    path: '/system-admin',
    name: 'SystemAdmin',
    component: () => import('@/views/admin/system.vue'),
    meta: {
      title: '系统管理',
      requiredPermission: '系统管理员' // 只有系统管理员可以访问
    }
  }
]
```

## 6. 组件中的权限使用

### 6.1 使用计算属性
```vue
<template>
  <div>
    <!-- 教师及以上可见 -->
    <el-button v-if="hasTeacherPermission">查看信息</el-button>
    
    <!-- 评审及以上可见 -->
    <el-button v-if="hasReviewerPermission" type="primary">开始评审</el-button>
    
    <!-- 学院管理员及以上可见 -->
    <el-button v-if="hasCollegeAdminPermission" type="success">管理学院</el-button>
    
    <!-- 只有系统管理员可见 -->
    <el-button v-if="hasSystemAdminPermission" type="danger">系统设置</el-button>
  </div>
</template>

<script>
export default {
  computed: {
    userPermission() {
      return this.$store.getters['user/userInfo'].qx
    },
    
    hasTeacherPermission() {
      return this.$store.getters['permission/hasPermission']('教师')
    },
    
    hasReviewerPermission() {
      return this.$store.getters['permission/hasPermission']('评审')
    },
    
    hasCollegeAdminPermission() {
      return this.$store.getters['permission/hasPermission']('学院管理员')
    },
    
    hasSystemAdminPermission() {
      return this.$store.getters['permission/hasPermission']('系统管理员', true) // 精确匹配
    }
  }
}
</script>
```

### 6.2 使用权限指令
```vue
<template>
  <div>
    <!-- 使用权限指令 -->
    <el-button v-permission="'教师'">教师功能</el-button>
    <el-button v-permission="'评审'">评审功能</el-button>
    <el-button v-permission="'学院管理员'">学院管理</el-button>
    <el-button v-permission="{ permission: '系统管理员', exact: true }">系统管理</el-button>
  </div>
</template>
```

### 6.3 在方法中检查权限
```javascript
export default {
  methods: {
    handleEdit() {
      if (!this.$store.getters['permission/canManageUsers']) {
        this.$message.error('权限不足')
        return
      }
      
      // 执行编辑操作
      this.editUser()
    },
    
    handleReview() {
      if (!this.$store.getters['permission/canReview']) {
        this.$message.error('您没有评审权限')
        return
      }
      
      // 执行评审操作
      this.startReview()
    }
  }
}
```

## 7. 菜单权限控制

```javascript
// 菜单配置
export const menuConfig = [
  {
    title: '个人中心',
    icon: 'user',
    path: '/profile',
    requiredPermission: '教师' // 所有用户可见
  },
  {
    title: '评审管理',
    icon: 'review',
    path: '/review',
    requiredPermission: '评审' // 评审及以上可见
  },
  {
    title: '学院管理',
    icon: 'college',
    path: '/college',
    requiredPermission: '学院管理员' // 学院管理员及以上可见
  },
  {
    title: '系统管理',
    icon: 'system',
    path: '/system',
    requiredPermission: '系统管理员',
    exact: true // 只有系统管理员可见
  }
]

// 过滤菜单
export function filterMenus(menus, userPermission) {
  return menus.filter(menu => {
    if (!menu.requiredPermission) return true
    
    return permissionUtil.hasPermission(
      userPermission, 
      menu.requiredPermission, 
      menu.exact
    )
  })
}
```

## 8. 权限错误页面

```vue
<!-- views/error/403.vue -->
<template>
  <div class="error-page">
    <div class="error-content">
      <h1>403</h1>
      <h2>权限不足</h2>
      <p>抱歉，您没有权限访问该页面</p>
      <p>当前权限：{{ userPermission }}</p>
      <el-button type="primary" @click="goBack">返回上一页</el-button>
      <el-button @click="goHome">回到首页</el-button>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    userPermission() {
      return this.$store.getters['user/userInfo'].qx
    }
  },
  
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    
    goHome() {
      this.$router.push('/')
    }
  }
}
</script>
```

## 注意事项

1. **前端权限控制只是用户体验优化**，真正的权限验证在后端
2. **权限名称必须与后端保持一致**：教师、评审、学院管理员、系统管理员
3. **权限级别**：高权限自动拥有低权限的所有功能
4. **精确匹配**：某些功能只允许特定权限访问时使用
5. **权限变更**：用户权限变更后需要重新获取用户信息
