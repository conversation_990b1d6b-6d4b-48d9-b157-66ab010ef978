package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxNationalProjectArchive;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_national_project_archive(国家/省部级项目归档主表)】的数据库操作Service
 * @createDate 2025-01-04 10:00:00
 */
public interface RchxNationalProjectArchiveService extends IService<RchxNationalProjectArchive> {

    /**
     * 根据归档编号查询
     */
    RchxNationalProjectArchive getByArchiveCode(String archiveCode);

    /**
     * 根据项目编号查询
     */
    RchxNationalProjectArchive getByProjectCode(String projectCode);

    /**
     * 根据项目名称模糊查询
     */
    List<RchxNationalProjectArchive> listByProjectNameLike(String projectName);

    /**
     * 根据项目负责人职工号查询
     */
    List<RchxNationalProjectArchive> listByProjectLeaderZgh(String leaderZgh);

    /**
     * 根据项目负责人姓名模糊查询
     */
    List<RchxNationalProjectArchive> listByProjectLeaderNameLike(String leaderName);

    /**
     * 根据归档状态查询
     */
    List<RchxNationalProjectArchive> listByArchiveStatus(String archiveStatus);

    /**
     * 根据项目级别ID查询
     */
    List<RchxNationalProjectArchive> listByLevelId(Integer levelId);

    /**
     * 根据部门ID查询
     */
    List<RchxNationalProjectArchive> listByDeptId(Integer deptId);

    /**
     * 根据项目时间范围查询
     */
    List<RchxNationalProjectArchive> listByProjectDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 根据归档时间范围查询
     */
    List<RchxNationalProjectArchive> listByArchiveTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询即将到期的项目（归档截止时间）
     */
    List<RchxNationalProjectArchive> listPendingByDeadline(LocalDateTime deadline);

    /**
     * 统计各状态的项目数量
     */
    List<Map<String, Object>> countByArchiveStatus();

    /**
     * 统计各级别的项目数量
     */
    List<Map<String, Object>> countByLevel();

    /**
     * 统计各部门的项目数量
     */
    List<Map<String, Object>> countByDepartment();

    /**
     * 获取项目归档概览（关联查询）
     */
    Map<String, Object> getArchiveOverview(Long id);

    /**
     * 更新归档状态
     */
    boolean updateArchiveStatus(Long id, String archiveStatus, String operatorZgh, String operatorName);

    /**
     * 批量更新归档状态
     */
    boolean batchUpdateArchiveStatus(List<Long> ids, String archiveStatus, String operatorZgh, String operatorName);

    /**
     * 提交归档
     */
    boolean submitArchive(Long id, String archiveBy, String archiveByName);

    /**
     * 审核归档
     */
    boolean reviewArchive(Long id, String reviewerZgh, String reviewerName, String reviewComments, java.math.BigDecimal reviewScore, String archiveStatus);

    /**
     * 设置归档截止时间
     */
    boolean setArchiveDeadline(Long id, LocalDateTime deadline);

    /**
     * 发送归档提醒
     */
    boolean sendArchiveReminder(Long id);

    /**
     * 自动生成归档编号
     */
    String generateArchiveCode(String levelType);

    /**
     * 验证归档编号是否唯一
     */
    boolean isArchiveCodeUnique(String archiveCode, Long excludeId);

    /**
     * 验证项目编号是否唯一
     */
    boolean isProjectCodeUnique(String projectCode, Long excludeId);

    /**
     * 获取用户的项目归档权限
     */
    boolean hasArchivePermission(String userZgh, Long archiveId);

    /**
     * 获取归档进度统计
     */
    Map<String, Object> getArchiveProgress();
}
