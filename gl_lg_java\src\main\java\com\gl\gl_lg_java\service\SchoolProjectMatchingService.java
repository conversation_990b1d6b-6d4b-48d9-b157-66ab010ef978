package com.gl.gl_lg_java.service;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.AnalysisResult;
import com.gl.gl_lg_java.dto.MatchResult;

import java.util.List;
import java.util.Map;

/**
 * 校级项目匹配分析服务接口
 */
public interface SchoolProjectMatchingService {
    
    /**
     * 执行校级项目匹配分析
     * 
     * @param projectCode 项目代码
     * @param params 分析参数
     * @return 分析结果
     */
    AnalysisResult analyzeProjectMatching(String projectCode, AnalysisParams params);
    
    /**
     * 获取分析结果
     * 
     * @param analysisId 分析ID
     * @return 分析结果
     */
    AnalysisResult getAnalysisResults(String analysisId);
    
    /**
     * 获取所有支持的校级项目
     * 
     * @return 项目列表
     */
    List<Map<String, Object>> getAllSchoolProjects();
    
    /**
     * 分析单个教师的匹配情况
     * 
     * @param teacherZgh 教师职工号
     * @param projectCode 项目代码
     * @param params 分析参数
     * @return 匹配结果
     */
    MatchResult analyzeSingleTeacher(String teacherZgh, String projectCode, AnalysisParams params);
    
    /**
     * 获取教师对所有项目的匹配情况
     * 
     * @param teacherZgh 教师职工号
     * @param params 分析参数
     * @return 匹配结果列表
     */
    List<MatchResult> analyzeTeacherForAllProjects(String teacherZgh, AnalysisParams params);
}
