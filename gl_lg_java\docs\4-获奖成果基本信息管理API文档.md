# 获奖成果基本信息管理API文档

## 基础信息
- **模块名称**: 获奖成果基本信息管理
- **基础路径**: `/api/hjcgjbxx`
- **权限要求**: 需要JWT Token认证
- **数据格式**: JSON

## 统一响应格式

```json
{
    "code": 200,           // 状态码：200-成功，500-失败，401-未授权，403-权限不足
    "message": "操作成功",  // 响应消息
    "data": {}            // 响应数据（可能为null）
}
```

## 接口列表

### 1. 根据获奖成果编号查询

**接口地址**: `GET /api/hjcgjbxx/{hjcgbh}`

**权限要求**: 教师+

**路径参数**:
- `hjcgbh` (string, 必填): 获奖成果编号

**请求示例**:
```
GET /api/hjcgjbxx/HJ2025001
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "hjcgbh": "HJ2025001",
        "hjcgmc": "人工智能教学创新成果",
        "jlmc": "国家级教学成果奖",
        "dywcrzgh": "T001",
        "dywcrxm": "张三",
        "hjjb": "国家级",
        "hjdj": "一等奖",
        "hjrq": "2025-06-01",
        "hjdw": "教育部",
        "shzt": "已审核",
        "dwmc": "计算机学院",
        "cjsj": "2025-07-18 10:00:00",
        "gxsj": "2025-07-18 10:00:00"
    }
}
```

### 2. 分页查询获奖成果信息

**接口地址**: `POST /api/hjcgjbxx/page`

**权限要求**: 教师+

**请求体**:
```json
{
    "hjcgbh": "HJ2025",             // 获奖成果编号（可选）
    "hjcgmc": "人工智能",           // 获奖成果名称（可选）
    "jlmc": "教学成果奖",           // 奖励名称（可选）
    "dywcrxm": "张三",              // 第一完成人姓名（可选）
    "dywcrzgh": "T001",             // 第一完成人职工号（可选）
    "hjjb": "国家级",               // 获奖级别（可选）
    "hjdj": "一等奖",               // 获奖等级（可选）
    "shzt": "已审核",               // 审核状态（可选）
    "dwmc": "计算机学院",           // 单位名称（可选）
    "startDate": "2025-01-01",      // 获奖日期开始（可选）
    "endDate": "2025-12-31",        // 获奖日期结束（可选）
    "pageNum": 1,                   // 页码（默认1）
    "pageSize": 10,                 // 页大小（默认10）
    "orderBy": "hjrq",              // 排序字段（默认hjrq）
    "orderDirection": "desc"        // 排序方向（默认desc）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "records": [
            {
                "hjcgbh": "HJ2025001",
                "hjcgmc": "人工智能教学创新成果",
                "jlmc": "国家级教学成果奖",
                "dywcrzgh": "T001",
                "dywcrxm": "张三",
                "hjjb": "国家级",
                "hjdj": "一等奖",
                "hjrq": "2025-06-01",
                "hjdw": "教育部",
                "shzt": "已审核",
                "dwmc": "计算机学院",
                "cjsj": "2025-07-18 10:00:00",
                "gxsj": "2025-07-18 10:00:00"
            }
        ],
        "total": 25,
        "size": 10,
        "current": 1,
        "pages": 3
    }
}
```

### 3. 多条件查询获奖成果信息

**接口地址**: `POST /api/hjcgjbxx/list`

**权限要求**: 教师+

**请求体**: 同分页查询，但不返回分页信息

### 4. 根据获奖成果名称模糊查询

**接口地址**: `GET /api/hjcgjbxx/name/{hjcgmc}`

**权限要求**: 学院管理员+

**路径参数**:
- `hjcgmc` (string, 必填): 获奖成果名称关键字

### 5. 根据第一完成人职工号查询

**接口地址**: `GET /api/hjcgjbxx/winner/{dywcrzgh}`

**权限要求**: 教师+

**路径参数**:
- `dywcrzgh` (string, 必填): 第一完成人职工号

### 6. 根据奖励名称模糊查询

**接口地址**: `GET /api/hjcgjbxx/award/{jlmc}`

**权限要求**: 学院管理员+

**路径参数**:
- `jlmc` (string, 必填): 奖励名称关键字

### 7. 根据获奖级别查询

**接口地址**: `GET /api/hjcgjbxx/level/{hjjb}`

**权限要求**: 学院管理员+

**路径参数**:
- `hjjb` (string, 必填): 获奖级别

### 8. 根据审核状态查询

**接口地址**: `GET /api/hjcgjbxx/audit/{shzt}`

**权限要求**: 评审+

**路径参数**:
- `shzt` (string, 必填): 审核状态

### 9. 新增获奖成果信息

**接口地址**: `POST /api/hjcgjbxx`

**权限要求**: 学院管理员+

**请求体**:
```json
{
    "hjcgbh": "HJ2025002",                      // 获奖成果编号（必填）
    "hjcgmc": "大数据分析技术创新成果",         // 获奖成果名称（必填）
    "jlmc": "省科技进步奖",                     // 奖励名称（必填）
    "dywcrzgh": "T002",                         // 第一完成人职工号（必填）
    "dywcrxm": "李四",                          // 第一完成人姓名（必填）
    "hjjb": "省级",                             // 获奖级别（必填）
    "hjdj": "二等奖",                           // 获奖等级（必填）
    "hjrq": "2025-05-15",                       // 获奖日期（必填）
    "hjdw": "省科技厅",                         // 获奖单位（可选）
    "shzt": "待审核",                           // 审核状态（必填）
    "dwmc": "计算机学院",                       // 单位名称（可选）
    "hjcgms": "该成果在大数据分析领域具有重要创新意义"  // 获奖成果描述（可选）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "新增成功",
    "data": "新增成功"
}
```

### 10. 更新获奖成果信息

**接口地址**: `PUT /api/hjcgjbxx`

**权限要求**: 学院管理员+（教师可修改自己的获奖成果）

**请求体**: 同新增，但hjcgbh不可修改

### 11. 删除获奖成果信息

**接口地址**: `DELETE /api/hjcgjbxx/{hjcgbh}`

**权限要求**: 系统管理员

**路径参数**:
- `hjcgbh` (string, 必填): 获奖成果编号

### 12. 批量新增获奖成果信息

**接口地址**: `POST /api/hjcgjbxx/batch`

**权限要求**: 学院管理员+

**请求体**:
```json
[
    {
        "hjcgbh": "HJ2025003",
        "hjcgmc": "机器学习算法优化成果",
        "jlmc": "市科技创新奖",
        "dywcrzgh": "T003",
        "dywcrxm": "王五",
        "hjjb": "市级",
        "hjdj": "一等奖",
        "hjrq": "2025-04-20",
        "hjdw": "市科技局",
        "shzt": "待审核",
        "dwmc": "计算机学院"
    }
]
```

### 13. 批量更新获奖成果信息

**接口地址**: `PUT /api/hjcgjbxx/batch`

**权限要求**: 学院管理员+

### 14. 批量删除获奖成果信息

**接口地址**: `DELETE /api/hjcgjbxx/batch`

**权限要求**: 系统管理员

**请求体**:
```json
["HJ2025001", "HJ2025002", "HJ2025003"]
```

## 获奖级别说明

- **国家级**: 国家部委颁发的奖项
- **省级**: 省级政府或部门颁发的奖项
- **市级**: 市级政府或部门颁发的奖项
- **校级**: 学校内部颁发的奖项
- **国际级**: 国际组织颁发的奖项

## 获奖等级说明

- **特等奖**: 最高等级奖项
- **一等奖**: 一等奖项
- **二等奖**: 二等奖项
- **三等奖**: 三等奖项
- **优秀奖**: 优秀奖项
- **鼓励奖**: 鼓励奖项

## 常见奖励类型

### 教学类
- **国家级教学成果奖**: 教育部颁发
- **省级教学成果奖**: 省教育厅颁发
- **优秀教师奖**: 各级教育部门颁发

### 科研类
- **国家科技进步奖**: 国务院颁发
- **省科技进步奖**: 省政府颁发
- **自然科学奖**: 各级科技部门颁发

### 人才类
- **长江学者**: 教育部颁发
- **杰出青年基金**: 国家自然科学基金委颁发
- **优秀青年基金**: 国家自然科学基金委颁发

## 审核状态说明

- **待审核**: 等待内部审核
- **审核中**: 正在进行内部审核
- **已审核**: 内部审核通过
- **审核不通过**: 内部审核未通过

## 权限说明

### 数据权限
- **教师**: 只能查看和修改自己的获奖成果
- **评审**: 可以查看获奖成果信息，进行成果评审
- **学院管理员**: 可以管理本学院的获奖成果
- **系统管理员**: 可以管理所有获奖成果

### 操作权限
- **查询**: 教师+
- **新增**: 学院管理员+
- **修改**: 学院管理员+（教师可修改自己的）
- **删除**: 系统管理员

## 错误码说明

- **200**: 操作成功
- **401**: 未授权（Token无效或过期）
- **403**: 权限不足
- **500**: 服务器内部错误

## 注意事项

1. 所有接口都需要在请求头中携带JWT Token
2. 教师权限的用户只能操作自己的获奖成果
3. 获奖成果编号必须唯一
4. 批量操作支持事务，失败时会回滚
5. 分页查询默认按获奖日期倒序排列
6. 日期格式统一使用 yyyy-MM-dd
7. 获奖级别和等级有固定的枚举值
8. 获奖成果需要提供相关证明材料
