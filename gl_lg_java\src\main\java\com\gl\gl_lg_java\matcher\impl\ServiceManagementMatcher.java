package com.gl.gl_lg_java.matcher.impl;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.matcher.ProjectMatcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;

/**
 * 服务管理人才支持计划项目匹配算法（简化版）
 */
@Component
@Slf4j
public class ServiceManagementMatcher implements ProjectMatcher {
    
    @Override
    public String getProjectCode() {
        return "FWGL";
    }
    
    @Override
    public String getProjectName() {
        return "服务管理人才支持计划项目";
    }
    
    @Override
    public MatchResult matchTeacher(RchxJzgjbxx teacher, AnalysisParams params) {
        log.info("开始分析教师{}的服务管理匹配度", teacher.getZgh());
        
        MatchResult result = MatchResult.builder()
            .teacherZgh(teacher.getZgh())
            .teacherName(teacher.getXm())
            .department(teacher.getBm())
            .title(teacher.getZc())
            .createdTime(LocalDateTime.now())
            .strengths(new ArrayList<>())
            .weaknesses(new ArrayList<>())
            .recommendations(new ArrayList<>())
            .build();
        
        try {
            // 简化的匹配逻辑
            double score = 65.0; // 基础分
            
            // 基于职称和管理经验推断
            if (teacher.getZc() != null) {
                if (teacher.getZc().contains("主任") || teacher.getZc().contains("院长") || teacher.getZc().contains("书记")) {
                    score += 20.0;
                    result.getStrengths().add("具有管理职务");
                } else if (teacher.getZc().contains("副")) {
                    score += 15.0;
                    result.getStrengths().add("具有副职管理经验");
                }
            }
            
            // 基于部门给分
            if (teacher.getBm() != null && !teacher.getBm().isEmpty()) {
                score += 10.0;
                result.getStrengths().add("有明确的部门归属，便于管理服务");
            }
            
            result.setMatchScore(score);
            result.setIsQualified(score >= params.getMinMatchScore());
            result.calculateMatchLevel();
            
            // 生成建议
            if (result.getIsQualified()) {
                result.getRecommendations().add("建议完善管理服务相关材料");
                result.getRecommendations().add("准备年度考核和师德考核证明");
            } else {
                result.getRecommendations().add("建议积累管理服务经验");
                result.getRecommendations().add("争取在关键时刻表现突出");
            }
            
            log.info("教师{}服务管理匹配分析完成，得分: {}", teacher.getZgh(), score);
            
        } catch (Exception e) {
            log.error("教师{}服务管理匹配分析失败", teacher.getZgh(), e);
            result.setIsQualified(false);
            result.setReason("分析过程中发生错误: " + e.getMessage());
            result.setMatchScore(0.0);
            result.calculateMatchLevel();
        }
        
        return result;
    }
    
    @Override
    public boolean validateBasicConditions(RchxJzgjbxx teacher) {
        return teacher.getXm() != null && !teacher.getXm().isEmpty() &&
               teacher.getZgh() != null && !teacher.getZgh().isEmpty();
    }
}
