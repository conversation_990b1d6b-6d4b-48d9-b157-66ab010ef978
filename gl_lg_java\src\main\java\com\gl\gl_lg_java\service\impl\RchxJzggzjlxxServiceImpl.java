package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxJzggzjlxx;
import com.gl.gl_lg_java.dto.JzggzjlxxQueryDTO;
import com.gl.gl_lg_java.mapper.RchxJzggzjlxxMapper;
import com.gl.gl_lg_java.service.RchxJzggzjlxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_rchx_jzggzjlxx(教职工工作简历信息)】的数据库操作Service实现
* @createDate 2025-07-18 11:11:41
*/
@Service
@Slf4j
public class RchxJzggzjlxxServiceImpl extends ServiceImpl<RchxJzggzjlxxMapper, RchxJzggzjlxx>
    implements RchxJzggzjlxxService{

    @Autowired
    private RchxJzggzjlxxMapper jzggzjlxxMapper;

    @Override
    public List<RchxJzggzjlxx> listByZgh(String zgh) {
        return jzggzjlxxMapper.findByZgh(zgh);
    }

    @Override
    public List<RchxJzggzjlxx> listByGzdwLike(String gzdw) {
        return jzggzjlxxMapper.findByGzdwLike(gzdw);
    }

    @Override
    public List<RchxJzggzjlxx> listByGznrLike(String gznr) {
        return jzggzjlxxMapper.findByGznrLike(gznr);
    }

    @Override
    public List<RchxJzggzjlxx> listByCrzwLike(String crzw) {
        return jzggzjlxxMapper.findByCrzwLike(crzw);
    }

    @Override
    public List<RchxJzggzjlxx> listByCszyLike(String cszy) {
        return jzggzjlxxMapper.findByCszyLike(cszy);
    }

    @Override
    public List<RchxJzggzjlxx> listByGzzmrLike(String gzzmr) {
        return jzggzjlxxMapper.findByGzzmrLike(gzzmr);
    }

    @Override
    public List<RchxJzggzjlxx> listByGzszd(String gzszd) {
        return jzggzjlxxMapper.findByGzszd(gzszd);
    }

    @Override
    public List<RchxJzggzjlxx> listByGzqsrqBetween(String startDate, String endDate) {
        return jzggzjlxxMapper.findByGzqsrqBetween(startDate, endDate);
    }

    @Override
    public List<RchxJzggzjlxx> listByMultiConditions(JzggzjlxxQueryDTO queryDTO) {
        // 使用QueryWrapper替代复杂动态SQL
        QueryWrapper<RchxJzggzjlxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getZgh())) {
            queryWrapper.eq("zgh", queryDTO.getZgh());
        }
        if (StringUtils.hasText(queryDTO.getGzdw())) {
            queryWrapper.like("gzdw", queryDTO.getGzdw());
        }
        if (StringUtils.hasText(queryDTO.getGznr())) {
            queryWrapper.like("gznr", queryDTO.getGznr());
        }
        if (StringUtils.hasText(queryDTO.getCrzw())) {
            queryWrapper.like("crzw", queryDTO.getCrzw());
        }
        if (StringUtils.hasText(queryDTO.getCszy())) {
            queryWrapper.like("cszy", queryDTO.getCszy());
        }
        if (StringUtils.hasText(queryDTO.getGzzmr())) {
            queryWrapper.like("gzzmr", queryDTO.getGzzmr());
        }
        if (StringUtils.hasText(queryDTO.getGzszd())) {
            queryWrapper.eq("gzszd", queryDTO.getGzszd());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("gzqsrq", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("gzqsrq", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("gzqsrq");

        return jzggzjlxxMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<RchxJzggzjlxx> pageByMultiConditions(JzggzjlxxQueryDTO queryDTO) {
        // 使用MyBatis-Plus的分页功能替代复杂动态SQL
        Page<RchxJzggzjlxx> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<RchxJzggzjlxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getZgh())) {
            queryWrapper.eq("zgh", queryDTO.getZgh());
        }
        if (StringUtils.hasText(queryDTO.getGzdw())) {
            queryWrapper.like("gzdw", queryDTO.getGzdw());
        }
        if (StringUtils.hasText(queryDTO.getGznr())) {
            queryWrapper.like("gznr", queryDTO.getGznr());
        }
        if (StringUtils.hasText(queryDTO.getCrzw())) {
            queryWrapper.like("crzw", queryDTO.getCrzw());
        }
        if (StringUtils.hasText(queryDTO.getCszy())) {
            queryWrapper.like("cszy", queryDTO.getCszy());
        }
        if (StringUtils.hasText(queryDTO.getGzzmr())) {
            queryWrapper.like("gzzmr", queryDTO.getGzzmr());
        }
        if (StringUtils.hasText(queryDTO.getGzszd())) {
            queryWrapper.eq("gzszd", queryDTO.getGzszd());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("gzqsrq", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("gzqsrq", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("gzqsrq");

        return jzggzjlxxMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveJzggzjlxx(RchxJzggzjlxx jzggzjlxx) {
        try {
            return save(jzggzjlxx);
        } catch (Exception e) {
            log.error("新增工作简历信息失败: {}", e.getMessage());
            throw new RuntimeException("新增工作简历信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateJzggzjlxx(RchxJzggzjlxx jzggzjlxx) {
        try {
            return updateById(jzggzjlxx);
        } catch (Exception e) {
            log.error("更新工作简历信息失败: {}", e.getMessage());
            throw new RuntimeException("更新工作简历信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByZgh(String zgh) {
        try {
            return removeById(zgh);
        } catch (Exception e) {
            log.error("删除工作简历信息失败: {}", e.getMessage());
            throw new RuntimeException("删除工作简历信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByZghs(List<String> zghs) {
        try {
            return removeByIds(zghs);
        } catch (Exception e) {
            log.error("批量删除工作简历信息失败: {}", e.getMessage());
            throw new RuntimeException("批量删除工作简历信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchJzggzjlxx(List<RchxJzggzjlxx> jzggzjlxxList) {
        try {
            return saveBatch(jzggzjlxxList);
        } catch (Exception e) {
            log.error("批量新增工作简历信息失败: {}", e.getMessage());
            throw new RuntimeException("批量新增工作简历信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchJzggzjlxx(List<RchxJzggzjlxx> jzggzjlxxList) {
        try {
            return updateBatchById(jzggzjlxxList);
        } catch (Exception e) {
            log.error("批量更新工作简历信息失败: {}", e.getMessage());
            throw new RuntimeException("批量更新工作简历信息失败: " + e.getMessage());
        }
    }
}
