package com.gl.gl_lg_java.matcher.impl;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.matcher.ProjectMatcher;
import com.gl.gl_lg_java.util.CommonConditionValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 青年人才未来工程项目匹配算法
 */
@Component
@Slf4j
public class YoungTalentMatcher implements ProjectMatcher {

    @Autowired
    private CommonConditionValidator validator;

    @Override
    public String getProjectCode() {
        return "QNRC";
    }

    @Override
    public String getProjectName() {
        return "青年人才未来工程项目";
    }

    @Override
    public MatchResult matchTeacher(RchxJzgjbxx teacher, AnalysisParams params) {
        log.info("开始分析教师{}的青年人才匹配度", teacher.getZgh());

        MatchResult result = MatchResult.builder()
                .teacherZgh(teacher.getZgh())
                .teacherName(teacher.getXm())
                .department(teacher.getBm())
                .title(teacher.getZc())
                .age(validator.calculateAge(teacher))
                .createdTime(LocalDateTime.now())
                .strengths(new ArrayList<>())
                .weaknesses(new ArrayList<>())
                .recommendations(new ArrayList<>())
                .build();

        try {
            // 1. 基本条件检查
            if (!validateBasicConditions(teacher)) {
                result.setIsQualified(false);
                result.setReason("不满足基本政治素养或学术道德要求");
                result.setMatchScore(0.0);
                result.calculateMatchLevel();
                return result;
            }

            // 2. 年龄限制检查
            if (!checkAgeRequirement(teacher, result)) {
                result.setIsQualified(false);
                result.calculateMatchLevel();
                return result;
            }

            // 3. 重要成果检查（两种情形任选其一）
            boolean situation1 = checkSituation1(teacher, result);
            boolean situation2 = false;

            if (!situation1) {
                situation2 = checkSituation2(teacher, result);
            }

            if (!situation1 && !situation2) {
                result.setIsQualified(false);
                result.setReason("未满足近五年重要成果要求");
                result.setMatchScore(30.0); // 基础分
                result.calculateMatchLevel();
                return result;
            }

            // 4. 计算综合评分
            double score = calculateComprehensiveScore(teacher, situation1, situation2);
            result.setMatchScore(score);
            result.setIsQualified(true);
            result.calculateMatchLevel();

            // 5. 生成建议
            generateRecommendations(teacher, result);

            log.info("教师{}青年人才匹配分析完成，得分: {}", teacher.getZgh(), score);

        } catch (Exception e) {
            log.error("教师{}青年人才匹配分析失败", teacher.getZgh(), e);
            result.setIsQualified(false);
            result.setReason("分析过程中发生错误: " + e.getMessage());
            result.setMatchScore(0.0);
            result.calculateMatchLevel();
        }

        return result;
    }

    @Override
    public boolean validateBasicConditions(RchxJzgjbxx teacher) {
        // 检查政治素养、学术道德等基本条件
        return validator.checkPoliticalQuality(teacher) &&
                validator.checkAcademicAndProfessionalEthics(teacher);
    }

    /**
     * 检查年龄要求
     */
    private boolean checkAgeRequirement(RchxJzgjbxx teacher, MatchResult result) {
        Integer age = validator.calculateAge(teacher);
        if (age == null) {
            result.setReason("出生日期信息缺失，无法计算年龄");
            result.getWeaknesses().add("出生日期信息缺失");
            return false;
        }

        String discipline = validator.getDisciplineType(teacher);
        int ageLimit = "自然科学".equals(discipline) ? 36 : 38;

        if (age <= ageLimit) {
            result.getStrengths().add(String.format("年龄符合要求（%d岁≤%d岁）", age, ageLimit));
            return true;
        } else {
            result.setReason(String.format("%s类申请者年龄不能超过%d周岁（当前%d岁）", discipline, ageLimit, age));
            result.getWeaknesses().add(String.format("年龄超过限制（%d岁>%d岁）", age, ageLimit));
            return false;
        }
    }

    /**
     * 检查情形1：满足任意1项
     */
    private boolean checkSituation1(RchxJzgjbxx teacher, MatchResult result) {
        boolean passed = false;

        // 1. 省部级科技或教学成果奖励二等奖及以上（排名第1）
        if (checkProvincialAwards(teacher)) {
            result.getStrengths().add("获得省部级二等奖及以上奖励（排名第1）");
            passed = true;
        }

        // 2. 主持国家自然科学基金面上项目1项
        if (checkNationalFundProjects(teacher)) {
            result.getStrengths().add("主持国家自然科学基金面上项目");
            passed = true;
        }

        // 3. 主持国家社会科学基金年度项目1项
        if (checkNationalSocialScienceFund(teacher)) {
            result.getStrengths().add("主持国家社会科学基金年度项目");
            passed = true;
        }

        // 4. 主持广西自然科学基金"杰出青年基金"
        if (checkGuangxiOutstandingYouthFund(teacher)) {
            result.getStrengths().add("主持广西杰出青年基金");
            passed = true;
        }

        if (!passed) {
            result.getWeaknesses().add("未满足情形1任何条件");
        }

        return passed;
    }

    /**
     * 检查情形2：满足任意1项
     */
    private boolean checkSituation2(RchxJzgjbxx teacher, MatchResult result) {
        List<String> passedConditions = new ArrayList<>();

        // 1. 获省部级及以上教学科研奖三等奖及以上
        if (checkProvincialAwardsThird(teacher)) {
            passedConditions.add("省部级三等奖及以上");
        }

        // 2. 新增主持省部级科研项目或达到经费要求
        if (checkResearchFunding(teacher)) {
            passedConditions.add("省部级项目或经费达标");
        }

        // 3. 高质量论文
        if (checkHighQualityPapers(teacher)) {
            passedConditions.add("高质量论文数量达标");
        }

        // 4. 学术专著
        if (checkAcademicBooks(teacher)) {
            passedConditions.add("主编学术专著或教材");
        }

        // 5. 学术兼职
        if (checkAcademicPositions(teacher)) {
            passedConditions.add("担任重要学术职务");
        }

        // 6. 发明专利
        if (checkInventionPatents(teacher)) {
            passedConditions.add("获发明专利授权2项及以上");
        }

        // 7. 政策采纳
        if (checkPolicyAdoption(teacher)) {
            passedConditions.add("研究报告被政府采纳或获领导批示");
        }

        if (!passedConditions.isEmpty()) {
            result.getStrengths().addAll(passedConditions);
            return true;
        } else {
            result.getWeaknesses().add("未满足情形2任何条件");
            return false;
        }
    }

    /**
     * 计算综合评分
     */
    private double calculateComprehensiveScore(RchxJzgjbxx teacher, boolean situation1, boolean situation2) {
        double baseScore = 60.0; // 基础分
        double bonusScore = 0.0;

        // 情形1通过给予更高分数
        if (situation1) {
            bonusScore += 25.0;
        } else if (situation2) {
            bonusScore += 15.0;
        }

        // 根据成果数量和质量给予额外分数
        bonusScore += calculateAchievementBonus(teacher);

        return Math.min(100.0, baseScore + bonusScore);
    }

    /**
     * 计算成果奖励分数（简化实现）
     */
    private double calculateAchievementBonus(RchxJzgjbxx teacher) {
        double bonus = 0.0;

        // 基于职称推断成果数量
        if (teacher.getZc() != null) {
            if (teacher.getZc().contains("教授")) {
                bonus += 15.0; // 教授级别成果较多
            } else if (teacher.getZc().contains("副教授")) {
                bonus += 10.0; // 副教授级别成果中等
            } else if (teacher.getZc().contains("讲师")) {
                bonus += 5.0; // 讲师级别成果较少
            }
        }

        return bonus;
    }

    /**
     * 生成改进建议
     */
    private void generateRecommendations(RchxJzgjbxx teacher, MatchResult result) {
        if (result.getIsQualified()) {
            result.getRecommendations().add("建议尽快准备申报材料");
            result.getRecommendations().add("完善成果证明文件");
        } else {
            result.getRecommendations().add("建议加强高质量论文发表");
            result.getRecommendations().add("积极申报国家级或省部级科研项目");
            result.getRecommendations().add("争取获得省部级以上奖励");
        }
    }

    // 以下是具体的条件检查方法（简化实现）
    private boolean checkProvincialAwards(RchxJzgjbxx teacher) {
        // 简化实现：基于职称推断
        return teacher.getZc() != null && teacher.getZc().contains("教授");
    }

    private boolean checkNationalFundProjects(RchxJzgjbxx teacher) {
        // 简化实现：基于职称推断
        return teacher.getZc() != null && teacher.getZc().contains("教授");
    }

    private boolean checkNationalSocialScienceFund(RchxJzgjbxx teacher) {
        // 简化实现：基于学科和职称推断
        String discipline = validator.getDisciplineType(teacher);
        return "社会科学".equals(discipline) && teacher.getZc() != null && teacher.getZc().contains("教授");
    }

    private boolean checkGuangxiOutstandingYouthFund(RchxJzgjbxx teacher) {
        // 简化实现：基于职称推断
        return teacher.getZc() != null && (teacher.getZc().contains("教授") || teacher.getZc().contains("副教授"));
    }

    private boolean checkProvincialAwardsThird(RchxJzgjbxx teacher) {
        // 简化实现：基于职称推断
        return teacher.getZc() != null && !teacher.getZc().isEmpty();
    }

    private boolean checkResearchFunding(RchxJzgjbxx teacher) {
        // 简化实现：基于职称推断
        return teacher.getZc() != null && (teacher.getZc().contains("教授") || teacher.getZc().contains("副教授"));
    }

    private boolean checkHighQualityPapers(RchxJzgjbxx teacher) {
        // 简化实现：基于职称推断
        return teacher.getZc() != null && (teacher.getZc().contains("教授") || teacher.getZc().contains("副教授"));
    }

    private boolean checkAcademicBooks(RchxJzgjbxx teacher) {
        // 简化实现：基于职称推断
        return teacher.getZc() != null && teacher.getZc().contains("教授");
    }

    private boolean checkAcademicPositions(RchxJzgjbxx teacher) {
        // 简化实现：基于职称推断
        return teacher.getZc() != null && teacher.getZc().contains("教授");
    }

    private boolean checkInventionPatents(RchxJzgjbxx teacher) {
        // 简化实现：基于职称推断
        return teacher.getZc() != null && (teacher.getZc().contains("教授") || teacher.getZc().contains("副教授"));
    }

    private boolean checkPolicyAdoption(RchxJzgjbxx teacher) {
        // 简化实现：基于学科和职称推断
        String discipline = validator.getDisciplineType(teacher);
        return "社会科学".equals(discipline) && teacher.getZc() != null && teacher.getZc().contains("教授");
    }
}
