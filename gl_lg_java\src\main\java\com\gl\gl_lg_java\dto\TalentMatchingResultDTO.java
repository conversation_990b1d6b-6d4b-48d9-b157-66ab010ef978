package com.gl.gl_lg_java.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 人才匹配分析结果DTO
 */
@Data
public class TalentMatchingResultDTO {
    
    /**
     * 教师职工号
     */
    private String teacherId;
    
    /**
     * 教师姓名
     */
    private String teacherName;
    
    /**
     * 所属部门
     */
    private String department;
    
    /**
     * 职称
     */
    private String title;
    
    /**
     * 职称等级
     */
    private String titleLevel;
    
    /**
     * 最高学历
     */
    private String education;
    
    // ========== 各维度得分 ==========
    
    /**
     * 学术影响力得分 (0-100)
     */
    private BigDecimal academicScore;
    
    /**
     * 领域深耕度得分 (0-100)
     */
    private BigDecimal fieldScore;
    
    /**
     * 团队适配值得分 (0-100)
     */
    private BigDecimal teamScore;
    
    /**
     * 成果落地性得分 (0-100)
     */
    private BigDecimal practicalScore;
    
    /**
     * 总适配度得分 (0-100)
     */
    private BigDecimal totalScore;
    
    /**
     * 排名
     */
    private Integer ranking;
    
    // ========== 详细信息 ==========
    
    /**
     * 得分明细
     */
    private Map<String, Object> scoreBreakdown;
    
    /**
     * 推荐理由
     */
    private List<String> recommendationReasons;
    
    /**
     * 风险预警
     */
    private List<String> riskWarnings;
    
    /**
     * 推荐等级
     */
    private RecommendationLevel recommendationLevel;
    
    // ========== 统计数据 ==========
    
    /**
     * 获奖总数
     */
    private Integer totalAwards;
    
    /**
     * 近3年获奖数
     */
    private Integer recentAwards;
    
    /**
     * 项目总数
     */
    private Integer totalProjects;
    
    /**
     * 合作项目数
     */
    private Integer collaborativeProjects;
    
    /**
     * 主要研究领域
     */
    private List<String> researchFields;
    
    /**
     * 代表性成果
     */
    private List<String> representativeAchievements;
    
    /**
     * 推荐等级枚举
     */
    public enum RecommendationLevel {
        HIGHLY_RECOMMENDED("强烈推荐", 85),
        RECOMMENDED("推荐", 70),
        CONDITIONAL("有条件推荐", 60),
        NOT_RECOMMENDED("不推荐", 0);
        
        private final String description;
        private final int threshold;
        
        RecommendationLevel(String description, int threshold) {
            this.description = description;
            this.threshold = threshold;
        }
        
        public String getDescription() {
            return description;
        }
        
        public int getThreshold() {
            return threshold;
        }
        
        /**
         * 根据分数获取推荐等级
         */
        public static RecommendationLevel fromScore(BigDecimal score) {
            int scoreInt = score.intValue();
            if (scoreInt >= HIGHLY_RECOMMENDED.threshold) {
                return HIGHLY_RECOMMENDED;
            } else if (scoreInt >= RECOMMENDED.threshold) {
                return RECOMMENDED;
            } else if (scoreInt >= CONDITIONAL.threshold) {
                return CONDITIONAL;
            } else {
                return NOT_RECOMMENDED;
            }
        }
    }
    
    /**
     * 获取得分等级描述
     */
    public String getScoreGrade() {
        if (totalScore.compareTo(new BigDecimal("90")) >= 0) {
            return "优秀";
        } else if (totalScore.compareTo(new BigDecimal("80")) >= 0) {
            return "良好";
        } else if (totalScore.compareTo(new BigDecimal("70")) >= 0) {
            return "中等";
        } else if (totalScore.compareTo(new BigDecimal("60")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }
    
    /**
     * 获取得分颜色
     */
    public String getScoreColor() {
        if (totalScore.compareTo(new BigDecimal("85")) >= 0) {
            return "#67C23A"; // 绿色
        } else if (totalScore.compareTo(new BigDecimal("70")) >= 0) {
            return "#E6A23C"; // 橙色
        } else if (totalScore.compareTo(new BigDecimal("60")) >= 0) {
            return "#F56C6C"; // 红色
        } else {
            return "#909399"; // 灰色
        }
    }
}
