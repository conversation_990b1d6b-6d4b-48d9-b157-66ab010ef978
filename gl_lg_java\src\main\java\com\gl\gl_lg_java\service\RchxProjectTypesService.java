package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectTypes;

import java.util.List;

/**
 * 项目类别表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface RchxProjectTypesService extends IService<RchxProjectTypes> {

    /**
     * 获取所有启用的项目类别（按排序号排序）
     * 
     * @return 项目类别列表
     */
    List<RchxProjectTypes> getEnabledTypes();

    /**
     * 根据大类ID获取启用的项目类别
     * 
     * @param categoryId 大类ID
     * @return 项目类别列表
     */
    List<RchxProjectTypes> getEnabledTypesByCategoryId(Integer categoryId);

    /**
     * 根据类别编码获取类别信息
     * 
     * @param typeCode 类别编码
     * @return 项目类别
     */
    RchxProjectTypes getByTypeCode(String typeCode);

    /**
     * 启用/禁用项目类别
     * 
     * @param id 类别ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean updateEnabled(Integer id, Boolean enabled);

    /**
     * 检查类别编码是否存在
     * 
     * @param typeCode 类别编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByTypeCode(String typeCode, Integer excludeId);
}
