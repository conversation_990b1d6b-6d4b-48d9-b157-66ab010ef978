package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectMidlateManagement;
import com.gl.gl_lg_java.service.RchxProjectMidlateManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 项目中后期管理Controller
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@RestController
@RequestMapping("/api/rchx/project-midlate-management")
@Api(tags = "项目中后期管理")
@Slf4j
public class RchxProjectMidlateManagementController {

    @Autowired
    private RchxProjectMidlateManagementService projectMidlateManagementService;

    /**
     * 分页查询项目中后期管理信息
     */
    @GetMapping("/page")
    @ApiOperation("分页查询项目中后期管理信息")
    public Result<IPage<Map<String, Object>>> pageQuery(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("项目编号") @RequestParam(required = false) String projectCode,
            @ApiParam("项目名称") @RequestParam(required = false) String projectName,
            @ApiParam("项目状态") @RequestParam(required = false) String projectStatus,
            @ApiParam("项目大类ID") @RequestParam(required = false) Integer categoryId,
            @ApiParam("项目类别ID") @RequestParam(required = false) Integer typeId,
            @ApiParam("部门ID") @RequestParam(required = false) Integer deptId,
            @ApiParam("项目负责人职工号") @RequestParam(required = false) String projectLeaderZgh,
            @ApiParam("风险等级") @RequestParam(required = false) String riskLevel) {
        try {
            Page<RchxProjectMidlateManagement> page = new Page<>(current, size);
            IPage<Map<String, Object>> result = projectMidlateManagementService.pageQueryWithDetails(
                    page, projectCode, projectName, projectStatus,
                    categoryId, typeId, deptId, projectLeaderZgh, riskLevel);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目中后期管理信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询项目详情")
    public Result<RchxProjectMidlateManagement> getById(@ApiParam("项目ID") @PathVariable Long id) {
        try {
            RchxProjectMidlateManagement project = projectMidlateManagementService.getById(id);
            if (project == null) {
                return Result.error("项目不存在");
            }
            return Result.success(project);
        } catch (Exception e) {
            log.error("查询项目详情失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建项目中后期管理记录
     */
    @PostMapping
    @ApiOperation("创建项目中后期管理记录")
    public Result<String> create(@RequestBody RchxProjectMidlateManagement projectMidlateManagement) {
        try {
            boolean success = projectMidlateManagementService.createProject(projectMidlateManagement);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建项目中后期管理记录失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目中后期管理记录
     */
    @PutMapping
    @ApiOperation("更新项目中后期管理记录")
    public Result<String> update(@RequestBody RchxProjectMidlateManagement projectMidlateManagement) {
        try {
            boolean success = projectMidlateManagementService.updateProject(projectMidlateManagement);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新项目中后期管理记录失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目中后期管理记录
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除项目中后期管理记录")
    public Result<String> delete(@ApiParam("项目ID") @PathVariable Long id,
            @ApiParam("更新人") @RequestParam String updateBy) {
        try {
            boolean success = projectMidlateManagementService.deleteProject(id, updateBy);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除项目中后期管理记录失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除项目中后期管理记录
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除项目中后期管理记录")
    public Result<String> batchDelete(@ApiParam("项目ID列表") @RequestBody List<Long> ids,
            @ApiParam("更新人") @RequestParam String updateBy) {
        try {
            boolean success = projectMidlateManagementService.batchDeleteProjects(ids, updateBy);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除项目中后期管理记录失败: ids={}", ids, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目进度
     */
    @PutMapping("/{id}/progress")
    @ApiOperation("更新项目进度")
    public Result<String> updateProgress(@ApiParam("项目ID") @PathVariable Long id,
            @ApiParam("进度百分比") @RequestParam BigDecimal progressPercentage,
            @ApiParam("进度说明") @RequestParam(required = false) String progressDescription,
            @ApiParam("更新人") @RequestParam String updateBy) {
        try {
            boolean success = projectMidlateManagementService.updateProgress(id, progressPercentage,
                    progressDescription, updateBy);
            if (success) {
                return Result.success("更新项目进度成功");
            } else {
                return Result.error("更新项目进度失败");
            }
        } catch (Exception e) {
            log.error("更新项目进度失败: id={}", id, e);
            return Result.error("更新项目进度失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目状态
     */
    @PutMapping("/{id}/status")
    @ApiOperation("更新项目状态")
    public Result<String> updateStatus(@ApiParam("项目ID") @PathVariable Long id,
            @ApiParam("项目状态") @RequestParam String projectStatus,
            @ApiParam("更新人") @RequestParam String updateBy) {
        try {
            boolean success = projectMidlateManagementService.updateStatus(id, projectStatus, updateBy);
            if (success) {
                return Result.success("更新项目状态成功");
            } else {
                return Result.error("更新项目状态失败");
            }
        } catch (Exception e) {
            log.error("更新项目状态失败: id={}", id, e);
            return Result.error("更新项目状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新风险等级
     */
    @PutMapping("/{id}/risk")
    @ApiOperation("更新风险等级")
    public Result<String> updateRiskLevel(@ApiParam("项目ID") @PathVariable Long id,
            @ApiParam("风险等级") @RequestParam String riskLevel,
            @ApiParam("风险描述") @RequestParam(required = false) String riskDescription,
            @ApiParam("更新人") @RequestParam String updateBy) {
        try {
            boolean success = projectMidlateManagementService.updateRiskLevel(id, riskLevel, riskDescription, updateBy);
            if (success) {
                return Result.success("更新风险等级成功");
            } else {
                return Result.error("更新风险等级失败");
            }
        } catch (Exception e) {
            log.error("更新风险等级失败: id={}", id, e);
            return Result.error("更新风险等级失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目负责人查询项目列表
     */
    @GetMapping("/by-leader/{projectLeaderZgh}")
    @ApiOperation("根据项目负责人查询项目列表")
    public Result<List<RchxProjectMidlateManagement>> getByProjectLeader(
            @ApiParam("项目负责人职工号") @PathVariable String projectLeaderZgh) {
        try {
            List<RchxProjectMidlateManagement> projects = projectMidlateManagementService
                    .getByProjectLeader(projectLeaderZgh);
            return Result.success(projects);
        } catch (Exception e) {
            log.error("根据项目负责人查询项目列表失败: projectLeaderZgh={}", projectLeaderZgh, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据部门查询项目列表
     */
    @GetMapping("/by-department/{deptId}")
    @ApiOperation("根据部门查询项目列表")
    public Result<List<RchxProjectMidlateManagement>> getByDepartment(@ApiParam("部门ID") @PathVariable Integer deptId) {
        try {
            List<RchxProjectMidlateManagement> projects = projectMidlateManagementService.getByDepartment(deptId);
            return Result.success(projects);
        } catch (Exception e) {
            log.error("根据部门查询项目列表失败: deptId={}", deptId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目状态查询项目列表
     */
    @GetMapping("/by-status/{projectStatus}")
    @ApiOperation("根据项目状态查询项目列表")
    public Result<List<RchxProjectMidlateManagement>> getByStatus(
            @ApiParam("项目状态") @PathVariable String projectStatus) {
        try {
            List<RchxProjectMidlateManagement> projects = projectMidlateManagementService.getByStatus(projectStatus);
            return Result.success(projects);
        } catch (Exception e) {
            log.error("根据项目状态查询项目列表失败: projectStatus={}", projectStatus, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取项目统计信息")
    public Result<Map<String, Object>> getProjectStatistics() {
        try {
            Map<String, Object> statistics = projectMidlateManagementService.getProjectStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取项目统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门项目统计
     */
    @GetMapping("/statistics/department")
    @ApiOperation("获取部门项目统计")
    public Result<List<Map<String, Object>>> getDepartmentStatistics() {
        try {
            List<Map<String, Object>> statistics = projectMidlateManagementService.getDepartmentStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取部门项目统计失败", e);
            return Result.error("获取部门统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取风险项目列表
     */
    @GetMapping("/high-risk")
    @ApiOperation("获取风险项目列表")
    public Result<List<RchxProjectMidlateManagement>> getHighRiskProjects() {
        try {
            List<RchxProjectMidlateManagement> projects = projectMidlateManagementService.getHighRiskProjects();
            return Result.success(projects);
        } catch (Exception e) {
            log.error("获取风险项目列表失败", e);
            return Result.error("获取风险项目列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取即将到期的项目
     */
    @GetMapping("/near-deadline")
    @ApiOperation("获取即将到期的项目")
    public Result<List<RchxProjectMidlateManagement>> getProjectsNearDeadline(
            @ApiParam("天数") @RequestParam(defaultValue = "30") Integer days) {
        try {
            List<RchxProjectMidlateManagement> projects = projectMidlateManagementService.getProjectsNearDeadline(days);
            return Result.success(projects);
        } catch (Exception e) {
            log.error("获取即将到期的项目失败: days={}", days, e);
            return Result.error("获取即将到期的项目失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目编号查询项目信息
     */
    @GetMapping("/by-code/{projectCode}")
    @ApiOperation("根据项目编号查询项目信息")
    public Result<RchxProjectMidlateManagement> getByProjectCode(@ApiParam("项目编号") @PathVariable String projectCode) {
        try {
            RchxProjectMidlateManagement project = projectMidlateManagementService.getByProjectCode(projectCode);
            if (project == null) {
                return Result.error("项目不存在");
            }
            return Result.success(project);
        } catch (Exception e) {
            log.error("根据项目编号查询项目信息失败: projectCode={}", projectCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 检查项目编号是否存在
     */
    @GetMapping("/exists/{projectCode}")
    @ApiOperation("检查项目编号是否存在")
    public Result<Boolean> existsByProjectCode(@ApiParam("项目编号") @PathVariable String projectCode) {
        try {
            boolean exists = projectMidlateManagementService.existsByProjectCode(projectCode);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查项目编号是否存在失败: projectCode={}", projectCode, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 激活/停用项目
     */
    @PutMapping("/{id}/toggle-status")
    @ApiOperation("激活/停用项目")
    public Result<String> toggleProjectStatus(@ApiParam("项目ID") @PathVariable Long id,
            @ApiParam("是否激活") @RequestParam Boolean isActive,
            @ApiParam("更新人") @RequestParam String updateBy) {
        try {
            boolean success = projectMidlateManagementService.toggleProjectStatus(id, isActive, updateBy);
            if (success) {
                String action = isActive ? "激活" : "停用";
                return Result.success(action + "项目成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("切换项目状态失败: id={}, isActive={}", id, isActive, e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目概览信息
     */
    @GetMapping("/{id}/overview")
    @ApiOperation("获取项目概览信息")
    public Result<Map<String, Object>> getProjectOverview(@ApiParam("项目ID") @PathVariable Long id) {
        try {
            Map<String, Object> overview = projectMidlateManagementService.getProjectOverview(id);
            return Result.success(overview);
        } catch (Exception e) {
            log.error("获取项目概览信息失败: id={}", id, e);
            return Result.error("获取项目概览信息失败: " + e.getMessage());
        }
    }

    /**
     * 导出项目列表
     */
    @GetMapping("/export")
    @ApiOperation("导出项目列表")
    public Result<List<Map<String, Object>>> exportProjectList(
            @ApiParam("项目状态") @RequestParam(required = false) String projectStatus,
            @ApiParam("部门ID") @RequestParam(required = false) Integer deptId,
            @ApiParam("项目负责人职工号") @RequestParam(required = false) String projectLeaderZgh) {
        try {
            List<Map<String, Object>> projects = projectMidlateManagementService.exportProjectList(projectStatus,
                    deptId, projectLeaderZgh);
            return Result.success(projects);
        } catch (Exception e) {
            log.error("导出项目列表失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }
}
