# 前端对接 - 获奖成果奖励等级查询功能

## 新增功能概述

为获奖成果基本信息模块新增了 **奖励等级（jldj）** 字段的查询功能，现在可以按照"一等奖"、"二等奖"、"三等奖"等奖励等级进行筛选查询。

## 🆕 新增接口

### 1. 按奖励等级查询接口

```javascript
// 接口地址
GET /api/hjcgjbxx/grade/{jldj}

// 示例请求
fetch('/api/hjcgjbxx/grade/一等奖', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('一等奖获奖成果:', data.data);
});
```

### 2. 多条件查询中新增jldj字段

```javascript
// 分页查询 - 新增jldj字段
const queryParams = {
  hjcgmc: "教学系统",      // 获奖成果名称
  jlmc: "教学成果奖",      // 奖励名称
  jldj: "一等奖",          // 🆕 奖励等级
  hjjb: "国家级",          // 获奖级别
  dywcrxm: "张三",         // 第一完成人姓名
  shzt: "已审核",          // 审核状态
  pageNum: 1,
  pageSize: 10
};

fetch('/api/hjcgjbxx/page', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(queryParams)
})
.then(response => response.json())
.then(data => {
  console.log('查询结果:', data.data);
});
```

## 前端组件更新建议

### 1. 查询表单新增奖励等级选择器

```vue
<template>
  <el-form :model="queryForm" ref="queryForm">
    <!-- 现有字段... -->
    
    <!-- 🆕 新增奖励等级选择器 -->
    <el-form-item label="奖励等级" prop="jldj">
      <el-select v-model="queryForm.jldj" placeholder="请选择奖励等级" clearable>
        <el-option label="特等奖" value="特等奖"></el-option>
        <el-option label="一等奖" value="一等奖"></el-option>
        <el-option label="二等奖" value="二等奖"></el-option>
        <el-option label="三等奖" value="三等奖"></el-option>
        <el-option label="优秀奖" value="优秀奖"></el-option>
        <el-option label="鼓励奖" value="鼓励奖"></el-option>
      </el-select>
    </el-form-item>
    
    <!-- 现有字段... -->
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      queryForm: {
        hjcgmc: '',
        jlmc: '',
        jldj: '',        // 🆕 新增字段
        hjjb: '',
        dywcrxm: '',
        shzt: '',
        pageNum: 1,
        pageSize: 10
      }
    }
  }
}
</script>
```

### 2. 表格显示新增奖励等级列

```vue
<template>
  <el-table :data="tableData">
    <el-table-column prop="hjcgmc" label="获奖成果名称"></el-table-column>
    <el-table-column prop="jlmc" label="奖励名称"></el-table-column>
    
    <!-- 🆕 新增奖励等级列 -->
    <el-table-column prop="jldj" label="奖励等级" width="100">
      <template slot-scope="scope">
        <el-tag 
          :type="getGradeTagType(scope.row.jldj)"
          size="small">
          {{ scope.row.jldj }}
        </el-tag>
      </template>
    </el-table-column>
    
    <el-table-column prop="hjjb" label="获奖级别"></el-table-column>
    <el-table-column prop="dywcrxm" label="第一完成人"></el-table-column>
    <el-table-column prop="hjrq" label="获奖日期"></el-table-column>
  </el-table>
</template>

<script>
export default {
  methods: {
    // 🆕 根据奖励等级返回不同的标签颜色
    getGradeTagType(jldj) {
      const gradeColors = {
        '特等奖': 'danger',
        '一等奖': 'success',
        '二等奖': 'warning',
        '三等奖': 'info',
        '优秀奖': 'primary',
        '鼓励奖': ''
      };
      return gradeColors[jldj] || '';
    }
  }
}
</script>
```

### 3. 快速筛选按钮组

```vue
<template>
  <div class="grade-filter-buttons">
    <el-button-group>
      <el-button 
        v-for="grade in gradeOptions" 
        :key="grade.value"
        :type="queryForm.jldj === grade.value ? 'primary' : ''"
        size="small"
        @click="filterByGrade(grade.value)">
        {{ grade.label }}
      </el-button>
      <el-button 
        :type="!queryForm.jldj ? 'primary' : ''"
        size="small"
        @click="clearGradeFilter()">
        全部
      </el-button>
    </el-button-group>
  </div>
</template>

<script>
export default {
  data() {
    return {
      gradeOptions: [
        { label: '特等奖', value: '特等奖' },
        { label: '一等奖', value: '一等奖' },
        { label: '二等奖', value: '二等奖' },
        { label: '三等奖', value: '三等奖' },
        { label: '优秀奖', value: '优秀奖' }
      ]
    }
  },
  methods: {
    // 🆕 按奖励等级筛选
    filterByGrade(grade) {
      this.queryForm.jldj = grade;
      this.handleQuery();
    },
    
    // 🆕 清除奖励等级筛选
    clearGradeFilter() {
      this.queryForm.jldj = '';
      this.handleQuery();
    }
  }
}
</script>
```

## 统计图表更新

### 奖励等级分布饼图

```javascript
// 获取奖励等级统计数据
fetch('/api/hjcgjbxx/stats/jldj', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => {
  // 转换为ECharts需要的格式
  const chartData = Object.entries(data.data).map(([name, value]) => ({
    name: name,
    value: value
  }));
  
  // 配置饼图
  const option = {
    title: {
      text: '奖励等级分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '奖励等级',
        type: 'pie',
        radius: '50%',
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  // 渲染图表
  myChart.setOption(option);
});
```

## 常用奖励等级枚举

```javascript
// 建议在前端定义奖励等级常量
export const AWARD_GRADES = {
  SPECIAL: '特等奖',
  FIRST: '一等奖',
  SECOND: '二等奖',
  THIRD: '三等奖',
  EXCELLENT: '优秀奖',
  ENCOURAGEMENT: '鼓励奖'
};

// 奖励等级选项数组
export const AWARD_GRADE_OPTIONS = [
  { label: '特等奖', value: AWARD_GRADES.SPECIAL, color: '#f56c6c' },
  { label: '一等奖', value: AWARD_GRADES.FIRST, color: '#67c23a' },
  { label: '二等奖', value: AWARD_GRADES.SECOND, color: '#e6a23c' },
  { label: '三等奖', value: AWARD_GRADES.THIRD, color: '#909399' },
  { label: '优秀奖', value: AWARD_GRADES.EXCELLENT, color: '#409eff' },
  { label: '鼓励奖', value: AWARD_GRADES.ENCOURAGEMENT, color: '#c0c4cc' }
];
```

## 测试建议

1. **功能测试**:
   - 测试按不同奖励等级查询
   - 测试多条件查询中包含奖励等级
   - 测试奖励等级统计接口

2. **边界测试**:
   - 测试空值查询
   - 测试不存在的奖励等级
   - 测试权限控制

3. **性能测试**:
   - 测试大数据量下的查询性能
   - 测试并发查询

## 注意事项

1. **权限控制**: 教师只能查看自己的获奖成果
2. **数据一致性**: 确保奖励等级的值与数据库中的值保持一致
3. **用户体验**: 建议使用下拉选择器而不是手动输入，避免输入错误
4. **国际化**: 如需支持多语言，建议将奖励等级做成可配置的

## 更新日志

- **2025-07-21**: 新增奖励等级（jldj）字段的查询功能
- 新增 `GET /api/hjcgjbxx/grade/{jldj}` 接口
- 在多条件查询中支持jldj字段筛选
- 更新查询DTO和相关Service、Mapper层代码
