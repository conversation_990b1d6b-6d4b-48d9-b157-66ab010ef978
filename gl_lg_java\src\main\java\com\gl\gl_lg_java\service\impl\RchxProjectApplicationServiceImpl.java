package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxProjectApplication;
import com.gl.gl_lg_java.mapper.RchxProjectApplicationMapper;
import com.gl.gl_lg_java.service.RchxProjectApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目申报表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@Service
public class RchxProjectApplicationServiceImpl extends ServiceImpl<RchxProjectApplicationMapper, RchxProjectApplication>
        implements RchxProjectApplicationService {

    @Override
    public IPage<RchxProjectApplication> pageQuery(Page<RchxProjectApplication> page,
            Integer categoryId,
            Integer typeId,
            Integer deptId,
            String status,
            String projectName,
            String applicantZgh) {
        QueryWrapper<RchxProjectApplication> queryWrapper = new QueryWrapper<>();

        if (categoryId != null) {
            queryWrapper.eq("category_id", categoryId);
        }
        if (typeId != null) {
            queryWrapper.eq("type_id", typeId);
        }
        if (deptId != null) {
            queryWrapper.eq("dept_id", deptId);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        if (StringUtils.hasText(projectName)) {
            queryWrapper.like("project_name", projectName);
        }
        if (StringUtils.hasText(applicantZgh)) {
            queryWrapper.eq("applicant_zgh", applicantZgh);
        }

        queryWrapper.orderByDesc("create_time");
        return page(page, queryWrapper);
    }

    @Override
    public RchxProjectApplication getByProjectCode(String projectCode) {
        QueryWrapper<RchxProjectApplication> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_code", projectCode);
        return getOne(queryWrapper);
    }

    @Override
    public List<RchxProjectApplication> getByApplicant(String applicantZgh) {
        QueryWrapper<RchxProjectApplication> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("applicant_zgh", applicantZgh)
                .orderByDesc("create_time");
        return list(queryWrapper);
    }

    @Override
    public boolean submitApplication(Long id, String submitBy) {
        try {
            RchxProjectApplication application = new RchxProjectApplication();
            application.setId(id);
            application.setStatus("SUBMITTED");
            application.setSubmitTime(LocalDateTime.now());
            application.setUpdateBy(submitBy);
            return updateById(application);
        } catch (Exception e) {
            log.error("提交申报失败: id={}, submitBy={}", id, submitBy, e);
            return false;
        }
    }

    @Override
    public boolean reviewApplication(Long id, boolean approved, String reviewerZgh, String reviewComments) {
        try {
            RchxProjectApplication application = new RchxProjectApplication();
            application.setId(id);
            application.setStatus(approved ? "APPROVED" : "REJECTED");
            application.setReviewTime(LocalDateTime.now());
            application.setReviewerZgh(reviewerZgh);
            application.setReviewComments(reviewComments);
            application.setUpdateBy(reviewerZgh);
            return updateById(application);
        } catch (Exception e) {
            log.error("审核申报失败: id={}, approved={}, reviewerZgh={}", id, approved, reviewerZgh, e);
            return false;
        }
    }

    @Override
    public boolean existsByProjectCode(String projectCode, Long excludeId) {
        QueryWrapper<RchxProjectApplication> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_code", projectCode);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) > 0;
    }

    @Override
    public Object getApplicationStatistics(Integer deptId, Integer categoryId) {
        QueryWrapper<RchxProjectApplication> queryWrapper = new QueryWrapper<>();

        if (deptId != null) {
            queryWrapper.eq("dept_id", deptId);
        }
        if (categoryId != null) {
            queryWrapper.eq("category_id", categoryId);
        }

        // 总数统计
        long totalCount = count(queryWrapper);

        // 简化的状态统计
        Map<String, Long> statusCount = new HashMap<>();
        statusCount.put("DRAFT", 0L);
        statusCount.put("SUBMITTED", 0L);
        statusCount.put("REVIEWING", 0L);
        statusCount.put("APPROVED", 0L);
        statusCount.put("REJECTED", 0L);

        // 获取所有记录并统计
        List<RchxProjectApplication> allRecords = this.list(queryWrapper);
        for (RchxProjectApplication record : allRecords) {
            String status = record.getStatus();
            if (status != null && statusCount.containsKey(status)) {
                statusCount.put(status, statusCount.get(status) + 1);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", totalCount);
        result.put("statusCount", statusCount);

        return result;
    }
}
