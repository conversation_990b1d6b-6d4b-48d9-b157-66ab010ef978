package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectMidlateManagement;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 项目中后期管理Service接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface RchxProjectMidlateManagementService extends IService<RchxProjectMidlateManagement> {

    /**
     * 分页查询项目中后期管理信息（包含关联信息）
     * 
     * @param page 分页参数
     * @param projectCode 项目编号
     * @param projectName 项目名称
     * @param projectStatus 项目状态
     * @param categoryId 项目大类ID
     * @param typeId 项目类别ID
     * @param deptId 部门ID
     * @param projectLeaderZgh 项目负责人职工号
     * @param riskLevel 风险等级
     * @return 分页结果
     */
    IPage<Map<String, Object>> pageQueryWithDetails(Page<RchxProjectMidlateManagement> page,
                                                    String projectCode,
                                                    String projectName,
                                                    String projectStatus,
                                                    Integer categoryId,
                                                    Integer typeId,
                                                    Integer deptId,
                                                    String projectLeaderZgh,
                                                    String riskLevel);

    /**
     * 根据项目负责人查询项目列表
     * 
     * @param projectLeaderZgh 项目负责人职工号
     * @return 项目列表
     */
    List<RchxProjectMidlateManagement> getByProjectLeader(String projectLeaderZgh);

    /**
     * 根据部门查询项目列表
     * 
     * @param deptId 部门ID
     * @return 项目列表
     */
    List<RchxProjectMidlateManagement> getByDepartment(Integer deptId);

    /**
     * 根据项目状态查询项目列表
     * 
     * @param projectStatus 项目状态
     * @return 项目列表
     */
    List<RchxProjectMidlateManagement> getByStatus(String projectStatus);

    /**
     * 更新项目进度
     * 
     * @param id 项目ID
     * @param progressPercentage 进度百分比
     * @param progressDescription 进度说明
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean updateProgress(Long id, BigDecimal progressPercentage, String progressDescription, String updateBy);

    /**
     * 更新项目状态
     * 
     * @param id 项目ID
     * @param projectStatus 项目状态
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean updateStatus(Long id, String projectStatus, String updateBy);

    /**
     * 更新风险等级
     * 
     * @param id 项目ID
     * @param riskLevel 风险等级
     * @param riskDescription 风险描述
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean updateRiskLevel(Long id, String riskLevel, String riskDescription, String updateBy);

    /**
     * 获取项目统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getProjectStatistics();

    /**
     * 获取部门项目统计
     * 
     * @return 部门统计信息
     */
    List<Map<String, Object>> getDepartmentStatistics();

    /**
     * 获取风险项目列表
     * 
     * @return 高风险项目列表
     */
    List<RchxProjectMidlateManagement> getHighRiskProjects();

    /**
     * 获取即将到期的项目
     * 
     * @param days 天数
     * @return 即将到期的项目列表
     */
    List<RchxProjectMidlateManagement> getProjectsNearDeadline(Integer days);

    /**
     * 创建项目中后期管理记录
     * 
     * @param projectMidlateManagement 项目中后期管理信息
     * @return 是否成功
     */
    boolean createProject(RchxProjectMidlateManagement projectMidlateManagement);

    /**
     * 更新项目中后期管理记录
     * 
     * @param projectMidlateManagement 项目中后期管理信息
     * @return 是否成功
     */
    boolean updateProject(RchxProjectMidlateManagement projectMidlateManagement);

    /**
     * 删除项目中后期管理记录（逻辑删除）
     * 
     * @param id 项目ID
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean deleteProject(Long id, String updateBy);

    /**
     * 批量删除项目中后期管理记录（逻辑删除）
     * 
     * @param ids 项目ID列表
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean batchDeleteProjects(List<Long> ids, String updateBy);

    /**
     * 根据项目编号查询项目信息
     * 
     * @param projectCode 项目编号
     * @return 项目信息
     */
    RchxProjectMidlateManagement getByProjectCode(String projectCode);

    /**
     * 检查项目编号是否存在
     * 
     * @param projectCode 项目编号
     * @return 是否存在
     */
    boolean existsByProjectCode(String projectCode);

    /**
     * 激活/停用项目
     * 
     * @param id 项目ID
     * @param isActive 是否激活
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean toggleProjectStatus(Long id, Boolean isActive, String updateBy);

    /**
     * 获取项目概览信息（包含考核和文件统计）
     * 
     * @param id 项目ID
     * @return 项目概览信息
     */
    Map<String, Object> getProjectOverview(Long id);

    /**
     * 导出项目列表
     * 
     * @param projectStatus 项目状态
     * @param deptId 部门ID
     * @param projectLeaderZgh 项目负责人职工号
     * @return 项目列表
     */
    List<Map<String, Object>> exportProjectList(String projectStatus, Integer deptId, String projectLeaderZgh);
}
