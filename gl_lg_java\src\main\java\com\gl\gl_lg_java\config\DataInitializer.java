package com.gl.gl_lg_java.config;

import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.mapper.RchxJzgjbxxMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 应用启动时数据初始化
 */
@Component
@Slf4j
public class DataInitializer implements ApplicationRunner {

    @Autowired
    private RchxJzgjbxxMapper rchxJzgjbxxMapper;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化默认数据...");
        
        // 创建默认管理员账号
        createDefaultAdmin();
        
        log.info("默认数据初始化完成");
    }
    
    /**
     * 创建默认管理员账号
     */
    private void createDefaultAdmin() {
        try {
            // 检查是否已存在admin账号
            RchxJzgjbxx existingAdmin = rchxJzgjbxxMapper.findByZgh("admin");
            if (existingAdmin != null) {
                log.info("默认管理员账号已存在，跳过创建");
                return;
            }

            // 创建默认管理员账号
            RchxJzgjbxx admin = new RchxJzgjbxx();
            admin.setZgh("admin");
            admin.setXm("系统管理员");
            admin.setSfzjh("admin");
            admin.setDh("admin");
            admin.setDzxx("<EMAIL>");
            admin.setZc("系统管理员");
            admin.setXb("男");
            admin.setPass("admin");
            admin.setBm("系统管理部");
            admin.setQx("系统管理员");

            int result = rchxJzgjbxxMapper.insert(admin);
            if (result > 0) {
                log.info("默认管理员账号创建成功: 用户名=admin, 密码=admin, 权限=系统管理员");
            } else {
                log.error("默认管理员账号创建失败");
            }

        } catch (Exception e) {
            log.error("创建默认管理员账号失败: {}", e.getMessage());
        }
    }
}
