package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gl.gl_lg_java.domain.RchxHjcgjbxx;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.domain.RchxKjxmjbxx;
import com.gl.gl_lg_java.dto.ProjectRequirementDTO;
import com.gl.gl_lg_java.dto.TalentMatchingResultDTO;
import com.gl.gl_lg_java.dto.TeacherCapabilityDTO;
import com.gl.gl_lg_java.mapper.RchxHjcgjbxxMapper;
import com.gl.gl_lg_java.mapper.RchxJzgjbxxMapper;
import com.gl.gl_lg_java.mapper.RchxKjxmjbxxMapper;
import com.gl.gl_lg_java.service.TalentMatchingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人才匹配分析服务实现
 */
@Service
@Slf4j
public class TalentMatchingServiceImpl implements TalentMatchingService {

    @Autowired
    private RchxJzgjbxxMapper jzgjbxxMapper;

    @Autowired
    private RchxHjcgjbxxMapper hjcgjbxxMapper;

    @Autowired
    private RchxKjxmjbxxMapper kjxmjbxxMapper;

    @Override
    public List<TalentMatchingResultDTO> analyzeTalentMatching(ProjectRequirementDTO requirement) {
        log.info("开始执行人才适配度分析，项目：{}", requirement.getProjectName());

        try {
            // 1. 硬条件筛选候选人
            List<RchxJzgjbxx> candidates = filterCandidatesByHardRequirements(requirement);
            log.info("硬条件筛选后候选人数量：{}", candidates.size());

            // 2. 批量查询所有相关数据 - 性能优化关键
            Map<String, List<RchxHjcgjbxx>> teacherAwardsMap = batchQueryTeacherAwards(candidates);
            Map<String, List<RchxKjxmjbxx>> teacherProjectsMap = batchQueryTeacherProjects(candidates);

            // 3. 批量计算所有候选人的适配度得分
            List<TalentMatchingResultDTO> results = batchCalculateMatchingScores(
                    candidates, teacherAwardsMap, teacherProjectsMap, requirement);

            // 4. 按总分降序排序并设置排名
            results.sort((a, b) -> b.getTotalScore().compareTo(a.getTotalScore()));
            for (int i = 0; i < results.size(); i++) {
                results.get(i).setRanking(i + 1);
                results.get(i).setRecommendationLevel(
                        TalentMatchingResultDTO.RecommendationLevel.fromScore(results.get(i).getTotalScore()));
            }

            // 5. 限制返回结果数量
            if (results.size() > requirement.getMaxResults()) {
                results = results.subList(0, requirement.getMaxResults());
            }

            log.info("分析完成，符合条件的候选人数量：{}", results.size());
            return results;

        } catch (Exception e) {
            log.error("人才适配度分析失败：{}", e.getMessage(), e);
            throw new RuntimeException("分析失败：" + e.getMessage());
        }
    }

    /**
     * 批量查询教师获奖数据 - 性能优化
     */
    private Map<String, List<RchxHjcgjbxx>> batchQueryTeacherAwards(List<RchxJzgjbxx> candidates) {
        if (candidates.isEmpty())
            return new HashMap<>();

        // 提取所有教师ID
        List<String> teacherIds = candidates.stream()
                .map(RchxJzgjbxx::getZgh)
                .collect(Collectors.toList());

        // 一次性查询所有教师的获奖数据
        List<RchxHjcgjbxx> allAwards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>().in("dywcrzgh", teacherIds));

        // 按教师ID分组
        return allAwards.stream()
                .filter(award -> award.getDywcrzgh() != null)
                .collect(Collectors.groupingBy(RchxHjcgjbxx::getDywcrzgh));
    }

    /**
     * 批量查询教师项目数据 - 性能优化
     */
    private Map<String, List<RchxKjxmjbxx>> batchQueryTeacherProjects(List<RchxJzgjbxx> candidates) {
        if (candidates.isEmpty())
            return new HashMap<>();

        // 提取所有教师ID
        List<String> teacherIds = candidates.stream()
                .map(RchxJzgjbxx::getZgh)
                .collect(Collectors.toList());

        // 一次性查询所有教师的项目数据
        List<RchxKjxmjbxx> allProjects = kjxmjbxxMapper.selectList(
                new QueryWrapper<RchxKjxmjbxx>().in("xmfzrh", teacherIds));

        // 按教师ID分组
        return allProjects.stream()
                .filter(project -> project.getXmfzrh() != null)
                .collect(Collectors.groupingBy(RchxKjxmjbxx::getXmfzrh));
    }

    /**
     * 批量计算匹配得分 - 性能优化
     */
    private List<TalentMatchingResultDTO> batchCalculateMatchingScores(
            List<RchxJzgjbxx> candidates,
            Map<String, List<RchxHjcgjbxx>> teacherAwardsMap,
            Map<String, List<RchxKjxmjbxx>> teacherProjectsMap,
            ProjectRequirementDTO requirement) {

        List<TalentMatchingResultDTO> results = new ArrayList<>();

        for (RchxJzgjbxx teacher : candidates) {
            String teacherId = teacher.getZgh();
            List<RchxHjcgjbxx> awards = teacherAwardsMap.getOrDefault(teacherId, new ArrayList<>());
            List<RchxKjxmjbxx> projects = teacherProjectsMap.getOrDefault(teacherId, new ArrayList<>());

            TalentMatchingResultDTO result = calculateTeacherMatchingScoreOptimized(
                    teacher, awards, projects, requirement);

            if (result.getTotalScore().compareTo(requirement.getMinScoreThreshold()) >= 0) {
                results.add(result);
            }
        }

        return results;
    }

    /**
     * 硬条件筛选候选人
     */
    private List<RchxJzgjbxx> filterCandidatesByHardRequirements(ProjectRequirementDTO requirement) {
        QueryWrapper<RchxJzgjbxx> wrapper = new QueryWrapper<>();

        // 职称等级筛选
        if (StringUtils.hasText(requirement.getMinTitleLevel())) {
            List<String> allowedTitles = getAllowedTitleLevels(requirement.getMinTitleLevel());
            wrapper.in("zcdj", allowedTitles);
        }

        // 学历筛选
        if (StringUtils.hasText(requirement.getMinEducation())) {
            List<String> allowedEducations = getAllowedEducations(requirement.getMinEducation());
            wrapper.in("zgxl", allowedEducations);
        }

        // 排除指定教师
        if (requirement.getExcludeTeacherIds() != null && !requirement.getExcludeTeacherIds().isEmpty()) {
            wrapper.notIn("zgh", requirement.getExcludeTeacherIds());
        }

        List<RchxJzgjbxx> candidates = jzgjbxxMapper.selectList(wrapper);

        // 学科门类筛选（通过获奖成果关联）
        if (requirement.getRequiredXkml() != null && !requirement.getRequiredXkml().isEmpty()) {
            candidates = candidates.stream()
                    .filter(teacher -> hasMatchingField(teacher.getZgh(), requirement.getRequiredXkml(), "xkml"))
                    .collect(Collectors.toList());
        }

        // 一级学科筛选
        if (requirement.getRequiredYjxk() != null && !requirement.getRequiredYjxk().isEmpty()) {
            candidates = candidates.stream()
                    .filter(teacher -> hasMatchingField(teacher.getZgh(), requirement.getRequiredYjxk(), "yjxk"))
                    .collect(Collectors.toList());
        }

        return candidates;
    }

    /**
     * 获取允许的职称等级
     */
    private List<String> getAllowedTitleLevels(String minLevel) {
        List<String> allLevels = Arrays.asList("正高级", "副高级", "中级", "初级");
        int minIndex = allLevels.indexOf(minLevel);
        if (minIndex == -1)
            return allLevels;
        return allLevels.subList(0, minIndex + 1);
    }

    /**
     * 获取允许的学历
     */
    private List<String> getAllowedEducations(String minEducation) {
        List<String> allEducations = Arrays.asList("博士研究生", "硕士研究生", "本科");
        int minIndex = -1;
        for (int i = 0; i < allEducations.size(); i++) {
            if (allEducations.get(i).contains(minEducation)) {
                minIndex = i;
                break;
            }
        }
        if (minIndex == -1)
            return allEducations;
        return allEducations.subList(0, minIndex + 1);
    }

    /**
     * 检查教师是否有匹配的研究领域
     */
    private boolean hasMatchingField(String teacherId, List<String> requiredFields, String fieldType) {
        QueryWrapper<RchxHjcgjbxx> wrapper = new QueryWrapper<>();
        wrapper.eq("dywcrzgh", teacherId);

        if ("xkml".equals(fieldType)) {
            wrapper.in("xkml", requiredFields);
        } else if ("yjxk".equals(fieldType)) {
            wrapper.in("yjxk", requiredFields);
        }

        return hjcgjbxxMapper.selectCount(wrapper) > 0;
    }

    /**
     * 优化后的教师匹配得分计算 - 使用预查询的数据
     */
    private TalentMatchingResultDTO calculateTeacherMatchingScoreOptimized(
            RchxJzgjbxx teacher,
            List<RchxHjcgjbxx> awards,
            List<RchxKjxmjbxx> projects,
            ProjectRequirementDTO requirement) {

        TalentMatchingResultDTO result = new TalentMatchingResultDTO();

        // 基本信息
        result.setTeacherId(teacher.getZgh());
        result.setTeacherName(teacher.getXm());
        result.setDepartment(teacher.getBm());
        result.setTitle(teacher.getZc());
        result.setTitleLevel(teacher.getZcdj());
        result.setEducation(teacher.getZgxl());

        // 计算各维度得分 - 使用预查询的数据，避免重复数据库查询
        BigDecimal academicScore = calculateAcademicInfluenceScoreOptimized(awards);
        BigDecimal fieldScore = calculateFieldExpertiseScoreOptimized(awards,
                requirement.getRequiredFields(), requirement.getProjectKeywords());
        BigDecimal teamScore = calculateTeamCompatibilityScoreOptimized(teacher, awards);
        BigDecimal practicalScore = calculateAchievementPracticalityScoreOptimized(awards);

        result.setAcademicScore(academicScore);
        result.setFieldScore(fieldScore);
        result.setTeamScore(teamScore);
        result.setPracticalScore(practicalScore);

        // 计算加权总分
        BigDecimal totalScore = academicScore.multiply(requirement.getAcademicWeight())
                .add(fieldScore.multiply(requirement.getFieldWeight()))
                .add(teamScore.multiply(requirement.getTeamWeight()))
                .add(practicalScore.multiply(requirement.getPracticalWeight()))
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);

        result.setTotalScore(totalScore);

        // 生成推荐理由和风险预警
        result.setRecommendationReasons(generateRecommendationReasonsOptimized(teacher, awards, requirement));
        result.setRiskWarnings(generateRiskWarningsOptimized(teacher, awards, requirement));

        // 统计数据
        fillStatisticsDataOptimized(result, awards, projects);

        return result;
    }

    /**
     * 优化版本：学术影响力计算 - 使用预查询数据
     */
    private BigDecimal calculateAcademicInfluenceScoreOptimized(List<RchxHjcgjbxx> awards) {
        BigDecimal totalScore = BigDecimal.ZERO;

        for (RchxHjcgjbxx award : awards) {
            // 基础分数（基于获奖级别）
            BigDecimal baseScore = getAwardLevelScore(award.getHjjb());

            // 时间衰减因子
            BigDecimal timeFactor = getTimeFactor(award.getHjrq());

            // 作者贡献因子
            BigDecimal contributionFactor = getContributionFactor(award.getZzzs());

            BigDecimal awardScore = baseScore.multiply(timeFactor).multiply(contributionFactor);
            totalScore = totalScore.add(awardScore);
        }

        return totalScore.min(new BigDecimal("100"));
    }

    /**
     * 优化版本：领域深耕度计算 - 使用预查询数据
     */
    private BigDecimal calculateFieldExpertiseScoreOptimized(List<RchxHjcgjbxx> awards,
            List<String> projectFields, List<String> projectKeywords) {
        BigDecimal totalScore = BigDecimal.ZERO;

        for (RchxHjcgjbxx award : awards) {
            // 学科门类匹配得分
            BigDecimal fieldMatchScore = calculateFieldMatchScore(award.getXkml(), projectFields);

            // 一级学科匹配得分
            BigDecimal disciplineMatchScore = calculateDisciplineMatchScore(award.getYjxk(), projectFields);

            // 关键词匹配得分
            BigDecimal keywordMatchScore = calculateKeywordMatchScore(award.getHjcgmc(), projectKeywords);

            totalScore = totalScore.add(fieldMatchScore).add(disciplineMatchScore).add(keywordMatchScore);
        }

        // 加上领域成果数量奖励
        totalScore = totalScore.add(new BigDecimal(awards.size() * 2));

        return totalScore.min(new BigDecimal("100"));
    }

    /**
     * 优化版本：团队适配值计算 - 使用预查询数据
     */
    private BigDecimal calculateTeamCompatibilityScoreOptimized(RchxJzgjbxx teacher, List<RchxHjcgjbxx> awards) {
        BigDecimal score = BigDecimal.ZERO;

        // 职称等级得分
        score = score.add(getTitleLevelScore(teacher.getZcdj()));

        // 学历得分
        score = score.add(getEducationScore(teacher.getZgxl()));

        // 合作经验得分 - 从预查询的获奖数据中计算
        long collaborativeCount = awards.stream()
                .filter(award -> "是".equals(award.getSfhz()))
                .count();
        score = score.add(new BigDecimal(collaborativeCount * 5));

        return score.min(new BigDecimal("100"));
    }

    /**
     * 优化版本：成果落地性计算 - 使用预查询数据
     */
    private BigDecimal calculateAchievementPracticalityScoreOptimized(List<RchxHjcgjbxx> awards) {
        BigDecimal totalScore = BigDecimal.ZERO;

        for (RchxHjcgjbxx award : awards) {
            // 审核状态得分
            BigDecimal auditScore = getAuditStatusScore(award.getShzt());

            // 颁奖单位权威性得分
            BigDecimal authorityScore = getAuthorityScore(award.getBjdw());

            // 成果形式实用性得分
            BigDecimal practicalityScore = getPracticalityScore(award.getCgxs());

            totalScore = totalScore.add(auditScore).add(authorityScore).add(practicalityScore);
        }

        return totalScore.min(new BigDecimal("100"));
    }

    /**
     * 优化版本：生成推荐理由 - 使用预查询数据
     */
    private List<String> generateRecommendationReasonsOptimized(RchxJzgjbxx teacher,
            List<RchxHjcgjbxx> awards, ProjectRequirementDTO requirement) {
        List<String> reasons = new ArrayList<>();

        // 职称优势
        if ("正高级".equals(teacher.getZcdj())) {
            reasons.add("具有正高级职称，学术地位突出，适合担任项目负责人");
        } else if ("副高级".equals(teacher.getZcdj())) {
            reasons.add("具有副高级职称，具备丰富的科研经验");
        }

        // 学历优势
        if (teacher.getZgxl() != null && teacher.getZgxl().contains("博士")) {
            reasons.add("具有博士学历，专业基础扎实，研究能力强");
        }

        // 获奖优势
        long nationalAwards = awards.stream()
                .filter(award -> award.getHjjb() != null && award.getHjjb().contains("国家"))
                .count();
        if (nationalAwards > 0) {
            reasons.add(String.format("获得%d项国家级奖励，学术影响力显著", nationalAwards));
        }

        // 近期活跃度
        long recentAwards = awards.stream()
                .filter(award -> isRecentAward(award.getHjrq()))
                .count();
        if (recentAwards > 0) {
            reasons.add(String.format("近3年获得%d项奖励，科研活跃度高", recentAwards));
        }

        // 合作经验
        long collaborativeAwards = awards.stream()
                .filter(award -> "是".equals(award.getSfhz()))
                .count();
        if (collaborativeAwards > 0) {
            reasons.add(String.format("参与%d项合作研究，具备良好的团队协作能力", collaborativeAwards));
        }

        return reasons;
    }

    /**
     * 优化版本：生成风险预警 - 使用预查询数据
     */
    private List<String> generateRiskWarningsOptimized(RchxJzgjbxx teacher,
            List<RchxHjcgjbxx> awards, ProjectRequirementDTO requirement) {
        List<String> warnings = new ArrayList<>();

        // 检查审核状态风险
        long pendingAuditCount = awards.stream()
                .filter(award -> "待审核".equals(award.getShzt()) || "审核中".equals(award.getShzt()))
                .count();
        if (pendingAuditCount > 0) {
            warnings.add(String.format("有%d项成果尚未完成审核，可能存在流程风险", pendingAuditCount));
        }

        // 检查近期活跃度
        long recentAwards = awards.stream()
                .filter(award -> isRecentAward(award.getHjrq()))
                .count();
        if (recentAwards == 0 && awards.size() > 0) {
            warnings.add("近3年无新获奖记录，科研活跃度可能不足");
        }

        // 检查学历匹配度
        if (requirement.getMinEducation() != null) {
            if (teacher.getZgxl() != null && !teacher.getZgxl().contains(requirement.getMinEducation())) {
                warnings.add(String.format("学历层次(%s)可能低于项目要求(%s)", teacher.getZgxl(), requirement.getMinEducation()));
            }
        }

        return warnings;
    }

    /**
     * 优化版本：填充统计数据 - 使用预查询数据
     */
    private void fillStatisticsDataOptimized(TalentMatchingResultDTO result,
            List<RchxHjcgjbxx> awards, List<RchxKjxmjbxx> projects) {

        // 获奖统计
        result.setTotalAwards(awards.size());
        result.setRecentAwards((int) awards.stream()
                .filter(award -> isRecentAward(award.getHjrq()))
                .count());

        // 项目统计
        result.setTotalProjects(projects.size());

        // 合作项目统计
        result.setCollaborativeProjects((int) awards.stream()
                .filter(award -> "是".equals(award.getSfhz()))
                .count());

        // 研究领域
        Set<String> fields = awards.stream()
                .map(RchxHjcgjbxx::getXkml)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        result.setResearchFields(new ArrayList<>(fields));
    }

    /**
     * 批量查询获奖成果数据
     */
    private Map<String, List<RchxHjcgjbxx>> batchQueryAwards(List<String> teacherIds) {
        if (teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        List<RchxHjcgjbxx> allAwards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>().in("dywcrzgh", teacherIds));

        return allAwards.stream()
                .filter(award -> award.getDywcrzgh() != null)
                .collect(Collectors.groupingBy(RchxHjcgjbxx::getDywcrzgh));
    }

    /**
     * 批量查询项目数据
     */
    private Map<String, List<RchxKjxmjbxx>> batchQueryProjects(List<String> teacherIds) {
        if (teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        List<RchxKjxmjbxx> allProjects = kjxmjbxxMapper.selectList(
                new QueryWrapper<RchxKjxmjbxx>().in("xmfzrh", teacherIds));

        return allProjects.stream()
                .filter(project -> project.getXmfzrh() != null)
                .collect(Collectors.groupingBy(RchxKjxmjbxx::getXmfzrh));
    }

    /**
     * 原始的教师匹配得分计算方法 - 保留用于单个教师查询
     */
    private TalentMatchingResultDTO calculateTeacherMatchingScore(RchxJzgjbxx teacher,
            ProjectRequirementDTO requirement) {
        TalentMatchingResultDTO result = new TalentMatchingResultDTO();

        // 基本信息
        result.setTeacherId(teacher.getZgh());
        result.setTeacherName(teacher.getXm());
        result.setDepartment(teacher.getBm());
        result.setTitle(teacher.getZc());
        result.setTitleLevel(teacher.getZcdj());
        result.setEducation(teacher.getZgxl());

        // 计算各维度得分
        BigDecimal academicScore = calculateAcademicInfluenceScore(teacher.getZgh());
        BigDecimal fieldScore = calculateFieldExpertiseScore(teacher.getZgh(),
                requirement.getRequiredFields(), requirement.getProjectKeywords());
        BigDecimal teamScore = calculateTeamCompatibilityScore(teacher.getZgh());
        BigDecimal practicalScore = calculateAchievementPracticalityScore(teacher.getZgh());

        result.setAcademicScore(academicScore);
        result.setFieldScore(fieldScore);
        result.setTeamScore(teamScore);
        result.setPracticalScore(practicalScore);

        // 计算加权总分
        BigDecimal totalScore = academicScore.multiply(requirement.getAcademicWeight())
                .add(fieldScore.multiply(requirement.getFieldWeight()))
                .add(teamScore.multiply(requirement.getTeamWeight()))
                .add(practicalScore.multiply(requirement.getPracticalWeight()))
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);

        result.setTotalScore(totalScore);

        // 生成推荐理由和风险预警
        result.setRecommendationReasons(generateRecommendationReasons(teacher.getZgh(), requirement));
        result.setRiskWarnings(generateRiskWarnings(teacher.getZgh(), requirement));

        // 统计数据
        fillStatisticsData(result, teacher.getZgh());

        return result;
    }

    /**
     * 填充统计数据
     */
    private void fillStatisticsData(TalentMatchingResultDTO result, String teacherId) {
        // 获奖统计
        List<RchxHjcgjbxx> awards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>().eq("dywcrzgh", teacherId));

        result.setTotalAwards(awards.size());
        result.setRecentAwards((int) awards.stream()
                .filter(award -> isRecentAward(award.getHjrq()))
                .count());

        // 项目统计
        List<RchxKjxmjbxx> projects = kjxmjbxxMapper.selectList(
                new QueryWrapper<RchxKjxmjbxx>().eq("xmfzrh", teacherId));
        result.setTotalProjects(projects.size());

        // 合作项目统计
        result.setCollaborativeProjects((int) awards.stream()
                .filter(award -> "是".equals(award.getSfhz()))
                .count());

        // 研究领域
        Set<String> fields = awards.stream()
                .map(RchxHjcgjbxx::getXkml)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        result.setResearchFields(new ArrayList<>(fields));
    }

    /**
     * 判断是否为近期获奖（近3年）
     */
    private boolean isRecentAward(String awardDate) {
        if (!StringUtils.hasText(awardDate))
            return false;
        try {
            LocalDate date = LocalDate.parse(awardDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return date.isAfter(LocalDate.now().minusYears(3));
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public BigDecimal calculateAcademicInfluenceScore(String teacherId) {
        List<RchxHjcgjbxx> awards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>().eq("dywcrzgh", teacherId));

        BigDecimal totalScore = BigDecimal.ZERO;

        for (RchxHjcgjbxx award : awards) {
            // 基础分数（基于获奖级别）
            BigDecimal baseScore = getAwardLevelScore(award.getHjjb());

            // 时间衰减因子
            BigDecimal timeFactor = getTimeFactor(award.getHjrq());

            // 作者贡献因子
            BigDecimal contributionFactor = getContributionFactor(award.getZzzs());

            BigDecimal awardScore = baseScore.multiply(timeFactor).multiply(contributionFactor);
            totalScore = totalScore.add(awardScore);
        }

        return totalScore.min(new BigDecimal("100"));
    }

    /**
     * 获取获奖级别得分
     */
    private BigDecimal getAwardLevelScore(String awardLevel) {
        if (awardLevel == null)
            return BigDecimal.ZERO;

        if (awardLevel.contains("国家")) {
            return new BigDecimal("30");
        } else if (awardLevel.contains("省")) {
            return new BigDecimal("20");
        } else if (awardLevel.contains("市")) {
            return new BigDecimal("10");
        } else {
            return new BigDecimal("5");
        }
    }

    /**
     * 获取时间衰减因子
     */
    private BigDecimal getTimeFactor(String awardDate) {
        if (!StringUtils.hasText(awardDate))
            return new BigDecimal("0.5");

        try {
            LocalDate date = LocalDate.parse(awardDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            long yearsAgo = LocalDate.now().getYear() - date.getYear();

            if (yearsAgo <= 2) {
                return new BigDecimal("1.5");
            } else if (yearsAgo <= 5) {
                return new BigDecimal("1.0");
            } else {
                return new BigDecimal("0.5");
            }
        } catch (Exception e) {
            return new BigDecimal("0.5");
        }
    }

    /**
     * 获取作者贡献因子
     */
    private BigDecimal getContributionFactor(String authorCount) {
        if (!StringUtils.hasText(authorCount))
            return new BigDecimal("1.0");

        try {
            int count = Integer.parseInt(authorCount);
            return count <= 3 ? new BigDecimal("1.0") : new BigDecimal("0.8");
        } catch (Exception e) {
            return new BigDecimal("1.0");
        }
    }

    @Override
    public BigDecimal calculateFieldExpertiseScore(String teacherId, List<String> projectFields,
            List<String> projectKeywords) {
        List<RchxHjcgjbxx> awards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>().eq("dywcrzgh", teacherId));

        BigDecimal totalScore = BigDecimal.ZERO;

        for (RchxHjcgjbxx award : awards) {
            // 学科门类匹配得分
            BigDecimal fieldMatchScore = calculateFieldMatchScore(award.getXkml(), projectFields);

            // 一级学科匹配得分
            BigDecimal disciplineMatchScore = calculateDisciplineMatchScore(award.getYjxk(), projectFields);

            // 关键词匹配得分
            BigDecimal keywordMatchScore = calculateKeywordMatchScore(award.getHjcgmc(), projectKeywords);

            totalScore = totalScore.add(fieldMatchScore).add(disciplineMatchScore).add(keywordMatchScore);
        }

        // 加上领域成果数量奖励
        totalScore = totalScore.add(new BigDecimal(awards.size() * 2));

        return totalScore.min(new BigDecimal("100"));
    }

    /**
     * 计算学科门类匹配得分
     */
    private BigDecimal calculateFieldMatchScore(String teacherField, List<String> projectFields) {
        if (teacherField == null || projectFields == null || projectFields.isEmpty()) {
            return BigDecimal.ZERO;
        }

        for (String projectField : projectFields) {
            if (teacherField.contains(projectField) || projectField.contains(teacherField)) {
                return new BigDecimal("20");
            }
        }

        // 相关领域匹配
        if (isRelatedField(teacherField, projectFields)) {
            return new BigDecimal("10");
        }

        return BigDecimal.ZERO;
    }

    /**
     * 计算一级学科匹配得分
     */
    private BigDecimal calculateDisciplineMatchScore(String teacherDiscipline, List<String> projectFields) {
        if (teacherDiscipline == null || projectFields == null || projectFields.isEmpty()) {
            return BigDecimal.ZERO;
        }

        for (String projectField : projectFields) {
            if (teacherDiscipline.contains(projectField) || projectField.contains(teacherDiscipline)) {
                return new BigDecimal("15");
            }
        }

        return BigDecimal.ZERO;
    }

    /**
     * 计算关键词匹配得分
     */
    private BigDecimal calculateKeywordMatchScore(String achievementName, List<String> projectKeywords) {
        if (achievementName == null || projectKeywords == null || projectKeywords.isEmpty()) {
            return BigDecimal.ZERO;
        }

        int matchCount = 0;
        for (String keyword : projectKeywords) {
            if (achievementName.contains(keyword)) {
                matchCount++;
            }
        }

        return new BigDecimal(matchCount * 5);
    }

    /**
     * 判断是否为相关领域
     */
    private boolean isRelatedField(String teacherField, List<String> projectFields) {
        // 简化的相关领域判断逻辑
        Map<String, List<String>> relatedFields = Map.of(
                "工学", Arrays.asList("计算机科学与技术", "测绘科学与技术", "材料科学与工程"),
                "理学", Arrays.asList("数学", "物理学", "化学", "地理学"),
                "管理学", Arrays.asList("管理科学与工程", "工商管理", "公共管理"));

        for (String projectField : projectFields) {
            List<String> related = relatedFields.get(teacherField);
            if (related != null && related.stream().anyMatch(r -> projectField.contains(r))) {
                return true;
            }
        }

        return false;
    }

    @Override
    public BigDecimal calculateTeamCompatibilityScore(String teacherId) {
        // 获取教师基本信息
        RchxJzgjbxx teacher = jzgjbxxMapper.selectById(teacherId);
        if (teacher == null)
            return BigDecimal.ZERO;

        BigDecimal score = BigDecimal.ZERO;

        // 职称等级得分
        score = score.add(getTitleLevelScore(teacher.getZcdj()));

        // 学历得分
        score = score.add(getEducationScore(teacher.getZgxl()));

        // 合作经验得分
        List<RchxHjcgjbxx> collaborativeAwards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>()
                        .eq("dywcrzgh", teacherId)
                        .eq("sfhz", "是"));
        score = score.add(new BigDecimal(collaborativeAwards.size() * 5));

        return score.min(new BigDecimal("100"));
    }

    /**
     * 获取职称等级得分
     */
    private BigDecimal getTitleLevelScore(String titleLevel) {
        if (titleLevel == null)
            return BigDecimal.ZERO;

        switch (titleLevel) {
            case "正高级":
                return new BigDecimal("40");
            case "副高级":
                return new BigDecimal("30");
            case "中级":
                return new BigDecimal("20");
            case "初级":
                return new BigDecimal("10");
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 获取学历得分
     */
    private BigDecimal getEducationScore(String education) {
        if (education == null)
            return BigDecimal.ZERO;

        if (education.contains("博士")) {
            return new BigDecimal("30");
        } else if (education.contains("硕士")) {
            return new BigDecimal("20");
        } else if (education.contains("本科")) {
            return new BigDecimal("10");
        } else {
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal calculateAchievementPracticalityScore(String teacherId) {
        List<RchxHjcgjbxx> awards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>().eq("dywcrzgh", teacherId));

        BigDecimal totalScore = BigDecimal.ZERO;

        for (RchxHjcgjbxx award : awards) {
            // 审核状态得分
            BigDecimal auditScore = getAuditStatusScore(award.getShzt());

            // 颁奖单位权威性得分
            BigDecimal authorityScore = getAuthorityScore(award.getBjdw());

            // 成果形式实用性得分
            BigDecimal practicalityScore = getPracticalityScore(award.getCgxs());

            totalScore = totalScore.add(auditScore).add(authorityScore).add(practicalityScore);
        }

        return totalScore.min(new BigDecimal("100"));
    }

    /**
     * 获取审核状态得分
     */
    private BigDecimal getAuditStatusScore(String auditStatus) {
        if (auditStatus == null)
            return BigDecimal.ZERO;

        switch (auditStatus) {
            case "已审核":
                return new BigDecimal("20");
            case "待审核":
                return new BigDecimal("10");
            case "审核中":
                return new BigDecimal("5");
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 获取颁奖单位权威性得分
     */
    private BigDecimal getAuthorityScore(String awardingUnit) {
        if (awardingUnit == null)
            return BigDecimal.ZERO;

        if (awardingUnit.contains("国家") || awardingUnit.contains("中央")) {
            return new BigDecimal("15");
        } else if (awardingUnit.contains("省") || awardingUnit.contains("部")) {
            return new BigDecimal("10");
        } else if (awardingUnit.contains("市") || awardingUnit.contains("厅")) {
            return new BigDecimal("5");
        } else {
            return new BigDecimal("2");
        }
    }

    /**
     * 获取成果形式实用性得分
     */
    private BigDecimal getPracticalityScore(String achievementForm) {
        if (achievementForm == null)
            return BigDecimal.ZERO;

        // 根据成果形式判断实用性
        if (achievementForm.contains("应用") || achievementForm.contains("产品") || achievementForm.contains("技术")) {
            return new BigDecimal("10");
        } else if (achievementForm.contains("理论") || achievementForm.contains("方法")) {
            return new BigDecimal("5");
        } else {
            return new BigDecimal("3");
        }
    }

    @Override
    public BigDecimal calculateTotalMatchingScore(String teacherId, ProjectRequirementDTO requirement) {
        BigDecimal academicScore = calculateAcademicInfluenceScore(teacherId);
        BigDecimal fieldScore = calculateFieldExpertiseScore(teacherId,
                requirement.getRequiredFields(), requirement.getProjectKeywords());
        BigDecimal teamScore = calculateTeamCompatibilityScore(teacherId);
        BigDecimal practicalScore = calculateAchievementPracticalityScore(teacherId);

        // 加权计算总分
        BigDecimal totalScore = academicScore.multiply(requirement.getAcademicWeight())
                .add(fieldScore.multiply(requirement.getFieldWeight()))
                .add(teamScore.multiply(requirement.getTeamWeight()))
                .add(practicalScore.multiply(requirement.getPracticalWeight()))
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);

        return totalScore;
    }

    @Override
    public List<String> generateRecommendationReasons(String teacherId, ProjectRequirementDTO requirement) {
        List<String> reasons = new ArrayList<>();

        // 获取教师信息
        RchxJzgjbxx teacher = jzgjbxxMapper.selectById(teacherId);
        if (teacher == null)
            return reasons;

        // 获取获奖信息
        List<RchxHjcgjbxx> awards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>().eq("dywcrzgh", teacherId));

        // 职称优势
        if ("正高级".equals(teacher.getZcdj())) {
            reasons.add("具有正高级职称，学术地位突出，适合担任项目负责人");
        } else if ("副高级".equals(teacher.getZcdj())) {
            reasons.add("具有副高级职称，具备丰富的科研经验");
        }

        // 学历优势
        if (teacher.getZgxl() != null && teacher.getZgxl().contains("博士")) {
            reasons.add("具有博士学历，专业基础扎实，研究能力强");
        }

        // 获奖优势
        long nationalAwards = awards.stream()
                .filter(award -> award.getHjjb() != null && award.getHjjb().contains("国家"))
                .count();
        if (nationalAwards > 0) {
            reasons.add(String.format("获得%d项国家级奖励，学术影响力显著", nationalAwards));
        }

        // 近期活跃度
        long recentAwards = awards.stream()
                .filter(award -> isRecentAward(award.getHjrq()))
                .count();
        if (recentAwards > 0) {
            reasons.add(String.format("近3年获得%d项奖励，科研活跃度高", recentAwards));
        }

        // 合作经验
        long collaborativeAwards = awards.stream()
                .filter(award -> "是".equals(award.getSfhz()))
                .count();
        if (collaborativeAwards > 0) {
            reasons.add(String.format("参与%d项合作研究，具备良好的团队协作能力", collaborativeAwards));
        }

        // 领域匹配
        if (requirement.getRequiredXkml() != null) {
            Set<String> teacherFields = awards.stream()
                    .map(RchxHjcgjbxx::getXkml)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            for (String requiredField : requirement.getRequiredXkml()) {
                if (teacherFields.stream().anyMatch(field -> field.contains(requiredField))) {
                    reasons.add(String.format("在%s领域有丰富研究经验，与项目需求高度匹配", requiredField));
                    break;
                }
            }
        }

        return reasons;
    }

    @Override
    public List<String> generateRiskWarnings(String teacherId, ProjectRequirementDTO requirement) {
        List<String> warnings = new ArrayList<>();

        // 获取教师信息
        RchxJzgjbxx teacher = jzgjbxxMapper.selectById(teacherId);
        if (teacher == null)
            return warnings;

        // 获取获奖信息
        List<RchxHjcgjbxx> awards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>().eq("dywcrzgh", teacherId));

        // 检查审核状态风险
        long pendingAuditCount = awards.stream()
                .filter(award -> "待审核".equals(award.getShzt()) || "审核中".equals(award.getShzt()))
                .count();
        if (pendingAuditCount > 0) {
            warnings.add(String.format("有%d项成果尚未完成审核，可能存在流程风险", pendingAuditCount));
        }

        // 检查近期活跃度
        long recentAwards = awards.stream()
                .filter(award -> isRecentAward(award.getHjrq()))
                .count();
        if (recentAwards == 0 && awards.size() > 0) {
            warnings.add("近3年无新获奖记录，科研活跃度可能不足");
        }

        // 检查学历匹配度
        if (requirement.getMinEducation() != null) {
            if (teacher.getZgxl() != null && !teacher.getZgxl().contains(requirement.getMinEducation())) {
                warnings.add(String.format("学历层次(%s)可能低于项目要求(%s)", teacher.getZgxl(), requirement.getMinEducation()));
            }
        }

        // 检查合作经验
        long collaborativeCount = awards.stream()
                .filter(award -> "是".equals(award.getSfhz()))
                .count();
        if (requirement.getRequireCollaboration() && collaborativeCount == 0) {
            warnings.add("缺乏团队合作经验，可能影响项目协作效果");
        }

        // 检查领域匹配度
        if (requirement.getRequiredXkml() != null && !requirement.getRequiredXkml().isEmpty()) {
            Set<String> teacherFields = awards.stream()
                    .map(RchxHjcgjbxx::getXkml)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            boolean hasMatchingField = false;
            for (String requiredField : requirement.getRequiredXkml()) {
                if (teacherFields.stream().anyMatch(field -> field.contains(requiredField))) {
                    hasMatchingField = true;
                    break;
                }
            }

            if (!hasMatchingField) {
                warnings.add("研究领域与项目需求匹配度较低，可能需要额外的学习成本");
            }
        }

        return warnings;
    }

    @Override
    public TeacherCapabilityDTO getTeacherCapabilityAnalysis(String teacherId) {
        // 获取教师基本信息
        RchxJzgjbxx teacher = jzgjbxxMapper.selectById(teacherId);
        if (teacher == null) {
            throw new RuntimeException("教师不存在：" + teacherId);
        }

        TeacherCapabilityDTO dto = new TeacherCapabilityDTO();

        // 基本信息
        dto.setTeacherId(teacher.getZgh());
        dto.setTeacherName(teacher.getXm());
        dto.setDepartment(teacher.getBm());
        dto.setTitle(teacher.getZc());
        dto.setTitleLevel(teacher.getZcdj());
        dto.setEducation(teacher.getZgxl());

        // 计算各维度得分
        dto.setAcademicInfluenceScore(calculateAcademicInfluenceScore(teacherId));
        dto.setFieldExpertiseScore(calculateFieldExpertiseScore(teacherId, null, null));
        dto.setTeamCompatibilityScore(calculateTeamCompatibilityScore(teacherId));
        dto.setAchievementPracticalityScore(calculateAchievementPracticalityScore(teacherId));

        // 计算综合得分
        BigDecimal overallScore = dto.getAcademicInfluenceScore()
                .add(dto.getFieldExpertiseScore())
                .add(dto.getTeamCompatibilityScore())
                .add(dto.getAchievementPracticalityScore())
                .divide(new BigDecimal("4"), 2, RoundingMode.HALF_UP);
        dto.setOverallScore(overallScore);

        // 填充详细统计数据
        fillDetailedStatistics(dto, teacherId);

        return dto;
    }

    /**
     * 填充详细统计数据
     */
    private void fillDetailedStatistics(TeacherCapabilityDTO dto, String teacherId) {
        // 获奖统计
        List<RchxHjcgjbxx> awards = hjcgjbxxMapper.selectList(
                new QueryWrapper<RchxHjcgjbxx>().eq("dywcrzgh", teacherId));

        TeacherCapabilityDTO.AwardStatistics awardStats = new TeacherCapabilityDTO.AwardStatistics();
        awardStats.setTotalAwards(awards.size());
        awardStats.setNationalAwards((int) awards.stream()
                .filter(award -> award.getHjjb() != null && award.getHjjb().contains("国家"))
                .count());
        awardStats.setProvincialAwards((int) awards.stream()
                .filter(award -> award.getHjjb() != null && award.getHjjb().contains("省"))
                .count());
        awardStats.setRecentAwards((int) awards.stream()
                .filter(award -> isRecentAward(award.getHjrq()))
                .count());

        // 获奖类型
        Set<String> awardTypes = awards.stream()
                .map(RchxHjcgjbxx::getJlmc)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        awardStats.setAwardTypes(new ArrayList<>(awardTypes));

        dto.setAwardStatistics(awardStats);

        // 项目统计
        List<RchxKjxmjbxx> projects = kjxmjbxxMapper.selectList(
                new QueryWrapper<RchxKjxmjbxx>().eq("xmfzrh", teacherId));

        TeacherCapabilityDTO.ProjectStatistics projectStats = new TeacherCapabilityDTO.ProjectStatistics();
        projectStats.setTotalProjects(projects.size());
        projectStats.setLeadingProjects(projects.size()); // 作为负责人的项目
        projectStats.setOngoingProjects((int) projects.stream()
                .filter(project -> "进行中".equals(project.getXmzxzt()))
                .count());
        projectStats.setCompletedProjects((int) projects.stream()
                .filter(project -> "已完成".equals(project.getXmzxzt()) || "已结项".equals(project.getXmzxzt()))
                .count());

        dto.setProjectStatistics(projectStats);

        // 合作统计
        TeacherCapabilityDTO.CollaborationStatistics collabStats = new TeacherCapabilityDTO.CollaborationStatistics();
        collabStats.setCollaborativeAwards((int) awards.stream()
                .filter(award -> "是".equals(award.getSfhz()))
                .count());
        collabStats.setCollaborationCount(collabStats.getCollaborativeAwards());

        if (awards.size() > 0) {
            BigDecimal collabRate = new BigDecimal(collabStats.getCollaborativeAwards())
                    .divide(new BigDecimal(awards.size()), 2, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            collabStats.setCollaborationRate(collabRate);
        }

        dto.setCollaborationStatistics(collabStats);

        // 研究领域分布
        Map<String, Long> fieldCounts = awards.stream()
                .filter(award -> award.getXkml() != null)
                .collect(Collectors.groupingBy(RchxHjcgjbxx::getXkml, Collectors.counting()));

        List<TeacherCapabilityDTO.FieldDistribution> fieldDistributions = fieldCounts.entrySet().stream()
                .map(entry -> {
                    TeacherCapabilityDTO.FieldDistribution fd = new TeacherCapabilityDTO.FieldDistribution();
                    fd.setFieldName(entry.getKey());
                    fd.setAchievementCount(entry.getValue().intValue());
                    fd.setPercentage(new BigDecimal(entry.getValue())
                            .divide(new BigDecimal(awards.size()), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")));
                    return fd;
                })
                .sorted((a, b) -> b.getAchievementCount().compareTo(a.getAchievementCount()))
                .collect(Collectors.toList());

        dto.setFieldDistributions(fieldDistributions);

        // 能力标签
        List<String> tags = new ArrayList<>();
        if (dto.getAcademicInfluenceScore().compareTo(new BigDecimal("80")) >= 0) {
            tags.add("学术影响力强");
        }
        if (awardStats.getNationalAwards() > 0) {
            tags.add("国家级获奖专家");
        }
        if (collabStats.getCollaborationRate().compareTo(new BigDecimal("50")) >= 0) {
            tags.add("团队协作能力强");
        }
        if (awardStats.getRecentAwards() > 0) {
            tags.add("科研活跃");
        }

        dto.setCapabilityTags(tags);
    }

    @Override
    public List<ProjectRequirementDTO> getPresetTemplates() {
        List<ProjectRequirementDTO> templates = new ArrayList<>();

        // 基础研究项目模板
        ProjectRequirementDTO basicResearch = new ProjectRequirementDTO();
        basicResearch.setProjectName("基础研究项目模板");
        basicResearch.setProjectType("基础研究");
        basicResearch.setMinTitleLevel("副高级");
        basicResearch.setMinEducation("硕士");
        basicResearch.setAcademicWeight(new BigDecimal("40"));
        basicResearch.setFieldWeight(new BigDecimal("30"));
        basicResearch.setTeamWeight(new BigDecimal("20"));
        basicResearch.setPracticalWeight(new BigDecimal("10"));
        templates.add(basicResearch);

        // 应用研究项目模板
        ProjectRequirementDTO appliedResearch = new ProjectRequirementDTO();
        appliedResearch.setProjectName("应用研究项目模板");
        appliedResearch.setProjectType("应用研究");
        appliedResearch.setMinTitleLevel("副高级");
        appliedResearch.setMinEducation("硕士");
        appliedResearch.setAcademicWeight(new BigDecimal("25"));
        appliedResearch.setFieldWeight(new BigDecimal("35"));
        appliedResearch.setTeamWeight(new BigDecimal("25"));
        appliedResearch.setPracticalWeight(new BigDecimal("15"));
        templates.add(appliedResearch);

        // 产业化项目模板
        ProjectRequirementDTO industrial = new ProjectRequirementDTO();
        industrial.setProjectName("产业化项目模板");
        industrial.setProjectType("产业化");
        industrial.setMinTitleLevel("中级");
        industrial.setMinEducation("本科");
        industrial.setAcademicWeight(new BigDecimal("15"));
        industrial.setFieldWeight(new BigDecimal("25"));
        industrial.setTeamWeight(new BigDecimal("30"));
        industrial.setPracticalWeight(new BigDecimal("30"));
        templates.add(industrial);

        return templates;
    }
}
