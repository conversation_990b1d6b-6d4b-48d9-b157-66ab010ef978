# 人才适配度分析性能优化方案

## 🚨 当前性能问题

### 问题分析
- **数据量**: 4000个教师
- **查询次数**: 每个教师需要3-4次数据库查询
- **总查询数**: 可能超过 **16000次** 数据库查询
- **响应时间**: 可能超过30秒，用户体验极差

### 查询分布
```
教师基本信息查询: 4000次
获奖成果查询: 4000次  
项目信息查询: 4000次
论文专利查询: 4000次 (如果需要)
总计: 16000+ 次数据库查询
```

## 🚀 优化方案

### 方案一：批量查询优化 ⭐⭐⭐⭐⭐ (推荐)

**核心思路**: 将N次单独查询合并为3次批量查询

#### 优化前 vs 优化后
```java
// 优化前 - 4000次查询
for (Teacher teacher : teachers) {
    List<Award> awards = awardMapper.selectByTeacherId(teacher.getId());  // 4000次
    List<Project> projects = projectMapper.selectByTeacherId(teacher.getId()); // 4000次
}

// 优化后 - 2次查询
List<String> teacherIds = teachers.stream().map(Teacher::getId).collect(toList());
List<Award> allAwards = awardMapper.selectByTeacherIds(teacherIds);  // 1次
List<Project> allProjects = projectMapper.selectByTeacherIds(teacherIds); // 1次

// 内存中分组
Map<String, List<Award>> teacherAwards = allAwards.stream()
    .collect(groupingBy(Award::getTeacherId));
```

#### 性能提升
- **查询次数**: 16000+ → 3次 (减少99.98%)
- **响应时间**: 30秒 → 2-3秒 (提升90%+)
- **数据库压力**: 大幅降低

---

### 方案二：Redis缓存优化 ⭐⭐⭐⭐

**适用场景**: 教师数据变化不频繁，可以缓存计算结果

#### 实现策略
```java
@Service
public class TalentMatchingCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public List<TalentMatchingResultDTO> getCachedResults(String cacheKey) {
        return (List<TalentMatchingResultDTO>) redisTemplate.opsForValue().get(cacheKey);
    }
    
    public void cacheResults(String cacheKey, List<TalentMatchingResultDTO> results) {
        // 缓存1小时
        redisTemplate.opsForValue().set(cacheKey, results, 1, TimeUnit.HOURS);
    }
}
```

#### 缓存策略
- **缓存Key**: `talent_matching:{projectType}:{weights_hash}`
- **缓存时间**: 1-2小时
- **失效策略**: 教师数据更新时清除相关缓存

---

### 方案三：分页 + 异步处理 ⭐⭐⭐

**适用场景**: 数据量特别大，用户可以接受分批返回结果

#### 实现方式
```java
@RestController
public class TalentMatchingController {
    
    @PostMapping("/analyze-async")
    public Result<String> analyzeAsync(@RequestBody ProjectRequirementDTO requirement) {
        String taskId = UUID.randomUUID().toString();
        
        // 异步执行分析
        CompletableFuture.runAsync(() -> {
            List<TalentMatchingResultDTO> results = talentMatchingService.analyzeTalentMatching(requirement);
            // 将结果存储到Redis或数据库
            cacheService.cacheResults(taskId, results);
        });
        
        return Result.success(taskId);
    }
    
    @GetMapping("/analyze-result/{taskId}")
    public Result<List<TalentMatchingResultDTO>> getAnalyzeResult(@PathVariable String taskId) {
        List<TalentMatchingResultDTO> results = cacheService.getCachedResults(taskId);
        return Result.success(results);
    }
}
```

---

### 方案四：数据库索引优化 ⭐⭐⭐

#### 必要索引
```sql
-- 获奖成果表索引
CREATE INDEX idx_hjcgjbxx_dywcrzgh ON t_rchx_hjcgjbxx(dywcrzgh);
CREATE INDEX idx_hjcgjbxx_hjrq ON t_rchx_hjcgjbxx(hjrq);
CREATE INDEX idx_hjcgjbxx_xkml ON t_rchx_hjcgjbxx(xkml);

-- 项目表索引  
CREATE INDEX idx_kjxmjbxx_xmfzrh ON t_rchx_kjxmjbxx(xmfzrh);
CREATE INDEX idx_kjxmjbxx_xmzxzt ON t_rchx_kjxmjbxx(xmzxzt);

-- 教职工表索引
CREATE INDEX idx_jzgjbxx_zcdj ON t_rchx_jzgjbxx(zcdj);
CREATE INDEX idx_jzgjbxx_zgxl ON t_rchx_jzgjbxx(zgxl);
CREATE INDEX idx_jzgjbxx_bm ON t_rchx_jzgjbxx(bm);
```

---

### 方案五：预计算 + 定时更新 ⭐⭐⭐⭐

**核心思路**: 定期计算教师能力画像，存储计算结果

#### 数据表设计
```sql
CREATE TABLE t_teacher_capability_cache (
    teacher_id VARCHAR(50) PRIMARY KEY,
    academic_score DECIMAL(5,2),
    field_score DECIMAL(5,2), 
    team_score DECIMAL(5,2),
    practical_score DECIMAL(5,2),
    capability_data TEXT,  -- JSON格式存储详细数据
    last_updated DATETIME,
    INDEX idx_academic_score (academic_score DESC),
    INDEX idx_last_updated (last_updated)
);
```

#### 定时任务
```java
@Component
public class TeacherCapabilityUpdateTask {
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void updateTeacherCapabilities() {
        List<RchxJzgjbxx> allTeachers = teacherService.list();
        
        for (RchxJzgjbxx teacher : allTeachers) {
            TeacherCapabilityDTO capability = calculateCapability(teacher);
            capabilityCacheService.updateCache(teacher.getZgh(), capability);
        }
    }
}
```

---

## 🎯 推荐实施方案

### 阶段一：立即优化 (1天)
1. **批量查询优化** - 立即见效，性能提升90%+
2. **数据库索引优化** - 确保查询效率

### 阶段二：中期优化 (3天)  
1. **Redis缓存** - 进一步提升响应速度
2. **分页处理** - 处理超大数据量场景

### 阶段三：长期优化 (1周)
1. **预计算系统** - 最佳性能体验
2. **异步处理** - 用户体验优化

## 📊 性能对比

| 方案 | 查询次数 | 响应时间 | 实现难度 | 维护成本 |
|------|----------|----------|----------|----------|
| **原始方案** | 16000+ | 30秒+ | 简单 | 低 |
| **批量查询** | 3次 | 2-3秒 | 简单 | 低 |
| **+ Redis缓存** | 0-3次 | 0.1-3秒 | 中等 | 中等 |
| **+ 预计算** | 1次 | 0.1秒 | 复杂 | 高 |

## 🔧 快速实施代码

### 1. 批量查询优化 (立即可用)

```java
// 在 TalentMatchingServiceImpl 中添加
private Map<String, List<RchxHjcgjbxx>> batchQueryAwards(List<String> teacherIds) {
    if (teacherIds.isEmpty()) return new HashMap<>();
    
    List<RchxHjcgjbxx> allAwards = hjcgjbxxMapper.selectList(
        new QueryWrapper<RchxHjcgjbxx>().in("dywcrzgh", teacherIds)
    );
    
    return allAwards.stream()
        .filter(award -> award.getDywcrzgh() != null)
        .collect(Collectors.groupingBy(RchxHjcgjbxx::getDywcrzgh));
}
```

### 2. 简单缓存 (可选)

```java
@Service
public class TalentMatchingService {
    
    private final Map<String, List<TalentMatchingResultDTO>> cache = new ConcurrentHashMap<>();
    
    public List<TalentMatchingResultDTO> analyzeTalentMatching(ProjectRequirementDTO requirement) {
        String cacheKey = generateCacheKey(requirement);
        
        // 检查缓存
        if (cache.containsKey(cacheKey)) {
            return cache.get(cacheKey);
        }
        
        // 执行分析
        List<TalentMatchingResultDTO> results = doAnalysis(requirement);
        
        // 缓存结果
        cache.put(cacheKey, results);
        
        return results;
    }
}
```

## ⚡ 立即可用的优化版本

我建议您先实施**批量查询优化**，这是最简单且效果最明显的优化方案。只需要修改几个方法，就能将性能提升90%以上！

需要我为您实现具体的批量查询优化代码吗？
