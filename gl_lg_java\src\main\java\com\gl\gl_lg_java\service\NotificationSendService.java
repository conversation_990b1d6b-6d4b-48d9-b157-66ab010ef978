package com.gl.gl_lg_java.service;

import com.gl.gl_lg_java.domain.RchxNotifications;
import com.gl.gl_lg_java.domain.RchxNotificationTypes;
import com.gl.gl_lg_java.mapper.NotificationMapper;
import com.gl.gl_lg_java.websocket.TeacherNotificationHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通知发送服务
 * 负责通过WebSocket发送通知消息
 */
@Service
@Slf4j
public class NotificationSendService {

    @Autowired
    private TeacherNotificationHandler teacherNotificationHandler;

    @Autowired
    private RchxNotificationTypesService rchxNotificationTypesService;

    @Autowired
    private NotificationMapper notificationMapper;

    @Autowired
    private NotificationRedisService notificationRedisService;

    /**
     * 异步发送通知给指定用户
     * 
     * @param zgh          职工号
     * @param notification 通知对象
     */
    @Async
    public void sendNotificationToUser(String zgh, RchxNotifications notification) {
        try {
            // 获取通知类型信息
            RchxNotificationTypes notificationType = rchxNotificationTypesService
                    .getNotificationTypeByCode(notification.getType());

            // 构建WebSocket消息
            Map<String, Object> message = buildNotificationMessage(notification, notificationType);

            // 发送WebSocket消息
            teacherNotificationHandler.sendNotificationToUser(zgh, message);

            log.info("发送通知消息成功: zgh={}, title={}, type={}",
                    zgh, notification.getTitle(), notification.getType());

        } catch (Exception e) {
            log.error("发送通知消息失败: zgh={}, notificationId={}, error={}",
                    zgh, notification.getId(), e.getMessage());
        }
    }

    /**
     * 批量发送通知给多个用户
     * 
     * @param zghList    职工号列表
     * @param title      通知标题
     * @param content    通知内容
     * @param type       通知类型
     * @param priority   优先级
     * @param senderZgh  发送者职工号
     * @param senderName 发送者姓名
     */
    @Async
    public void batchSendNotification(List<String> zghList, String title, String content,
            String type, Integer priority, String senderZgh, String senderName) {
        try {
            for (String zgh : zghList) {
                // 创建通知记录
                Long notificationId = createNotificationDirect(
                        zgh, title, content, type, priority, senderZgh, senderName);

                if (notificationId != null) {
                    log.debug("批量发送通知: zgh={}, title={}, notificationId={}", zgh, title, notificationId);
                }
            }

            log.info("批量发送通知完成: count={}, title={}, type={}", zghList.size(), title, type);

        } catch (Exception e) {
            log.error("批量发送通知失败: count={}, title={}, error={}", zghList.size(), title, e.getMessage());
        }
    }

    /**
     * 发送系统公告
     * 
     * @param title      公告标题
     * @param content    公告内容
     * @param priority   优先级
     * @param senderZgh  发送者职工号
     * @param senderName 发送者姓名
     */
    @Async
    public void sendSystemAnnouncement(String title, String content, Integer priority,
            String senderZgh, String senderName) {
        try {
            // 这里可以获取所有在线用户或者所有用户
            // 暂时先记录日志，具体实现可以根据需求调整
            log.info("发送系统公告: title={}, priority={}, sender={}", title, priority, senderName);

            // TODO: 实现获取所有用户列表的逻辑
            // List<String> allUsers = getAllUsers();
            // batchSendNotification(allUsers, title, content, "system_announcement",
            // priority, senderZgh, senderName);

        } catch (Exception e) {
            log.error("发送系统公告失败: title={}, error={}", title, e.getMessage());
        }
    }

    /**
     * 发送教师信息更新通知
     * 
     * @param zgh          教师职工号
     * @param teacherName  教师姓名
     * @param updateFields 更新字段
     * @param adminZgh     管理员职工号
     * @param adminName    管理员姓名
     */
    public void sendTeacherInfoUpdateNotification(String zgh, String teacherName, String updateFields,
            String adminZgh, String adminName) {
        try {
            String title = "您的个人信息已更新";
            String content = String.format("尊敬的%s老师，您好！\n\n您的个人信息已由管理员%s更新。\n更新内容：%s\n\n如有疑问，请联系系统管理员。",
                    teacherName, adminName, updateFields);

            createNotificationDirect(zgh, title, content, "teacher_info_updated",
                    2, adminZgh, adminName);

            log.info("发送教师信息更新通知: zgh={}, teacherName={}, admin={}", zgh, teacherName, adminName);

        } catch (Exception e) {
            log.error("发送教师信息更新通知失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }

    /**
     * 发送权限变更通知
     * 
     * @param zgh           教师职工号
     * @param teacherName   教师姓名
     * @param oldPermission 原权限
     * @param newPermission 新权限
     * @param adminZgh      管理员职工号
     * @param adminName     管理员姓名
     */
    public void sendPermissionChangeNotification(String zgh, String teacherName, String oldPermission,
            String newPermission, String adminZgh, String adminName) {
        try {
            String title = "您的系统权限已调整";
            String content = String.format("尊敬的%s老师，您好！\n\n您的系统权限已由管理员%s调整。\n原权限：%s\n新权限：%s\n\n请重新登录系统以获取最新权限。",
                    teacherName, adminName, oldPermission, newPermission);

            createNotificationDirect(zgh, title, content, "permission_changed",
                    3, adminZgh, adminName);

            log.info("发送权限变更通知: zgh={}, teacherName={}, oldPermission={}, newPermission={}",
                    zgh, teacherName, oldPermission, newPermission);

        } catch (Exception e) {
            log.error("发送权限变更通知失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }

    /**
     * 发送密码重置成功通知
     * 
     * @param zgh         教师职工号
     * @param teacherName 教师姓名
     */
    public void sendPasswordResetSuccessNotification(String zgh, String teacherName) {
        try {
            String title = "密码重置成功";
            String content = String.format("尊敬的%s老师，您好！\n\n您的账户密码已成功重置。\n重置时间：%s\n\n如非本人操作，请立即联系系统管理员。",
                    teacherName, java.time.LocalDateTime.now()
                            .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            createNotificationDirect(zgh, title, content, "password_reset_success",
                    2, "system", "系统");

            log.info("发送密码重置成功通知: zgh={}, teacherName={}", zgh, teacherName);

        } catch (Exception e) {
            log.error("发送密码重置成功通知失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }

    /**
     * 构建通知消息对象
     * 
     * @param notification     通知对象
     * @param notificationType 通知类型
     * @return 消息对象
     */
    private Map<String, Object> buildNotificationMessage(RchxNotifications notification,
            RchxNotificationTypes notificationType) {
        Map<String, Object> message = new HashMap<>();

        message.put("id", notification.getId());
        message.put("title", notification.getTitle());
        message.put("content", notification.getContent());
        message.put("type", notification.getType());
        message.put("priority", notification.getPriority());
        message.put("priorityText", notification.getPriorityText());
        message.put("priorityColor", notification.getPriorityColor());
        message.put("createTime", notification.getCreateTime());
        message.put("senderName", notification.getSenderName());

        if (notificationType != null) {
            message.put("typeName", notificationType.getTypeName());
            message.put("icon", notificationType.getIconOrDefault());
            message.put("color", notificationType.getColorOrDefault());
        }

        return message;
    }

    /**
     * 直接创建通知（避免循环依赖）
     * 
     * @param zgh        接收者职工号
     * @param title      标题
     * @param content    内容
     * @param type       类型
     * @param priority   优先级
     * @param senderZgh  发送者职工号
     * @param senderName 发送者姓名
     * @return 通知ID
     */
    private Long createNotificationDirect(String zgh, String title, String content, String type,
            Integer priority, String senderZgh, String senderName) {
        try {
            RchxNotifications notification = new RchxNotifications();
            notification.setZgh(zgh);
            notification.setTitle(title);
            notification.setContent(content);
            notification.setType(type);
            notification.setPriority(priority != null ? priority : 1);
            notification.setIsRead(0);
            notification.setSenderZgh(senderZgh);
            notification.setSenderName(senderName);
            notification.setCreateTime(java.time.LocalDateTime.now());

            // 直接使用Mapper插入
            int result = notificationMapper.insert(notification);

            if (result > 0) {
                // 更新Redis缓存
                notificationRedisService.incrementTeacherUnreadCount(zgh, 1);

                // 异步发送WebSocket通知
                sendNotificationToUser(zgh, notification);

                log.info("直接创建通知成功: zgh={}, title={}, type={}, id={}",
                        zgh, title, type, notification.getId());

                return notification.getId();
            }

            return null;
        } catch (Exception e) {
            log.error("直接创建通知失败: zgh={}, title={}, error={}", zgh, title, e.getMessage());
            return null;
        }
    }
}
