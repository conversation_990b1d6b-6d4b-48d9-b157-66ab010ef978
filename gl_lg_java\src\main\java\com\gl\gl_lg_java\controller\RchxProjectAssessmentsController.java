package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectAssessments;
import com.gl.gl_lg_java.service.RchxProjectAssessmentsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 项目考核管理Controller
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@RestController
@RequestMapping("/api/rchx/project-assessments")
@Api(tags = "项目考核管理")
@Slf4j
public class RchxProjectAssessmentsController {

    @Autowired
    private RchxProjectAssessmentsService assessmentsService;

    /**
     * 分页查询项目考核信息
     */
    @GetMapping("/page")
    @ApiOperation("分页查询项目考核信息")
    public Result<IPage<RchxProjectAssessments>> pageQuery(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("项目编号") @RequestParam(required = false) String projectCode,
            @ApiParam("项目名称") @RequestParam(required = false) String projectName,
            @ApiParam("考核类型") @RequestParam(required = false) String assessmentType,
            @ApiParam("考核状态") @RequestParam(required = false) String assessmentStatus,
            @ApiParam("项目负责人职工号") @RequestParam(required = false) String projectLeaderZgh,
            @ApiParam("考核人职工号") @RequestParam(required = false) String assessorZgh,
            @ApiParam("考核结果") @RequestParam(required = false) String assessmentResult) {
        try {
            Page<RchxProjectAssessments> page = new Page<>(current, size);
            IPage<RchxProjectAssessments> result = assessmentsService.pageQuery(
                    page, projectCode, projectName, assessmentType,
                    assessmentStatus, projectLeaderZgh, assessorZgh, assessmentResult);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目考核信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询考核详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询考核详情")
    public Result<RchxProjectAssessments> getById(@ApiParam("考核ID") @PathVariable Long id) {
        try {
            RchxProjectAssessments assessment = assessmentsService.getById(id);
            if (assessment == null) {
                return Result.error("考核记录不存在");
            }
            return Result.success(assessment);
        } catch (Exception e) {
            log.error("查询考核详情失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建考核任务
     */
    @PostMapping
    @ApiOperation("创建考核任务")
    public Result<String> create(@RequestBody RchxProjectAssessments assessment) {
        try {
            boolean success = assessmentsService.createAssessment(assessment);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建考核任务失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新考核任务
     */
    @PutMapping
    @ApiOperation("更新考核任务")
    public Result<String> update(@RequestBody RchxProjectAssessments assessment) {
        try {
            boolean success = assessmentsService.updateAssessment(assessment);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新考核任务失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除考核任务
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除考核任务")
    public Result<String> delete(@ApiParam("考核ID") @PathVariable Long id) {
        try {
            boolean success = assessmentsService.deleteAssessment(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除考核任务失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除考核任务
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除考核任务")
    public Result<String> batchDelete(@ApiParam("考核ID列表") @RequestBody List<Long> ids) {
        try {
            boolean success = assessmentsService.batchDeleteAssessments(ids);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除考核任务失败: ids={}", ids, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 开始考核
     */
    @PutMapping("/{id}/start")
    @ApiOperation("开始考核")
    public Result<String> startAssessment(@ApiParam("考核ID") @PathVariable Long id,
            @ApiParam("考核人职工号") @RequestParam String assessorZgh,
            @ApiParam("考核人姓名") @RequestParam String assessorName) {
        try {
            boolean success = assessmentsService.startAssessment(id, assessorZgh, assessorName);
            if (success) {
                return Result.success("开始考核成功");
            } else {
                return Result.error("开始考核失败");
            }
        } catch (Exception e) {
            log.error("开始考核失败: id={}", id, e);
            return Result.error("开始考核失败: " + e.getMessage());
        }
    }

    /**
     * 完成考核
     */
    @PutMapping("/{id}/complete")
    @ApiOperation("完成考核")
    public Result<String> completeAssessment(@ApiParam("考核ID") @PathVariable Long id,
            @ApiParam("考核得分") @RequestParam BigDecimal assessmentScore,
            @ApiParam("考核意见") @RequestParam(required = false) String assessmentComments,
            @ApiParam("改进建议") @RequestParam(required = false) String improvementSuggestions,
            @ApiParam("后续行动") @RequestParam(required = false) String followUpActions,
            @ApiParam("更新人") @RequestParam String updateBy) {
        try {
            boolean success = assessmentsService.completeAssessment(
                    id, assessmentScore, assessmentComments, improvementSuggestions, followUpActions, updateBy);
            if (success) {
                return Result.success("完成考核成功");
            } else {
                return Result.error("完成考核失败");
            }
        } catch (Exception e) {
            log.error("完成考核失败: id={}", id, e);
            return Result.error("完成考核失败: " + e.getMessage());
        }
    }

    /**
     * 取消考核
     */
    @PutMapping("/{id}/cancel")
    @ApiOperation("取消考核")
    public Result<String> cancelAssessment(@ApiParam("考核ID") @PathVariable Long id,
            @ApiParam("更新人") @RequestParam String updateBy) {
        try {
            boolean success = assessmentsService.cancelAssessment(id, updateBy);
            if (success) {
                return Result.success("取消考核成功");
            } else {
                return Result.error("取消考核失败");
            }
        } catch (Exception e) {
            log.error("取消考核失败: id={}", id, e);
            return Result.error("取消考核失败: " + e.getMessage());
        }
    }

    /**
     * 更新考核状态
     */
    @PutMapping("/{id}/status")
    @ApiOperation("更新考核状态")
    public Result<String> updateStatus(@ApiParam("考核ID") @PathVariable Long id,
            @ApiParam("考核状态") @RequestParam String assessmentStatus,
            @ApiParam("更新人") @RequestParam String updateBy) {
        try {
            boolean success = assessmentsService.updateAssessmentStatus(id, assessmentStatus, updateBy);
            if (success) {
                return Result.success("更新考核状态成功");
            } else {
                return Result.error("更新考核状态失败");
            }
        } catch (Exception e) {
            log.error("更新考核状态失败: id={}", id, e);
            return Result.error("更新考核状态失败: " + e.getMessage());
        }
    }

    /**
     * 发送考核提醒
     */
    @PostMapping("/{id}/reminder")
    @ApiOperation("发送考核提醒")
    public Result<String> sendReminder(@ApiParam("考核ID") @PathVariable Long id) {
        try {
            boolean success = assessmentsService.sendAssessmentReminder(id);
            if (success) {
                return Result.success("发送提醒成功");
            } else {
                return Result.error("发送提醒失败");
            }
        } catch (Exception e) {
            log.error("发送考核提醒失败: id={}", id, e);
            return Result.error("发送提醒失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目ID查询考核列表
     */
    @GetMapping("/by-project/{projectId}")
    @ApiOperation("根据项目ID查询考核列表")
    public Result<List<RchxProjectAssessments>> getByProjectId(@ApiParam("项目ID") @PathVariable Long projectId) {
        try {
            List<RchxProjectAssessments> assessments = assessmentsService.getByProjectId(projectId);
            return Result.success(assessments);
        } catch (Exception e) {
            log.error("根据项目ID查询考核列表失败: projectId={}", projectId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目编号查询考核列表
     */
    @GetMapping("/by-project-code/{projectCode}")
    @ApiOperation("根据项目编号查询考核列表")
    public Result<List<RchxProjectAssessments>> getByProjectCode(@ApiParam("项目编号") @PathVariable String projectCode) {
        try {
            List<RchxProjectAssessments> assessments = assessmentsService.getByProjectCode(projectCode);
            return Result.success(assessments);
        } catch (Exception e) {
            log.error("根据项目编号查询考核列表失败: projectCode={}", projectCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据考核类型查询考核列表
     */
    @GetMapping("/by-type/{assessmentType}")
    @ApiOperation("根据考核类型查询考核列表")
    public Result<List<RchxProjectAssessments>> getByAssessmentType(
            @ApiParam("考核类型") @PathVariable String assessmentType) {
        try {
            List<RchxProjectAssessments> assessments = assessmentsService.getByAssessmentType(assessmentType);
            return Result.success(assessments);
        } catch (Exception e) {
            log.error("根据考核类型查询考核列表失败: assessmentType={}", assessmentType, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据考核状态查询考核列表
     */
    @GetMapping("/by-status/{assessmentStatus}")
    @ApiOperation("根据考核状态查询考核列表")
    public Result<List<RchxProjectAssessments>> getByAssessmentStatus(
            @ApiParam("考核状态") @PathVariable String assessmentStatus) {
        try {
            List<RchxProjectAssessments> assessments = assessmentsService.getByAssessmentStatus(assessmentStatus);
            return Result.success(assessments);
        } catch (Exception e) {
            log.error("根据考核状态查询考核列表失败: assessmentStatus={}", assessmentStatus, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目负责人查询考核列表
     */
    @GetMapping("/by-leader/{projectLeaderZgh}")
    @ApiOperation("根据项目负责人查询考核列表")
    public Result<List<RchxProjectAssessments>> getByProjectLeader(
            @ApiParam("项目负责人职工号") @PathVariable String projectLeaderZgh) {
        try {
            List<RchxProjectAssessments> assessments = assessmentsService.getByProjectLeader(projectLeaderZgh);
            return Result.success(assessments);
        } catch (Exception e) {
            log.error("根据项目负责人查询考核列表失败: projectLeaderZgh={}", projectLeaderZgh, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取考核统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取考核统计信息")
    public Result<Map<String, Object>> getAssessmentStatistics() {
        try {
            Map<String, Object> statistics = assessmentsService.getAssessmentStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取考核统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取考核类型统计
     */
    @GetMapping("/statistics/type")
    @ApiOperation("获取考核类型统计")
    public Result<List<Map<String, Object>>> getAssessmentTypeStatistics() {
        try {
            List<Map<String, Object>> statistics = assessmentsService.getAssessmentTypeStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取考核类型统计失败", e);
            return Result.error("获取考核类型统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目负责人考核统计
     */
    @GetMapping("/statistics/leader")
    @ApiOperation("获取项目负责人考核统计")
    public Result<List<Map<String, Object>>> getProjectLeaderAssessmentStatistics() {
        try {
            List<Map<String, Object>> statistics = assessmentsService.getProjectLeaderAssessmentStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取项目负责人考核统计失败", e);
            return Result.error("获取项目负责人考核统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取即将到期的考核任务
     */
    @GetMapping("/near-deadline")
    @ApiOperation("获取即将到期的考核任务")
    public Result<List<RchxProjectAssessments>> getAssessmentsNearDeadline(
            @ApiParam("天数") @RequestParam(defaultValue = "7") Integer days) {
        try {
            List<RchxProjectAssessments> assessments = assessmentsService.getAssessmentsNearDeadline(days);
            return Result.success(assessments);
        } catch (Exception e) {
            log.error("获取即将到期的考核任务失败: days={}", days, e);
            return Result.error("获取即将到期的考核任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取逾期的考核任务
     */
    @GetMapping("/overdue")
    @ApiOperation("获取逾期的考核任务")
    public Result<List<RchxProjectAssessments>> getOverdueAssessments() {
        try {
            List<RchxProjectAssessments> assessments = assessmentsService.getOverdueAssessments();
            return Result.success(assessments);
        } catch (Exception e) {
            log.error("获取逾期的考核任务失败", e);
            return Result.error("获取逾期的考核任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据考核编号查询
     */
    @GetMapping("/by-code/{assessmentCode}")
    @ApiOperation("根据考核编号查询")
    public Result<RchxProjectAssessments> getByAssessmentCode(@ApiParam("考核编号") @PathVariable String assessmentCode) {
        try {
            RchxProjectAssessments assessment = assessmentsService.getByAssessmentCode(assessmentCode);
            if (assessment == null) {
                return Result.error("考核记录不存在");
            }
            return Result.success(assessment);
        } catch (Exception e) {
            log.error("根据考核编号查询失败: assessmentCode={}", assessmentCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 检查考核编号是否存在
     */
    @GetMapping("/exists/{assessmentCode}")
    @ApiOperation("检查考核编号是否存在")
    public Result<Boolean> existsByAssessmentCode(@ApiParam("考核编号") @PathVariable String assessmentCode) {
        try {
            boolean exists = assessmentsService.existsByAssessmentCode(assessmentCode);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查考核编号是否存在失败: assessmentCode={}", assessmentCode, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 生成考核编号
     */
    @GetMapping("/generate-code/{assessmentType}")
    @ApiOperation("生成考核编号")
    public Result<String> generateAssessmentCode(@ApiParam("考核类型") @PathVariable String assessmentType) {
        try {
            String assessmentCode = assessmentsService.generateAssessmentCode(assessmentType);
            return Result.success(assessmentCode);
        } catch (Exception e) {
            log.error("生成考核编号失败: assessmentType={}", assessmentType, e);
            return Result.error("生成考核编号失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目的最新考核信息
     */
    @GetMapping("/latest-by-project/{projectId}")
    @ApiOperation("获取项目的最新考核信息")
    public Result<RchxProjectAssessments> getLatestAssessmentByProjectId(
            @ApiParam("项目ID") @PathVariable Long projectId) {
        try {
            RchxProjectAssessments assessment = assessmentsService.getLatestAssessmentByProjectId(projectId);
            return Result.success(assessment);
        } catch (Exception e) {
            log.error("获取项目最新考核信息失败: projectId={}", projectId, e);
            return Result.error("获取项目最新考核信息失败: " + e.getMessage());
        }
    }

    /**
     * 导出考核列表
     */
    @GetMapping("/export")
    @ApiOperation("导出考核列表")
    public Result<List<RchxProjectAssessments>> exportAssessmentList(
            @ApiParam("考核类型") @RequestParam(required = false) String assessmentType,
            @ApiParam("考核状态") @RequestParam(required = false) String assessmentStatus,
            @ApiParam("项目负责人职工号") @RequestParam(required = false) String projectLeaderZgh) {
        try {
            List<RchxProjectAssessments> assessments = assessmentsService.exportAssessmentList(assessmentType,
                    assessmentStatus, projectLeaderZgh);
            return Result.success(assessments);
        } catch (Exception e) {
            log.error("导出考核列表失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }
}
