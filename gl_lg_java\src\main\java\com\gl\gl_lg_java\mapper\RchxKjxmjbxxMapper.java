package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gl.gl_lg_java.domain.RchxKjxmjbxx;
import com.gl.gl_lg_java.dto.KjxmStatisticsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_kjxmjbxx(科技项目基本信息)】的数据库操作Mapper
 * @createDate 2025-07-18 11:11:41
 * @Entity com.gl.gl_lg_java.domain.RchxKjxmjbxx
 */
@Mapper
public interface RchxKjxmjbxxMapper extends BaseMapper<RchxKjxmjbxx> {

        /**
         * 核心字段列表 - 用于列表查询优化
         */
        String CORE_FIELDS = "xmid, xmbh, xmmc, xmfzrh, fzrxm, dwmc, xmly, xmxz, xmlb, xmjb, xmzxzt, shzt, lxrq, ksrq";

        /**
         * 根据项目主键查询（详情查询，返回所有字段）
         */
        @Select("SELECT * FROM t_rchx_kjxmjbxx WHERE xmid = #{xmid}")
        RchxKjxmjbxx findByXmid(@Param("xmid") String xmid);

        /**
         * 根据项目编号查询（详情查询，返回所有字段）
         */
        @Select("SELECT * FROM t_rchx_kjxmjbxx WHERE xmbh = #{xmbh}")
        RchxKjxmjbxx findByXmbh(@Param("xmbh") String xmbh);

        /**
         * 根据项目名称模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE xmmc LIKE CONCAT('%', #{xmmc}, '%')")
        List<RchxKjxmjbxx> findByXmmcLike(@Param("xmmc") String xmmc);

        /**
         * 根据项目负责人号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE xmfzrh = #{xmfzrh}")
        List<RchxKjxmjbxx> findByXmfzrh(@Param("xmfzrh") String xmfzrh);

        /**
         * 根据负责人姓名模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE fzrxm LIKE CONCAT('%', #{fzrxm}, '%')")
        List<RchxKjxmjbxx> findByFzrxmLike(@Param("fzrxm") String fzrxm);

        /**
         * 根据单位号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE dwh = #{dwh}")
        List<RchxKjxmjbxx> findByDwh(@Param("dwh") String dwh);

        /**
         * 根据单位名称模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE dwmc LIKE CONCAT('%', #{dwmc}, '%')")
        List<RchxKjxmjbxx> findByDwmcLike(@Param("dwmc") String dwmc);

        /**
         * 根据项目来源查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE xmly = #{xmly}")
        List<RchxKjxmjbxx> findByXmly(@Param("xmly") String xmly);

        /**
         * 根据项目性质查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE xmxz = #{xmxz}")
        List<RchxKjxmjbxx> findByXmxz(@Param("xmxz") String xmxz);

        /**
         * 根据项目类别查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE xmlb = #{xmlb}")
        List<RchxKjxmjbxx> findByXmlb(@Param("xmlb") String xmlb);

        /**
         * 根据项目级别查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE xmjb = #{xmjb}")
        List<RchxKjxmjbxx> findByXmjb(@Param("xmjb") String xmjb);

        /**
         * 根据项目执行状态查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE xmzxzt = #{xmzxzt}")
        List<RchxKjxmjbxx> findByXmzxzt(@Param("xmzxzt") String xmzxzt);

        /**
         * 根据审核状态查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjxmjbxx WHERE shzt = #{shzt}")
        List<RchxKjxmjbxx> findByShzt(@Param("shzt") String shzt);

        /**
         * 根据立项日期范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_kjxmjbxx WHERE lxrq &gt;= #{startDate} AND lxrq &lt;= #{endDate}")
        List<RchxKjxmjbxx> findByLxrqBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

        /**
         * 根据开始日期范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_kjxmjbxx WHERE ksrq &gt;= #{startDate} AND ksrq &lt;= #{endDate}")
        List<RchxKjxmjbxx> findByKsrqBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

        /**
         * 按项目级别统计
         */
        @Select("SELECT COALESCE(xmjb, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_kjxmjbxx GROUP BY xmjb")
        List<Map<String, Object>> getStatsByXmjb();

        /**
         * 按执行状态统计
         */
        @Select("SELECT COALESCE(xmzxzt, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_kjxmjbxx GROUP BY xmzxzt")
        List<Map<String, Object>> getStatsByXmzxzt();

        /**
         * 按审核状态统计
         */
        @Select("SELECT COALESCE(shzt, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_kjxmjbxx GROUP BY shzt")
        List<Map<String, Object>> getStatsByShzt();

        /**
         * 快速获取所有项目级别选项（不统计数量）
         */
        @Select("SELECT DISTINCT xmjb FROM t_rchx_kjxmjbxx WHERE xmjb IS NOT NULL AND xmjb != '' ORDER BY xmjb")
        List<String> findDistinctXmjb();

        /**
         * 快速获取所有执行状态选项（不统计数量）
         */
        @Select("SELECT DISTINCT xmzxzt FROM t_rchx_kjxmjbxx WHERE xmzxzt IS NOT NULL AND xmzxzt != '' ORDER BY xmzxzt")
        List<String> findDistinctXmzxzt();

        /**
         * 快速获取所有审核状态选项（不统计数量）
         */
        @Select("SELECT DISTINCT shzt FROM t_rchx_kjxmjbxx WHERE shzt IS NOT NULL AND shzt != '' ORDER BY shzt")
        List<String> findDistinctShzt();

        /**
         * 统计教师个人科技项目数量（按项目负责人号）
         */
        @Select("SELECT COUNT(*) FROM t_rchx_kjxmjbxx WHERE xmfzrh = #{zgh}")
        Long getPersonalCountByZgh(@Param("zgh") String zgh);

        // 多条件查询已移至Service层使用QueryWrapper实现，更灵活且不易出错

        // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错

        /**
         * 统计单位项目数量和经费
         */
        @Select("SELECT dwmc, COUNT(*) as projectCount, " +
                        "COALESCE(SUM(CASE WHEN htjf REGEXP '^[0-9]+(\\\\.[0-9]+)?$' THEN CAST(htjf AS DECIMAL(15,2)) ELSE 0 END), 0) as totalFunding "
                        +
                        "FROM t_rchx_kjxmjbxx " +
                        "WHERE dwmc IS NOT NULL AND dwmc != '' " +
                        "GROUP BY dwmc " +
                        "ORDER BY projectCount DESC")
        List<KjxmStatisticsDTO.UnitStatistics> getUnitStatistics();

        /**
         * 统计项目类别数量和经费
         */
        @Select("SELECT xmlb, COUNT(*) as projectCount, " +
                        "COALESCE(SUM(CASE WHEN htjf REGEXP '^[0-9]+(\\\\.[0-9]+)?$' THEN CAST(htjf AS DECIMAL(15,2)) ELSE 0 END), 0) as totalFunding "
                        +
                        "FROM t_rchx_kjxmjbxx " +
                        "WHERE xmlb IS NOT NULL AND xmlb != '' " +
                        "GROUP BY xmlb " +
                        "ORDER BY projectCount DESC")
        List<KjxmStatisticsDTO.CategoryStatistics> getCategoryStatistics();

        /**
         * 统计负责人TOP10（按项目数量排序）
         */
        @Select("SELECT fzrxm, dwmc, COUNT(*) as projectCount, " +
                        "COALESCE(SUM(CASE WHEN htjf REGEXP '^[0-9]+(\\\\.[0-9]+)?$' THEN CAST(htjf AS DECIMAL(15,2)) ELSE 0 END), 0) as totalFunding "
                        +
                        "FROM t_rchx_kjxmjbxx " +
                        "WHERE fzrxm IS NOT NULL AND fzrxm != '' " +
                        "GROUP BY fzrxm, dwmc " +
                        "ORDER BY projectCount DESC, totalFunding DESC " +
                        "LIMIT 10")
        List<KjxmStatisticsDTO.LeaderStatistics> getTopLeaders();

        /**
         * 获取总体统计数据
         */
        @Select("SELECT " +
                        "COUNT(*) as totalProjects, " +
                        "COUNT(DISTINCT dwmc) as totalUnits, " +
                        "COUNT(DISTINCT xmlb) as totalCategories, " +
                        "COALESCE(SUM(CASE WHEN htjf REGEXP '^[0-9]+(\\\\.[0-9]+)?$' THEN CAST(htjf AS DECIMAL(15,2)) ELSE 0 END), 0) as totalFunding "
                        +
                        "FROM t_rchx_kjxmjbxx " +
                        "WHERE dwmc IS NOT NULL AND dwmc != ''")
        Map<String, Object> getOverallStatistics();
}
