package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.config.MinioProperties;
import com.gl.gl_lg_java.domain.RchxProjectMidlateFiles;
import com.gl.gl_lg_java.mapper.RchxProjectMidlateFilesMapper;
import com.gl.gl_lg_java.service.RchxProjectMidlateFilesService;
import io.minio.GetObjectArgs;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 项目中后期文件管理Service实现类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Slf4j
public class RchxProjectMidlateFilesServiceImpl
        extends ServiceImpl<RchxProjectMidlateFilesMapper, RchxProjectMidlateFiles>
        implements RchxProjectMidlateFilesService {

    @Autowired
    private RchxProjectMidlateFilesMapper midlateFilesMapper;

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioProperties minioProperties;

    @Override
    public IPage<RchxProjectMidlateFiles> pageQuery(Page<RchxProjectMidlateFiles> page,
            String businessType,
            Long businessId,
            String projectCode,
            String fileCategory,
            String uploadBy,
            Boolean isRequired,
            Boolean isConfidential) {
        try {
            return midlateFilesMapper.selectPageWithConditions(
                    page, businessType, businessId, projectCode,
                    fileCategory, uploadBy, isRequired, isConfidential);
        } catch (Exception e) {
            log.error("分页查询文件信息失败", e);
            throw new RuntimeException("分页查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> getByBusiness(String businessType, Long businessId) {
        try {
            return midlateFilesMapper.selectByBusiness(businessType, businessId);
        } catch (Exception e) {
            log.error("根据业务类型和业务ID查询文件列表失败: businessType={}, businessId={}",
                    businessType, businessId, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> getByProjectCode(String projectCode) {
        try {
            return midlateFilesMapper.selectByProjectCode(projectCode);
        } catch (Exception e) {
            log.error("根据项目编号查询文件列表失败: projectCode={}", projectCode, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> getByCategory(String businessType, Long businessId, String fileCategory) {
        try {
            return midlateFilesMapper.selectByCategory(businessType, businessId, fileCategory);
        } catch (Exception e) {
            log.error("根据文件类别查询文件列表失败: businessType={}, businessId={}, fileCategory={}",
                    businessType, businessId, fileCategory, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> getByUploader(String uploadBy) {
        try {
            return midlateFilesMapper.selectByUploader(uploadBy);
        } catch (Exception e) {
            log.error("根据上传人查询文件列表失败: uploadBy={}", uploadBy, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> getConfidentialFiles() {
        try {
            return midlateFilesMapper.selectConfidentialFiles();
        } catch (Exception e) {
            log.error("查询机密文件列表失败", e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> getRequiredFiles() {
        try {
            return midlateFilesMapper.selectRequiredFiles();
        } catch (Exception e) {
            log.error("查询必需文件列表失败", e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RchxProjectMidlateFiles uploadFile(MultipartFile file,
            String businessType,
            Long businessId,
            String projectCode,
            String fileCategory,
            String fileDescription,
            Boolean isRequired,
            Boolean isConfidential,
            String versionNumber,
            String uploadBy,
            String uploadByName) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                throw new RuntimeException("文件不能为空");
            }

            // 计算文件MD5
            String fileMd5 = calculateFileMd5(file);

            // 生成唯一文件名
            String originalFileName = file.getOriginalFilename();
            String fileName = generateUniqueFileName(originalFileName, businessType);
            String extension = originalFileName.substring(originalFileName.lastIndexOf("."));

            // 构建MinIO对象路径
            String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String objectName = String.format("midlate-files/%s/%s/%s", businessType.toLowerCase(), datePath, fileName);

            // 上传到MinIO，设置公共读取权限
            Map<String, String> headers = new HashMap<>();
            headers.put("x-amz-acl", "public-read");

            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(objectName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .headers(headers)
                    .build());

            // 创建文件记录
            RchxProjectMidlateFiles midlateFile = new RchxProjectMidlateFiles();
            midlateFile.setBusinessType(businessType);
            midlateFile.setBusinessId(businessId);
            midlateFile.setProjectCode(projectCode);
            midlateFile.setFileCategory(fileCategory);
            midlateFile.setFileName(fileName);
            midlateFile.setFileOriginalName(originalFileName);
            midlateFile.setFilePath(objectName);
            midlateFile.setFileSize(file.getSize());

            // 简化文件类型，避免超长MIME类型
            String fileType = simplifyFileType(file.getContentType(), extension);
            midlateFile.setFileType(fileType);

            midlateFile.setFileExtension(extension);
            midlateFile.setBucketName(minioProperties.getBucketName());
            midlateFile.setObjectName(objectName);
            midlateFile.setFileMd5(fileMd5);
            midlateFile.setFileDescription(fileDescription);
            midlateFile.setIsRequired(isRequired != null ? isRequired : false);
            midlateFile.setIsConfidential(isConfidential != null ? isConfidential : false);
            midlateFile.setVersionNumber(versionNumber != null ? versionNumber : "1.0");
            midlateFile.setUploadBy(uploadBy);
            midlateFile.setUploadByName(uploadByName);
            midlateFile.setDownloadCount(0);
            midlateFile.setIsDeleted(false);

            // 保存文件记录
            save(midlateFile);

            log.info("文件上传成功: fileName={}, objectName={}", fileName, objectName);
            return midlateFile;

        } catch (Exception e) {
            log.error("文件上传失败: fileName={}", file.getOriginalFilename(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] downloadFile(Long fileId) {
        try {
            RchxProjectMidlateFiles file = getById(fileId);
            if (file == null || file.getIsDeleted()) {
                throw new RuntimeException("文件不存在");
            }

            // 更新下载统计
            midlateFilesMapper.updateDownloadInfo(fileId, LocalDateTime.now());

            // 从MinIO下载文件
            try (InputStream inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(file.getBucketName())
                            .object(file.getObjectName())
                            .build())) {

                return inputStream.readAllBytes();
            }

        } catch (Exception e) {
            log.error("文件下载失败: fileId={}", fileId, e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    @Override
    public String getFileDownloadUrl(Long fileId) {
        try {
            RchxProjectMidlateFiles file = getById(fileId);
            if (file == null || file.getIsDeleted()) {
                throw new RuntimeException("文件不存在");
            }

            // 生成MinIO的预签名下载URL，有效期7天
            String presignedUrl = minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(file.getBucketName())
                            .object(file.getObjectName())
                            .expiry(7, TimeUnit.DAYS)
                            .build());

            return presignedUrl;

        } catch (Exception e) {
            log.error("获取文件下载URL失败: fileId={}", fileId, e);
            throw new RuntimeException("获取文件下载URL失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteFile(Long fileId, String deleteBy) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int result = midlateFilesMapper.logicalDeleteFile(fileId, now, deleteBy);

            if (result > 0) {
                log.info("删除文件成功: fileId={}", fileId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除文件失败: fileId={}", fileId, e);
            throw new RuntimeException("删除文件失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean batchDeleteFiles(List<Long> fileIds, String deleteBy) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int result = midlateFilesMapper.batchLogicalDeleteFiles(fileIds, now, deleteBy);

            if (result > 0) {
                log.info("批量删除文件成功: fileIds={}", fileIds);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("批量删除文件失败: fileIds={}", fileIds, e);
            throw new RuntimeException("批量删除文件失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getFileStatistics() {
        try {
            return midlateFilesMapper.getFileStatistics();
        } catch (Exception e) {
            log.error("获取文件统计信息失败", e);
            throw new RuntimeException("获取文件统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getFileCategoryStatistics() {
        try {
            return midlateFilesMapper.getFileCategoryStatistics();
        } catch (Exception e) {
            log.error("获取文件类别统计失败", e);
            throw new RuntimeException("获取文件类别统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getBusinessTypeFileStatistics() {
        try {
            return midlateFilesMapper.getBusinessTypeFileStatistics();
        } catch (Exception e) {
            log.error("获取业务类型文件统计失败", e);
            throw new RuntimeException("获取业务类型文件统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getProjectFileStatistics() {
        try {
            return midlateFilesMapper.getProjectFileStatistics();
        } catch (Exception e) {
            log.error("获取项目文件统计失败", e);
            throw new RuntimeException("获取项目文件统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getUploaderFileStatistics() {
        try {
            return midlateFilesMapper.getUploaderFileStatistics();
        } catch (Exception e) {
            log.error("获取上传人文件统计失败", e);
            throw new RuntimeException("获取上传人文件统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> getRecentUploadedFiles(Integer limit) {
        try {
            return midlateFilesMapper.getRecentUploadedFiles(limit);
        } catch (Exception e) {
            log.error("获取最近上传的文件失败: limit={}", limit, e);
            throw new RuntimeException("获取最近上传的文件失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> getPopularDownloadFiles(Integer limit) {
        try {
            return midlateFilesMapper.getPopularDownloadFiles(limit);
        } catch (Exception e) {
            log.error("获取热门下载文件失败: limit={}", limit, e);
            throw new RuntimeException("获取热门下载文件失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> checkDuplicateFiles(String fileMd5) {
        try {
            return midlateFilesMapper.selectByFileMd5(fileMd5);
        } catch (Exception e) {
            log.error("检查文件重复失败: fileMd5={}", fileMd5, e);
            throw new RuntimeException("检查文件重复失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateFileInfo(Long fileId, String fileDescription, Boolean isRequired,
            Boolean isConfidential, String versionNumber) {
        try {
            RchxProjectMidlateFiles file = new RchxProjectMidlateFiles();
            file.setId(fileId);
            file.setFileDescription(fileDescription);
            file.setIsRequired(isRequired);
            file.setIsConfidential(isConfidential);
            file.setVersionNumber(versionNumber);

            boolean result = updateById(file);
            if (result) {
                log.info("更新文件信息成功: fileId={}", fileId);
            }
            return result;
        } catch (Exception e) {
            log.error("更新文件信息失败: fileId={}", fileId, e);
            throw new RuntimeException("更新文件信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RchxProjectMidlateFiles replaceFile(Long fileId, MultipartFile newFile, String versionNumber,
            String uploadBy, String uploadByName) {
        try {
            // 获取原文件信息
            RchxProjectMidlateFiles originalFile = getById(fileId);
            if (originalFile == null || originalFile.getIsDeleted()) {
                throw new RuntimeException("原文件不存在");
            }

            // 上传新文件
            RchxProjectMidlateFiles newFileRecord = uploadFile(
                    newFile,
                    originalFile.getBusinessType(),
                    originalFile.getBusinessId(),
                    originalFile.getProjectCode(),
                    originalFile.getFileCategory(),
                    originalFile.getFileDescription(),
                    originalFile.getIsRequired(),
                    originalFile.getIsConfidential(),
                    versionNumber,
                    uploadBy,
                    uploadByName);

            // 逻辑删除原文件
            deleteFile(fileId, uploadBy);

            log.info("替换文件成功: originalFileId={}, newFileId={}", fileId, newFileRecord.getId());
            return newFileRecord;

        } catch (Exception e) {
            log.error("替换文件失败: fileId={}", fileId, e);
            throw new RuntimeException("替换文件失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectMidlateFiles> getFileVersionHistory(String businessType, Long businessId,
            String originalFileName) {
        try {
            QueryWrapper<RchxProjectMidlateFiles> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("business_type", businessType)
                    .eq("business_id", businessId)
                    .eq("file_original_name", originalFileName)
                    .orderByDesc("upload_time");

            return list(queryWrapper);
        } catch (Exception e) {
            log.error("获取文件版本历史失败: businessType={}, businessId={}, originalFileName={}",
                    businessType, businessId, originalFileName, e);
            throw new RuntimeException("获取文件版本历史失败: " + e.getMessage());
        }
    }

    @Override
    public boolean existsByFileName(String fileName, String businessType, Long businessId) {
        try {
            return midlateFilesMapper.countByFileName(fileName, businessType, businessId) > 0;
        } catch (Exception e) {
            log.error("检查文件名是否存在失败: fileName={}, businessType={}, businessId={}",
                    fileName, businessType, businessId, e);
            return false;
        }
    }

    @Override
    public String generateUniqueFileName(String originalFileName, String businessType) {
        try {
            String extension = "";
            String baseName = originalFileName;

            int dotIndex = originalFileName.lastIndexOf(".");
            if (dotIndex > 0) {
                extension = originalFileName.substring(dotIndex);
                baseName = originalFileName.substring(0, dotIndex);
            }

            // 生成UUID作为唯一标识
            String uuid = UUID.randomUUID().toString().replace("-", "");
            return String.format("%s_%s%s", baseName, uuid, extension);
        } catch (Exception e) {
            log.error("生成唯一文件名失败: originalFileName={}", originalFileName, e);
            throw new RuntimeException("生成唯一文件名失败: " + e.getMessage());
        }
    }

    @Override
    public String calculateFileMd5(MultipartFile file) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] digest = md5.digest(file.getBytes());

            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("计算文件MD5失败: fileName={}", file.getOriginalFilename(), e);
            throw new RuntimeException("计算文件MD5失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateFileType(MultipartFile file, List<String> allowedTypes) {
        try {
            if (allowedTypes == null || allowedTypes.isEmpty()) {
                return true; // 如果没有限制，则允许所有类型
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                return false;
            }

            String extension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
            return allowedTypes.contains(extension);
        } catch (Exception e) {
            log.error("验证文件类型失败: fileName={}", file.getOriginalFilename(), e);
            return false;
        }
    }

    @Override
    public boolean validateFileSize(MultipartFile file, Long maxSizeInMB) {
        try {
            if (maxSizeInMB == null || maxSizeInMB <= 0) {
                return true; // 如果没有限制，则允许任意大小
            }

            long maxSizeInBytes = maxSizeInMB * 1024 * 1024;
            return file.getSize() <= maxSizeInBytes;
        } catch (Exception e) {
            log.error("验证文件大小失败: fileName={}", file.getOriginalFilename(), e);
            return false;
        }
    }

    /**
     * 简化文件类型，避免超长MIME类型导致数据库字段长度超限
     */
    private String simplifyFileType(String contentType, String extension) {
        if (contentType == null || contentType.isEmpty()) {
            return getFileTypeByExtension(extension);
        }

        // 如果MIME类型太长，截断或简化
        if (contentType.length() > 50) {
            // 根据扩展名返回简化的类型
            return getFileTypeByExtension(extension);
        }

        return contentType;
    }

    /**
     * 根据文件扩展名获取简化的文件类型
     */
    private String getFileTypeByExtension(String extension) {
        if (extension == null) {
            return "application/octet-stream";
        }

        switch (extension.toLowerCase()) {
            case ".pdf":
                return "application/pdf";
            case ".doc":
            case ".docx":
                return "application/msword";
            case ".xls":
            case ".xlsx":
                return "application/excel";
            case ".ppt":
            case ".pptx":
                return "application/powerpoint";
            case ".txt":
                return "text/plain";
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".zip":
                return "application/zip";
            case ".rar":
                return "application/rar";
            default:
                return "application/octet-stream";
        }
    }
}
