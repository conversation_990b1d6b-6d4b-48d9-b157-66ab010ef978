# 快速完成所有CRUD的脚本

## 当前进度

### ✅ 已完成
1. **RchxJzgjbxx** - 教职工基本信息（完整CRUD）
2. **RchxZlcgjbxx** - 专利成果基本信息（完整CRUD）
3. **RchxKjxmjbxx** - 科技项目基本信息（完整CRUD）
4. **RchxHjcgjbxx** - 获奖成果基本信息（完整CRUD）

### 🔄 进行中
5. **RchxKjlwjbxx** - 科技论文基本信息（Mapper已完成）

### ❌ 待完成
6. **RchxJzggzjlxx** - 教职工工作简历信息
7. **RchxXmysxx** - 项目预算信息
8. **Permission** - 权限管理

## 快速生成方案

### 方案1：使用VS Code批量替换

#### 步骤1：复制获奖成果模块
1. 复制 `HjcgjbxxQueryDTO.java` → `KjlwjbxxQueryDTO.java`
2. 复制 `RchxHjcgjbxxService.java` → `RchxKjlwjbxxService.java`
3. 复制 `RchxHjcgjbxxServiceImpl.java` → `RchxKjlwjbxxServiceImpl.java`
4. 复制 `HjcgjbxxController.java` → `KjlwjbxxController.java`

#### 步骤2：批量替换关键字
在每个文件中使用VS Code的查找替换功能（Ctrl+H）：

```
查找：Hjcgjbxx
替换：Kjlwjbxx

查找：hjcgjbxx
替换：kjlwjbxx

查找：hjcgbh
替换：lwbh

查找：获奖成果
替换：科技论文

查找：/api/hjcgjbxx
替换：/api/kjlwjbxx

查找：HjcgjbxxQueryDTO
替换：KjlwjbxxQueryDTO

查找：第一完成人
替换：第一作者

查找：dywcrzgh
替换：dyzzbh
```

### 方案2：完整的代码模板

#### 科技论文Service接口
```java
package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxKjlwjbxx;
import com.gl.gl_lg_java.dto.KjlwjbxxQueryDTO;

import java.util.List;

public interface RchxKjlwjbxxService extends IService<RchxKjlwjbxx> {
    
    RchxKjlwjbxx getByLwbh(String lwbh);
    List<RchxKjlwjbxx> listByLwmcLike(String lwmc);
    List<RchxKjlwjbxx> listByDyzzbh(String dyzzbh);
    List<RchxKjlwjbxx> listByDyzzxmLike(String dyzzxm);
    List<RchxKjlwjbxx> listByTxzzbh(String txzzbh);
    List<RchxKjlwjbxx> listByKwmcLike(String kwmc);
    List<RchxKjlwjbxx> listByKwlx(String kwlx);
    List<RchxKjlwjbxx> listByFbfw(String fbfw);
    List<RchxKjlwjbxx> listByShzt(String shzt);
    List<RchxKjlwjbxx> listByFbrqBetween(String startDate, String endDate);
    List<RchxKjlwjbxx> listByMultiConditions(KjlwjbxxQueryDTO queryDTO);
    IPage<RchxKjlwjbxx> pageByMultiConditions(KjlwjbxxQueryDTO queryDTO);
    boolean saveKjlwjbxx(RchxKjlwjbxx kjlwjbxx);
    boolean updateByLwbh(RchxKjlwjbxx kjlwjbxx);
    boolean removeByLwbh(String lwbh);
    boolean removeBatchByLwbhs(List<String> lwbhs);
    boolean saveBatchKjlwjbxx(List<RchxKjlwjbxx> kjlwjbxxList);
    boolean updateBatchKjlwjbxx(List<RchxKjlwjbxx> kjlwjbxxList);
}
```

#### 权限控制规则
```java
// 在Controller中的权限控制
String currentUserZgh = (String) request.getAttribute("currentUserZgh");
String currentUserQx = (String) request.getAttribute("currentUserQx");

// 教师只能查看自己的论文
if ("教师".equals(currentUserQx) && result != null && 
    !currentUserZgh.equals(result.getDyzzbh()) && 
    !currentUserZgh.equals(result.getTxzzbh())) {
    return Result.error(403, "权限不足，只能查看自己的论文");
}
```

## 教职工工作简历信息

### 主键配置
```java
// 需要查看实体类确定主键字段
// 可能是复合主键或单独的ID字段
```

### 核心字段
- 职工号 (zgh)
- 工作单位 (gzdw)
- 工作内容 (gznr)
- 开始时间 (kssj)
- 结束时间 (jssj)

### API路径
- `/api/jzggzjlxx` - 教职工工作简历信息

### 权限控制
- 教师：只能查看和修改自己的工作简历
- 学院管理员：可以查看本学院教职工的工作简历
- 系统管理员：可以管理所有工作简历

## 项目预算信息

### 关联关系
- 与科技项目基本信息关联
- 通过项目ID关联

### 核心字段
- 项目ID (xmid)
- 预算科目 (yskm)
- 预算金额 (ysje)
- 实际支出 (sjzc)

### API路径
- `/api/xmysxx` - 项目预算信息

## 权限管理

### Permission实体类
- 权限ID (id)
- 权限名称 (name)
- 权限描述 (description)
- 权限级别 (level)

### API路径
- `/api/permission` - 权限管理

## 快速完成时间表

### 今天完成
1. **科技论文基本信息** - 30分钟
   - Service接口和实现类
   - Controller
   - 测试验证

### 明天完成
2. **教职工工作简历信息** - 30分钟
3. **项目预算信息** - 30分钟
4. **权限管理** - 30分钟

## 测试验证清单

### 基础功能测试
- [ ] 启动应用无错误
- [ ] 登录接口正常
- [ ] 各个查询接口正常
- [ ] 新增接口正常
- [ ] 更新接口正常
- [ ] 删除接口正常
- [ ] 批量操作接口正常

### 权限控制测试
- [ ] 教师权限控制正常
- [ ] 评审权限控制正常
- [ ] 学院管理员权限控制正常
- [ ] 系统管理员权限控制正常

### API文档
- [ ] 接口文档完整
- [ ] 参数说明清晰
- [ ] 响应示例正确

## 优化建议

1. **代码复用**：使用模板快速生成，减少重复工作
2. **统一规范**：保持命名和结构的一致性
3. **权限控制**：确保每个接口都有正确的权限控制
4. **异常处理**：统一的异常处理和错误信息
5. **日志记录**：完善的操作日志记录

这样你就可以在最短时间内完成所有实体类的CRUD接口了！
