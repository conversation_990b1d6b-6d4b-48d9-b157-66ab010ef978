# 校级项目在线申报系统 - Postman测试集合

## 环境变量设置

在Postman中创建环境变量：

```json
{
  "baseUrl": "http://localhost:8080",
  "categoryId": "1",
  "applicationId": "1",
  "fileId": "1",
  "uploadBy": "202401001",
  "uploadByName": "张三"
}
```

## 1. 项目类别管理测试

### 1.1 分页查询项目类别

```http
GET {{baseUrl}}/api/school-project/categories/page?current=1&size=10&categoryName=教学
```

### 1.2 查询启用的项目类别

```http
GET {{baseUrl}}/api/school-project/categories/enabled
```

### 1.3 创建项目类别

```http
POST {{baseUrl}}/api/school-project/categories
Content-Type: application/json

{
  "categoryCode": "CAT2025001",
  "categoryName": "教学改革项目",
  "fundingRangeMin": 1.00,
  "fundingRangeMax": 5.00,
  "applicationRequirements": "需提交教学改革方案、教学效果证明材料",
  "evaluationCriteria": "教学效果、创新性、推广价值",
  "projectDurationMonths": 12,
  "description": "支持教师开展教学改革创新研究",
  "sortOrder": 1,
  "isEnabled": true
}
```

### 1.4 更新项目类别

```http
PUT {{baseUrl}}/api/school-project/categories/{{categoryId}}
Content-Type: application/json

{
  "categoryCode": "CAT2025001",
  "categoryName": "教学改革项目（更新）",
  "fundingRangeMin": 2.00,
  "fundingRangeMax": 8.00,
  "applicationRequirements": "需提交教学改革方案、教学效果证明材料、推广计划",
  "evaluationCriteria": "教学效果、创新性、推广价值、可持续性",
  "projectDurationMonths": 18,
  "description": "支持教师开展教学改革创新研究，注重成果推广",
  "sortOrder": 1,
  "isEnabled": true
}
```

### 1.5 根据ID查询项目类别

```http
GET {{baseUrl}}/api/school-project/categories/{{categoryId}}
```

### 1.6 根据编码查询项目类别

```http
GET {{baseUrl}}/api/school-project/categories/code/CAT2025001
```

### 1.7 启用/禁用类别

```http
PUT {{baseUrl}}/api/school-project/categories/{{categoryId}}/status?isEnabled=false
```

### 1.8 调整类别排序

```http
PUT {{baseUrl}}/api/school-project/categories/{{categoryId}}/sort?sortOrder=5
```

### 1.9 获取类别统计信息

```http
GET {{baseUrl}}/api/school-project/categories/statistics
```

### 1.10 根据资助金额查询类别

```http
GET {{baseUrl}}/api/school-project/categories/funding-range?fundingAmount=5.0
```

### 1.11 生成类别编码

```http
GET {{baseUrl}}/api/school-project/categories/generate-code?categoryName=科研启动项目
```

### 1.12 导出类别列表

```http
GET {{baseUrl}}/api/school-project/categories/export?isEnabled=true
```

### 1.13 批量删除项目类别

```http
DELETE {{baseUrl}}/api/school-project/categories/batch
Content-Type: application/json

[2, 3, 4]
```

### 1.14 删除项目类别

```http
DELETE {{baseUrl}}/api/school-project/categories/{{categoryId}}
```

## 2. 文件管理测试

### 2.1 上传文件

```http
POST {{baseUrl}}/api/school-project/files/upload
Content-Type: multipart/form-data

# Form Data:
applicationId: {{applicationId}}
applicationCode: SCHOOL2024001
fileCategory: APPLICATION
fileDescription: 项目申报书
isRequired: true
uploadBy: {{uploadBy}}
uploadByName: {{uploadByName}}
file: [选择文件]
```

**Postman设置步骤：**
1. 选择POST方法
2. 在Body选项卡中选择form-data
3. 添加以上键值对
4. 对于file字段，选择File类型并上传文件

### 2.2 获取文件下载URL

```http
GET {{baseUrl}}/api/school-project/files/download/{{fileId}}
```

### 2.3 直接下载文件流

```http
GET {{baseUrl}}/api/school-project/files/stream/{{fileId}}
```

### 2.4 分页查询文件信息

```http
GET {{baseUrl}}/api/school-project/files/page?current=1&size=10&applicationId={{applicationId}}
```

### 2.5 根据申报ID查询文件列表

```http
GET {{baseUrl}}/api/school-project/files/application/{{applicationId}}
```

### 2.6 根据申报编号查询文件列表

```http
GET {{baseUrl}}/api/school-project/files/application-code/SCHOOL2024001
```

### 2.7 获取文件统计信息

```http
GET {{baseUrl}}/api/school-project/files/statistics
```

### 2.8 检查必需文件是否完整

```http
GET {{baseUrl}}/api/school-project/files/check-required/{{applicationId}}
```

### 2.9 删除文件

```http
DELETE {{baseUrl}}/api/school-project/files/{{fileId}}?deleteBy={{uploadBy}}
```

### 2.10 批量删除文件

```http
DELETE {{baseUrl}}/api/school-project/files/batch?deleteBy={{uploadBy}}
Content-Type: application/json

[1, 2, 3]
```

## 3. 测试场景

### 3.1 完整的项目类别管理流程

1. **创建类别** → 1.3
2. **查询类别** → 1.1, 1.5
3. **更新类别** → 1.4
4. **调整排序** → 1.8
5. **查看统计** → 1.9
6. **禁用类别** → 1.7
7. **删除类别** → 1.14

### 3.2 完整的文件管理流程

1. **上传文件** → 2.1
2. **查询文件** → 2.4, 2.5
3. **下载文件** → 2.2, 2.3
4. **检查完整性** → 2.8
5. **查看统计** → 2.7
6. **删除文件** → 2.9

### 3.3 错误场景测试

#### 3.3.1 参数验证测试

```http
# 创建类别时缺少必填字段
POST {{baseUrl}}/api/school-project/categories
Content-Type: application/json

{
  "categoryName": "测试类别"
  # 缺少categoryCode
}
```

#### 3.3.2 重复编码测试

```http
# 创建重复编码的类别
POST {{baseUrl}}/api/school-project/categories
Content-Type: application/json

{
  "categoryCode": "CAT2025001",  # 已存在的编码
  "categoryName": "重复编码测试"
}
```

#### 3.3.3 文件类型限制测试

```http
# 上传不支持的文件类型
POST {{baseUrl}}/api/school-project/files/upload
Content-Type: multipart/form-data

# 上传.exe或其他不支持的文件类型
```

#### 3.3.4 文件大小限制测试

```http
# 上传超过100MB的文件
POST {{baseUrl}}/api/school-project/files/upload
Content-Type: multipart/form-data

# 上传大于100MB的文件
```

#### 3.3.5 资源不存在测试

```http
# 查询不存在的类别
GET {{baseUrl}}/api/school-project/categories/99999

# 下载不存在的文件
GET {{baseUrl}}/api/school-project/files/download/99999
```

## 4. 测试数据准备

### 4.1 项目类别测试数据

```json
[
  {
    "categoryCode": "CAT2025001",
    "categoryName": "教学改革项目",
    "fundingRangeMin": 1.00,
    "fundingRangeMax": 5.00,
    "sortOrder": 1
  },
  {
    "categoryCode": "CAT2025002", 
    "categoryName": "科研启动项目",
    "fundingRangeMin": 2.00,
    "fundingRangeMax": 10.00,
    "sortOrder": 2
  },
  {
    "categoryCode": "CAT2025003",
    "categoryName": "学科建设项目", 
    "fundingRangeMin": 5.00,
    "fundingRangeMax": 20.00,
    "sortOrder": 3
  }
]
```

### 4.2 测试文件准备

准备以下类型的测试文件：
- **PDF文件**：项目申报书.pdf (< 10MB)
- **Word文档**：预算书.docx (< 5MB)
- **Excel表格**：成果统计.xlsx (< 3MB)
- **图片文件**：效果图.jpg (< 2MB)
- **压缩文件**：附件材料.zip (< 20MB)

## 5. 自动化测试脚本

### 5.1 Postman Pre-request Script

```javascript
// 设置时间戳
pm.environment.set("timestamp", Date.now());

// 生成随机编码
pm.environment.set("randomCode", "CAT" + Date.now());
```

### 5.2 Postman Test Script

```javascript
// 验证响应状态
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

// 验证响应格式
pm.test("Response has correct format", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('code');
    pm.expect(jsonData).to.have.property('message');
    pm.expect(jsonData).to.have.property('data');
});

// 验证成功响应
pm.test("Response is successful", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.code).to.eql(200);
});

// 保存响应数据到环境变量
if (pm.response.code === 200) {
    var jsonData = pm.response.json();
    if (jsonData.data && jsonData.data.id) {
        pm.environment.set("lastCreatedId", jsonData.data.id);
    }
}
```

## 6. 性能测试建议

1. **并发上传测试**：同时上传多个文件
2. **大文件上传测试**：上传接近100MB的文件
3. **批量操作测试**：批量删除大量记录
4. **分页查询测试**：查询大量数据的分页性能
5. **文件下载测试**：并发下载文件的性能

## 7. 注意事项

1. **环境准备**：确保MinIO服务正常运行
2. **数据库状态**：测试前确保数据库表结构正确
3. **文件清理**：测试后及时清理上传的测试文件
4. **权限设置**：根据实际需求配置API访问权限
5. **日志监控**：测试时关注服务器日志输出
