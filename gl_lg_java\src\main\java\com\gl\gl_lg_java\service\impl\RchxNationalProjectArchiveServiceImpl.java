package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxNationalProjectArchive;
import com.gl.gl_lg_java.mapper.RchxNationalProjectArchiveMapper;
import com.gl.gl_lg_java.service.RchxNationalProjectArchiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_national_project_archive(国家/省部级项目归档主表)】的数据库操作Service实现
 * @createDate 2025-01-04 10:00:00
 */
@Service
@Slf4j
public class RchxNationalProjectArchiveServiceImpl extends ServiceImpl<RchxNationalProjectArchiveMapper, RchxNationalProjectArchive>
        implements RchxNationalProjectArchiveService {

    @Autowired
    private RchxNationalProjectArchiveMapper archiveMapper;

    @Override
    public RchxNationalProjectArchive getByArchiveCode(String archiveCode) {
        return archiveMapper.findByArchiveCode(archiveCode);
    }

    @Override
    public RchxNationalProjectArchive getByProjectCode(String projectCode) {
        return archiveMapper.findByProjectCode(projectCode);
    }

    @Override
    public List<RchxNationalProjectArchive> listByProjectNameLike(String projectName) {
        return archiveMapper.findByProjectNameLike(projectName);
    }

    @Override
    public List<RchxNationalProjectArchive> listByProjectLeaderZgh(String leaderZgh) {
        return archiveMapper.findByProjectLeaderZgh(leaderZgh);
    }

    @Override
    public List<RchxNationalProjectArchive> listByProjectLeaderNameLike(String leaderName) {
        return archiveMapper.findByProjectLeaderNameLike(leaderName);
    }

    @Override
    public List<RchxNationalProjectArchive> listByArchiveStatus(String archiveStatus) {
        return archiveMapper.findByArchiveStatus(archiveStatus);
    }

    @Override
    public List<RchxNationalProjectArchive> listByLevelId(Integer levelId) {
        return archiveMapper.findByLevelId(levelId);
    }

    @Override
    public List<RchxNationalProjectArchive> listByDeptId(Integer deptId) {
        return archiveMapper.findByDeptId(deptId);
    }

    @Override
    public List<RchxNationalProjectArchive> listByProjectDateRange(LocalDate startDate, LocalDate endDate) {
        return archiveMapper.findByProjectDateRange(startDate, endDate);
    }

    @Override
    public List<RchxNationalProjectArchive> listByArchiveTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return archiveMapper.findByArchiveTimeRange(startTime, endTime);
    }

    @Override
    public List<RchxNationalProjectArchive> listPendingByDeadline(LocalDateTime deadline) {
        return archiveMapper.findPendingByDeadline(deadline);
    }

    @Override
    public List<Map<String, Object>> countByArchiveStatus() {
        return archiveMapper.countByArchiveStatus();
    }

    @Override
    public List<Map<String, Object>> countByLevel() {
        return archiveMapper.countByLevel();
    }

    @Override
    public List<Map<String, Object>> countByDepartment() {
        return archiveMapper.countByDepartment();
    }

    @Override
    public Map<String, Object> getArchiveOverview(Long id) {
        return archiveMapper.getArchiveOverview(id);
    }

    @Override
    @Transactional
    public boolean updateArchiveStatus(Long id, String archiveStatus, String operatorZgh, String operatorName) {
        try {
            UpdateWrapper<RchxNationalProjectArchive> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id)
                        .set("archive_status", archiveStatus)
                        .set("update_by", operatorZgh);
            
            // 如果状态是已归档，设置归档时间和归档人
            if ("ARCHIVED".equals(archiveStatus)) {
                updateWrapper.set("archive_time", LocalDateTime.now())
                           .set("archive_by", operatorZgh)
                           .set("archive_by_name", operatorName);
            }
            
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("更新归档状态失败: id={}, status={}", id, archiveStatus, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean batchUpdateArchiveStatus(List<Long> ids, String archiveStatus, String operatorZgh, String operatorName) {
        try {
            UpdateWrapper<RchxNationalProjectArchive> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", ids)
                        .set("archive_status", archiveStatus)
                        .set("update_by", operatorZgh);
            
            if ("ARCHIVED".equals(archiveStatus)) {
                updateWrapper.set("archive_time", LocalDateTime.now())
                           .set("archive_by", operatorZgh)
                           .set("archive_by_name", operatorName);
            }
            
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("批量更新归档状态失败: ids={}, status={}", ids, archiveStatus, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean submitArchive(Long id, String archiveBy, String archiveByName) {
        return updateArchiveStatus(id, "REVIEWING", archiveBy, archiveByName);
    }

    @Override
    @Transactional
    public boolean reviewArchive(Long id, String reviewerZgh, String reviewerName, String reviewComments, 
                               java.math.BigDecimal reviewScore, String archiveStatus) {
        try {
            UpdateWrapper<RchxNationalProjectArchive> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id)
                        .set("archive_status", archiveStatus)
                        .set("review_time", LocalDateTime.now())
                        .set("reviewer_zgh", reviewerZgh)
                        .set("reviewer_name", reviewerName)
                        .set("review_comments", reviewComments)
                        .set("review_score", reviewScore)
                        .set("update_by", reviewerZgh);
            
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("审核归档失败: id={}, reviewer={}", id, reviewerZgh, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean setArchiveDeadline(Long id, LocalDateTime deadline) {
        try {
            UpdateWrapper<RchxNationalProjectArchive> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id)
                        .set("archive_deadline", deadline);
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("设置归档截止时间失败: id={}, deadline={}", id, deadline, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean sendArchiveReminder(Long id) {
        try {
            UpdateWrapper<RchxNationalProjectArchive> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id)
                        .set("reminder_sent", true)
                        .set("last_reminder_time", LocalDateTime.now());
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("发送归档提醒失败: id={}", id, e);
            return false;
        }
    }

    @Override
    public String generateArchiveCode(String levelType) {
        String year = String.valueOf(LocalDateTime.now().getYear());
        String prefix;
        
        switch (levelType.toUpperCase()) {
            case "NATIONAL":
                prefix = "NAT";
                break;
            case "PROVINCIAL":
                prefix = "PROV";
                break;
            case "MUNICIPAL":
                prefix = "MUN";
                break;
            default:
                prefix = "ARCH";
        }
        
        // 查询当年最大编号
        QueryWrapper<RchxNationalProjectArchive> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("archive_code", prefix + year)
                   .orderByDesc("archive_code")
                   .last("LIMIT 1");
        
        RchxNationalProjectArchive lastArchive = getOne(queryWrapper);
        int sequence = 1;
        
        if (lastArchive != null && lastArchive.getArchiveCode() != null) {
            String lastCode = lastArchive.getArchiveCode();
            String sequenceStr = lastCode.substring(lastCode.length() - 3);
            sequence = Integer.parseInt(sequenceStr) + 1;
        }
        
        return prefix + year + String.format("%03d", sequence);
    }

    @Override
    public boolean isArchiveCodeUnique(String archiveCode, Long excludeId) {
        QueryWrapper<RchxNationalProjectArchive> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("archive_code", archiveCode);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) == 0;
    }

    @Override
    public boolean isProjectCodeUnique(String projectCode, Long excludeId) {
        QueryWrapper<RchxNationalProjectArchive> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_code", projectCode);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) == 0;
    }

    @Override
    public boolean hasArchivePermission(String userZgh, Long archiveId) {
        // 简单权限检查：项目负责人或创建人可以访问
        QueryWrapper<RchxNationalProjectArchive> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", archiveId)
                   .and(wrapper -> wrapper.eq("project_leader_zgh", userZgh)
                                         .or()
                                         .eq("create_by", userZgh));
        return count(queryWrapper) > 0;
    }

    @Override
    public Map<String, Object> getArchiveProgress() {
        // 获取归档进度统计
        List<Map<String, Object>> statusCounts = countByArchiveStatus();
        
        // 这里可以进一步处理统计数据，计算进度百分比等
        // 返回包含各种统计信息的Map
        return Map.of(
            "statusCounts", statusCounts,
            "totalCount", count(),
            "levelCounts", countByLevel(),
            "departmentCounts", countByDepartment()
        );
    }
}
