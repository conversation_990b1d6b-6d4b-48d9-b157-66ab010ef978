package com.gl.gl_lg_java.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Plus配置类
 * 主要用于配置分页插件
 */
@Configuration
@Slf4j
public class MybatisPlusConfig {

    /**
     * 配置MyBatis-Plus拦截器
     * 包含分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 添加分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor();
        
        // 设置数据库类型为MySQL
        paginationInterceptor.setDbType(DbType.MYSQL);
        
        // 设置最大单页限制数量，默认500条，-1不受限制
        paginationInterceptor.setMaxLimit(1000L);
        
        // 溢出总页数后是否进行处理
        // true: 溢出后回到首页
        // false: 溢出后继续请求（会导致查询结果为空）
        paginationInterceptor.setOverflow(false);
        
        // 生成 count 查询的优化开关
        // true: 开启优化，会自动优化count查询
        // false: 关闭优化
        paginationInterceptor.setOptimizeJoin(true);
        
        interceptor.addInnerInterceptor(paginationInterceptor);
        
        log.info("MyBatis-Plus分页插件配置完成");
        return interceptor;
    }
}
