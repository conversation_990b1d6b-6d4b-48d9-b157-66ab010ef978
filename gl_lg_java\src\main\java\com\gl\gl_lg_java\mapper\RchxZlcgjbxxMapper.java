package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gl.gl_lg_java.domain.RchxZlcgjbxx;
import com.gl.gl_lg_java.dto.ZlcgStatisticsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_zlcgjbxx(专利成果基本信息)】的数据库操作Mapper
 * @createDate 2025-07-18 11:11:41
 * @Entity com.gl.gl_lg_java.domain.RchxZlcgjbxx
 */
@Mapper
public interface RchxZlcgjbxxMapper extends BaseMapper<RchxZlcgjbxx> {

        /**
         * 核心字段列表 - 用于列表查询优化
         */
        String CORE_FIELDS = "zlcgbh, zlcgmc, dwmc, zllx, zlzt, dyfmr, dyfmrzgh, pzrq, shzt, czsj";

        /**
         * 根据专利成果编号查询（详情查询，返回所有字段）
         */
        @Select("SELECT * FROM t_rchx_zlcgjbxx WHERE zlcgbh = #{zlcgbh}")
        RchxZlcgjbxx findByZlcgbh(@Param("zlcgbh") String zlcgbh);

        /**
         * 根据专利成果名称模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_zlcgjbxx WHERE zlcgmc LIKE CONCAT('%', #{zlcgmc}, '%')")
        List<RchxZlcgjbxx> findByZlcgmcLike(@Param("zlcgmc") String zlcgmc);

        /**
         * 根据单位号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_zlcgjbxx WHERE dwh = #{dwh}")
        List<RchxZlcgjbxx> findByDwh(@Param("dwh") String dwh);

        /**
         * 根据单位名称模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_zlcgjbxx WHERE dwmc LIKE CONCAT('%', #{dwmc}, '%')")
        List<RchxZlcgjbxx> findByDwmcLike(@Param("dwmc") String dwmc);

        /**
         * 根据专利类型查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_zlcgjbxx WHERE zllx = #{zllx}")
        List<RchxZlcgjbxx> findByZllx(@Param("zllx") String zllx);

        /**
         * 根据专利状态查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_zlcgjbxx WHERE zlzt = #{zlzt}")
        List<RchxZlcgjbxx> findByZlzt(@Param("zlzt") String zlzt);

        /**
         * 根据审核状态查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_zlcgjbxx WHERE shzt = #{shzt}")
        List<RchxZlcgjbxx> findByShzt(@Param("shzt") String shzt);

        /**
         * 根据第一发明人职工号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_zlcgjbxx WHERE dyfmrzgh = #{dyfmrzgh}")
        List<RchxZlcgjbxx> findByDyfmrzgh(@Param("dyfmrzgh") String dyfmrzgh);

        /**
         * 根据第一发明人姓名模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_zlcgjbxx WHERE dyfmr LIKE CONCAT('%', #{dyfmr}, '%')")
        List<RchxZlcgjbxx> findByDyfmrLike(@Param("dyfmr") String dyfmr);

        /**
         * 根据批准日期范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_zlcgjbxx WHERE pzrq &gt;= #{startDate} AND pzrq &lt;= #{endDate}")
        List<RchxZlcgjbxx> findByPzrqBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

        /**
         * 根据申请日期范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_zlcgjbxx WHERE sqzlrq &gt;= #{startDate} AND sqzlrq &lt;= #{endDate}")
        List<RchxZlcgjbxx> findBySqzlrqBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

        /**
         * 按专利类型统计
         */
        @Select("SELECT COALESCE(zllx, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_zlcgjbxx GROUP BY zllx")
        List<Map<String, Object>> getStatsByZllx();

        /**
         * 按专利状态统计
         */
        @Select("SELECT COALESCE(zlzt, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_zlcgjbxx GROUP BY zlzt")
        List<Map<String, Object>> getStatsByZlzt();

        /**
         * 按审核状态统计
         */
        @Select("SELECT COALESCE(shzt, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_zlcgjbxx GROUP BY shzt")
        List<Map<String, Object>> getStatsByShzt();

        /**
         * 快速获取所有专利类型选项（不统计数量）
         */
        @Select("SELECT DISTINCT zllx FROM t_rchx_zlcgjbxx WHERE zllx IS NOT NULL AND zllx != '' ORDER BY zllx")
        List<String> findDistinctZllx();

        /**
         * 快速获取所有专利状态选项（不统计数量）
         */
        @Select("SELECT DISTINCT zlzt FROM t_rchx_zlcgjbxx WHERE zlzt IS NOT NULL AND zlzt != '' ORDER BY zlzt")
        List<String> findDistinctZlzt();

        /**
         * 快速获取所有审核状态选项（不统计数量）
         */
        @Select("SELECT DISTINCT shzt FROM t_rchx_zlcgjbxx WHERE shzt IS NOT NULL AND shzt != '' ORDER BY shzt")
        List<String> findDistinctShzt();

        /**
         * 统计教师个人专利成果数量（按第一发明人职工号）
         */
        @Select("SELECT COUNT(*) FROM t_rchx_zlcgjbxx WHERE dyfmrzgh = #{zgh}")
        Long getPersonalCountByZgh(@Param("zgh") String zgh);

        // 多条件查询已移至Service层使用QueryWrapper实现，更灵活且不易出错

        // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错

        /**
         * 统计单位专利数量
         */
        @Select("SELECT dwmc, COUNT(*) as patentCount " +
                        "FROM t_rchx_zlcgjbxx " +
                        "WHERE dwmc IS NOT NULL AND dwmc != '' " +
                        "GROUP BY dwmc " +
                        "ORDER BY patentCount DESC")
        List<ZlcgStatisticsDTO.UnitStatistics> getUnitStatistics();

        /**
         * 统计专利类型数量
         */
        @Select("SELECT zllx, COUNT(*) as patentCount " +
                        "FROM t_rchx_zlcgjbxx " +
                        "WHERE zllx IS NOT NULL AND zllx != '' " +
                        "GROUP BY zllx " +
                        "ORDER BY patentCount DESC")
        List<ZlcgStatisticsDTO.PatentTypeStatistics> getPatentTypeStatistics();

        /**
         * 统计第一发明人TOP10（按专利数量排序）
         */
        @Select("SELECT dyfmr, dwmc, COUNT(*) as patentCount " +
                        "FROM t_rchx_zlcgjbxx " +
                        "WHERE dyfmr IS NOT NULL AND dyfmr != '' " +
                        "GROUP BY dyfmr, dwmc " +
                        "ORDER BY patentCount DESC, dyfmr ASC " +
                        "LIMIT 10")
        List<ZlcgStatisticsDTO.FirstInventorStatistics> getTopFirstInventors();

        /**
         * 获取总体统计数据
         */
        @Select("SELECT " +
                        "COUNT(*) as totalPatents, " +
                        "COUNT(DISTINCT dwmc) as totalUnits, " +
                        "COUNT(DISTINCT zllx) as totalPatentTypes " +
                        "FROM t_rchx_zlcgjbxx " +
                        "WHERE dwmc IS NOT NULL AND dwmc != ''")
        Map<String, Object> getOverallStatistics();
}
