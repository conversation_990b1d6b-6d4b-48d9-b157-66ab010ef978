package com.gl.gl_lg_java.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis工具类
 * 用于教师通知系统的缓存操作
 */
@Component
@Slf4j
public class RedisUtil {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // Redis重试配置
    private static final int MAX_RETRY_TIMES = 3;
    private static final long RETRY_DELAY_MS = 1000;

    /**
     * 执行Redis操作，带重试机制
     */
    private <T> T executeWithRetry(Supplier<T> operation, String operationName, String key, T defaultValue) {
        Exception lastException = null;

        for (int i = 0; i < MAX_RETRY_TIMES; i++) {
            try {
                return operation.get();
            } catch (Exception e) {
                lastException = e;
                log.warn("Redis{}操作失败，第{}次重试: key={}, error={}", operationName, i + 1, key, e.getMessage());

                if (i < MAX_RETRY_TIMES - 1) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("Redis{}操作最终失败: key={}, error={}", operationName, key,
                lastException != null ? lastException.getMessage() : "未知错误");
        return defaultValue;
    }

    /**
     * 设置缓存
     * @param key 键
     * @param value 值
     * @param expire 过期时间(秒)
     */
    public void set(String key, Object value, long expire) {
        try {
            redisTemplate.opsForValue().set(key, value, expire, TimeUnit.SECONDS);
            log.debug("Redis设置缓存: key={}, expire={}秒", key, expire);
        } catch (Exception e) {
            log.error("Redis设置缓存失败: key={}, error={}", key, e.getMessage());
        }
    }

    /**
     * 设置缓存（永不过期）
     * @param key 键
     * @param value 值
     */
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            log.debug("Redis设置缓存: key={}", key);
        } catch (Exception e) {
            log.error("Redis设置缓存失败: key={}, error={}", key, e.getMessage());
        }
    }

    /**
     * 获取缓存
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            log.debug("Redis获取缓存: key={}, found={}", key, value != null);
            return value;
        } catch (Exception e) {
            log.error("Redis获取缓存失败: key={}, error={}", key, e.getMessage());
            return null;
        }
    }

    /**
     * 删除缓存
     * @param key 键
     * @return 是否删除成功
     */
    public boolean delete(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            log.debug("Redis删除缓存: key={}, result={}", key, result);
            return result != null && result;
        } catch (Exception e) {
            log.error("Redis删除缓存失败: key={}, error={}", key, e.getMessage());
            return false;
        }
    }

    /**
     * 判断key是否存在
     * @param key 键
     * @return 是否存在
     */
    public boolean hasKey(String key) {
        return executeWithRetry(() -> {
            Boolean result = redisTemplate.hasKey(key);
            return result != null && result;
        }, "检查key存在性", key, false);
    }

    /**
     * 设置过期时间
     * @param key 键
     * @param expire 过期时间(秒)
     * @return 是否设置成功
     */
    public boolean expire(String key, long expire) {
        try {
            Boolean result = redisTemplate.expire(key, expire, TimeUnit.SECONDS);
            log.debug("Redis设置过期时间: key={}, expire={}秒, result={}", key, expire, result);
            return result != null && result;
        } catch (Exception e) {
            log.error("Redis设置过期时间失败: key={}, error={}", key, e.getMessage());
            return false;
        }
    }

    /**
     * 获取过期时间
     * @param key 键
     * @return 过期时间(秒)，-1表示永不过期，-2表示key不存在
     */
    public long getExpire(String key) {
        try {
            Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            return expire != null ? expire : -2;
        } catch (Exception e) {
            log.error("Redis获取过期时间失败: key={}, error={}", key, e.getMessage());
            return -2;
        }
    }

    /**
     * 递增
     * @param key 键
     * @param delta 递增值
     * @return 递增后的值
     */
    public long increment(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().increment(key, delta);
            log.debug("Redis递增: key={}, delta={}, result={}", key, delta, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("Redis递增失败: key={}, error={}", key, e.getMessage());
            return 0;
        }
    }

    /**
     * 递减
     * @param key 键
     * @param delta 递减值
     * @return 递减后的值
     */
    public long decrement(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().decrement(key, delta);
            log.debug("Redis递减: key={}, delta={}, result={}", key, delta, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("Redis递减失败: key={}, error={}", key, e.getMessage());
            return 0;
        }
    }

    // ========== Hash操作 ==========

    /**
     * Hash设置
     * @param key 键
     * @param field 字段
     * @param value 值
     */
    public void hSet(String key, String field, Object value) {
        try {
            redisTemplate.opsForHash().put(key, field, value);
            log.debug("Redis Hash设置: key={}, field={}", key, field);
        } catch (Exception e) {
            log.error("Redis Hash设置失败: key={}, field={}, error={}", key, field, e.getMessage());
        }
    }

    /**
     * Hash获取
     * @param key 键
     * @param field 字段
     * @return 值
     */
    public Object hGet(String key, String field) {
        try {
            Object value = redisTemplate.opsForHash().get(key, field);
            log.debug("Redis Hash获取: key={}, field={}, found={}", key, field, value != null);
            return value;
        } catch (Exception e) {
            log.error("Redis Hash获取失败: key={}, field={}, error={}", key, field, e.getMessage());
            return null;
        }
    }

    /**
     * Hash删除
     * @param key 键
     * @param fields 字段
     * @return 删除的数量
     */
    public long hDelete(String key, Object... fields) {
        try {
            Long result = redisTemplate.opsForHash().delete(key, fields);
            log.debug("Redis Hash删除: key={}, fields={}, result={}", key, fields, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("Redis Hash删除失败: key={}, error={}", key, e.getMessage());
            return 0;
        }
    }

    /**
     * 测试Redis连接
     * @return 是否连接成功
     */
    public boolean testConnection() {
        try {
            String testKey = "test:connection:" + System.currentTimeMillis();
            set(testKey, "test", 10);
            Object value = get(testKey);
            delete(testKey);
            boolean success = "test".equals(value);
            log.info("Redis连接测试: {}", success ? "成功" : "失败");
            return success;
        } catch (Exception e) {
            log.error("Redis连接测试失败: {}", e.getMessage());
            return false;
        }
    }
}
