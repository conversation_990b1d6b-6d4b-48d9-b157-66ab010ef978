package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.config.MinioProperties;
import com.gl.gl_lg_java.domain.RchxSchoolProjectFiles;
import com.gl.gl_lg_java.mapper.RchxSchoolProjectFilesMapper;
import com.gl.gl_lg_java.service.RchxSchoolProjectFilesService;
import io.minio.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 校级项目申报文件Service实现类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Slf4j
public class RchxSchoolProjectFilesServiceImpl extends ServiceImpl<RchxSchoolProjectFilesMapper, RchxSchoolProjectFiles>
        implements RchxSchoolProjectFilesService {

    @Autowired
    private RchxSchoolProjectFilesMapper filesMapper;

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioProperties minioProperties;

    @Override
    @Transactional
    public RchxSchoolProjectFiles uploadFile(MultipartFile file, Long applicationId, String applicationCode,
            String fileCategory, String fileDescription, Boolean isRequired,
            String uploadBy, String uploadByName) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                throw new RuntimeException("文件不能为空");
            }

            // 验证文件类型和大小
            if (!isFileTypeAllowed(getFileExtension(file.getOriginalFilename()))) {
                throw new RuntimeException("不支持的文件类型");
            }

            if (!isFileSizeAllowed(file.getSize())) {
                throw new RuntimeException("文件大小超出限制");
            }

            // 确保存储桶存在
            String bucketName = minioProperties.getSchoolProjectBucketName();
            if (!bucketExists(bucketName)) {
                createBucket(bucketName);
            }

            // 生成对象名称
            String objectName = generateObjectName(applicationCode, fileCategory, file.getOriginalFilename());

            // 上传文件到MinIO
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(file.getInputStream(), file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build());

            // 创建文件记录
            RchxSchoolProjectFiles fileRecord = new RchxSchoolProjectFiles();
            fileRecord.setApplicationId(applicationId);
            fileRecord.setApplicationCode(applicationCode);
            fileRecord.setFileCategory(fileCategory);
            fileRecord.setFileName(generateFileName(file.getOriginalFilename()));
            fileRecord.setFileOriginalName(file.getOriginalFilename());
            fileRecord.setFilePath(objectName);
            fileRecord.setFileSize(file.getSize());
            fileRecord.setFileType(file.getContentType());
            fileRecord.setFileExtension(getFileExtension(file.getOriginalFilename()));
            fileRecord.setBucketName(bucketName);
            fileRecord.setObjectName(objectName);
            fileRecord.setFileMd5(calculateFileMd5(file));
            fileRecord.setFileDescription(fileDescription);
            fileRecord.setIsRequired(isRequired != null ? isRequired : false);
            fileRecord.setUploadBy(uploadBy);
            fileRecord.setUploadByName(uploadByName);
            fileRecord.setDownloadCount(0);
            fileRecord.setIsDeleted(false);

            // 保存到数据库
            save(fileRecord);

            log.info("文件上传成功: applicationCode={}, fileName={}, objectName={}",
                    applicationCode, file.getOriginalFilename(), objectName);

            return fileRecord;

        } catch (Exception e) {
            log.error("文件上传失败: applicationCode={}, fileName={}",
                    applicationCode, file.getOriginalFilename(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] downloadFile(Long fileId) {
        try {
            RchxSchoolProjectFiles file = getById(fileId);
            if (file == null || file.getIsDeleted()) {
                throw new RuntimeException("文件不存在");
            }

            // 更新下载统计
            filesMapper.updateDownloadInfo(fileId, LocalDateTime.now());

            // 从MinIO下载文件
            try (InputStream inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(file.getBucketName())
                            .object(file.getObjectName())
                            .build())) {

                return inputStream.readAllBytes();
            }

        } catch (Exception e) {
            log.error("文件下载失败: fileId={}", fileId, e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    @Override
    public String getFileDownloadUrl(Long fileId) {
        try {
            RchxSchoolProjectFiles file = getById(fileId);
            if (file == null || file.getIsDeleted()) {
                throw new RuntimeException("文件不存在");
            }

            // 生成MinIO的预签名下载URL，有效期7天
            String presignedUrl = minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(file.getBucketName())
                            .object(file.getObjectName())
                            .expiry(7, TimeUnit.DAYS)
                            .build());

            return presignedUrl;

        } catch (Exception e) {
            log.error("获取文件下载URL失败: fileId={}", fileId, e);
            throw new RuntimeException("获取文件下载URL失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<Map<String, Object>> pageQueryWithDetails(Page<RchxSchoolProjectFiles> page,
            Long applicationId,
            String applicationCode,
            String fileCategory,
            String uploadBy,
            Boolean isRequired) {
        try {
            return filesMapper.selectPageWithDetails(page, applicationId, applicationCode,
                    fileCategory, uploadBy, isRequired);
        } catch (Exception e) {
            log.error("分页查询文件信息失败", e);
            throw new RuntimeException("分页查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectFiles> getFilesByApplicationId(Long applicationId) {
        try {
            return filesMapper.selectByApplicationId(applicationId);
        } catch (Exception e) {
            log.error("根据申报ID查询文件列表失败: applicationId={}", applicationId, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectFiles> getFilesByApplicationCode(String applicationCode) {
        try {
            return filesMapper.selectByApplicationCode(applicationCode);
        } catch (Exception e) {
            log.error("根据申报编号查询文件列表失败: applicationCode={}", applicationCode, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectFiles> getFilesByCategory(Long applicationId, String fileCategory) {
        try {
            return filesMapper.selectByCategory(applicationId, fileCategory);
        } catch (Exception e) {
            log.error("根据文件类别查询文件列表失败: applicationId={}, fileCategory={}",
                    applicationId, fileCategory, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectFiles> getFilesByUploader(String uploadBy) {
        try {
            return filesMapper.selectByUploader(uploadBy);
        } catch (Exception e) {
            log.error("根据上传人查询文件列表失败: uploadBy={}", uploadBy, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectFiles> getRequiredFiles(Long applicationId) {
        try {
            return filesMapper.selectRequiredFiles(applicationId);
        } catch (Exception e) {
            log.error("查询必需文件列表失败: applicationId={}", applicationId, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectFiles> getFilesByMd5(String fileMd5) {
        try {
            return filesMapper.selectByFileMd5(fileMd5);
        } catch (Exception e) {
            log.error("根据文件MD5查询失败: fileMd5={}", fileMd5, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateDownloadCount(Long fileId) {
        try {
            int result = filesMapper.updateDownloadInfo(fileId, LocalDateTime.now());
            if (result > 0) {
                log.info("更新下载次数成功: fileId={}", fileId);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("更新下载次数失败: fileId={}", fileId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean logicalDeleteFile(Long fileId, String deleteBy) {
        try {
            int result = filesMapper.logicalDeleteFile(fileId, LocalDateTime.now(), deleteBy);
            if (result > 0) {
                log.info("逻辑删除文件成功: fileId={}", fileId);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("逻辑删除文件失败: fileId={}", fileId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean batchLogicalDeleteFiles(List<Long> fileIds, String deleteBy) {
        try {
            int result = filesMapper.batchLogicalDeleteFiles(fileIds, LocalDateTime.now(), deleteBy);
            if (result > 0) {
                log.info("批量逻辑删除文件成功: fileIds={}", fileIds);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量逻辑删除文件失败: fileIds={}", fileIds, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getFileStatistics() {
        try {
            return filesMapper.getFileStatistics();
        } catch (Exception e) {
            log.error("获取文件统计信息失败", e);
            throw new RuntimeException("获取文件统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getFileCategoryStatistics() {
        try {
            return filesMapper.getFileCategoryStatistics();
        } catch (Exception e) {
            log.error("获取文件类别统计失败", e);
            throw new RuntimeException("获取文件类别统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getApplicationFileStatistics() {
        try {
            return filesMapper.getApplicationFileStatistics();
        } catch (Exception e) {
            log.error("获取申报文件统计失败", e);
            throw new RuntimeException("获取申报文件统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getUploaderFileStatistics() {
        try {
            return filesMapper.getUploaderFileStatistics();
        } catch (Exception e) {
            log.error("获取上传人文件统计失败", e);
            throw new RuntimeException("获取上传人文件统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectFiles> getRecentUploadedFiles(Integer limit) {
        try {
            return filesMapper.getRecentUploadedFiles(limit != null ? limit : 10);
        } catch (Exception e) {
            log.error("获取最近上传的文件失败", e);
            throw new RuntimeException("获取最近上传的文件失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectFiles> getPopularDownloadFiles(Integer limit) {
        try {
            return filesMapper.getPopularDownloadFiles(limit != null ? limit : 10);
        } catch (Exception e) {
            log.error("获取热门下载文件失败", e);
            throw new RuntimeException("获取热门下载文件失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> checkRequiredFiles(Long applicationId) {
        try {
            return filesMapper.checkRequiredFiles(applicationId);
        } catch (Exception e) {
            log.error("检查必需文件失败: applicationId={}", applicationId, e);
            throw new RuntimeException("检查必需文件失败: " + e.getMessage());
        }
    }

    @Override
    public boolean existsByFileName(String fileName, Long applicationId) {
        try {
            return filesMapper.countByFileName(fileName, applicationId) > 0;
        } catch (Exception e) {
            log.error("检查文件名是否存在失败: fileName={}, applicationId={}", fileName, applicationId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean createFile(RchxSchoolProjectFiles file) {
        try {
            boolean result = save(file);
            if (result) {
                log.info("创建文件记录成功: fileName={}", file.getFileName());
            }
            return result;
        } catch (Exception e) {
            log.error("创建文件记录失败: fileName={}", file.getFileName(), e);
            throw new RuntimeException("创建文件记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateFile(RchxSchoolProjectFiles file) {
        try {
            boolean result = updateById(file);
            if (result) {
                log.info("更新文件记录成功: fileId={}", file.getId());
            }
            return result;
        } catch (Exception e) {
            log.error("更新文件记录失败: fileId={}", file.getId(), e);
            throw new RuntimeException("更新文件记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteFile(Long fileId) {
        try {
            boolean result = removeById(fileId);
            if (result) {
                log.info("删除文件记录成功: fileId={}", fileId);
            }
            return result;
        } catch (Exception e) {
            log.error("删除文件记录失败: fileId={}", fileId, e);
            throw new RuntimeException("删除文件记录失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectFiles> exportFileList(Long applicationId, String fileCategory) {
        try {
            QueryWrapper<RchxSchoolProjectFiles> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_deleted", false);

            if (applicationId != null) {
                queryWrapper.eq("application_id", applicationId);
            }

            if (fileCategory != null && !fileCategory.trim().isEmpty()) {
                queryWrapper.eq("file_category", fileCategory);
            }

            queryWrapper.orderByDesc("upload_time");

            return list(queryWrapper);
        } catch (Exception e) {
            log.error("导出文件列表失败", e);
            throw new RuntimeException("导出文件列表失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证文件类型是否允许
     */
    private boolean isFileTypeAllowed(String fileExtension) {
        String[] allowedTypes = minioProperties.getAllowedFileTypes();
        if (allowedTypes == null || allowedTypes.length == 0) {
            return true;
        }

        // 根据扩展名判断MIME类型
        String mimeType = getFileMimeType(fileExtension);
        return Arrays.asList(allowedTypes).contains(mimeType);
    }

    /**
     * 验证文件大小是否允许
     */
    private boolean isFileSizeAllowed(long fileSize) {
        Long maxSize = minioProperties.getMaxFileSize();
        if (maxSize == null || maxSize <= 0) {
            return true;
        }
        return fileSize <= maxSize;
    }

    /**
     * 检查存储桶是否存在
     */
    private boolean bucketExists(String bucketName) {
        try {
            return minioClient.bucketExists(
                    BucketExistsArgs.builder()
                            .bucket(bucketName)
                            .build());
        } catch (Exception e) {
            log.error("检查存储桶是否存在失败: {}", bucketName, e);
            return false;
        }
    }

    /**
     * 创建存储桶
     */
    private boolean createBucket(String bucketName) {
        try {
            if (!bucketExists(bucketName)) {
                minioClient.makeBucket(
                        MakeBucketArgs.builder()
                                .bucket(bucketName)
                                .build());
                log.info("创建存储桶成功: {}", bucketName);
            }
            return true;
        } catch (Exception e) {
            log.error("创建存储桶失败: {}", bucketName, e);
            return false;
        }
    }

    /**
     * 生成对象名称
     */
    private String generateObjectName(String applicationCode, String fileCategory, String originalFileName) {
        try {
            // 生成格式: applicationCode/fileCategory/yyyyMMdd/uuid_originalFileName
            String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
            String uuid = UUID.randomUUID().toString().replace("-", "");
            String extension = getFileExtension(originalFileName);

            String nameWithoutExt = originalFileName;
            if (originalFileName.contains(".")) {
                nameWithoutExt = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
            }

            return String.format("%s/%s/%s/%s_%s%s",
                    applicationCode,
                    fileCategory,
                    dateStr,
                    uuid,
                    nameWithoutExt,
                    extension);
        } catch (Exception e) {
            log.error("生成对象名称失败: applicationCode={}, fileCategory={}, originalFileName={}",
                    applicationCode, fileCategory, originalFileName, e);
            throw new RuntimeException("生成对象名称失败: " + e.getMessage());
        }
    }

    /**
     * 生成文件名（去除特殊字符）
     */
    private String generateFileName(String originalFileName) {
        if (!StringUtils.hasText(originalFileName)) {
            return "unknown_file";
        }

        // 移除特殊字符，保留中文、英文、数字、点号、下划线、连字符
        String cleanName = originalFileName.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9._-]", "_");

        // 如果文件名过长，截取前100个字符
        if (cleanName.length() > 100) {
            String extension = getFileExtension(cleanName);
            String nameWithoutExt = cleanName.substring(0, cleanName.lastIndexOf('.'));
            if (nameWithoutExt.length() > 95) {
                nameWithoutExt = nameWithoutExt.substring(0, 95);
            }
            cleanName = nameWithoutExt + extension;
        }

        return cleanName;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }

        return fileName.substring(lastDotIndex);
    }

    /**
     * 根据扩展名获取MIME类型
     */
    private String getFileMimeType(String extension) {
        if (!StringUtils.hasText(extension)) {
            return "application/octet-stream";
        }

        String ext = extension.toLowerCase();

        switch (ext) {
            case ".pdf":
                return "application/pdf";
            case ".doc":
                return "application/msword";
            case ".docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case ".xls":
                return "application/vnd.ms-excel";
            case ".xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case ".ppt":
                return "application/vnd.ms-powerpoint";
            case ".pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".txt":
                return "text/plain";
            case ".zip":
                return "application/zip";
            case ".rar":
                return "application/x-rar-compressed";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 计算文件MD5
     */
    private String calculateFileMd5(MultipartFile file) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] digest = md5.digest(file.getBytes());

            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("计算文件MD5失败: fileName={}", file.getOriginalFilename(), e);
            return null;
        }
    }
}
