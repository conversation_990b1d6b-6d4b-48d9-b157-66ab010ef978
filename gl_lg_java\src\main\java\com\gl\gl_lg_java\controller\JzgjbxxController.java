package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gl.gl_lg_java.annotation.RequirePermission;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.dto.JzgjbxxQueryDTO;
import com.gl.gl_lg_java.service.RchxJzgjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 教职工基本信息控制器
 */
@RestController
@RequestMapping("/api/jzgjbxx")
@Slf4j
public class JzgjbxxController {
    
    @Autowired
    private RchxJzgjbxxService jzgjbxxService;
    
    /**
     * 根据职工号查询
     */
    @GetMapping("/{zgh}")
    @RequirePermission("教师")
    public Result<RchxJzgjbxx> getByZgh(@PathVariable String zgh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的信息
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(zgh)) {
                return Result.error(403, "权限不足，只能查看自己的信息");
            }
            
            RchxJzgjbxx result = jzgjbxxService.getByZgh(zgh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询教职工信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询教职工信息
     */
    @PostMapping("/page")
    @RequirePermission("学院管理员")
    public Result<IPage<RchxJzgjbxx>> getPage(@RequestBody JzgjbxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");

            // 权限控制：教师只能查看自己的信息（但年龄段筛选除外）
            if ("教师".equals(currentUserQx)) {
                // 如果是年龄段筛选，允许查看所有符合条件的教师
                if (queryDTO.getAgeRange() == null || queryDTO.getAgeRange().trim().isEmpty()) {
                    // 非年龄段筛选，只能查看自己的信息
                    queryDTO.setZgh(currentUserZgh);
                }
                // 年龄段筛选时不限制职工号，允许查看所有符合年龄段条件的教师
            }

            IPage<RchxJzgjbxx> result = jzgjbxxService.pageByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询教职工信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 多条件查询教职工信息
     */
    @PostMapping("/list")
    @RequirePermission("学院管理员")
    public Result<List<RchxJzgjbxx>> getList(@RequestBody JzgjbxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            
            // 权限控制：教师只能查看自己的信息
            if ("教师".equals(currentUserQx)) {
                queryDTO.setZgh(currentUserZgh);
            }
            
            List<RchxJzgjbxx> result = jzgjbxxService.listByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询教职工信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据姓名模糊查询
     */
    @GetMapping("/name/{xm}")
    @RequirePermission("学院管理员")
    public Result<List<RchxJzgjbxx>> getByXmLike(@PathVariable String xm) {
        try {
            List<RchxJzgjbxx> result = jzgjbxxService.listByXmLike(xm);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据姓名查询教职工信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据部门查询
     */
    @GetMapping("/dept/{bm}")
    @RequirePermission("学院管理员")
    public Result<List<RchxJzgjbxx>> getByBm(@PathVariable String bm) {
        try {
            List<RchxJzgjbxx> result = jzgjbxxService.listByBm(bm);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据部门查询教职工信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据职称查询
     */
    @GetMapping("/title/{zc}")
    @RequirePermission("学院管理员")
    public Result<List<RchxJzgjbxx>> getByZc(@PathVariable String zc) {
        try {
            List<RchxJzgjbxx> result = jzgjbxxService.listByZc(zc);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据职称查询教职工信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据权限查询
     */
    @GetMapping("/permission/{qx}")
    @RequirePermission("系统管理员")
    public Result<List<RchxJzgjbxx>> getByQx(@PathVariable String qx) {
        try {
            List<RchxJzgjbxx> result = jzgjbxxService.listByQx(qx);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据权限查询教职工信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/department-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getDepartmentOptions() {
        try {
            List<Map<String, Object>> options = jzgjbxxService.getDepartmentOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取部门选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前状态选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/dqzt-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getDqztOptions() {
        try {
            List<Map<String, Object>> options = jzgjbxxService.getDqztOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取当前状态选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 按职称统计（包含具体教师信息）
     */
    @GetMapping("/stats/zc")
    @RequirePermission("教师")
    public Result<Map<String, Object>> getStatsByZc() {
        try {
            log.info("查询职称统计（包含具体教师信息）");
            Map<String, Object> stats = jzgjbxxService.getStatsByZc();
            log.info("职称统计结果: 统计数据={}", stats.get("statistics"));
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询职称统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取职称选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/zc-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getZcOptions() {
        try {
            List<Map<String, Object>> options = jzgjbxxService.getZcOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取职称选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 按职称等级统计
     */
    @GetMapping("/stats/zcdj")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByZcdj() {
        try {
            log.info("查询职称等级统计");
            Map<String, Integer> stats = jzgjbxxService.getStatsByZcdj();
            log.info("职称等级统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询职称等级统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按职称等级统计（包含具体教师信息）
     */
    @GetMapping("/stats/zcdj-with-teachers")
    @RequirePermission("教师")
    public Result<Map<String, Object>> getStatsByZcdjWithTeachers() {
        try {
            log.info("查询职称等级统计（包含具体教师信息）");
            Map<String, Object> stats = jzgjbxxService.getStatsByZcdjWithTeachers();
            log.info("职称等级统计结果: 统计数据={}", stats.get("statistics"));
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询职称等级统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按职称统计（仅返回统计数据）
     */
    @GetMapping("/stats/zc-count")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByZcCount() {
        try {
            log.info("查询职称统计（仅统计数据）");
            Map<String, Integer> stats = jzgjbxxService.getStatsByZcCount();
            log.info("职称统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询职称统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按年龄段统计
     */
    @GetMapping("/stats/age")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByAge() {
        try {
            log.info("查询年龄段统计");
            Map<String, Integer> stats = jzgjbxxService.getStatsByAge();
            log.info("年龄段统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询年龄段统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按最高学历层次统计
     */
    @GetMapping("/stats/zgxlcc")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByZgxlcc() {
        try {
            log.info("查询最高学历层次统计");
            Map<String, Integer> stats = jzgjbxxService.getStatsByZgxlcc();
            log.info("最高学历层次统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询最高学历层次统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取职称等级选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/zcdj-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getZcdjOptions() {
        try {
            List<Map<String, Object>> options = jzgjbxxService.getZcdjOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取职称等级选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取最高学历层次选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/zgxlcc-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getZgxlccOptions() {
        try {
            List<Map<String, Object>> options = jzgjbxxService.getZgxlccOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取最高学历层次选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 按年龄段分页查询教师信息
     * @param ageRange 年龄段：35岁以下、35-45岁、45岁以上，为空则查询所有
     * @param pageNum 页码，默认1
     * @param pageSize 每页大小，默认20
     */
    @GetMapping("/stats/age-range")
    @RequirePermission("教师")
    public Result<IPage<RchxJzgjbxx>> getPageByAgeRange(
            @RequestParam(value = "ageRange", required = false) String ageRange,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            log.info("按年龄段分页查询教师信息: ageRange={}, pageNum={}, pageSize={}", ageRange, pageNum, pageSize);
            IPage<RchxJzgjbxx> result = jzgjbxxService.getPageByAgeRange(ageRange, pageNum, pageSize);
            return Result.success(result);
        } catch (Exception e) {
            log.error("按年龄段分页查询教师信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }



    /**
     * 获取年龄段选项列表 - 用于前端筛选
     */
    @GetMapping("/age-range-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getAgeRangeOptions() {
        try {
            List<Map<String, Object>> options = jzgjbxxService.getAgeRangeOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取年龄段选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取教职工总数统计
     */
    @GetMapping("/total-count")
    @RequirePermission("教师")
    public Result<Map<String, Object>> getTotalCount() {
        try {
            log.info("获取教职工总数统计");
            Map<String, Object> stats = jzgjbxxService.getTotalCount();
            log.info("教职工总数统计获取成功，总数: {}", stats.get("totalCount"));
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取教职工总数统计失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 新增教职工信息
     */
    @PostMapping
    @RequirePermission("学院管理员")
    public Result<String> create(@RequestBody RchxJzgjbxx jzgjbxx) {
        try {
            boolean success = jzgjbxxService.saveJzgjbxx(jzgjbxx);
            if (success) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增教职工信息失败: {}", e.getMessage());
            return Result.error("新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新教职工信息
     */
    @PutMapping
    @RequirePermission("学院管理员")
    public Result<String> update(@RequestBody RchxJzgjbxx jzgjbxx, HttpServletRequest request) {
        try {
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            
            // 权限控制：教师只能修改自己的信息
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(jzgjbxx.getZgh())) {
                return Result.error(403, "权限不足，只能修改自己的信息");
            }
            
            boolean success = jzgjbxxService.updateByZgh(jzgjbxx);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新教职工信息失败: {}", e.getMessage());
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除教职工信息
     */
    @DeleteMapping("/{zgh}")
    @RequirePermission("系统管理员")
    public Result<String> delete(@PathVariable String zgh) {
        try {
            boolean success = jzgjbxxService.removeByZgh(zgh);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除教职工信息失败: {}", e.getMessage());
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除教职工信息
     */
    @DeleteMapping("/batch")
    @RequirePermission("系统管理员")
    public Result<String> deleteBatch(@RequestBody List<String> zghs) {
        try {
            boolean success = jzgjbxxService.removeBatchByZghs(zghs);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除教职工信息失败: {}", e.getMessage());
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量新增教职工信息
     */
    @PostMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> createBatch(@RequestBody List<RchxJzgjbxx> jzgjbxxList) {
        try {
            boolean success = jzgjbxxService.saveBatchJzgjbxx(jzgjbxxList);
            if (success) {
                return Result.success("批量新增成功");
            } else {
                return Result.error("批量新增失败");
            }
        } catch (Exception e) {
            log.error("批量新增教职工信息失败: {}", e.getMessage());
            return Result.error("批量新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新教职工信息
     */
    @PutMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> updateBatch(@RequestBody List<RchxJzgjbxx> jzgjbxxList) {
        try {
            boolean success = jzgjbxxService.updateBatchJzgjbxx(jzgjbxxList);
            if (success) {
                return Result.success("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新教职工信息失败: {}", e.getMessage());
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }
}
