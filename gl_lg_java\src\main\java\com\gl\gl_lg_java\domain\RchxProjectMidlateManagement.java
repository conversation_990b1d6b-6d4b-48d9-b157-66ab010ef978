package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目中后期管理实体类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_rchx_project_midlate_management")
public class RchxProjectMidlateManagement implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 管理ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目编号
     */
    @TableField("project_code")
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目大类ID
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 项目类别ID
     */
    @TableField("type_id")
    private Integer typeId;

    /**
     * 所属部门ID
     */
    @TableField("dept_id")
    private Integer deptId;

    /**
     * 项目负责人职工号
     */
    @TableField("project_leader_zgh")
    private String projectLeaderZgh;

    /**
     * 项目负责人姓名
     */
    @TableField("project_leader_name")
    private String projectLeaderName;

    /**
     * 项目负责人电话
     */
    @TableField("project_leader_phone")
    private String projectLeaderPhone;

    /**
     * 项目负责人邮箱
     */
    @TableField("project_leader_email")
    private String projectLeaderEmail;

    /**
     * 项目负责人职称
     */
    @TableField("project_leader_title")
    private String projectLeaderTitle;

    /**
     * 项目开始时间
     */
    @TableField("project_start_date")
    private LocalDate projectStartDate;

    /**
     * 项目结束时间
     */
    @TableField("project_end_date")
    private LocalDate projectEndDate;

    /**
     * 项目周期(月)
     */
    @TableField("project_duration_months")
    private Integer projectDurationMonths;

    /**
     * 项目预算(万元)
     */
    @TableField("project_budget")
    private BigDecimal projectBudget;

    /**
     * 项目描述
     */
    @TableField("project_description")
    private String projectDescription;

    /**
     * 团队成员信息
     */
    @TableField("team_members")
    private String teamMembers;

    /**
     * 项目状态(ONGOING进行中,MIDTERM_REVIEW中期检查,FINAL_REVIEW结项评审,COMPLETED已完成,SUSPENDED暂停,TERMINATED终止)
     */
    @TableField("project_status")
    private String projectStatus;

    /**
     * 项目进度百分比
     */
    @TableField("progress_percentage")
    private BigDecimal progressPercentage;

    /**
     * 进度说明
     */
    @TableField("progress_description")
    private String progressDescription;

    /**
     * 当前阶段
     */
    @TableField("current_phase")
    private String currentPhase;

    /**
     * 下一个里程碑
     */
    @TableField("next_milestone")
    private String nextMilestone;

    /**
     * 里程碑日期
     */
    @TableField("milestone_date")
    private LocalDate milestoneDate;

    /**
     * 风险等级(LOW低,MEDIUM中,HIGH高,CRITICAL严重)
     */
    @TableField("risk_level")
    private String riskLevel;

    /**
     * 风险描述
     */
    @TableField("risk_description")
    private String riskDescription;

    /**
     * 管理备注
     */
    @TableField("management_notes")
    private String managementNotes;

    /**
     * 最后更新时间
     */
    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    /**
     * 最后更新人职工号
     */
    @TableField("last_update_by")
    private String lastUpdateBy;

    /**
     * 来源申报ID
     */
    @TableField("source_application_id")
    private Long sourceApplicationId;

    /**
     * 来源中检ID
     */
    @TableField("source_inspection_id")
    private Long sourceInspectionId;

    /**
     * 来源结项ID
     */
    @TableField("source_completion_id")
    private Long sourceCompletionId;

    /**
     * 是否激活(0否,1是)
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人职工号
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人职工号
     */
    @TableField("update_by")
    private String updateBy;
}
