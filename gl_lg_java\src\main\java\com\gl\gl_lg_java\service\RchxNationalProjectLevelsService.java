package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxNationalProjectLevels;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_national_project_levels(国家/省部级项目级别表)】的数据库操作Service
 * @createDate 2025-01-04 10:00:00
 */
public interface RchxNationalProjectLevelsService extends IService<RchxNationalProjectLevels> {

    /**
     * 根据级别编码查询
     */
    RchxNationalProjectLevels getByLevelCode(String levelCode);

    /**
     * 根据级别类型查询
     */
    List<RchxNationalProjectLevels> listByLevelType(String levelType);

    /**
     * 根据级别名称模糊查询
     */
    List<RchxNationalProjectLevels> listByLevelNameLike(String levelName);

    /**
     * 查询启用的级别
     */
    List<RchxNationalProjectLevels> listEnabledLevels();

    /**
     * 根据级别权重范围查询
     */
    List<RchxNationalProjectLevels> listByLevelWeightBetween(Integer minWeight, Integer maxWeight);

    /**
     * 根据资助金额查询匹配的级别
     */
    List<RchxNationalProjectLevels> listByFundingAmount(BigDecimal amount);

    /**
     * 统计各级别类型的数量
     */
    List<Map<String, Object>> countByLevelType();

    /**
     * 获取最大权重值
     */
    Integer getMaxLevelWeight();

    /**
     * 获取最大排序号
     */
    Integer getMaxSortOrder();

    /**
     * 启用/禁用级别
     */
    boolean updateEnabledStatus(Integer id, Boolean enabled);

    /**
     * 批量启用/禁用级别
     */
    boolean batchUpdateEnabledStatus(List<Integer> ids, Boolean enabled);

    /**
     * 调整级别排序
     */
    boolean updateSortOrder(Integer id, Integer sortOrder);

    /**
     * 验证级别编码是否唯一
     */
    boolean isLevelCodeUnique(String levelCode, Integer excludeId);

    /**
     * 根据级别类型获取推荐的权重值
     */
    Integer getRecommendedWeight(String levelType);
}
