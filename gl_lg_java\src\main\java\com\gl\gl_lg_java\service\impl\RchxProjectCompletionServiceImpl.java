package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxProjectCompletion;
import com.gl.gl_lg_java.mapper.RchxProjectCompletionMapper;
import com.gl.gl_lg_java.service.RchxProjectCompletionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目结项表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@Service
public class RchxProjectCompletionServiceImpl extends ServiceImpl<RchxProjectCompletionMapper, RchxProjectCompletion>
        implements RchxProjectCompletionService {

    @Override
    public IPage<RchxProjectCompletion> pageQuery(Page<RchxProjectCompletion> page,
            Integer categoryId,
            Integer typeId,
            Integer deptId,
            String status,
            String projectCode,
            String projectLeaderZgh) {
        QueryWrapper<RchxProjectCompletion> queryWrapper = new QueryWrapper<>();

        if (categoryId != null) {
            queryWrapper.eq("category_id", categoryId);
        }
        if (typeId != null) {
            queryWrapper.eq("type_id", typeId);
        }
        if (deptId != null) {
            queryWrapper.eq("dept_id", deptId);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        if (StringUtils.hasText(projectCode)) {
            queryWrapper.like("project_code", projectCode);
        }
        if (StringUtils.hasText(projectLeaderZgh)) {
            queryWrapper.eq("project_leader_zgh", projectLeaderZgh);
        }

        queryWrapper.orderByDesc("create_time");
        return page(page, queryWrapper);
    }

    @Override
    public List<RchxProjectCompletion> getByProjectCode(String projectCode) {
        QueryWrapper<RchxProjectCompletion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_code", projectCode)
                .orderByDesc("create_time");
        return list(queryWrapper);
    }

    @Override
    public List<RchxProjectCompletion> getByProjectLeader(String projectLeaderZgh) {
        QueryWrapper<RchxProjectCompletion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_leader_zgh", projectLeaderZgh)
                .orderByDesc("create_time");
        return list(queryWrapper);
    }

    @Override
    public boolean submitCompletion(Long id, String submitBy) {
        try {
            RchxProjectCompletion completion = new RchxProjectCompletion();
            completion.setId(id);
            completion.setStatus("SUBMITTED");
            completion.setSubmitTime(LocalDateTime.now());
            completion.setUpdateBy(submitBy);
            return updateById(completion);
        } catch (Exception e) {
            log.error("提交结项失败: id={}, submitBy={}", id, submitBy, e);
            return false;
        }
    }

    @Override
    public boolean reviewCompletion(Long id, boolean approved, String reviewerZgh, String reviewComments,
            BigDecimal completionScore) {
        try {
            RchxProjectCompletion completion = new RchxProjectCompletion();
            completion.setId(id);
            completion.setStatus(approved ? "APPROVED" : "REJECTED");
            completion.setReviewTime(LocalDateTime.now());
            completion.setReviewerZgh(reviewerZgh);
            completion.setReviewComments(reviewComments);
            completion.setCompletionScore(completionScore);
            completion.setUpdateBy(reviewerZgh);
            return updateById(completion);
        } catch (Exception e) {
            log.error("审核结项失败: id={}, approved={}, reviewerZgh={}", id, approved, reviewerZgh, e);
            return false;
        }
    }

    @Override
    public Object getCompletionStatistics(Integer deptId, Integer categoryId) {
        QueryWrapper<RchxProjectCompletion> queryWrapper = new QueryWrapper<>();

        if (deptId != null) {
            queryWrapper.eq("dept_id", deptId);
        }
        if (categoryId != null) {
            queryWrapper.eq("category_id", categoryId);
        }

        // 总数统计
        long totalCount = count(queryWrapper);

        // 简化的状态统计
        Map<String, Long> statusCount = new HashMap<>();
        statusCount.put("DRAFT", 0L);
        statusCount.put("SUBMITTED", 0L);
        statusCount.put("REVIEWING", 0L);
        statusCount.put("APPROVED", 0L);
        statusCount.put("REJECTED", 0L);

        // 获取所有记录并统计
        List<RchxProjectCompletion> allRecords = this.list(queryWrapper);
        for (RchxProjectCompletion record : allRecords) {
            String status = record.getStatus();
            if (status != null && statusCount.containsKey(status)) {
                statusCount.put(status, statusCount.get(status) + 1);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", totalCount);
        result.put("statusCount", statusCount);

        return result;
    }
}
