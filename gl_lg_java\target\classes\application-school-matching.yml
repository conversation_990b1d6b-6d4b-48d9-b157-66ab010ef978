# 校级项目人才匹配系统配置
school-project-matching:
  # 分析结果缓存时间（小时）
  cache-duration: 24
  
  # 最大并发分析数
  max-concurrent-analysis: 5
  
  # 默认分析参数
  default-params:
    time-range: 5
    min-match-score: 60
    max-results: 100
    include-details: true
  
  # 项目配置
  projects:
    CXTD:
      name: "创新团队支持计划项目"
      category: "团队类"
      enabled: true
      min-score-threshold: 70
    
    FWGL:
      name: "服务管理人才支持计划项目"
      category: "管理类"
      enabled: true
      min-score-threshold: 65
    
    LJRC:
      name: "领军人才引育计划项目"
      category: "领军类"
      enabled: true
      min-score-threshold: 75
    
    PFXZ1:
      name: "屏风学者引育计划项目-Ⅰ类"
      category: "屏风学者"
      enabled: true
      min-score-threshold: 70
    
    PFXZ2:
      name: "屏风学者引育计划项目-Ⅱ类"
      category: "屏风学者"
      enabled: true
      min-score-threshold: 65
    
    PFXZ3:
      name: "屏风学者引育计划项目-Ⅲ类"
      category: "屏风学者"
      enabled: true
      min-score-threshold: 60
    
    QNRC:
      name: "青年人才未来工程项目"
      category: "青年类"
      enabled: true
      min-score-threshold: 65
    
    SHFW:
      name: "社会服务人才培育计划项目"
      category: "服务类"
      enabled: true
      min-score-threshold: 60

# 日志配置
logging:
  level:
    com.gl.gl_lg_java.matcher: DEBUG
    com.gl.gl_lg_java.service.impl.SchoolProjectMatchingServiceImpl: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
