package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectTypes;
import com.gl.gl_lg_java.service.RchxProjectTypesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目类别表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@RestController
@RequestMapping("/api/project-types")
public class RchxProjectTypesController {

    @Autowired
    private RchxProjectTypesService projectTypesService;

    /**
     * 分页查询项目类别
     */
    @GetMapping("/page")
    public Result<IPage<RchxProjectTypes>> page(@RequestParam(defaultValue = "1") Integer current,
                                               @RequestParam(defaultValue = "10") Integer size,
                                               @RequestParam(required = false) Integer categoryId,
                                               @RequestParam(required = false) String typeName,
                                               @RequestParam(required = false) Boolean isEnabled) {
        try {
            Page<RchxProjectTypes> page = new Page<>(current, size);
            IPage<RchxProjectTypes> result = projectTypesService.page(page);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目类别失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的项目类别
     */
    @GetMapping("/enabled")
    public Result<List<RchxProjectTypes>> getEnabledTypes() {
        try {
            List<RchxProjectTypes> types = projectTypesService.getEnabledTypes();
            return Result.success(types);
        } catch (Exception e) {
            log.error("获取启用的项目类别失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据大类ID获取启用的项目类别
     */
    @GetMapping("/enabled/category/{categoryId}")
    public Result<List<RchxProjectTypes>> getEnabledTypesByCategoryId(@PathVariable Integer categoryId) {
        try {
            List<RchxProjectTypes> types = projectTypesService.getEnabledTypesByCategoryId(categoryId);
            return Result.success(types);
        } catch (Exception e) {
            log.error("根据大类ID获取启用的项目类别失败: categoryId={}", categoryId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目类别
     */
    @GetMapping("/{id}")
    public Result<RchxProjectTypes> getById(@PathVariable Integer id) {
        try {
            RchxProjectTypes type = projectTypesService.getById(id);
            if (type == null) {
                return Result.error("项目类别不存在");
            }
            return Result.success(type);
        } catch (Exception e) {
            log.error("查询项目类别失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据类别编码查询项目类别
     */
    @GetMapping("/code/{typeCode}")
    public Result<RchxProjectTypes> getByTypeCode(@PathVariable String typeCode) {
        try {
            RchxProjectTypes type = projectTypesService.getByTypeCode(typeCode);
            if (type == null) {
                return Result.error("项目类别不存在");
            }
            return Result.success(type);
        } catch (Exception e) {
            log.error("根据编码查询项目类别失败: typeCode={}", typeCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目类别
     */
    @PostMapping
    public Result<String> save(@RequestBody RchxProjectTypes type) {
        try {
            // 检查编码是否重复
            if (projectTypesService.existsByTypeCode(type.getTypeCode(), null)) {
                return Result.error("类别编码已存在");
            }
            
            boolean success = projectTypesService.save(type);
            return success ? Result.success("新增成功") : Result.error("新增失败");
        } catch (Exception e) {
            log.error("新增项目类别失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目类别
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Integer id, @RequestBody RchxProjectTypes type) {
        try {
            // 检查编码是否重复
            if (projectTypesService.existsByTypeCode(type.getTypeCode(), id)) {
                return Result.error("类别编码已存在");
            }
            
            type.setId(id);
            boolean success = projectTypesService.updateById(type);
            return success ? Result.success("更新成功") : Result.error("更新失败");
        } catch (Exception e) {
            log.error("更新项目类别失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目类别
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Integer id) {
        try {
            boolean success = projectTypesService.removeById(id);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除项目类别失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用项目类别
     */
    @PutMapping("/{id}/enabled")
    public Result<String> updateEnabled(@PathVariable Integer id, @RequestParam Boolean enabled) {
        try {
            boolean success = projectTypesService.updateEnabled(id, enabled);
            return success ? Result.success("操作成功") : Result.error("操作失败");
        } catch (Exception e) {
            log.error("更新项目类别启用状态失败: id={}, enabled={}", id, enabled, e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
}
