package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gl.gl_lg_java.domain.RchxXmysxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_xmysxx(项目预算信息)】的数据库操作Mapper
 * @createDate 2025-07-18 11:11:41
 * @Entity com.gl.gl_lg_java.domain.RchxXmysxx
 */
@Mapper
public interface RchxXmysxxMapper extends BaseMapper<RchxXmysxx> {

        /**
         * 核心字段列表 - 用于列表查询优化
         */
        String CORE_FIELDS = "xmbh, xmmc, zcje, zcsj, pdh, jbr, bxr, cjrgh, cjrxm, shzt";

        /**
         * 根据项目编号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_xmysxx WHERE xmbh = #{xmbh} ORDER BY zcsj DESC")
        List<RchxXmysxx> findByXmbh(@Param("xmbh") String xmbh);

        /**
         * 根据项目名称模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_xmysxx WHERE xmmc LIKE CONCAT('%', #{xmmc}, '%') ORDER BY zcsj DESC")
        List<RchxXmysxx> findByXmmcLike(@Param("xmmc") String xmmc);

        /**
         * 根据凭单号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_xmysxx WHERE pdh = #{pdh} ORDER BY zcsj DESC")
        List<RchxXmysxx> findByPdh(@Param("pdh") String pdh);

        /**
         * 根据经办人模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_xmysxx WHERE jbr LIKE CONCAT('%', #{jbr}, '%') ORDER BY zcsj DESC")
        List<RchxXmysxx> findByJbrLike(@Param("jbr") String jbr);

        /**
         * 根据报销人模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_xmysxx WHERE bxr LIKE CONCAT('%', #{bxr}, '%') ORDER BY zcsj DESC")
        List<RchxXmysxx> findByBxrLike(@Param("bxr") String bxr);

        /**
         * 根据创建人工号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_xmysxx WHERE cjrgh = #{cjrgh} ORDER BY zcsj DESC")
        List<RchxXmysxx> findByCjrgh(@Param("cjrgh") String cjrgh);

        /**
         * 根据创建人姓名模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_xmysxx WHERE cjrxm LIKE CONCAT('%', #{cjrxm}, '%') ORDER BY zcsj DESC")
        List<RchxXmysxx> findByCjrxmLike(@Param("cjrxm") String cjrxm);

        /**
         * 根据审核状态查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_xmysxx WHERE shzt = #{shzt} ORDER BY zcsj DESC")
        List<RchxXmysxx> findByShzt(@Param("shzt") String shzt);

        /**
         * 根据支出金额范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_xmysxx WHERE CAST(zcje AS DECIMAL(10,2)) >= CAST(#{startAmount} AS DECIMAL(10,2)) AND CAST(zcje AS DECIMAL(10,2)) <= CAST(#{endAmount} AS DECIMAL(10,2)) ORDER BY zcsj DESC")
        List<RchxXmysxx> findByZcjeBetween(@Param("startAmount") String startAmount,
                        @Param("endAmount") String endAmount);

        /**
         * 根据支出时间范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_xmysxx WHERE zcsj >= #{startDate} AND zcsj <= #{endDate} ORDER BY zcsj DESC")
        List<RchxXmysxx> findByZcsjBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

        // 多条件查询已移至Service层使用QueryWrapper实现，更灵活且不易出错

        // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错
}