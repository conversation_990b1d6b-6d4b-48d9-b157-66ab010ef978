package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxSchoolProjectFiles;
import com.gl.gl_lg_java.service.RchxSchoolProjectFilesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 校级项目文件管理Controller
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@RestController
@RequestMapping("/api/school-project/files")
@Api(tags = "校级项目文件管理")
@Slf4j
public class SchoolProjectFileController {

    @Autowired
    private RchxSchoolProjectFilesService filesService;

    /**
     * 上传文件
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("上传文件")
    public Result<RchxSchoolProjectFiles> uploadFile(
            @ApiParam("申报ID") @RequestParam Long applicationId,
            @ApiParam("申报编号") @RequestParam String applicationCode,
            @ApiParam("文件类别") @RequestParam String fileCategory,
            @ApiParam("文件描述") @RequestParam(required = false) String fileDescription,
            @ApiParam("是否必需文件") @RequestParam(defaultValue = "false") Boolean isRequired,
            @ApiParam("上传人职工号") @RequestParam String uploadBy,
            @ApiParam("上传人姓名") @RequestParam String uploadByName,
            @ApiParam("文件") @RequestParam("file") MultipartFile file) {

        try {
            // 直接调用Service上传文件
            RchxSchoolProjectFiles fileRecord = filesService.uploadFile(
                    file, applicationId, applicationCode, fileCategory,
                    fileDescription, isRequired, uploadBy, uploadByName);
            return Result.success(fileRecord);

        } catch (Exception e) {
            log.error("文件上传失败: applicationCode={}, fileName={}", applicationCode, file.getOriginalFilename(), e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件下载URL
     */
    @GetMapping("/download/{fileId}")
    @ApiOperation("获取文件下载URL")
    public Result<String> getFileDownloadUrl(@PathVariable Long fileId) {
        try {
            String downloadUrl = filesService.getFileDownloadUrl(fileId);
            return Result.success(downloadUrl);
        } catch (Exception e) {
            log.error("获取文件下载URL失败: fileId={}", fileId, e);
            return Result.error("获取下载URL失败: " + e.getMessage());
        }
    }

    /**
     * 直接下载文件流
     */
    @GetMapping("/stream/{fileId}")
    @ApiOperation("直接下载文件流")
    public void downloadFileStream(@PathVariable Long fileId, HttpServletResponse response) {
        try {
            // 查询文件记录
            RchxSchoolProjectFiles fileRecord = filesService.getById(fileId);
            if (fileRecord == null || fileRecord.getIsDeleted()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 获取文件字节数组
            byte[] fileBytes = filesService.downloadFile(fileId);

            // 设置响应头
            response.setContentType(fileRecord.getFileType());
            response.setContentLength(fileBytes.length);
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + URLEncoder.encode(fileRecord.getFileOriginalName(), "UTF-8") + "\"");

            // 写入响应流
            try (OutputStream outputStream = response.getOutputStream()) {
                outputStream.write(fileBytes);
                outputStream.flush();
            }

            // 更新下载次数
            filesService.updateDownloadCount(fileId);

        } catch (Exception e) {
            log.error("直接下载文件流失败: fileId={}", fileId, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    // 预签名URL功能已集成到getFileDownloadUrl方法中

    /**
     * 删除文件
     */
    @DeleteMapping("/{fileId}")
    @ApiOperation("删除文件")
    public Result<String> deleteFile(@PathVariable Long fileId, @RequestParam String deleteBy) {
        try {
            boolean success = filesService.logicalDeleteFile(fileId, deleteBy);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除文件失败: fileId={}", fileId, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除文件
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除文件")
    public Result<String> batchDeleteFiles(@RequestBody List<Long> fileIds, @RequestParam String deleteBy) {
        try {
            boolean success = filesService.batchLogicalDeleteFiles(fileIds, deleteBy);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除文件失败: fileIds={}", fileIds, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询文件信息
     */
    @GetMapping("/page")
    @ApiOperation("分页查询文件信息")
    public Result<IPage<Map<String, Object>>> pageQuery(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("申报ID") @RequestParam(required = false) Long applicationId,
            @ApiParam("申报编号") @RequestParam(required = false) String applicationCode,
            @ApiParam("文件类别") @RequestParam(required = false) String fileCategory,
            @ApiParam("上传人") @RequestParam(required = false) String uploadBy,
            @ApiParam("是否必需文件") @RequestParam(required = false) Boolean isRequired) {

        try {
            Page<RchxSchoolProjectFiles> page = new Page<>(current, size);
            IPage<Map<String, Object>> result = filesService.pageQueryWithDetails(
                    page, applicationId, applicationCode, fileCategory, uploadBy, isRequired);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询文件信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据申报ID查询文件列表
     */
    @GetMapping("/application/{applicationId}")
    @ApiOperation("根据申报ID查询文件列表")
    public Result<List<RchxSchoolProjectFiles>> getFilesByApplicationId(@PathVariable Long applicationId) {
        try {
            List<RchxSchoolProjectFiles> files = filesService.getFilesByApplicationId(applicationId);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据申报ID查询文件列表失败: applicationId={}", applicationId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据申报编号查询文件列表
     */
    @GetMapping("/application-code/{applicationCode}")
    @ApiOperation("根据申报编号查询文件列表")
    public Result<List<RchxSchoolProjectFiles>> getFilesByApplicationCode(@PathVariable String applicationCode) {
        try {
            List<RchxSchoolProjectFiles> files = filesService.getFilesByApplicationCode(applicationCode);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据申报编号查询文件列表失败: applicationCode={}", applicationCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取文件统计信息")
    public Result<Map<String, Object>> getFileStatistics() {
        try {
            Map<String, Object> statistics = filesService.getFileStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取文件统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查申报的必需文件是否完整
     */
    @GetMapping("/check-required/{applicationId}")
    @ApiOperation("检查申报的必需文件是否完整")
    public Result<Map<String, Object>> checkRequiredFiles(@PathVariable Long applicationId) {
        try {
            Map<String, Object> checkResult = filesService.checkRequiredFiles(applicationId);
            return Result.success(checkResult);
        } catch (Exception e) {
            log.error("检查必需文件失败: applicationId={}", applicationId, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }
}
