package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxSchoolProjectFiles;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 校级项目申报文件Service接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface RchxSchoolProjectFilesService extends IService<RchxSchoolProjectFiles> {

    /**
     * 上传文件
     *
     * @param file            上传的文件
     * @param applicationId   申报ID
     * @param applicationCode 申报编号
     * @param fileCategory    文件类别
     * @param fileDescription 文件描述
     * @param isRequired      是否必需文件
     * @param uploadBy        上传人职工号
     * @param uploadByName    上传人姓名
     * @return 文件记录
     */
    RchxSchoolProjectFiles uploadFile(MultipartFile file, Long applicationId, String applicationCode,
            String fileCategory, String fileDescription, Boolean isRequired,
            String uploadBy, String uploadByName);

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     * @return 文件字节数组
     */
    byte[] downloadFile(Long fileId);

    /**
     * 获取文件下载URL
     *
     * @param fileId 文件ID
     * @return 预签名下载URL
     */
    String getFileDownloadUrl(Long fileId);

    /**
     * 分页查询文件信息
     * 
     * @param page            分页参数
     * @param applicationId   申报ID
     * @param applicationCode 申报编号
     * @param fileCategory    文件类别
     * @param uploadBy        上传人
     * @param isRequired      是否必需文件
     * @return 分页结果
     */
    IPage<Map<String, Object>> pageQueryWithDetails(Page<RchxSchoolProjectFiles> page,
            Long applicationId,
            String applicationCode,
            String fileCategory,
            String uploadBy,
            Boolean isRequired);

    /**
     * 根据申报ID查询文件列表
     * 
     * @param applicationId 申报ID
     * @return 文件列表
     */
    List<RchxSchoolProjectFiles> getFilesByApplicationId(Long applicationId);

    /**
     * 根据申报编号查询文件列表
     * 
     * @param applicationCode 申报编号
     * @return 文件列表
     */
    List<RchxSchoolProjectFiles> getFilesByApplicationCode(String applicationCode);

    /**
     * 根据文件类别查询文件列表
     * 
     * @param applicationId 申报ID
     * @param fileCategory  文件类别
     * @return 文件列表
     */
    List<RchxSchoolProjectFiles> getFilesByCategory(Long applicationId, String fileCategory);

    /**
     * 根据上传人查询文件列表
     * 
     * @param uploadBy 上传人职工号
     * @return 文件列表
     */
    List<RchxSchoolProjectFiles> getFilesByUploader(String uploadBy);

    /**
     * 查询必需文件列表
     * 
     * @param applicationId 申报ID
     * @return 必需文件列表
     */
    List<RchxSchoolProjectFiles> getRequiredFiles(Long applicationId);

    /**
     * 根据文件MD5查询（用于重复文件检测）
     * 
     * @param fileMd5 文件MD5值
     * @return 文件列表
     */
    List<RchxSchoolProjectFiles> getFilesByMd5(String fileMd5);

    /**
     * 更新下载次数
     * 
     * @param fileId 文件ID
     * @return 是否成功
     */
    boolean updateDownloadCount(Long fileId);

    /**
     * 逻辑删除文件
     * 
     * @param fileId   文件ID
     * @param deleteBy 删除人
     * @return 是否成功
     */
    boolean logicalDeleteFile(Long fileId, String deleteBy);

    /**
     * 批量逻辑删除文件
     * 
     * @param fileIds  文件ID列表
     * @param deleteBy 删除人
     * @return 是否成功
     */
    boolean batchLogicalDeleteFiles(List<Long> fileIds, String deleteBy);

    /**
     * 获取文件统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getFileStatistics();

    /**
     * 获取文件类别统计
     * 
     * @return 文件类别统计信息
     */
    List<Map<String, Object>> getFileCategoryStatistics();

    /**
     * 获取申报文件统计
     * 
     * @return 申报文件统计信息
     */
    List<Map<String, Object>> getApplicationFileStatistics();

    /**
     * 获取上传人文件统计
     * 
     * @return 上传人文件统计信息
     */
    List<Map<String, Object>> getUploaderFileStatistics();

    /**
     * 获取最近上传的文件
     * 
     * @param limit 限制数量
     * @return 最近上传的文件列表
     */
    List<RchxSchoolProjectFiles> getRecentUploadedFiles(Integer limit);

    /**
     * 获取热门下载文件
     * 
     * @param limit 限制数量
     * @return 热门下载文件列表
     */
    List<RchxSchoolProjectFiles> getPopularDownloadFiles(Integer limit);

    /**
     * 检查申报的必需文件是否完整
     * 
     * @param applicationId 申报ID
     * @return 检查结果
     */
    Map<String, Object> checkRequiredFiles(Long applicationId);

    /**
     * 检查文件名是否存在
     * 
     * @param fileName      文件名
     * @param applicationId 申报ID
     * @return 是否存在
     */
    boolean existsByFileName(String fileName, Long applicationId);

    /**
     * 创建文件记录
     * 
     * @param file 文件信息
     * @return 是否成功
     */
    boolean createFile(RchxSchoolProjectFiles file);

    /**
     * 更新文件记录
     * 
     * @param file 文件信息
     * @return 是否成功
     */
    boolean updateFile(RchxSchoolProjectFiles file);

    /**
     * 删除文件记录
     * 
     * @param fileId 文件ID
     * @return 是否成功
     */
    boolean deleteFile(Long fileId);

    /**
     * 导出文件列表
     * 
     * @param applicationId 申报ID
     * @param fileCategory  文件类别
     * @return 文件列表
     */
    List<RchxSchoolProjectFiles> exportFileList(Long applicationId, String fileCategory);
}
