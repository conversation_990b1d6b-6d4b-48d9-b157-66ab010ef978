package com.gl.gl_lg_java.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 教师能力详细分析DTO
 */
@Data
public class TeacherCapabilityDTO {
    
    /**
     * 教师基本信息
     */
    private String teacherId;
    private String teacherName;
    private String department;
    private String title;
    private String titleLevel;
    private String education;
    
    // ========== 能力维度得分 ==========
    
    /**
     * 学术影响力得分
     */
    private BigDecimal academicInfluenceScore;
    
    /**
     * 领域深耕度得分
     */
    private BigDecimal fieldExpertiseScore;
    
    /**
     * 团队适配值得分
     */
    private BigDecimal teamCompatibilityScore;
    
    /**
     * 成果落地性得分
     */
    private BigDecimal achievementPracticalityScore;
    
    /**
     * 综合能力得分
     */
    private BigDecimal overallScore;
    
    // ========== 详细统计数据 ==========
    
    /**
     * 获奖统计
     */
    private AwardStatistics awardStatistics;
    
    /**
     * 项目统计
     */
    private ProjectStatistics projectStatistics;
    
    /**
     * 合作统计
     */
    private CollaborationStatistics collaborationStatistics;
    
    /**
     * 研究领域分布
     */
    private List<FieldDistribution> fieldDistributions;
    
    /**
     * 能力标签
     */
    private List<String> capabilityTags;
    
    /**
     * 专业关键词
     */
    private List<String> expertiseKeywords;
    
    /**
     * 发展趋势
     */
    private TrendAnalysis trendAnalysis;
    
    /**
     * 获奖统计
     */
    @Data
    public static class AwardStatistics {
        private Integer totalAwards;           // 获奖总数
        private Integer nationalAwards;        // 国家级获奖
        private Integer provincialAwards;      // 省级获奖
        private Integer recentAwards;          // 近3年获奖
        private String highestAwardLevel;      // 最高获奖级别
        private List<String> awardTypes;       // 获奖类型
        private Map<String, Integer> awardsByYear; // 按年份统计
    }
    
    /**
     * 项目统计
     */
    @Data
    public static class ProjectStatistics {
        private Integer totalProjects;        // 项目总数
        private Integer leadingProjects;      // 主持项目数
        private Integer participatingProjects; // 参与项目数
        private Integer ongoingProjects;      // 进行中项目
        private Integer completedProjects;    // 已完成项目
        private List<String> projectTypes;   // 项目类型
    }
    
    /**
     * 合作统计
     */
    @Data
    public static class CollaborationStatistics {
        private Integer collaborativeAwards;  // 合作获奖数
        private Integer collaborationCount;   // 合作次数
        private List<String> collaborationUnits; // 合作单位
        private BigDecimal collaborationRate; // 合作率
    }
    
    /**
     * 领域分布
     */
    @Data
    public static class FieldDistribution {
        private String fieldName;            // 领域名称
        private Integer achievementCount;    // 成果数量
        private BigDecimal percentage;       // 占比
        private String level;               // 水平等级
    }
    
    /**
     * 趋势分析
     */
    @Data
    public static class TrendAnalysis {
        private String overallTrend;         // 整体趋势：上升/稳定/下降
        private BigDecimal growthRate;       // 增长率
        private List<YearlyData> yearlyData; // 年度数据
        private String prediction;          // 发展预测
    }
    
    /**
     * 年度数据
     */
    @Data
    public static class YearlyData {
        private Integer year;
        private Integer awardCount;
        private Integer projectCount;
        private BigDecimal score;
    }
    
    /**
     * 获取能力等级
     */
    public String getCapabilityLevel() {
        if (overallScore.compareTo(new BigDecimal("90")) >= 0) {
            return "顶尖专家";
        } else if (overallScore.compareTo(new BigDecimal("80")) >= 0) {
            return "资深专家";
        } else if (overallScore.compareTo(new BigDecimal("70")) >= 0) {
            return "专业人才";
        } else if (overallScore.compareTo(new BigDecimal("60")) >= 0) {
            return "合格人才";
        } else {
            return "待发展人才";
        }
    }
    
    /**
     * 获取优势领域
     */
    public String getPrimaryExpertise() {
        if (fieldDistributions != null && !fieldDistributions.isEmpty()) {
            return fieldDistributions.get(0).getFieldName();
        }
        return "暂无数据";
    }
    
    /**
     * 获取能力雷达图数据
     */
    public Map<String, BigDecimal> getRadarData() {
        return Map.of(
            "学术影响力", academicInfluenceScore,
            "领域深耕度", fieldExpertiseScore,
            "团队适配值", teamCompatibilityScore,
            "成果落地性", achievementPracticalityScore
        );
    }
}
