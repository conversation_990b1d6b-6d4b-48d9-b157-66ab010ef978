package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxProjectCategories;
import com.gl.gl_lg_java.mapper.RchxProjectCategoriesMapper;
import com.gl.gl_lg_java.service.RchxProjectCategoriesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目大类表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@Service
public class RchxProjectCategoriesServiceImpl extends ServiceImpl<RchxProjectCategoriesMapper, RchxProjectCategories> 
        implements RchxProjectCategoriesService {

    @Override
    public List<RchxProjectCategories> getEnabledCategories() {
        QueryWrapper<RchxProjectCategories> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_enabled", true)
                   .orderByAsc("sort_order", "id");
        return list(queryWrapper);
    }

    @Override
    public RchxProjectCategories getByCategoryCode(String categoryCode) {
        QueryWrapper<RchxProjectCategories> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_code", categoryCode);
        return getOne(queryWrapper);
    }

    @Override
    public boolean updateEnabled(Integer id, Boolean enabled) {
        try {
            RchxProjectCategories category = new RchxProjectCategories();
            category.setId(id);
            category.setIsEnabled(enabled);
            return updateById(category);
        } catch (Exception e) {
            log.error("更新项目大类启用状态失败: id={}, enabled={}", id, enabled, e);
            return false;
        }
    }

    @Override
    public boolean existsByCategoryCode(String categoryCode, Integer excludeId) {
        QueryWrapper<RchxProjectCategories> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_code", categoryCode);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) > 0;
    }
}
