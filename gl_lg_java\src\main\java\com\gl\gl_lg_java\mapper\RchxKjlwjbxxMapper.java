package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gl.gl_lg_java.domain.RchxKjlwjbxx;
import com.gl.gl_lg_java.dto.KjlwStatisticsDTO;
import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_kjlwjbxx(科技论文基本信息)】的数据库操作Mapper
 * @createDate 2025-07-18 11:11:41
 * @Entity com.gl.gl_lg_java.domain.RchxKjlwjbxx
 */
@Mapper
public interface RchxKjlwjbxxMapper extends BaseMapper<RchxKjlwjbxx> {

        /**
         * 核心字段列表 - 用于列表查询优化
         */
        String CORE_FIELDS = "lwbh, lwmc, dyzzbh, dyzzxm, txzzbh, txzzxm, kwmc, kwlx, fbfw, fbrq, dwmc, shzt, cjsj";

        /**
         * 根据论文编号查询（详情查询，返回所有字段）
         */
        @Select("SELECT * FROM t_rchx_kjlwjbxx WHERE lwbh = #{lwbh}")
        @Results(id = "kjlwjbxxResultMap", value = {
                        @Result(property = "lwbh", column = "lwbh", id = true),
                        @Result(property = "lwmc", column = "lwmc"),
                        @Result(property = "dyzzbh", column = "dyzzbh"),
                        @Result(property = "dyzzxm", column = "dyzzxm"),
                        @Result(property = "txzzbh", column = "txzzbh"),
                        @Result(property = "txzzxm", column = "txzzxm"),
                        @Result(property = "kwmc", column = "kwmc"),
                        @Result(property = "kwlx", column = "kwlx"),
                        @Result(property = "fbfw", column = "fbfw"),
                        @Result(property = "fbrq", column = "fbrq"),
                        @Result(property = "shzt", column = "shzt"),
                        @Result(property = "dwmc", column = "dwmc"),
                        @Result(property = "tstamp", column = "tstamp")
        })
        RchxKjlwjbxx findByLwbh(@Param("lwbh") String lwbh);

        /**
         * 根据论文名称模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjlwjbxx WHERE lwmc LIKE CONCAT('%', #{lwmc}, '%')")
        List<RchxKjlwjbxx> findByLwmcLike(@Param("lwmc") String lwmc);

        /**
         * 根据第一作者编号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjlwjbxx WHERE dyzzbh = #{dyzzbh}")
        List<RchxKjlwjbxx> findByDyzzbh(@Param("dyzzbh") String dyzzbh);

        /**
         * 根据第一作者姓名模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjlwjbxx WHERE dyzzxm LIKE CONCAT('%', #{dyzzxm}, '%')")
        List<RchxKjlwjbxx> findByDyzzxmLike(@Param("dyzzxm") String dyzzxm);

        /**
         * 根据通讯作者编号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjlwjbxx WHERE txzzbh = #{txzzbh}")
        List<RchxKjlwjbxx> findByTxzzbh(@Param("txzzbh") String txzzbh);

        /**
         * 根据刊物名称模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjlwjbxx WHERE kwmc LIKE CONCAT('%', #{kwmc}, '%')")
        List<RchxKjlwjbxx> findByKwmcLike(@Param("kwmc") String kwmc);

        /**
         * 根据刊物类型查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjlwjbxx WHERE kwlx = #{kwlx}")
        List<RchxKjlwjbxx> findByKwlx(@Param("kwlx") String kwlx);

        /**
         * 根据发表范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjlwjbxx WHERE fbfw = #{fbfw}")
        List<RchxKjlwjbxx> findByFbfw(@Param("fbfw") String fbfw);

        /**
         * 根据审核状态查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_kjlwjbxx WHERE shzt = #{shzt}")
        List<RchxKjlwjbxx> findByShzt(@Param("shzt") String shzt);

        /**
         * 根据发表日期范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_kjlwjbxx WHERE fbrq &gt;= #{startDate} AND fbrq &lt;= #{endDate}")
        List<RchxKjlwjbxx> findByFbrqBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

        /**
         * 按刊物类型统计
         */
        @Select("SELECT COALESCE(kwlx, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_kjlwjbxx GROUP BY kwlx")
        List<Map<String, Object>> getStatsByKwlx();

        /**
         * 按审核状态统计
         */
        @Select("SELECT COALESCE(shzt, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_kjlwjbxx GROUP BY shzt")
        List<Map<String, Object>> getStatsByShzt();

        /**
         * 快速获取所有刊物类型选项（不统计数量）
         */
        @Select("SELECT DISTINCT kwlx FROM t_rchx_kjlwjbxx WHERE kwlx IS NOT NULL AND kwlx != '' ORDER BY kwlx")
        List<String> findDistinctKwlx();

        /**
         * 快速获取所有审核状态选项（不统计数量）
         */
        @Select("SELECT DISTINCT shzt FROM t_rchx_kjlwjbxx WHERE shzt IS NOT NULL AND shzt != '' ORDER BY shzt")
        List<String> findDistinctShzt();

        /**
         * 统计教师个人科技论文数量（按第一作者编号或通讯作者编号）
         */
        @Select("SELECT COUNT(*) FROM t_rchx_kjlwjbxx WHERE dyzzbh = #{zgh} OR txzzbh = #{zgh}")
        Long getPersonalCountByZgh(@Param("zgh") String zgh);

        // 多条件查询已移至Service层使用QueryWrapper实现，更灵活且不易出错

        // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错

        /**
         * 统计单位论文数量
         */
        @Select("SELECT dwmc, COUNT(*) as paperCount " +
                        "FROM t_rchx_kjlwjbxx " +
                        "WHERE dwmc IS NOT NULL AND dwmc != '' " +
                        "GROUP BY dwmc " +
                        "ORDER BY paperCount DESC")
        List<KjlwStatisticsDTO.UnitStatistics> getUnitStatistics();

        /**
         * 统计发布范围论文数量
         */
        @Select("SELECT fbfw, COUNT(*) as paperCount " +
                        "FROM t_rchx_kjlwjbxx " +
                        "WHERE fbfw IS NOT NULL AND fbfw != '' " +
                        "GROUP BY fbfw " +
                        "ORDER BY paperCount DESC")
        List<KjlwStatisticsDTO.PublishScopeStatistics> getPublishScopeStatistics();

        /**
         * 统计学科类别论文数量
         */
        @Select("SELECT xklb, COUNT(*) as paperCount " +
                        "FROM t_rchx_kjlwjbxx " +
                        "WHERE xklb IS NOT NULL AND xklb != '' " +
                        "GROUP BY xklb " +
                        "ORDER BY paperCount DESC")
        List<KjlwStatisticsDTO.SubjectCategoryStatistics> getSubjectCategoryStatistics();

        /**
         * 获取总体统计数据
         */
        @Select("SELECT " +
                        "COUNT(*) as totalPapers, " +
                        "COUNT(DISTINCT dwmc) as totalUnits, " +
                        "COUNT(DISTINCT fbfw) as totalPublishScopes, " +
                        "COUNT(DISTINCT xklb) as totalSubjectCategories " +
                        "FROM t_rchx_kjlwjbxx " +
                        "WHERE dwmc IS NOT NULL AND dwmc != ''")
        Map<String, Object> getOverallStatistics();
}