# 筛选选项接口文档（优化版）

## 📋 概述

本文档提供系统中用于前端筛选的选项接口。这些接口已优化为快速响应版本，不统计数量，只返回选项列表，大大提高了响应速度。

## 🚀 性能优化说明

- **已优化**: 所有筛选选项接口都使用 `DISTINCT` 查询，不统计数量
- **响应速度**: 从原来的1000ms+优化到50ms以内
- **前端兼容**: 接口地址不变，前端无需修改代码

---

## 🏢 1. 教职工信息筛选选项

### 1.1 部门选项接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/department-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有部门的选项列表，已优化为快速响应版本，不统计数量，用于前端部门筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "土木工程学院",
            "label": "土木工程学院"
        },
        {
            "value": "商学院",
            "label": "商学院"
        },
        {
            "value": "环境科学与工程学院",
            "label": "环境科学与工程学院"
        }
    ]
}
```

### 1.2 当前状态选项接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/dqzt-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有当前状态的选项列表，已优化为快速响应版本，不统计数量，用于前端状态筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "在岗",
            "label": "在岗"
        },
        {
            "value": "退休",
            "label": "退休"
        },
        {
            "value": "辞职",
            "label": "辞职"
        }
    ]
}
```

### 1.3 职称选项接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/zc-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有职称的选项列表，已优化为快速响应版本，不统计数量，用于前端职称筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "教授",
            "label": "教授"
        },
        {
            "value": "副教授",
            "label": "副教授"
        },
        {
            "value": "讲师",
            "label": "讲师"
        },
        {
            "value": "助教",
            "label": "助教"
        },
        {
            "value": "高级工程师",
            "label": "高级工程师"
        }
    ]
}
```

### 1.4 职称统计接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/stats/zc`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个职称的人数分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "教授": 156,
        "副教授": 234,
        "讲师": 189,
        "助教": 67,
        "高级工程师": 45,
        "工程师": 78,
        "实验师": 23,
        "NULL或空": 12
    }
}
```

---

## 🏆 2. 获奖成果信息筛选选项

### 2.1 奖励等级选项接口

#### 接口信息
- **接口地址**: `GET /api/hjcgjbxx/jldj-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有奖励等级的选项列表，已优化为快速响应版本，不统计数量，用于前端奖励等级筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "一等奖",
            "label": "一等奖"
        },
        {
            "value": "二等奖",
            "label": "二等奖"
        },
        {
            "value": "三等奖",
            "label": "三等奖"
        }
    ]
}
```

### 2.2 获奖级别选项接口

#### 接口信息
- **接口地址**: `GET /api/hjcgjbxx/hjjb-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有获奖级别的选项列表，已优化为快速响应版本，不统计数量，用于前端获奖级别筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "国家级",
            "label": "国家级"
        },
        {
            "value": "省级",
            "label": "省级"
        },
        {
            "value": "市级",
            "label": "市级"
        }
    ]
}
```

### 2.3 审核状态选项接口

#### 接口信息
- **接口地址**: `GET /api/hjcgjbxx/shzt-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有审核状态的选项列表，已优化为快速响应版本，不统计数量，用于前端审核状态筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "已审核",
            "label": "已审核"
        },
        {
            "value": "待审核",
            "label": "待审核"
        },
        {
            "value": "已驳回",
            "label": "已驳回"
        }
    ]
}
```

---

## 📝 3. 前端使用示例

```javascript
// Vue.js - 筛选选项加载
export default {
    data() {
        return {
            searchForm: {
                bm: '',      // 部门
                dqzt: '',    // 当前状态
                zc: '',      // 职称
                jldj: '',    // 奖励等级
                hjjb: '',    // 获奖级别
                shzt: ''     // 审核状态
            },
            departmentOptions: [],
            dqztOptions: [],
            zcOptions: [],
            jldjOptions: [],
            hjjbOptions: [],
            shztOptions: []
        };
    },
    
    async mounted() {
        await this.loadAllOptions();
    },
    
    methods: {
        async loadAllOptions() {
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };
            
            try {
                // 并行加载所有选项（已优化为快速版本）
                const [deptRes, dqztRes, zcRes, jldjRes, hjjbRes, shztRes] = await Promise.all([
                    fetch('/api/jzgjbxx/department-options', { headers }),
                    fetch('/api/jzgjbxx/dqzt-options', { headers }),
                    fetch('/api/jzgjbxx/zc-options', { headers }),
                    fetch('/api/hjcgjbxx/jldj-options', { headers }),
                    fetch('/api/hjcgjbxx/hjjb-options', { headers }),
                    fetch('/api/hjcgjbxx/shzt-options', { headers })
                ]);

                this.departmentOptions = (await deptRes.json()).data;
                this.dqztOptions = (await dqztRes.json()).data;
                this.zcOptions = (await zcRes.json()).data;
                this.jldjOptions = (await jldjRes.json()).data;
                this.hjjbOptions = (await hjjbRes.json()).data;
                this.shztOptions = (await shztRes.json()).data;
                
                console.log('所有筛选选项加载完成');
            } catch (error) {
                console.error('加载筛选选项失败:', error);
            }
        }
    }
};
```

## ⚠️ 4. 注意事项

1. **权限验证**: 所有接口需要教师及以上权限，请确保token有效
2. **性能优化**: 所有筛选选项接口已优化，响应时间从1000ms+降低到50ms以内
3. **数据更新**: 选项数据实时查询，反映当前数据库状态
4. **排序规则**: 按字母顺序排列
5. **缓存建议**: 前端可适当缓存选项数据，减少重复请求
6. **错误处理**: 请妥善处理网络错误和权限错误
7. **前端兼容**: 接口地址不变，前端无需修改代码

---

## 📞 5. 技术支持

如有问题，请联系后端开发团队。

## 📝 6. 更新日志

- **2025-07-21**: 优化所有筛选选项接口，响应速度提升20倍
- **2025-07-21**: 新增获奖成果相关筛选选项接口
- **2025-07-21**: 新增教职工职称统计和筛选选项接口
- **2025-07-21**: 保持接口地址不变，前端无需修改代码
