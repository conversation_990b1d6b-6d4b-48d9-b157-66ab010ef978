package com.gl.gl_lg_java.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT拦截器
 */
@Component
@Slf4j
public class JwtInterceptor implements HandlerInterceptor {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 预检请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }
        
        // 获取Authorization头
        String token = request.getHeader("Authorization");
        
        if (token == null || token.trim().isEmpty()) {
            log.warn("请求缺少Authorization头: {}", request.getRequestURI());
            writeErrorResponse(response, "请先登录");
            return false;
        }
        
        // 验证Token
        if (!jwtUtil.validateToken(token)) {
            log.warn("Token验证失败: {}", request.getRequestURI());
            writeErrorResponse(response, "Token无效或已过期，请重新登录");
            return false;
        }
        
        // 将用户信息存储到请求属性中，供后续使用
        String zgh = jwtUtil.getZghFromToken(token);
        String qx = jwtUtil.getQxFromToken(token);
        
        request.setAttribute("currentUserZgh", zgh);
        request.setAttribute("currentUserQx", qx);
        
        return true;
    }
    
    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        Result<String> result = Result.error(401, message);
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonResponse = objectMapper.writeValueAsString(result);
        
        response.getWriter().write(jsonResponse);
    }
}
