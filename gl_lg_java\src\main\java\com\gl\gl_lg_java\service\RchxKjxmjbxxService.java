package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxKjxmjbxx;
import com.gl.gl_lg_java.dto.KjxmjbxxQueryDTO;
import com.gl.gl_lg_java.dto.KjxmStatisticsDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_kjxmjbxx(科技项目基本信息)】的数据库操作Service
 * @createDate 2025-07-18 11:11:41
 */
public interface RchxKjxmjbxxService extends IService<RchxKjxmjbxx> {

    /**
     * 根据项目主键查询
     */
    RchxKjxmjbxx getByXmid(String xmid);

    /**
     * 根据项目编号查询
     */
    RchxKjxmjbxx getByXmbh(String xmbh);

    /**
     * 根据项目名称模糊查询
     */
    List<RchxKjxmjbxx> listByXmmcLike(String xmmc);

    /**
     * 根据项目负责人号查询
     */
    List<RchxKjxmjbxx> listByXmfzrh(String xmfzrh);

    /**
     * 根据负责人姓名模糊查询
     */
    List<RchxKjxmjbxx> listByFzrxmLike(String fzrxm);

    /**
     * 根据项目执行状态查询
     */
    List<RchxKjxmjbxx> listByXmzxzt(String xmzxzt);

    /**
     * 根据审核状态查询
     */
    List<RchxKjxmjbxx> listByShzt(String shzt);

    /**
     * 多条件查询
     */
    List<RchxKjxmjbxx> listByMultiConditions(KjxmjbxxQueryDTO queryDTO);

    /**
     * 分页多条件查询
     */
    IPage<RchxKjxmjbxx> pageByMultiConditions(KjxmjbxxQueryDTO queryDTO);

    /**
     * 新增科技项目信息
     */
    boolean saveKjxmjbxx(RchxKjxmjbxx kjxmjbxx);

    /**
     * 根据项目主键更新
     */
    boolean updateByXmid(RchxKjxmjbxx kjxmjbxx);

    /**
     * 根据项目主键删除
     */
    boolean removeByXmid(String xmid);

    /**
     * 批量删除
     */
    boolean removeBatchByXmids(List<String> xmids);

    /**
     * 批量新增
     */
    boolean saveBatchKjxmjbxx(List<RchxKjxmjbxx> kjxmjbxxList);

    /**
     * 批量更新
     */
    boolean updateBatchKjxmjbxx(List<RchxKjxmjbxx> kjxmjbxxList);

    /**
     * 按项目级别统计
     */
    Map<String, Integer> getStatsByXmjb();

    /**
     * 按执行状态统计
     */
    Map<String, Integer> getStatsByXmzxzt();

    /**
     * 按审核状态统计
     */
    Map<String, Integer> getStatsByShzt();

    /**
     * 获取项目级别选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getXmjbOptions();

    /**
     * 获取执行状态选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getXmzxztOptions();

    /**
     * 获取审核状态选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getShztOptions();

    /**
     * 统计教师个人科技项目数量
     */
    Long getPersonalCount(String zgh);

    /**
     * 获取科技项目综合统计数据
     */
    KjxmStatisticsDTO.ComprehensiveStatistics getComprehensiveStatistics();

    // ========== 人才匹配算法专用方法 ==========

    /**
     * 检查教师是否主持国家自然科学基金面上项目
     */
    boolean hasNationalNaturalScienceFund(String teacherZgh);

    /**
     * 检查教师是否主持国家社会科学基金
     */
    boolean hasNationalSocialScienceFund(String teacherZgh);

    /**
     * 检查教师是否主持广西杰出青年基金
     */
    boolean hasGuangxiYouthFund(String teacherZgh);

    /**
     * 检查教师科研经费是否达标
     */
    boolean hasAdequateResearchFunding(String teacherZgh);

    /**
     * 统计教师项目数量（按级别）
     */
    Map<String, Integer> countProjectsByLevel(String teacherZgh);

    /**
     * 统计教师项目总数
     */
    int countByTeacher(String teacherZgh);
}
