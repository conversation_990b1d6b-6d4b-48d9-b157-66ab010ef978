package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxProjectTypes;
import com.gl.gl_lg_java.mapper.RchxProjectTypesMapper;
import com.gl.gl_lg_java.service.RchxProjectTypesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目类别表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@Service
public class RchxProjectTypesServiceImpl extends ServiceImpl<RchxProjectTypesMapper, RchxProjectTypes> 
        implements RchxProjectTypesService {

    @Override
    public List<RchxProjectTypes> getEnabledTypes() {
        QueryWrapper<RchxProjectTypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_enabled", true)
                   .orderByAsc("category_id", "sort_order", "id");
        return list(queryWrapper);
    }

    @Override
    public List<RchxProjectTypes> getEnabledTypesByCategoryId(Integer categoryId) {
        QueryWrapper<RchxProjectTypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_id", categoryId)
                   .eq("is_enabled", true)
                   .orderByAsc("sort_order", "id");
        return list(queryWrapper);
    }

    @Override
    public RchxProjectTypes getByTypeCode(String typeCode) {
        QueryWrapper<RchxProjectTypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type_code", typeCode);
        return getOne(queryWrapper);
    }

    @Override
    public boolean updateEnabled(Integer id, Boolean enabled) {
        try {
            RchxProjectTypes type = new RchxProjectTypes();
            type.setId(id);
            type.setIsEnabled(enabled);
            return updateById(type);
        } catch (Exception e) {
            log.error("更新项目类别启用状态失败: id={}, enabled={}", id, enabled, e);
            return false;
        }
    }

    @Override
    public boolean existsByTypeCode(String typeCode, Integer excludeId) {
        QueryWrapper<RchxProjectTypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type_code", typeCode);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) > 0;
    }
}
