# WebSocket长连接优化方案

## 📋 概述

本文档描述了将WebSocket通知系统从短连接模式升级为长连接模式（类似微信/QQ）的技术方案，包括前端调整和后端配合要求。

## 🎯 目标

- 实现像微信/QQ一样的长连接机制
- 登录后保持持久连接，减少频繁重连
- 提供真正的实时通知体验
- 优化网络资源使用和用户体验

---

## 🔧 前端调整详情

### 1. WebSocket连接管理优化

#### 1.1 长连接配置参数
```javascript
// 原配置
{
  maxReconnectAttempts: 3,        // 最大重连3次
  reconnectInterval: 10000,       // 重连间隔10秒
  heartbeatInterval: 60000,       // 心跳60秒
}

// 新配置 - 长连接模式
{
  maxReconnectAttempts: 999,      // 几乎无限重连
  baseReconnectInterval: 1000,    // 基础重连间隔1秒
  maxReconnectInterval: 30000,    // 最大重连间隔30秒
  heartbeatInterval: 30000,       // 心跳30秒
  heartbeatTimeout: 10000,        // 心跳超时10秒
}
```

#### 1.2 智能重连策略
- **前3次快速重连**：1秒、2秒、3秒
- **4-10次中等间隔**：5秒间隔
- **10次以后长间隔**：30秒间隔，保持长连接

#### 1.3 网络状态感知
```javascript
// 监听网络状态变化
window.addEventListener('online', handleNetworkOnline)
window.addEventListener('offline', handleNetworkOffline)

// 监听页面可见性变化
document.addEventListener('visibilitychange', handleVisibilityChange)
```

### 2. 心跳检测机制优化

#### 2.1 智能心跳发送
- 只在页面可见且网络正常时发送心跳
- 心跳消息包含时间戳和客户端ID
- 连续3次心跳超时自动重连

#### 2.2 心跳消息格式
```javascript
// 发送心跳
{
  type: 'heartbeat',
  timestamp: Date.now(),
  clientId: token
}

// 期望响应
{
  type: 'pong',
  timestamp: Date.now(),
  clientTimestamp: originalTimestamp
}
```

### 3. 用户界面优化

#### 3.1 连接状态显示
- 实时显示连接状态（连接中/已连接/重连中）
- 显示重连次数和进度
- 不同状态使用不同图标和颜色

#### 3.2 通知面板改进
- 添加关闭按钮，用户可手动关闭面板
- 点击面板外部自动关闭
- 优化面板定位和样式

---

## 🔧 后端配合要求

### 1. WebSocket配置调整

#### 1.1 连接超时配置
```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new NotificationWebSocketHandler(), "/ws/teacher-notifications")
                .setAllowedOrigins("*")
                .withSockJS()
                .setHeartbeatTime(25000)        // 心跳间隔25秒
                .setDisconnectDelay(5000)       // 断开延迟5秒
                .setSessionCookieNeeded(false);
    }
}
```

#### 1.2 应用配置优化
```yaml
# application.yml
server:
  tomcat:
    connection-timeout: 60000      # 连接超时60秒
    keep-alive-timeout: 60000      # Keep-Alive超时60秒
    max-keep-alive-requests: 1000  # 最大Keep-Alive请求数

spring:
  websocket:
    max-text-message-buffer-size: 8192
    max-binary-message-buffer-size: 8192
    connection-timeout: 60000
```

### 2. 心跳处理逻辑

#### 2.1 心跳响应处理
```java
private void handleHeartbeat(WebSocketSession session, String userId, Map<String, Object> msg) {
    // 更新最后心跳时间
    lastHeartbeat.put(userId, System.currentTimeMillis());
    
    // 回复心跳响应
    sendMessage(session, Map.of(
        "type", "pong",
        "timestamp", System.currentTimeMillis(),
        "clientTimestamp", msg.get("timestamp")
    ));
    
    log.debug("收到用户 {} 心跳", userId);
}
```

#### 2.2 连接池管理
```java
// 管理所有活跃连接
private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
private final Map<String, Long> lastHeartbeat = new ConcurrentHashMap<>();
```

### 3. 僵尸连接清理

#### 3.1 定时清理任务
```java
@Scheduled(fixedRate = 60000) // 每分钟执行一次
public void cleanupDeadConnections() {
    long now = System.currentTimeMillis();
    long timeout = 90000; // 90秒超时
    
    // 查找超时连接
    List<String> deadConnections = lastHeartbeat.entrySet().stream()
        .filter(entry -> now - entry.getValue() > timeout)
        .map(Map.Entry::getKey)
        .collect(Collectors.toList());
    
    // 清理僵尸连接
    deadConnections.forEach(this::removeConnection);
}
```

### 4. 数据库连接池优化

#### 4.1 HikariCP配置
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20          # 最大连接数
      minimum-idle: 5                # 最小空闲连接
      connection-timeout: 30000      # 连接超时30秒
      idle-timeout: 600000           # 空闲超时10分钟
      max-lifetime: 1800000          # 连接最大生命周期30分钟
      leak-detection-threshold: 60000 # 连接泄漏检测60秒
```

---

## 📊 关键改动对比

| 项目 | 原方案 | 新方案（长连接） | 说明 |
|------|--------|------------------|------|
| 最大重连次数 | 3次 | 999次 | 几乎无限重连 |
| 重连间隔 | 固定10秒 | 智能递增1-30秒 | 前期快速，后期稳定 |
| 心跳间隔 | 60秒 | 30秒 | 保持连接活跃 |
| 心跳超时 | 无 | 10秒 | 及时发现连接问题 |
| 网络感知 | 无 | 有 | 网络恢复自动重连 |
| 页面感知 | 无 | 有 | 后台时优化资源 |
| 连接清理 | 无 | 定时清理 | 防止内存泄漏 |

---

## ✅ 实施检查清单

### 前端检查项
- [ ] WebSocket工具类已更新为长连接版本
- [ ] 通知组件已集成新的连接状态显示
- [ ] 心跳检测机制已优化
- [ ] 网络状态监听已添加
- [ ] 页面可见性监听已添加
- [ ] 通知面板关闭按钮已添加

### 后端检查项
- [ ] WebSocket配置已调整超时参数
- [ ] 心跳响应处理已实现
- [ ] 连接池管理已优化
- [ ] 僵尸连接清理任务已添加
- [ ] 数据库连接池已优化
- [ ] 应用配置已更新

### 测试验证项
- [ ] 长连接建立成功
- [ ] 心跳机制正常工作
- [ ] 网络断开自动重连
- [ ] 页面切换连接管理正常
- [ ] 通知实时性验证
- [ ] 内存使用情况正常

---

## 🚀 预期效果

1. **连接稳定性提升**：从频繁重连变为持久连接
2. **实时性改善**：真正的实时通知，无延迟
3. **用户体验优化**：类似微信/QQ的连接体验
4. **资源使用优化**：智能的网络和CPU资源管理
5. **可维护性提升**：完善的状态监控和错误处理
