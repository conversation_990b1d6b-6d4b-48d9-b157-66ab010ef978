package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxSchoolProjectCategories;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 校级项目类别Service接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface RchxSchoolProjectCategoriesService extends IService<RchxSchoolProjectCategories> {

    /**
     * 分页查询项目类别（包含统计信息）
     * 
     * @param page 分页参数
     * @param categoryName 类别名称
     * @param isEnabled 是否启用
     * @return 分页结果
     */
    IPage<Map<String, Object>> pageQueryWithStats(Page<RchxSchoolProjectCategories> page,
                                                  String categoryName,
                                                  Boolean isEnabled);

    /**
     * 查询启用的项目类别（按排序）
     * 
     * @return 启用的类别列表
     */
    List<RchxSchoolProjectCategories> getEnabledCategories();

    /**
     * 根据类别编码查询
     * 
     * @param categoryCode 类别编码
     * @return 类别信息
     */
    RchxSchoolProjectCategories getByCategoryCode(String categoryCode);

    /**
     * 检查类别编码是否存在
     * 
     * @param categoryCode 类别编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsByCategoryCode(String categoryCode, Integer excludeId);

    /**
     * 创建项目类别
     * 
     * @param category 类别信息
     * @return 是否成功
     */
    boolean createCategory(RchxSchoolProjectCategories category);

    /**
     * 更新项目类别
     * 
     * @param category 类别信息
     * @return 是否成功
     */
    boolean updateCategory(RchxSchoolProjectCategories category);

    /**
     * 删除项目类别
     * 
     * @param id 类别ID
     * @return 是否成功
     */
    boolean deleteCategory(Integer id);

    /**
     * 批量删除项目类别
     * 
     * @param ids 类别ID列表
     * @return 是否成功
     */
    boolean batchDeleteCategories(List<Integer> ids);

    /**
     * 启用/禁用类别
     * 
     * @param id 类别ID
     * @param isEnabled 是否启用
     * @return 是否成功
     */
    boolean toggleCategoryStatus(Integer id, Boolean isEnabled);

    /**
     * 调整类别排序
     * 
     * @param id 类别ID
     * @param sortOrder 排序号
     * @return 是否成功
     */
    boolean updateSortOrder(Integer id, Integer sortOrder);

    /**
     * 获取类别统计信息
     * 
     * @return 统计信息
     */
    List<Map<String, Object>> getCategoryStatistics();

    /**
     * 根据资助金额范围查询类别
     * 
     * @param fundingAmount 资助金额
     * @return 符合条件的类别列表
     */
    List<RchxSchoolProjectCategories> getCategoriesByFundingRange(BigDecimal fundingAmount);

    /**
     * 生成类别编码
     * 
     * @param categoryName 类别名称
     * @return 类别编码
     */
    String generateCategoryCode(String categoryName);

    /**
     * 导出类别列表
     * 
     * @param isEnabled 是否启用
     * @return 类别列表
     */
    List<RchxSchoolProjectCategories> exportCategoryList(Boolean isEnabled);
}
