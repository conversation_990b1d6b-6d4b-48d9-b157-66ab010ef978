package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxZlcgjbxx;
import com.gl.gl_lg_java.dto.ZlcgjbxxQueryDTO;
import com.gl.gl_lg_java.dto.ZlcgStatisticsDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_zlcgjbxx(专利成果基本信息)】的数据库操作Service
 * @createDate 2025-07-18 11:11:41
 */
public interface RchxZlcgjbxxService extends IService<RchxZlcgjbxx> {

    /**
     * 根据专利成果编号查询
     */
    RchxZlcgjbxx getByZlcgbh(String zlcgbh);

    /**
     * 根据专利成果名称模糊查询
     */
    List<RchxZlcgjbxx> listByZlcgmcLike(String zlcgmc);

    /**
     * 根据单位号查询
     */
    List<RchxZlcgjbxx> listByDwh(String dwh);

    /**
     * 根据单位名称模糊查询
     */
    List<RchxZlcgjbxx> listByDwmcLike(String dwmc);

    /**
     * 根据专利类型查询
     */
    List<RchxZlcgjbxx> listByZllx(String zllx);

    /**
     * 根据专利状态查询
     */
    List<RchxZlcgjbxx> listByZlzt(String zlzt);

    /**
     * 根据审核状态查询
     */
    List<RchxZlcgjbxx> listByShzt(String shzt);

    /**
     * 根据第一发明人职工号查询
     */
    List<RchxZlcgjbxx> listByDyfmrzgh(String dyfmrzgh);

    /**
     * 根据第一发明人姓名模糊查询
     */
    List<RchxZlcgjbxx> listByDyfmrLike(String dyfmr);

    /**
     * 根据批准日期范围查询
     */
    List<RchxZlcgjbxx> listByPzrqBetween(String startDate, String endDate);

    /**
     * 根据申请日期范围查询
     */
    List<RchxZlcgjbxx> listBySqzlrqBetween(String startDate, String endDate);

    /**
     * 多条件查询
     */
    List<RchxZlcgjbxx> listByMultiConditions(ZlcgjbxxQueryDTO queryDTO);

    /**
     * 分页多条件查询
     */
    IPage<RchxZlcgjbxx> pageByMultiConditions(ZlcgjbxxQueryDTO queryDTO);

    /**
     * 新增专利成果信息
     */
    boolean saveZlcgjbxx(RchxZlcgjbxx zlcgjbxx);

    /**
     * 根据编号更新专利成果信息
     */
    boolean updateByZlcgbh(RchxZlcgjbxx zlcgjbxx);

    /**
     * 根据编号删除专利成果信息
     */
    boolean removeByZlcgbh(String zlcgbh);

    /**
     * 批量删除专利成果信息
     */
    boolean removeBatchByZlcgbhs(List<String> zlcgbhs);

    /**
     * 批量新增专利成果信息
     */
    boolean saveBatchZlcgjbxx(List<RchxZlcgjbxx> zlcgjbxxList);

    /**
     * 批量更新专利成果信息
     */
    boolean updateBatchZlcgjbxx(List<RchxZlcgjbxx> zlcgjbxxList);

    /**
     * 按专利类型统计
     */
    Map<String, Integer> getStatsByZllx();

    /**
     * 按专利状态统计
     */
    Map<String, Integer> getStatsByZlzt();

    /**
     * 按审核状态统计
     */
    Map<String, Integer> getStatsByShzt();

    /**
     * 获取专利类型选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getZllxOptions();

    /**
     * 获取专利状态选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getZlztOptions();

    /**
     * 获取审核状态选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getShztOptions();

    /**
     * 统计教师个人专利成果数量
     */
    Long getPersonalCount(String zgh);

    /**
     * 获取专利成果综合统计数据
     */
    ZlcgStatisticsDTO.ComprehensiveStatistics getComprehensiveStatistics();

    // ========== 人才匹配算法专用方法 ==========

    /**
     * 检查教师是否有发明专利（指定数量）
     */
    boolean hasInventionPatents(String teacherZgh, int minCount);

    /**
     * 统计教师专利总数
     */
    int countByTeacher(String teacherZgh);

    /**
     * 统计教师发明专利数量
     */
    int countInventionPatentsByTeacher(String teacherZgh);
}
