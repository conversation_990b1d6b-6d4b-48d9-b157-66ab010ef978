package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gl.gl_lg_java.annotation.RequirePermission;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxZlcgjbxx;
import com.gl.gl_lg_java.dto.ZlcgjbxxQueryDTO;
import com.gl.gl_lg_java.dto.ZlcgStatisticsDTO;
import com.gl.gl_lg_java.service.RchxZlcgjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
/**
// * 专利成果基本信息控制器
 */
@RestController
@RequestMapping("/api/zlcgjbxx")
@Slf4j
public class ZlcgjbxxController {
    
    @Autowired
    private RchxZlcgjbxxService zlcgjbxxService;
    
    /**
     * 根据专利成果编号查询
     */
    @GetMapping("/{zlcgbh}")
    @RequirePermission("教师")
    public Result<RchxZlcgjbxx> getByZlcgbh(@PathVariable String zlcgbh) {
        try {
            RchxZlcgjbxx result = zlcgjbxxService.getByZlcgbh(zlcgbh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询专利成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询专利成果信息
     */
    @PostMapping("/page")
    @RequirePermission("教师")
    public Result<IPage<RchxZlcgjbxx>> getPage(@RequestBody ZlcgjbxxQueryDTO queryDTO) {
        try {
            IPage<RchxZlcgjbxx> result = zlcgjbxxService.pageByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询专利成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 多条件查询专利成果信息
     */
    @PostMapping("/list")
    @RequirePermission("教师")
    public Result<List<RchxZlcgjbxx>> getList(@RequestBody ZlcgjbxxQueryDTO queryDTO) {
        try {
            List<RchxZlcgjbxx> result = zlcgjbxxService.listByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询专利成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据专利成果名称模糊查询
     */
    @GetMapping("/name/{zlcgmc}")
    @RequirePermission("教师")
    public Result<List<RchxZlcgjbxx>> getByZlcgmcLike(@PathVariable String zlcgmc) {
        try {
            List<RchxZlcgjbxx> result = zlcgjbxxService.listByZlcgmcLike(zlcgmc);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据名称查询专利成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据第一发明人职工号查询
     */
    @GetMapping("/inventor/{dyfmrzgh}")
    @RequirePermission("教师")
    public Result<List<RchxZlcgjbxx>> getByDyfmrzgh(@PathVariable String dyfmrzgh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的专利
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(dyfmrzgh)) {
                return Result.error(403, "权限不足，只能查看自己的专利信息");
            }
            
            List<RchxZlcgjbxx> result = zlcgjbxxService.listByDyfmrzgh(dyfmrzgh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据发明人查询专利成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增专利成果信息
     */
    @PostMapping
    @RequirePermission("学院管理员")
    public Result<String> create(@RequestBody RchxZlcgjbxx zlcgjbxx) {
        try {
            boolean success = zlcgjbxxService.saveZlcgjbxx(zlcgjbxx);
            if (success) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增专利成果信息失败: {}", e.getMessage());
            return Result.error("新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新专利成果信息
     */
    @PutMapping
    @RequirePermission("学院管理员")
    public Result<String> update(@RequestBody RchxZlcgjbxx zlcgjbxx) {
        try {
            boolean success = zlcgjbxxService.updateByZlcgbh(zlcgjbxx);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新专利成果信息失败: {}", e.getMessage());
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除专利成果信息
     */
    @DeleteMapping("/{zlcgbh}")
    @RequirePermission("系统管理员")
    public Result<String> delete(@PathVariable String zlcgbh) {
        try {
            boolean success = zlcgjbxxService.removeByZlcgbh(zlcgbh);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除专利成果信息失败: {}", e.getMessage());
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除专利成果信息
     */
    @DeleteMapping("/batch")
    @RequirePermission("系统管理员")
    public Result<String> deleteBatch(@RequestBody List<String> zlcgbhs) {
        try {
            boolean success = zlcgjbxxService.removeBatchByZlcgbhs(zlcgbhs);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除专利成果信息失败: {}", e.getMessage());
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量新增专利成果信息
     */
    @PostMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> createBatch(@RequestBody List<RchxZlcgjbxx> zlcgjbxxList) {
        try {
            boolean success = zlcgjbxxService.saveBatchZlcgjbxx(zlcgjbxxList);
            if (success) {
                return Result.success("批量新增成功");
            } else {
                return Result.error("批量新增失败");
            }
        } catch (Exception e) {
            log.error("批量新增专利成果信息失败: {}", e.getMessage());
            return Result.error("批量新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新专利成果信息
     */
    @PutMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> updateBatch(@RequestBody List<RchxZlcgjbxx> zlcgjbxxList) {
        try {
            boolean success = zlcgjbxxService.updateBatchZlcgjbxx(zlcgjbxxList);
            if (success) {
                return Result.success("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新专利成果信息失败: {}", e.getMessage());
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据专利类型查询
     */
    @GetMapping("/type/{zllx}")
    @RequirePermission("教师")
    public Result<List<RchxZlcgjbxx>> getByZllx(@PathVariable String zllx) {
        try {
            List<RchxZlcgjbxx> result = zlcgjbxxService.listByZllx(zllx);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据专利类型查询失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据专利状态查询
     */
    @GetMapping("/status/{zlzt}")
    @RequirePermission("教师")
    public Result<List<RchxZlcgjbxx>> getByZlzt(@PathVariable String zlzt) {
        try {
            List<RchxZlcgjbxx> result = zlcgjbxxService.listByZlzt(zlzt);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据专利状态查询失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据审核状态查询
     */
    @GetMapping("/audit/{shzt}")
    @RequirePermission("评审")
    public Result<List<RchxZlcgjbxx>> getByShzt(@PathVariable String shzt) {
        try {
            List<RchxZlcgjbxx> result = zlcgjbxxService.listByShzt(shzt);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据审核状态查询失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按专利类型统计
     */
    @GetMapping("/stats/zllx")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByZllx() {
        try {
            log.info("查询专利类型统计");
            Map<String, Integer> stats = zlcgjbxxService.getStatsByZllx();
            log.info("专利类型统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询专利类型统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按专利状态统计
     */
    @GetMapping("/stats/zlzt")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByZlzt() {
        try {
            log.info("查询专利状态统计");
            Map<String, Integer> stats = zlcgjbxxService.getStatsByZlzt();
            log.info("专利状态统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询专利状态统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按审核状态统计
     */
    @GetMapping("/stats/shzt")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByShzt() {
        try {
            log.info("查询审核状态统计");
            Map<String, Integer> stats = zlcgjbxxService.getStatsByShzt();
            log.info("审核状态统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询审核状态统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取专利类型选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/zllx-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getZllxOptions() {
        try {
            List<Map<String, Object>> options = zlcgjbxxService.getZllxOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取专利类型选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取专利状态选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/zlzt-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getZlztOptions() {
        try {
            List<Map<String, Object>> options = zlcgjbxxService.getZlztOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取专利状态选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核状态选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/shzt-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getShztOptions() {
        try {
            List<Map<String, Object>> options = zlcgjbxxService.getShztOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取审核状态选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 统计教师个人专利成果数量
     * @param zgh 职工号，可选参数。如果不传则查询当前登录用户
     */
    @GetMapping("/personal-count")
    @RequirePermission("教师")
    public Result<Map<String, Object>> getPersonalCount(
            @RequestParam(value = "zgh", required = false) String zgh,
            HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");

            // 如果没传zgh参数，则查询当前登录用户
            if (zgh == null || zgh.trim().isEmpty()) {
                zgh = currentUserZgh;
            }

            // 权限控制：普通教师只能查询自己的数据
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(zgh)) {
                return Result.error(403, "权限不足，只能查询自己的专利成果数量");
            }

            log.info("查询教师个人专利成果数量，职工号: {}", zgh);

            Long count = zlcgjbxxService.getPersonalCount(zgh);

            Map<String, Object> result = new HashMap<>();
            result.put("zgh", zgh);
            result.put("count", count);
            result.put("type", "专利成果");

            log.info("教师个人专利成果数量查询成功，职工号: {}, 数量: {}", zgh, count);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询教师个人专利成果数量失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取专利成果综合统计数据
     * 包括：单位统计、专利类型统计、第一发明人TOP10、总体统计
     */
    @GetMapping("/statistics")
    public Result<ZlcgStatisticsDTO.ComprehensiveStatistics> getStatistics() {
        try {
            log.info("开始获取专利成果统计数据");

            ZlcgStatisticsDTO.ComprehensiveStatistics statistics = zlcgjbxxService.getComprehensiveStatistics();

            log.info("专利成果统计数据获取成功: 专利总数={}, 单位总数={}, 专利类型总数={}",
                    statistics.getTotalPatents(), statistics.getTotalUnits(), statistics.getTotalPatentTypes());

            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取专利成果统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取单位专利统计（单独接口）
     */
    @GetMapping("/statistics/units")
    public Result<List<ZlcgStatisticsDTO.UnitStatistics>> getUnitStatistics() {
        try {
            log.info("开始获取单位专利统计数据");

            ZlcgStatisticsDTO.ComprehensiveStatistics statistics = zlcgjbxxService.getComprehensiveStatistics();

            log.info("单位专利统计数据获取成功，共{}个单位", statistics.getUnitStatistics().size());

            return Result.success(statistics.getUnitStatistics());

        } catch (Exception e) {
            log.error("获取单位专利统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取专利类型统计（单独接口）
     */
    @GetMapping("/statistics/patent-types")
    public Result<List<ZlcgStatisticsDTO.PatentTypeStatistics>> getPatentTypeStatistics() {
        try {
            log.info("开始获取专利类型统计数据");

            ZlcgStatisticsDTO.ComprehensiveStatistics statistics = zlcgjbxxService.getComprehensiveStatistics();

            log.info("专利类型统计数据获取成功，共{}个类型", statistics.getPatentTypeStatistics().size());

            return Result.success(statistics.getPatentTypeStatistics());

        } catch (Exception e) {
            log.error("获取专利类型统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取第一发明人TOP10统计（单独接口）
     */
    @GetMapping("/statistics/top-first-inventors")
    public Result<List<ZlcgStatisticsDTO.FirstInventorStatistics>> getTopFirstInventors() {
        try {
            log.info("开始获取第一发明人TOP10统计数据");

            ZlcgStatisticsDTO.ComprehensiveStatistics statistics = zlcgjbxxService.getComprehensiveStatistics();

            log.info("第一发明人TOP10统计数据获取成功，共{}个发明人", statistics.getTopFirstInventors().size());

            return Result.success(statistics.getTopFirstInventors());

        } catch (Exception e) {
            log.error("获取第一发明人TOP10统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }
}
