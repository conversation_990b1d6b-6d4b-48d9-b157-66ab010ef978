# 剩余实体类CRUD接口生成说明

## 需要生成CRUD的实体类

根据项目中的Mapper文件，以下实体类需要创建完整的CRUD接口：

### 1. RchxKjxmjbxx - 科技项目基本信息
- **主键**: xmid (项目主键)
- **核心字段**: xmbh(项目编号), xmmc(项目名称), fzrxm(负责人姓名), xmfzrh(项目负责人号)
- **状态字段**: xmzxzt(项目执行状态), shzt(审核状态)
- **时间字段**: lxrq(立项日期), ksrq(开始日期), jhwcrq(计划完成日期), jxrq(结项日期)

### 2. RchxXmysxx - 项目预算信息
- **关联**: 与科技项目关联
- **核心字段**: 预算相关字段

### 3. RchxHjcgjbxx - 获奖成果基本信息
- **主键**: hjcgbh (获奖成果编号)
- **核心字段**: hjcgmc(获奖成果名称), jlmc(奖励名称), dywcrxm(第一完成人姓名)
- **状态字段**: shzt(审核状态)

### 4. RchxKjlwjbxx - 科技论文基本信息
- **核心字段**: 论文相关信息

### 5. RchxJzggzjlxx - 教职工工作简历信息
- **关联**: 与教职工基本信息关联
- **核心字段**: zgh(职工号), gzdw(工作单位), gznr(工作内容)

## 统一CRUD接口设计

为了快速生成，我将为每个实体类创建标准的CRUD接口：

### Mapper层
- 基础查询方法（根据主键、核心字段查询）
- 模糊查询方法（支持名称、编号等模糊查询）
- 多条件查询方法
- 分页查询方法

### Service层
- 继承IService<T>
- 自定义查询方法
- CRUD操作方法
- 批量操作方法

### Controller层
- RESTful API设计
- 权限控制注解
- 统一响应格式
- 异常处理

### DTO层
- 查询条件封装
- 分页参数
- 排序参数

## 权限设计

### 科技项目管理
- **教师**: 查看自己负责的项目
- **评审**: 查看项目信息，进行项目评审
- **学院管理员**: 管理本学院的项目
- **系统管理员**: 管理所有项目

### 获奖成果管理
- **教师**: 查看自己的获奖成果
- **评审**: 查看和评审获奖成果
- **学院管理员**: 管理本学院的获奖成果
- **系统管理员**: 管理所有获奖成果

### 科技论文管理
- **教师**: 查看自己的论文
- **评审**: 查看和评审论文
- **学院管理员**: 管理本学院的论文
- **系统管理员**: 管理所有论文

### 工作简历管理
- **教师**: 查看和修改自己的工作简历
- **学院管理员**: 查看本学院教职工的工作简历
- **系统管理员**: 管理所有工作简历

## API接口规范

### URL设计
- `/api/kjxmjbxx` - 科技项目基本信息
- `/api/xmysxx` - 项目预算信息
- `/api/hjcgjbxx` - 获奖成果基本信息
- `/api/kjlwjbxx` - 科技论文基本信息
- `/api/jzggzjlxx` - 教职工工作简历信息

### 标准接口
每个模块都包含以下标准接口：

#### 查询接口
- `GET /{id}` - 根据主键查询
- `POST /page` - 分页查询
- `POST /list` - 多条件查询
- `GET /name/{name}` - 根据名称模糊查询

#### 新增接口
- `POST /` - 新增单条记录
- `POST /batch` - 批量新增

#### 更新接口
- `PUT /` - 更新单条记录
- `PUT /batch` - 批量更新

#### 删除接口
- `DELETE /{id}` - 删除单条记录
- `DELETE /batch` - 批量删除

## 实现优先级

由于字段较多，建议按以下优先级实现：

### 第一优先级（核心功能）
1. **科技项目基本信息** - 项目管理核心
2. **获奖成果基本信息** - 成果管理重要模块

### 第二优先级（扩展功能）
3. **科技论文基本信息** - 学术成果管理
4. **教职工工作简历信息** - 人员履历管理

### 第三优先级（辅助功能）
5. **项目预算信息** - 财务管理辅助

## 快速生成方案

为了提高开发效率，我将：

1. **创建模板代码** - 基于已完成的教职工和专利成果模块
2. **批量生成接口** - 使用代码生成器快速创建
3. **统一测试** - 创建统一的测试用例
4. **完善文档** - 生成完整的API文档

## 下一步行动

1. 首先完成科技项目基本信息的完整CRUD
2. 然后批量生成其他实体类的CRUD接口
3. 最后完善权限控制和API文档

这样可以确保快速完成所有实体类的CRUD接口，同时保持代码质量和一致性。
