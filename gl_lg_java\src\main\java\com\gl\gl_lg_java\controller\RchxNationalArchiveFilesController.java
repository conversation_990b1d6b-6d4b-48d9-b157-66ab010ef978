package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxNationalArchiveFiles;
import com.gl.gl_lg_java.mapper.RchxNationalArchiveFilesMapper;

import com.gl.gl_lg_java.service.RchxNationalArchiveFilesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 国家/省部级项目归档文件管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@RestController
@RequestMapping("/api/national-archive-files")
@Slf4j
public class RchxNationalArchiveFilesController {

    @Autowired
    private RchxNationalArchiveFilesService filesService;

    // Mapper已移除，直接使用Service层

    /**
     * 分页查询归档文件
     */
    @PostMapping("/page")
    public Result<IPage<RchxNationalArchiveFiles>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long archiveId,
            @RequestParam(required = false) String projectCode,
            @RequestParam(required = false) String fileCategory,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String uploadBy,
            @RequestParam(required = false) Boolean isRequired,
            @RequestParam(required = false) Boolean isConfidential) {

        try {
            Page<RchxNationalArchiveFiles> page = new Page<>(current, size);

            // 使用QueryWrapper构建查询条件
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<RchxNationalArchiveFiles> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();

            // 默认只查询未删除的文件
            queryWrapper.eq("is_deleted", false);

            if (archiveId != null) {
                queryWrapper.eq("archive_id", archiveId);
            }
            if (projectCode != null && !projectCode.isEmpty()) {
                queryWrapper.eq("project_code", projectCode);
            }
            if (fileCategory != null && !fileCategory.isEmpty()) {
                queryWrapper.eq("file_category", fileCategory);
            }
            if (fileName != null && !fileName.isEmpty()) {
                queryWrapper.like("file_name", fileName);
            }
            if (uploadBy != null && !uploadBy.isEmpty()) {
                queryWrapper.eq("upload_by", uploadBy);
            }
            if (isRequired != null) {
                queryWrapper.eq("is_required", isRequired);
            }
            if (isConfidential != null) {
                queryWrapper.eq("is_confidential", isConfidential);
            }

            queryWrapper.orderByDesc("upload_time");

            IPage<RchxNationalArchiveFiles> result = filesService.page(page, queryWrapper);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询归档文件失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据归档ID查询文件
     */
    @GetMapping("/archive/{archiveId}")
    public Result<List<RchxNationalArchiveFiles>> getByArchiveId(@PathVariable Long archiveId) {
        try {
            List<RchxNationalArchiveFiles> files = filesService.listByArchiveId(archiveId);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据归档ID查询文件失败: {}", archiveId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目编号查询文件
     */
    @GetMapping("/project/{projectCode}")
    public Result<List<RchxNationalArchiveFiles>> getByProjectCode(@PathVariable String projectCode) {
        try {
            List<RchxNationalArchiveFiles> files = filesService.listByProjectCode(projectCode);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据项目编号查询文件失败: {}", projectCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件类别查询
     */
    @GetMapping("/category/{fileCategory}")
    public Result<List<RchxNationalArchiveFiles>> getByFileCategory(@PathVariable String fileCategory) {
        try {
            List<RchxNationalArchiveFiles> files = filesService.listByFileCategory(fileCategory);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据文件类别查询失败: {}", fileCategory, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据归档ID和文件类别查询
     */
    @GetMapping("/archive/{archiveId}/category/{fileCategory}")
    public Result<List<RchxNationalArchiveFiles>> getByArchiveIdAndCategory(
            @PathVariable Long archiveId, @PathVariable String fileCategory) {
        try {
            List<RchxNationalArchiveFiles> files = filesService.listByArchiveIdAndCategory(archiveId, fileCategory);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据归档ID和文件类别查询失败: archiveId={}, category={}", archiveId, fileCategory, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询文件
     */
    @GetMapping("/{id}")
    public Result<RchxNationalArchiveFiles> getById(@PathVariable Long id) {
        try {
            RchxNationalArchiveFiles file = filesService.getById(id);
            if (file != null && !file.getIsDeleted()) {
                return Result.success(file);
            } else {
                return Result.error("文件不存在");
            }
        } catch (Exception e) {
            log.error("根据ID查询文件失败: {}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public Result<RchxNationalArchiveFiles> uploadFile(
            @RequestParam Long archiveId,
            @RequestParam String projectCode,
            @RequestParam String fileCategory,
            @RequestParam MultipartFile file,
            @RequestParam String uploadBy,
            @RequestParam String uploadByName,
            @RequestParam(required = false) String description,
            @RequestParam(defaultValue = "false") Boolean isRequired,
            @RequestParam(defaultValue = "false") Boolean isConfidential) {

        try {
            RchxNationalArchiveFiles uploadedFile = filesService.uploadFile(
                    archiveId, projectCode, fileCategory, file, uploadBy, uploadByName,
                    description, isRequired, isConfidential);
            return Result.success(uploadedFile);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传文件
     */
    @PostMapping("/upload/batch")
    public Result<List<RchxNationalArchiveFiles>> batchUploadFiles(
            @RequestParam Long archiveId,
            @RequestParam String projectCode,
            @RequestParam String fileCategory,
            @RequestParam List<MultipartFile> files,
            @RequestParam String uploadBy,
            @RequestParam String uploadByName,
            @RequestParam(defaultValue = "false") Boolean isRequired,
            @RequestParam(defaultValue = "false") Boolean isConfidential) {

        try {
            List<RchxNationalArchiveFiles> uploadedFiles = filesService.batchUploadFiles(
                    archiveId, projectCode, fileCategory, files, uploadBy, uploadByName, isRequired, isConfidential);
            return Result.success(uploadedFiles);
        } catch (Exception e) {
            log.error("批量文件上传失败", e);
            return Result.error("批量文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     */
    @GetMapping("/{id}/download")
    public ResponseEntity<byte[]> downloadFile(@PathVariable Long id) {
        try {
            RchxNationalArchiveFiles file = filesService.getById(id);
            if (file == null || file.getIsDeleted()) {
                return ResponseEntity.notFound().build();
            }

            byte[] fileData = filesService.downloadFile(id);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", file.getFileOriginalName());

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileData);

        } catch (Exception e) {
            log.error("文件下载失败: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取文件下载URL
     */
    @GetMapping("/{id}/download-url")
    public Result<String> getFileDownloadUrl(@PathVariable Long id) {
        try {
            String downloadUrl = filesService.getFileDownloadUrl(id);
            return Result.success(downloadUrl);
        } catch (Exception e) {
            log.error("获取文件下载URL失败: {}", id, e);
            return Result.error("获取下载URL失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件信息
     */
    @PutMapping("/{id}")
    public Result<String> updateFileInfo(@PathVariable Long id,
            @RequestParam String fileName,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Boolean isRequired,
            @RequestParam(required = false) Boolean isConfidential) {
        try {
            boolean success = filesService.updateFileInfo(id, fileName, description, isRequired, isConfidential);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新文件信息失败: {}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件（逻辑删除）
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteFile(@PathVariable Long id, @RequestParam String deleteBy) {
        try {
            boolean success = filesService.deleteFile(id, deleteBy);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除文件失败: {}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除文件（逻辑删除）
     */
    @DeleteMapping("/batch")
    public Result<String> batchDeleteFiles(@RequestBody List<Long> fileIds, @RequestParam String deleteBy) {
        try {
            boolean success = filesService.batchDeleteFiles(fileIds, deleteBy);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除文件失败: {}", fileIds, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 物理删除文件
     */
    @DeleteMapping("/{id}/physical")
    public Result<String> physicalDeleteFile(@PathVariable Long id) {
        try {
            boolean success = filesService.physicalDeleteFile(id);
            if (success) {
                return Result.success("物理删除成功");
            } else {
                return Result.error("物理删除失败");
            }
        } catch (Exception e) {
            log.error("物理删除文件失败: {}", id, e);
            return Result.error("物理删除失败: " + e.getMessage());
        }
    }

    /**
     * 检查归档项目的文件完整性
     */
    @GetMapping("/archive/{archiveId}/completeness")
    public Result<Map<String, Object>> checkArchiveFileCompleteness(@PathVariable Long archiveId) {
        try {
            Map<String, Object> completeness = filesService.checkArchiveFileCompleteness(archiveId);
            return Result.success(completeness);
        } catch (Exception e) {
            log.error("检查文件完整性失败: {}", archiveId, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 统计各文件类别的数量
     */
    @GetMapping("/statistics/category")
    public Result<List<Map<String, Object>>> countByFileCategory() {
        try {
            List<Map<String, Object>> statistics = filesService.countByFileCategory();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("统计文件类别数量失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 统计各归档项目的文件数量
     */
    @GetMapping("/statistics/archive")
    public Result<List<Map<String, Object>>> countByArchiveId() {
        try {
            List<Map<String, Object>> statistics = filesService.countByArchiveId();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("统计归档文件数量失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件存储统计信息
     */
    @GetMapping("/statistics/storage")
    public Result<Map<String, Object>> getFileStatistics() {
        try {
            Map<String, Object> statistics = filesService.getFileStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取文件存储统计失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }
}
