# 项目管理系统API文档

## 📋 概述

本文档描述了项目管理系统的所有API接口，包括基础字典管理、项目全生命周期管理（征集、申报、中检、结项）和文件管理等功能。

### 🔗 基础信息

- **服务地址**: `http://localhost:8080`
- **API前缀**: `/api`
- **数据格式**: JSON
- **字符编码**: UTF-8

### 📝 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

**响应状态码说明：**
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

---

## 🏢 1. 基础字典管理

### 1.1 项目大类管理

#### 分页查询项目大类
```http
GET /api/project-categories/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryName | String | 否 | - | 大类名称（模糊查询） |
| isEnabled | Boolean | 否 | - | 是否启用 |

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "categoryCode": "TECH",
        "categoryName": "技术类项目",
        "description": "技术研发类项目",
        "sortOrder": 1,
        "isEnabled": true,
        "createTime": "2024-08-04T10:00:00",
        "updateTime": "2024-08-04T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 获取所有启用的项目大类
```http
GET /api/project-categories/enabled
```

#### 根据ID查询项目大类
```http
GET /api/project-categories/{id}
```

#### 根据大类编码查询项目大类
```http
GET /api/project-categories/code/{categoryCode}
```

#### 新增项目大类
```http
POST /api/project-categories
```

**请求体：**
```json
{
  "categoryCode": "TECH",
  "categoryName": "技术类项目",
  "description": "技术研发类项目",
  "sortOrder": 1,
  "isEnabled": true
}
```

#### 更新项目大类
```http
PUT /api/project-categories/{id}
```

#### 删除项目大类
```http
DELETE /api/project-categories/{id}
```

#### 启用/禁用项目大类
```http
PUT /api/project-categories/{id}/enabled?enabled=true
```

### 1.2 项目类别管理

#### 分页查询项目类别
```http
GET /api/project-types/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryId | Integer | 否 | - | 所属大类ID |
| typeName | String | 否 | - | 类别名称（模糊查询） |
| isEnabled | Boolean | 否 | - | 是否启用 |

#### 获取所有启用的项目类别
```http
GET /api/project-types/enabled
```

#### 根据大类ID获取启用的项目类别
```http
GET /api/project-types/enabled/category/{categoryId}
```

#### 根据ID查询项目类别
```http
GET /api/project-types/{id}
```

#### 根据类别编码查询项目类别
```http
GET /api/project-types/code/{typeCode}
```

#### 新增项目类别
```http
POST /api/project-types
```

**请求体：**
```json
{
  "typeCode": "TECH_AI",
  "typeName": "人工智能技术",
  "categoryId": 1,
  "description": "人工智能相关技术研发",
  "sortOrder": 1,
  "isEnabled": true
}
```

#### 更新项目类别
```http
PUT /api/project-types/{id}
```

#### 删除项目类别
```http
DELETE /api/project-types/{id}
```

#### 启用/禁用项目类别
```http
PUT /api/project-types/{id}/enabled?enabled=true
```

### 1.3 管理部门管理

#### 分页查询管理部门
```http
GET /api/departments/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| deptName | String | 否 | - | 部门名称（模糊查询） |
| isEnabled | Boolean | 否 | - | 是否启用 |

#### 获取所有启用的管理部门
```http
GET /api/departments/enabled
```

#### 根据ID查询管理部门
```http
GET /api/departments/{id}
```

#### 根据部门编码查询管理部门
```http
GET /api/departments/code/{deptCode}
```

#### 新增管理部门
```http
POST /api/departments
```

**请求体：**
```json
{
  "deptCode": "TECH_DEPT",
  "deptName": "技术部",
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>",
  "description": "负责技术类项目管理",
  "sortOrder": 1,
  "isEnabled": true
}
```

#### 更新管理部门
```http
PUT /api/departments/{id}
```

#### 删除管理部门
```http
DELETE /api/departments/{id}
```

#### 启用/禁用管理部门
```http
PUT /api/departments/{id}/enabled?enabled=true
```

---

## 📊 2. 项目业务流程管理

### 2.1 项目选题征集管理

#### 分页查询项目征集
```http
GET /api/project-collection/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryId | Integer | 否 | - | 项目大类ID |
| typeId | Integer | 否 | - | 项目类别ID |
| deptId | Integer | 否 | - | 管理部门ID |
| status | String | 否 | - | 状态(DRAFT,PUBLISHED,CLOSED) |
| collectionName | String | 否 | - | 征集名称（模糊查询） |

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "collectionCode": "COLLECT_2024_001",
        "collectionName": "2024年技术创新项目征集",
        "categoryId": 1,
        "typeId": 1,
        "deptId": 1,
        "guideContent": "征集指南内容...",
        "acceptStartTime": "2024-08-01T00:00:00",
        "acceptEndTime": "2024-08-31T23:59:59",
        "status": "PUBLISHED",
        "submitTime": null,
        "reviewTime": null,
        "reviewerZgh": null,
        "reviewComments": null,
        "createTime": "2024-08-04T10:00:00",
        "updateTime": "2024-08-04T10:00:00",
        "createBy": "admin",
        "updateBy": "admin"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 获取当前有效的征集
```http
GET /api/project-collection/active
```

#### 根据ID查询项目征集
```http
GET /api/project-collection/{id}
```

#### 根据征集编号查询项目征集
```http
GET /api/project-collection/code/{collectionCode}
```

#### 新增项目征集
```http
POST /api/project-collection
```

**请求体：**
```json
{
  "collectionCode": "COLLECT_2024_001",
  "collectionName": "2024年技术创新项目征集",
  "categoryId": 1,
  "typeId": 1,
  "deptId": 1,
  "guideContent": "征集指南内容...",
  "acceptStartTime": "2024-08-01T00:00:00",
  "acceptEndTime": "2024-08-31T23:59:59",
  "status": "DRAFT",
  "createBy": "admin"
}
```

#### 更新项目征集
```http
PUT /api/project-collection/{id}
```

#### 删除项目征集
```http
DELETE /api/project-collection/{id}
```

#### 提交征集
```http
PUT /api/project-collection/{id}/submit?submitBy=admin
```

#### 审核征集
```http
PUT /api/project-collection/{id}/review
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| approved | Boolean | 是 | 是否通过 |
| reviewerZgh | String | 是 | 审核人职工号 |
| reviewComments | String | 否 | 审核意见 |

#### 发布征集
```http
PUT /api/project-collection/{id}/publish
```

#### 关闭征集
```http
PUT /api/project-collection/{id}/close
```

### 2.2 项目申报管理

#### 分页查询项目申报
```http
GET /api/project-application/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryId | Integer | 否 | - | 项目大类ID |
| typeId | Integer | 否 | - | 项目类别ID |
| deptId | Integer | 否 | - | 管理部门ID |
| status | String | 否 | - | 状态 |
| projectName | String | 否 | - | 项目名称（模糊查询） |
| applicantZgh | String | 否 | - | 申报人职工号 |

#### 根据项目编号查询申报信息
```http
GET /api/project-application/code/{projectCode}
```

#### 根据申报人获取申报列表
```http
GET /api/project-application/applicant/{applicantZgh}
```

#### 新增项目申报
```http
POST /api/project-application
```

**请求体：**
```json
{
  "projectCode": "PROJ_2024_001",
  "projectName": "AI智能识别系统",
  "categoryId": 1,
  "typeId": 1,
  "deptId": 1,
  "applicantZgh": "T001",
  "applicantName": "张三",
  "guideContent": "申报指南内容...",
  "acceptStartTime": "2024-08-01T00:00:00",
  "acceptEndTime": "2024-08-31T23:59:59",
  "status": "DRAFT",
  "createBy": "T001"
}
```

#### 提交申报
```http
PUT /api/project-application/{id}/submit?submitBy=T001
```

#### 审核申报
```http
PUT /api/project-application/{id}/review
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| approved | Boolean | 是 | 是否通过 |
| reviewerZgh | String | 是 | 审核人职工号 |
| reviewComments | String | 否 | 审核意见 |

#### 获取申报统计信息
```http
GET /api/project-application/statistics
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deptId | Integer | 否 | 部门ID |
| categoryId | Integer | 否 | 大类ID |

### 2.3 项目中检管理

#### 分页查询项目中检
```http
GET /api/project-inspection/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryId | Integer | 否 | - | 项目大类ID |
| typeId | Integer | 否 | - | 项目类别ID |
| deptId | Integer | 否 | - | 管理部门ID |
| status | String | 否 | - | 状态 |
| projectCode | String | 否 | - | 项目编号 |
| projectLeaderZgh | String | 否 | - | 项目负责人职工号 |

#### 根据项目编号获取中检信息
```http
GET /api/project-inspection/project/{projectCode}
```

#### 根据项目负责人获取中检列表
```http
GET /api/project-inspection/leader/{projectLeaderZgh}
```

#### 新增项目中检
```http
POST /api/project-inspection
```

**请求体：**
```json
{
  "projectCode": "PROJ_2024_001",
  "inspectionName": "AI智能识别系统中期检查",
  "categoryId": 1,
  "typeId": 1,
  "deptId": 1,
  "projectLeaderZgh": "T001",
  "projectLeaderName": "张三",
  "guideContent": "中检指南内容...",
  "acceptStartTime": "2024-09-01T00:00:00",
  "acceptEndTime": "2024-09-30T23:59:59",
  "inspectionProgress": 60.00,
  "progressDescription": "项目进展顺利，已完成60%",
  "status": "DRAFT",
  "createBy": "T001"
}
```

#### 提交中检
```http
PUT /api/project-inspection/{id}/submit?submitBy=T001
```

#### 审核中检
```http
PUT /api/project-inspection/{id}/review
```

#### 获取中检统计信息
```http
GET /api/project-inspection/statistics
```

### 2.4 项目结项管理

#### 分页查询项目结项
```http
GET /api/project-completion/page
```

#### 根据项目编号获取结项信息
```http
GET /api/project-completion/project/{projectCode}
```

#### 根据项目负责人获取结项列表
```http
GET /api/project-completion/leader/{projectLeaderZgh}
```

#### 新增项目结项
```http
POST /api/project-completion
```

**请求体：**
```json
{
  "projectCode": "PROJ_2024_001",
  "completionName": "AI智能识别系统结项",
  "categoryId": 1,
  "typeId": 1,
  "deptId": 1,
  "projectLeaderZgh": "T001",
  "projectLeaderName": "张三",
  "guideContent": "结项指南内容...",
  "acceptStartTime": "2024-11-01T00:00:00",
  "acceptEndTime": "2024-11-30T23:59:59",
  "completionResult": "成功开发AI智能识别系统",
  "achievementSummary": "项目成果总结...",
  "status": "DRAFT",
  "createBy": "T001"
}
```

#### 提交结项
```http
PUT /api/project-completion/{id}/submit?submitBy=T001
```

#### 审核结项
```http
PUT /api/project-completion/{id}/review
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| approved | Boolean | 是 | 是否通过 |
| reviewerZgh | String | 是 | 审核人职工号 |
| reviewComments | String | 否 | 审核意见 |
| completionScore | BigDecimal | 否 | 结项评分 |

#### 获取结项统计信息
```http
GET /api/project-completion/statistics
```

---

## 📁 3. 文件管理

### 3.1 项目文件管理

#### 分页查询项目文件
```http
GET /api/project-files/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| businessType | String | 否 | - | 业务类型 |
| businessId | Long | 否 | - | 业务ID |
| projectCode | String | 否 | - | 项目编号 |
| fileCategory | String | 否 | - | 文件分类 |
| uploadBy | String | 否 | - | 上传人 |

#### 根据业务信息获取文件列表
```http
GET /api/project-files/business/{businessType}/{businessId}
```

#### 根据项目编号获取文件列表
```http
GET /api/project-files/project/{projectCode}
```

#### 上传文件
```http
POST /api/project-files/upload
```

**请求参数（Form Data）：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 上传的文件 |
| businessType | String | 是 | 业务类型 |
| businessId | Long | 是 | 业务ID |
| projectCode | String | 否 | 项目编号 |
| fileCategory | String | 是 | 文件分类 |
| description | String | 否 | 文件描述 |
| uploadBy | String | 是 | 上传人 |

#### 删除文件
```http
DELETE /api/project-files/{id}?deleteBy=admin
```

#### 批量删除文件
```http
DELETE /api/project-files/batch
```

**请求体：**
```json
{
  "ids": [1, 2, 3],
  "deleteBy": "admin"
}
```

#### 获取文件下载URL
```http
GET /api/project-files/{id}/download-url
```

#### 获取文件统计信息
```http
GET /api/project-files/statistics
```

---

## 📝 4. 枚举值说明

### 4.1 业务类型 (businessType)
- `COLLECTION`: 项目征集
- `APPLICATION`: 项目申报
- `INSPECTION`: 项目中检
- `COMPLETION`: 项目结项

### 4.2 文件分类 (fileCategory)
- `GUIDE`: 填报指南
- `MATERIAL`: 申报材料
- `REPORT`: 报告文档
- `OTHER`: 其他文件

### 4.3 征集状态
- `DRAFT`: 草稿
- `PUBLISHED`: 已发布
- `CLOSED`: 已关闭

### 3.2 申报状态
- `DRAFT`: 草稿
- `SUBMITTED`: 已提交
- `REVIEWING`: 审核中
- `APPROVED`: 已通过
- `REJECTED`: 已拒绝

### 3.3 中检状态
- `DRAFT`: 草稿
- `SUBMITTED`: 已提交
- `REVIEWING`: 审核中
- `APPROVED`: 已通过
- `REJECTED`: 已拒绝

### 4.7 结项状态
- `DRAFT`: 草稿
- `SUBMITTED`: 已提交
- `REVIEWING`: 审核中
- `APPROVED`: 已通过
- `REJECTED`: 已拒绝

---

## 🔧 5. 错误处理

### 5.1 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权 | 检查登录状态和Token |
| 403 | 权限不足 | 联系管理员分配权限 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 5.2 业务错误示例

```json
{
  "code": 400,
  "message": "大类编码已存在",
  "data": null
}
```

---

## 📚 6. 使用示例

### 6.1 创建完整的项目征集流程

```javascript
// 1. 创建项目大类
const category = await fetch('/api/project-categories', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    categoryCode: 'TECH',
    categoryName: '技术类项目',
    description: '技术研发类项目',
    sortOrder: 1,
    isEnabled: true
  })
});

// 2. 创建项目类别
const type = await fetch('/api/project-types', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    typeCode: 'TECH_AI',
    typeName: '人工智能技术',
    categoryId: 1,
    description: '人工智能相关技术研发',
    sortOrder: 1,
    isEnabled: true
  })
});

// 3. 创建项目征集
const collection = await fetch('/api/project-collection', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    collectionCode: 'COLLECT_2024_001',
    collectionName: '2024年AI技术创新项目征集',
    categoryId: 1,
    typeId: 1,
    deptId: 1,
    guideContent: '征集指南内容...',
    acceptStartTime: '2024-08-01T00:00:00',
    acceptEndTime: '2024-08-31T23:59:59',
    status: 'DRAFT',
    createBy: 'admin'
  })
});

// 4. 发布征集
await fetch('/api/project-collection/1/publish', {
  method: 'PUT'
});
```

### 6.2 文件上传示例

```javascript
// 上传项目申报材料
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('businessType', 'APPLICATION');
formData.append('businessId', '1');
formData.append('projectCode', 'PROJ_2024_001');
formData.append('fileCategory', 'MATERIAL');
formData.append('description', '项目申报书');
formData.append('uploadBy', 'T001');

const response = await fetch('/api/project-files/upload', {
  method: 'POST',
  body: formData
});

const result = await response.json();
console.log('文件上传结果:', result);
```

### 6.3 项目全流程管理示例

```javascript
// 1. 创建项目申报
const application = await fetch('/api/project-application', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    projectCode: 'PROJ_2024_001',
    projectName: 'AI智能识别系统',
    categoryId: 1,
    typeId: 1,
    deptId: 1,
    applicantZgh: 'T001',
    applicantName: '张三',
    status: 'DRAFT',
    createBy: 'T001'
  })
});

// 2. 提交申报
await fetch('/api/project-application/1/submit?submitBy=T001', {
  method: 'PUT'
});

// 3. 审核申报
await fetch('/api/project-application/1/review', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  body: 'approved=true&reviewerZgh=ADMIN&reviewComments=申报材料完整，同意立项'
});

// 4. 创建中检
const inspection = await fetch('/api/project-inspection', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    projectCode: 'PROJ_2024_001',
    inspectionName: 'AI智能识别系统中期检查',
    categoryId: 1,
    typeId: 1,
    deptId: 1,
    projectLeaderZgh: 'T001',
    projectLeaderName: '张三',
    inspectionProgress: 60.00,
    progressDescription: '项目进展顺利，已完成60%',
    status: 'DRAFT',
    createBy: 'T001'
  })
});

// 5. 创建结项
const completion = await fetch('/api/project-completion', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    projectCode: 'PROJ_2024_001',
    completionName: 'AI智能识别系统结项',
    categoryId: 1,
    typeId: 1,
    deptId: 1,
    projectLeaderZgh: 'T001',
    projectLeaderName: '张三',
    completionResult: '成功开发AI智能识别系统',
    achievementSummary: '项目成果总结...',
    status: 'DRAFT',
    createBy: 'T001'
  })
});
```

---

## 🚀 7. 前端对接指南

### 7.1 环境配置

#### 基础配置
```javascript
// config.js
const API_CONFIG = {
  baseURL: 'http://localhost:8080/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

// 创建axios实例
import axios from 'axios';

const apiClient = axios.create(API_CONFIG);

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 添加token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    const { data } = response;
    if (data.code === 200) {
      return data.data;
    } else {
      throw new Error(data.message || '请求失败');
    }
  },
  error => {
    console.error('API Error:', error);
    throw error;
  }
);

export default apiClient;
```

### 7.2 API封装示例

#### 基础字典API封装
```javascript
// api/dictionary.js
import apiClient from '@/utils/apiClient';

export const dictionaryAPI = {
  // 项目大类
  categories: {
    // 分页查询
    getPage: (params) => apiClient.get('/project-categories/page', { params }),
    // 获取启用的大类
    getEnabled: () => apiClient.get('/project-categories/enabled'),
    // 根据ID查询
    getById: (id) => apiClient.get(`/project-categories/${id}`),
    // 新增
    create: (data) => apiClient.post('/project-categories', data),
    // 更新
    update: (id, data) => apiClient.put(`/project-categories/${id}`, data),
    // 删除
    delete: (id) => apiClient.delete(`/project-categories/${id}`),
    // 启用/禁用
    updateEnabled: (id, enabled) => apiClient.put(`/project-categories/${id}/enabled`, null, {
      params: { enabled }
    })
  },

  // 项目类别
  types: {
    getPage: (params) => apiClient.get('/project-types/page', { params }),
    getEnabled: () => apiClient.get('/project-types/enabled'),
    getEnabledByCategory: (categoryId) => apiClient.get(`/project-types/enabled/category/${categoryId}`),
    getById: (id) => apiClient.get(`/project-types/${id}`),
    create: (data) => apiClient.post('/project-types', data),
    update: (id, data) => apiClient.put(`/project-types/${id}`, data),
    delete: (id) => apiClient.delete(`/project-types/${id}`),
    updateEnabled: (id, enabled) => apiClient.put(`/project-types/${id}/enabled`, null, {
      params: { enabled }
    })
  },

  // 管理部门
  departments: {
    getPage: (params) => apiClient.get('/departments/page', { params }),
    getEnabled: () => apiClient.get('/departments/enabled'),
    getById: (id) => apiClient.get(`/departments/${id}`),
    create: (data) => apiClient.post('/departments', data),
    update: (id, data) => apiClient.put(`/departments/${id}`, data),
    delete: (id) => apiClient.delete(`/departments/${id}`),
    updateEnabled: (id, enabled) => apiClient.put(`/departments/${id}/enabled`, null, {
      params: { enabled }
    })
  }
};
```

#### 项目业务API封装
```javascript
// api/project.js
import apiClient from '@/utils/apiClient';

export const projectAPI = {
  // 项目征集
  collection: {
    getPage: (params) => apiClient.get('/project-collection/page', { params }),
    getActive: () => apiClient.get('/project-collection/active'),
    getById: (id) => apiClient.get(`/project-collection/${id}`),
    getByCode: (code) => apiClient.get(`/project-collection/code/${code}`),
    create: (data) => apiClient.post('/project-collection', data),
    update: (id, data) => apiClient.put(`/project-collection/${id}`, data),
    delete: (id) => apiClient.delete(`/project-collection/${id}`),
    submit: (id, submitBy) => apiClient.put(`/project-collection/${id}/submit`, null, {
      params: { submitBy }
    }),
    review: (id, params) => apiClient.put(`/project-collection/${id}/review`, null, { params }),
    publish: (id) => apiClient.put(`/project-collection/${id}/publish`),
    close: (id) => apiClient.put(`/project-collection/${id}/close`)
  },

  // 项目申报
  application: {
    getPage: (params) => apiClient.get('/project-application/page', { params }),
    getById: (id) => apiClient.get(`/project-application/${id}`),
    getByCode: (code) => apiClient.get(`/project-application/code/${code}`),
    getByApplicant: (zgh) => apiClient.get(`/project-application/applicant/${zgh}`),
    create: (data) => apiClient.post('/project-application', data),
    update: (id, data) => apiClient.put(`/project-application/${id}`, data),
    delete: (id) => apiClient.delete(`/project-application/${id}`),
    submit: (id, submitBy) => apiClient.put(`/project-application/${id}/submit`, null, {
      params: { submitBy }
    }),
    review: (id, params) => apiClient.put(`/project-application/${id}/review`, null, { params }),
    getStatistics: (params) => apiClient.get('/project-application/statistics', { params })
  },

  // 项目中检
  inspection: {
    getPage: (params) => apiClient.get('/project-inspection/page', { params }),
    getById: (id) => apiClient.get(`/project-inspection/${id}`),
    getByProject: (code) => apiClient.get(`/project-inspection/project/${code}`),
    getByLeader: (zgh) => apiClient.get(`/project-inspection/leader/${zgh}`),
    create: (data) => apiClient.post('/project-inspection', data),
    update: (id, data) => apiClient.put(`/project-inspection/${id}`, data),
    delete: (id) => apiClient.delete(`/project-inspection/${id}`),
    submit: (id, submitBy) => apiClient.put(`/project-inspection/${id}/submit`, null, {
      params: { submitBy }
    }),
    review: (id, params) => apiClient.put(`/project-inspection/${id}/review`, null, { params }),
    getStatistics: (params) => apiClient.get('/project-inspection/statistics', { params })
  },

  // 项目结项
  completion: {
    getPage: (params) => apiClient.get('/project-completion/page', { params }),
    getById: (id) => apiClient.get(`/project-completion/${id}`),
    getByProject: (code) => apiClient.get(`/project-completion/project/${code}`),
    getByLeader: (zgh) => apiClient.get(`/project-completion/leader/${zgh}`),
    create: (data) => apiClient.post('/project-completion', data),
    update: (id, data) => apiClient.put(`/project-completion/${id}`, data),
    delete: (id) => apiClient.delete(`/project-completion/${id}`),
    submit: (id, submitBy) => apiClient.put(`/project-completion/${id}/submit`, null, {
      params: { submitBy }
    }),
    review: (id, params) => apiClient.put(`/project-completion/${id}/review`, null, { params }),
    getStatistics: (params) => apiClient.get('/project-completion/statistics', { params })
  }
};
```

#### 文件管理API封装
```javascript
// api/files.js
import apiClient from '@/utils/apiClient';

export const filesAPI = {
  // 分页查询文件
  getPage: (params) => apiClient.get('/project-files/page', { params }),

  // 根据业务获取文件
  getByBusiness: (businessType, businessId) =>
    apiClient.get(`/project-files/business/${businessType}/${businessId}`),

  // 根据项目获取文件
  getByProject: (projectCode) => apiClient.get(`/project-files/project/${projectCode}`),

  // 根据分类获取文件
  getByCategory: (businessType, businessId, fileCategory) =>
    apiClient.get(`/project-files/category/${businessType}/${businessId}/${fileCategory}`),

  // 上传文件
  upload: (formData) => apiClient.post('/project-files/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),

  // 删除文件
  delete: (id, deleteBy) => apiClient.delete(`/project-files/${id}`, {
    params: { deleteBy }
  }),

  // 批量删除文件
  batchDelete: (ids, deleteBy) => apiClient.delete('/project-files/batch', {
    data: { ids, deleteBy }
  }),

  // 获取下载URL
  getDownloadUrl: (id) => apiClient.get(`/project-files/${id}/download-url`),

  // 获取统计信息
  getStatistics: (params) => apiClient.get('/project-files/statistics', { params })
};
```

### 7.3 Vue组件示例

#### 项目大类管理组件
```vue
<template>
  <div class="category-management">
    <!-- 查询条件 -->
    <el-form :model="queryForm" inline>
      <el-form-item label="大类名称">
        <el-input v-model="queryForm.categoryName" placeholder="请输入大类名称" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryForm.isEnabled" placeholder="请选择状态">
          <el-option label="全部" :value="null" />
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadData">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="success" @click="showAddDialog">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" v-loading="loading">
      <el-table-column prop="categoryCode" label="大类编码" />
      <el-table-column prop="categoryName" label="大类名称" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="sortOrder" label="排序" />
      <el-table-column prop="isEnabled" label="状态">
        <template #default="{ row }">
          <el-switch
            v-model="row.isEnabled"
            @change="handleStatusChange(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="showEditDialog(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      @current-change="loadData"
      @size-change="loadData"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle">
      <el-form :model="formData" :rules="formRules" ref="formRef">
        <el-form-item label="大类编码" prop="categoryCode">
          <el-input v-model="formData.categoryCode" />
        </el-form-item>
        <el-form-item label="大类名称" prop="categoryName">
          <el-input v-model="formData.categoryName" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="formData.description" type="textarea" />
        </el-form-item>
        <el-form-item label="排序号" prop="sortOrder">
          <el-input-number v-model="formData.sortOrder" :min="1" />
        </el-form-item>
        <el-form-item label="是否启用" prop="isEnabled">
          <el-switch v-model="formData.isEnabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { dictionaryAPI } from '@/api/dictionary';

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const formRef = ref();

// 查询表单
const queryForm = reactive({
  categoryName: '',
  isEnabled: null
});

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 表单数据
const formData = reactive({
  id: null,
  categoryCode: '',
  categoryName: '',
  description: '',
  sortOrder: 1,
  isEnabled: true
});

// 表单验证规则
const formRules = {
  categoryCode: [
    { required: true, message: '请输入大类编码', trigger: 'blur' }
  ],
  categoryName: [
    { required: true, message: '请输入大类名称', trigger: 'blur' }
  ]
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...queryForm
    };
    const result = await dictionaryAPI.categories.getPage(params);
    tableData.value = result.records;
    pagination.total = result.total;
  } catch (error) {
    ElMessage.error('加载数据失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 重置查询
const resetQuery = () => {
  Object.assign(queryForm, {
    categoryName: '',
    isEnabled: null
  });
  pagination.current = 1;
  loadData();
};

// 显示新增对话框
const showAddDialog = () => {
  dialogTitle.value = '新增项目大类';
  Object.assign(formData, {
    id: null,
    categoryCode: '',
    categoryName: '',
    description: '',
    sortOrder: 1,
    isEnabled: true
  });
  dialogVisible.value = true;
};

// 显示编辑对话框
const showEditDialog = (row) => {
  dialogTitle.value = '编辑项目大类';
  Object.assign(formData, row);
  dialogVisible.value = true;
};

// 保存数据
const handleSave = async () => {
  try {
    await formRef.value.validate();

    if (formData.id) {
      await dictionaryAPI.categories.update(formData.id, formData);
      ElMessage.success('更新成功');
    } else {
      await dictionaryAPI.categories.create(formData);
      ElMessage.success('新增成功');
    }

    dialogVisible.value = false;
    loadData();
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message);
  }
};

// 状态变更
const handleStatusChange = async (row) => {
  try {
    await dictionaryAPI.categories.updateEnabled(row.id, row.isEnabled);
    ElMessage.success('状态更新成功');
  } catch (error) {
    ElMessage.error('状态更新失败: ' + error.message);
    row.isEnabled = !row.isEnabled; // 回滚状态
  }
};

// 删除数据
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      type: 'warning'
    });

    await dictionaryAPI.categories.delete(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message);
    }
  }
};

// 初始化
onMounted(() => {
  loadData();
});
</script>
```

### 7.4 文件上传组件示例

```vue
<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadAction"
      :headers="uploadHeaders"
      :data="uploadData"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :before-upload="beforeUpload"
      :file-list="fileList"
      multiple
      drag
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        将文件拖到此处，或<em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">
          支持jpg/png/gif/pdf/doc/docx/xls/xlsx文件，且不超过10MB
        </div>
      </template>
    </el-upload>

    <!-- 文件列表 -->
    <el-table :data="uploadedFiles" style="margin-top: 20px;">
      <el-table-column prop="fileOriginalName" label="文件名" />
      <el-table-column prop="fileSize" label="文件大小">
        <template #default="{ row }">
          {{ formatFileSize(row.fileSize) }}
        </template>
      </el-table-column>
      <el-table-column prop="uploadTime" label="上传时间" />
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button size="small" @click="downloadFile(row)">下载</el-button>
          <el-button size="small" type="danger" @click="deleteFile(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { filesAPI } from '@/api/files';

const props = defineProps({
  businessType: {
    type: String,
    required: true
  },
  businessId: {
    type: [String, Number],
    required: true
  },
  projectCode: {
    type: String,
    default: ''
  },
  fileCategory: {
    type: String,
    required: true
  },
  uploadBy: {
    type: String,
    required: true
  }
});

const uploadRef = ref();
const fileList = ref([]);
const uploadedFiles = ref([]);

// 上传配置
const uploadAction = computed(() => `${import.meta.env.VITE_API_BASE_URL}/api/project-files/upload`);
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}));
const uploadData = computed(() => ({
  businessType: props.businessType,
  businessId: props.businessId,
  projectCode: props.projectCode,
  fileCategory: props.fileCategory,
  uploadBy: props.uploadBy
}));

// 上传前检查
const beforeUpload = (file) => {
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif',
    'application/pdf',
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];

  const isAllowedType = allowedTypes.includes(file.type);
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isAllowedType) {
    ElMessage.error('只能上传jpg/png/gif/pdf/doc/docx/xls/xlsx格式的文件!');
    return false;
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

// 上传成功
const handleUploadSuccess = (response, file) => {
  ElMessage.success('文件上传成功');
  loadUploadedFiles();
  uploadRef.value.clearFiles();
};

// 上传失败
const handleUploadError = (error) => {
  ElMessage.error('文件上传失败: ' + error.message);
};

// 加载已上传文件
const loadUploadedFiles = async () => {
  try {
    const files = await filesAPI.getByBusiness(props.businessType, props.businessId);
    uploadedFiles.value = files.filter(file => file.fileCategory === props.fileCategory);
  } catch (error) {
    ElMessage.error('加载文件列表失败: ' + error.message);
  }
};

// 下载文件
const downloadFile = async (file) => {
  try {
    const downloadUrl = await filesAPI.getDownloadUrl(file.id);
    window.open(downloadUrl, '_blank');
  } catch (error) {
    ElMessage.error('获取下载链接失败: ' + error.message);
  }
};

// 删除文件
const deleteFile = async (file) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '提示', {
      type: 'warning'
    });

    await filesAPI.delete(file.id, props.uploadBy);
    ElMessage.success('文件删除成功');
    loadUploadedFiles();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('文件删除失败: ' + error.message);
    }
  }
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) return size + ' B';
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
  return (size / 1024 / 1024).toFixed(1) + ' MB';
};

// 初始化
onMounted(() => {
  loadUploadedFiles();
});
</script>
```

---

## 📞 8. 技术支持

如有API使用问题，请联系：
- **开发团队**: GL开发团队
- **邮箱**: <EMAIL>
- **文档版本**: v1.0
- **最后更新**: 2024-08-04
