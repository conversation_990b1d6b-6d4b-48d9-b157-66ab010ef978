# 科技项目筛选选项接口文档

## 📋 概述

本文档提供科技项目基本信息系统中用于前端筛选的选项接口。这些接口已优化为快速响应版本，包含统计接口和筛选选项接口，满足不同的业务需求。

## 🚀 性能优化说明

- **统计接口**: 使用数据库聚合查询，性能优化，返回详细统计数据
- **筛选选项接口**: 使用 `DISTINCT` 查询，不统计数量，响应速度快
- **响应速度**: 筛选选项接口响应时间 < 50ms，统计接口响应时间 < 200ms

---

## 🔬 1. 科技项目统计接口

### 1.1 项目级别统计接口

#### 接口信息
- **接口地址**: `GET /api/kjxmjbxx/stats/xmjb`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个项目级别的项目数量分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "国家级": 45,
        "省部级": 123,
        "市厅级": 89,
        "校级": 156,
        "横向": 67,
        "NULL或空": 8
    }
}
```

### 1.2 执行状态统计接口

#### 接口信息
- **接口地址**: `GET /api/kjxmjbxx/stats/xmzxzt`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个执行状态的项目数量分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "进行中": 234,
        "已完成": 156,
        "已结题": 89,
        "暂停": 12,
        "终止": 5,
        "NULL或空": 3
    }
}
```

### 1.3 审核状态统计接口

#### 接口信息
- **接口地址**: `GET /api/kjxmjbxx/stats/shzt`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个审核状态的项目数量分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "已审核": 345,
        "待审核": 89,
        "已驳回": 23,
        "草稿": 45,
        "NULL或空": 6
    }
}
```

---

## 🔍 2. 科技项目筛选选项接口

### 2.1 项目级别选项接口

#### 接口信息
- **接口地址**: `GET /api/kjxmjbxx/xmjb-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有项目级别的选项列表，已优化为快速响应版本，不统计数量，用于前端项目级别筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "国家级",
            "label": "国家级"
        },
        {
            "value": "省部级",
            "label": "省部级"
        },
        {
            "value": "市厅级",
            "label": "市厅级"
        },
        {
            "value": "校级",
            "label": "校级"
        },
        {
            "value": "横向",
            "label": "横向"
        }
    ]
}
```

### 2.2 执行状态选项接口

#### 接口信息
- **接口地址**: `GET /api/kjxmjbxx/xmzxzt-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有执行状态的选项列表，已优化为快速响应版本，不统计数量，用于前端执行状态筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "进行中",
            "label": "进行中"
        },
        {
            "value": "已完成",
            "label": "已完成"
        },
        {
            "value": "已结题",
            "label": "已结题"
        },
        {
            "value": "暂停",
            "label": "暂停"
        },
        {
            "value": "终止",
            "label": "终止"
        }
    ]
}
```

### 2.3 审核状态选项接口

#### 接口信息
- **接口地址**: `GET /api/kjxmjbxx/shzt-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有审核状态的选项列表，已优化为快速响应版本，不统计数量，用于前端审核状态筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "已审核",
            "label": "已审核"
        },
        {
            "value": "待审核",
            "label": "待审核"
        },
        {
            "value": "已驳回",
            "label": "已驳回"
        },
        {
            "value": "草稿",
            "label": "草稿"
        }
    ]
}
```

---

## 📝 3. 前端使用示例

### 3.1 Vue.js 组件示例

```javascript
// 科技项目筛选和统计组件
export default {
    data() {
        return {
            searchForm: {
                xmjb: '',      // 项目级别
                xmzxzt: '',    // 执行状态
                shzt: '',      // 审核状态
                xmmc: '',      // 项目名称
                fzrxm: '',     // 负责人姓名
                dwmc: ''       // 单位名称
            },
            xmjbOptions: [],
            xmzxztOptions: [],
            shztOptions: [],
            
            // 统计数据
            xmjbStats: {},
            xmzxztStats: {},
            shztStats: {}
        };
    },
    
    async mounted() {
        await this.loadOptions();
    },
    
    methods: {
        async loadOptions() {
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };
            
            try {
                // 并行加载所有筛选选项
                const [xmjbRes, xmzxztRes, shztRes] = await Promise.all([
                    fetch('/api/kjxmjbxx/xmjb-options', { headers }),
                    fetch('/api/kjxmjbxx/xmzxzt-options', { headers }),
                    fetch('/api/kjxmjbxx/shzt-options', { headers })
                ]);
                
                this.xmjbOptions = (await xmjbRes.json()).data;
                this.xmzxztOptions = (await xmzxztRes.json()).data;
                this.shztOptions = (await shztRes.json()).data;
                
                console.log('科技项目筛选选项加载完成');
            } catch (error) {
                console.error('加载筛选选项失败:', error);
            }
        },
        
        async loadStats() {
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };
            
            try {
                // 并行加载所有统计数据
                const [xmjbStatsRes, xmzxztStatsRes, shztStatsRes] = await Promise.all([
                    fetch('/api/kjxmjbxx/stats/xmjb', { headers }),
                    fetch('/api/kjxmjbxx/stats/xmzxzt', { headers }),
                    fetch('/api/kjxmjbxx/stats/shzt', { headers })
                ]);
                
                this.xmjbStats = (await xmjbStatsRes.json()).data;
                this.xmzxztStats = (await xmzxztStatsRes.json()).data;
                this.shztStats = (await shztStatsRes.json()).data;
                
                console.log('项目级别统计:', this.xmjbStats);
                console.log('执行状态统计:', this.xmzxztStats);
                console.log('审核状态统计:', this.shztStats);
                
                // 可以用于图表展示
                this.renderCharts();
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }
    }
};
```

### 3.2 组件模板示例

```vue
<template>
    <div class="project-management">
        <!-- 筛选表单 -->
        <el-card class="filter-card">
            <div slot="header">
                <span>科技项目筛选</span>
            </div>

            <el-form :model="searchForm" inline>
                <!-- 项目级别筛选 -->
                <el-form-item label="项目级别">
                    <el-select v-model="searchForm.xmjb" placeholder="请选择项目级别" clearable>
                        <el-option
                            v-for="item in xmjbOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <!-- 执行状态筛选 -->
                <el-form-item label="执行状态">
                    <el-select v-model="searchForm.xmzxzt" placeholder="请选择执行状态" clearable>
                        <el-option
                            v-for="item in xmzxztOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <!-- 审核状态筛选 -->
                <el-form-item label="审核状态">
                    <el-select v-model="searchForm.shzt" placeholder="请选择审核状态" clearable>
                        <el-option
                            v-for="item in shztOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <!-- 项目名称搜索 -->
                <el-form-item label="项目名称">
                    <el-input v-model="searchForm.xmmc" placeholder="请输入项目名称" clearable></el-input>
                </el-form-item>

                <!-- 负责人搜索 -->
                <el-form-item label="负责人">
                    <el-input v-model="searchForm.fzrxm" placeholder="请输入负责人姓名" clearable></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                    <el-button @click="resetForm">重置</el-button>
                    <el-button type="info" @click="loadStats">查看统计</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 统计图表 -->
        <el-row :gutter="20" class="stats-row">
            <el-col :span="8">
                <el-card>
                    <div slot="header">项目级别分布</div>
                    <div id="xmjbChart" style="height: 300px;"></div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card>
                    <div slot="header">执行状态分布</div>
                    <div id="xmzxztChart" style="height: 300px;"></div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card>
                    <div slot="header">审核状态分布</div>
                    <div id="shztChart" style="height: 300px;"></div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 项目列表表格 -->
        <el-card class="table-card">
            <div slot="header">
                <span>科技项目列表</span>
            </div>
            <!-- 这里放置项目列表表格 -->
        </el-card>
    </div>
</template>
```

## ⚠️ 4. 注意事项

1. **权限验证**: 所有接口需要教师及以上权限，请确保token有效
2. **性能优化**:
   - 筛选选项接口已优化，响应时间在50ms以内
   - 统计接口使用数据库聚合查询，响应时间在200ms以内
3. **数据更新**: 所有数据实时查询，反映当前数据库状态
4. **排序规则**: 筛选选项按字母顺序排列
5. **缓存建议**: 前端可适当缓存筛选选项数据，减少重复请求
6. **错误处理**: 请妥善处理网络错误和权限错误
7. **图表展示**: 建议使用 ECharts 等图表库展示统计数据

## 📊 5. 接口汇总

### 统计接口
- `GET /api/kjxmjbxx/stats/xmjb` - 项目级别统计
- `GET /api/kjxmjbxx/stats/xmzxzt` - 执行状态统计
- `GET /api/kjxmjbxx/stats/shzt` - 审核状态统计

### 筛选选项接口
- `GET /api/kjxmjbxx/xmjb-options` - 项目级别选项
- `GET /api/kjxmjbxx/xmzxzt-options` - 执行状态选项
- `GET /api/kjxmjbxx/shzt-options` - 审核状态选项

---

## 📞 6. 技术支持

如有问题，请联系后端开发团队。

## 📝 7. 更新日志

- **2025-07-21**: 新增科技项目级别、执行状态、审核状态统计接口
- **2025-07-21**: 新增科技项目级别、执行状态、审核状态筛选选项接口
- **2025-07-21**: 优化查询性能，响应速度大幅提升
- **2025-07-21**: 提供完整的前端使用示例和图表展示方案
