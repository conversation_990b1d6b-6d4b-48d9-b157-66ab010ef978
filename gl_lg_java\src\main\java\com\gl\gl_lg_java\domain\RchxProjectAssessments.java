package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 项目考核管理实体类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_rchx_project_assessments")
public class RchxProjectAssessments implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 考核编号
     */
    @TableField("assessment_code")
    private String assessmentCode;

    /**
     * 项目管理ID
     */
    @TableField("project_id")
    private Long projectId;

    /**
     * 项目编号
     */
    @TableField("project_code")
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目负责人职工号
     */
    @TableField("project_leader_zgh")
    private String projectLeaderZgh;

    /**
     * 项目负责人姓名
     */
    @TableField("project_leader_name")
    private String projectLeaderName;

    /**
     * 考核类型(MIDTERM中期考核,FINAL结项考核,SPECIAL专项考核,ANNUAL年度考核,QUARTERLY季度考核)
     */
    @TableField("assessment_type")
    private String assessmentType;

    /**
     * 考核名称
     */
    @TableField("assessment_name")
    private String assessmentName;

    /**
     * 考核描述
     */
    @TableField("assessment_description")
    private String assessmentDescription;

    /**
     * 考核标准
     */
    @TableField("assessment_criteria")
    private String assessmentCriteria;

    /**
     * 考核状态(PENDING待考核,ONGOING考核中,COMPLETED已完成,CANCELLED已取消,OVERDUE已逾期)
     */
    @TableField("assessment_status")
    private String assessmentStatus;

    /**
     * 考核时间
     */
    @TableField("assessment_date")
    private LocalDateTime assessmentDate;

    /**
     * 考核截止时间
     */
    @TableField("assessment_deadline")
    private LocalDateTime assessmentDeadline;

    /**
     * 考核地点
     */
    @TableField("assessment_location")
    private String assessmentLocation;

    /**
     * 考核方式(ONLINE线上,OFFLINE线下,HYBRID混合)
     */
    @TableField("assessment_method")
    private String assessmentMethod;

    /**
     * 考核专家组
     */
    @TableField("assessor_group")
    private String assessorGroup;

    /**
     * 考核得分
     */
    @TableField("assessment_score")
    private BigDecimal assessmentScore;

    /**
     * 满分
     */
    @TableField("max_score")
    private BigDecimal maxScore;

    /**
     * 及格分
     */
    @TableField("pass_score")
    private BigDecimal passScore;

    /**
     * 考核结果(EXCELLENT优秀,GOOD良好,PASS合格,FAIL不合格)
     */
    @TableField("assessment_result")
    private String assessmentResult;

    /**
     * 考核意见
     */
    @TableField("assessment_comments")
    private String assessmentComments;

    /**
     * 改进建议
     */
    @TableField("improvement_suggestions")
    private String improvementSuggestions;

    /**
     * 后续行动
     */
    @TableField("follow_up_actions")
    private String followUpActions;

    /**
     * 主要考核人职工号
     */
    @TableField("assessor_zgh")
    private String assessorZgh;

    /**
     * 主要考核人姓名
     */
    @TableField("assessor_name")
    private String assessorName;

    /**
     * 考核开始时间
     */
    @TableField("assessment_start_time")
    private LocalDateTime assessmentStartTime;

    /**
     * 考核结束时间
     */
    @TableField("assessment_end_time")
    private LocalDateTime assessmentEndTime;

    /**
     * 是否公开(0否,1是)
     */
    @TableField("is_public")
    private Boolean isPublic;

    /**
     * 优先级(HIGH高,NORMAL普通,LOW低)
     */
    @TableField("priority_level")
    private String priorityLevel;

    /**
     * 是否已发送提醒(0否,1是)
     */
    @TableField("reminder_sent")
    private Boolean reminderSent;

    /**
     * 最后提醒时间
     */
    @TableField("last_reminder_time")
    private LocalDateTime lastReminderTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人职工号
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人职工号
     */
    @TableField("update_by")
    private String updateBy;
}
