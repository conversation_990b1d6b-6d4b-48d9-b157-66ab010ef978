package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxNotifications;
import com.gl.gl_lg_java.mapper.NotificationMapper;
import com.gl.gl_lg_java.service.NotificationRedisService;
import com.gl.gl_lg_java.service.NotificationSendService;
import com.gl.gl_lg_java.service.RchxNotificationsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_notifications(教师通知表)】的数据库操作Service实现
 * @createDate 2025-07-24 13:00:00
 */
@Service
@Slf4j
public class RchxNotificationsServiceImpl extends ServiceImpl<NotificationMapper, RchxNotifications>
        implements RchxNotificationsService {

    @Autowired
    private NotificationMapper notificationMapper;

    @Autowired
    private NotificationRedisService notificationRedisService;

    @Autowired
    private NotificationSendService notificationSendService;

    @Override
    public IPage<RchxNotifications> getNotificationPage(String zgh, long current, long size, 
                                                        Integer isRead, String type) {
        try {
            Page<RchxNotifications> page = new Page<>(current, size);
            IPage<RchxNotifications> result = notificationMapper.selectNotificationPage(page, zgh, isRead, type);
            
            log.debug("查询教师通知列表: zgh={}, current={}, size={}, total={}", 
                     zgh, current, size, result.getTotal());
            
            return result;
        } catch (Exception e) {
            log.error("查询教师通知列表失败: zgh={}, error={}", zgh, e.getMessage());
            throw new RuntimeException("查询通知列表失败", e);
        }
    }

    @Override
    public long getUnreadCount(String zgh) {
        try {
            // 先从Redis缓存获取
            long cachedCount = notificationRedisService.getTeacherUnreadCount(zgh);
            if (cachedCount >= 0) {
                return cachedCount;
            }

            // 缓存中没有，从数据库查询
            long count = notificationMapper.countUnreadByZgh(zgh);
            
            // 更新Redis缓存
            notificationRedisService.setTeacherUnreadCount(zgh, count);
            
            log.debug("获取教师未读通知数量: zgh={}, count={}", zgh, count);
            return count;
        } catch (Exception e) {
            log.error("获取教师未读通知数量失败: zgh={}, error={}", zgh, e.getMessage());
            return 0;
        }
    }

    @Override
    public List<RchxNotifications> getLatestNotifications(String zgh, int limit) {
        try {
            List<RchxNotifications> notifications = notificationMapper.selectLatestNotifications(zgh, limit);
            log.debug("获取教师最新通知: zgh={}, limit={}, count={}", zgh, limit, notifications.size());
            return notifications;
        } catch (Exception e) {
            log.error("获取教师最新通知失败: zgh={}, error={}", zgh, e.getMessage());
            throw new RuntimeException("获取最新通知失败", e);
        }
    }

    @Override
    @Transactional
    public boolean markAsRead(String zgh, Long notificationId) {
        try {
            // 更新数据库
            RchxNotifications notification = new RchxNotifications();
            notification.setId(notificationId);
            notification.setIsRead(1);
            notification.setReadTime(LocalDateTime.now());
            
            boolean success = updateById(notification);
            
            if (success) {
                // 更新Redis缓存
                notificationRedisService.decrementTeacherUnreadCount(zgh, 1);
                log.info("标记通知已读成功: zgh={}, notificationId={}", zgh, notificationId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("标记通知已读失败: zgh={}, notificationId={}, error={}", zgh, notificationId, e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional
    public int batchMarkAsRead(String zgh, List<Long> ids) {
        try {
            int count = notificationMapper.batchMarkAsRead(zgh, ids);
            
            if (count > 0) {
                // 更新Redis缓存
                notificationRedisService.decrementTeacherUnreadCount(zgh, count);
                log.info("批量标记通知已读成功: zgh={}, count={}", zgh, count);
            }
            
            return count;
        } catch (Exception e) {
            log.error("批量标记通知已读失败: zgh={}, error={}", zgh, e.getMessage());
            return 0;
        }
    }

    @Override
    @Transactional
    public int markAllAsRead(String zgh) {
        try {
            int count = notificationMapper.markAllAsRead(zgh);
            
            if (count > 0) {
                // 清除Redis缓存中的未读数量
                notificationRedisService.setTeacherUnreadCount(zgh, 0);
                log.info("标记所有通知已读成功: zgh={}, count={}", zgh, count);
            }
            
            return count;
        } catch (Exception e) {
            log.error("标记所有通知已读失败: zgh={}, error={}", zgh, e.getMessage());
            return 0;
        }
    }

    @Override
    @Transactional
    public Long createNotification(String zgh, String title, String content, String type, 
                                   Integer priority, String senderZgh, String senderName) {
        try {
            RchxNotifications notification = new RchxNotifications();
            notification.setZgh(zgh);
            notification.setTitle(title);
            notification.setContent(content);
            notification.setType(type);
            notification.setPriority(priority != null ? priority : 1);
            notification.setIsRead(0);
            notification.setSenderZgh(senderZgh);
            notification.setSenderName(senderName);
            notification.setCreateTime(LocalDateTime.now());

            boolean success = save(notification);
            
            if (success) {
                // 更新Redis缓存
                notificationRedisService.incrementTeacherUnreadCount(zgh, 1);
                
                // 异步发送WebSocket通知
                notificationSendService.sendNotificationToUser(zgh, notification);
                
                log.info("创建通知成功: zgh={}, title={}, type={}, id={}", 
                        zgh, title, type, notification.getId());
                
                return notification.getId();
            }
            
            return null;
        } catch (Exception e) {
            log.error("创建通知失败: zgh={}, title={}, error={}", zgh, title, e.getMessage());
            throw new RuntimeException("创建通知失败", e);
        }
    }

    @Override
    public Map<String, Object> getSystemStats() {
        try {
            Map<String, Object> stats = notificationMapper.getSystemStats();
            log.debug("获取系统统计信息: {}", stats);
            return stats;
        } catch (Exception e) {
            log.error("获取系统统计信息失败: {}", e.getMessage());
            throw new RuntimeException("获取系统统计信息失败", e);
        }
    }
}
