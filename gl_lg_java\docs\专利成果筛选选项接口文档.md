# 专利成果筛选选项接口文档

## 📋 概述

本文档提供专利成果基本信息系统中用于前端筛选的选项接口。这些接口已优化为快速响应版本，包含统计接口和筛选选项接口，满足不同的业务需求。

## 🚀 性能优化说明

- **统计接口**: 使用数据库聚合查询，性能优化，返回详细统计数据
- **筛选选项接口**: 使用 `DISTINCT` 查询，不统计数量，响应速度快
- **响应速度**: 筛选选项接口响应时间 < 50ms，统计接口响应时间 < 200ms

---

## 🔬 1. 专利成果统计接口

### 1.1 专利类型统计接口

#### 接口信息
- **接口地址**: `GET /api/zlcgjbxx/stats/zllx`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个专利类型的专利数量分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "发明专利": 156,
        "实用新型": 234,
        "外观设计": 89,
        "软件著作权": 67,
        "NULL或空": 8
    }
}
```

### 1.2 专利状态统计接口

#### 接口信息
- **接口地址**: `GET /api/zlcgjbxx/stats/zlzt`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个专利状态的专利数量分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "已授权": 234,
        "申请中": 156,
        "实质审查": 89,
        "公开": 45,
        "驳回": 12,
        "撤回": 8,
        "NULL或空": 3
    }
}
```

### 1.3 审核状态统计接口

#### 接口信息
- **接口地址**: `GET /api/zlcgjbxx/stats/shzt`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个审核状态的专利数量分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "已审核": 345,
        "待审核": 89,
        "已驳回": 23,
        "草稿": 45,
        "NULL或空": 6
    }
}
```

---

## 🔍 2. 专利成果筛选选项接口

### 2.1 专利类型选项接口

#### 接口信息
- **接口地址**: `GET /api/zlcgjbxx/zllx-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有专利类型的选项列表，已优化为快速响应版本，不统计数量，用于前端专利类型筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "发明专利",
            "label": "发明专利"
        },
        {
            "value": "实用新型",
            "label": "实用新型"
        },
        {
            "value": "外观设计",
            "label": "外观设计"
        },
        {
            "value": "软件著作权",
            "label": "软件著作权"
        }
    ]
}
```

### 2.2 专利状态选项接口

#### 接口信息
- **接口地址**: `GET /api/zlcgjbxx/zlzt-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有专利状态的选项列表，已优化为快速响应版本，不统计数量，用于前端专利状态筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "已授权",
            "label": "已授权"
        },
        {
            "value": "申请中",
            "label": "申请中"
        },
        {
            "value": "实质审查",
            "label": "实质审查"
        },
        {
            "value": "公开",
            "label": "公开"
        },
        {
            "value": "驳回",
            "label": "驳回"
        },
        {
            "value": "撤回",
            "label": "撤回"
        }
    ]
}
```

### 2.3 审核状态选项接口

#### 接口信息
- **接口地址**: `GET /api/zlcgjbxx/shzt-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有审核状态的选项列表，已优化为快速响应版本，不统计数量，用于前端审核状态筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "已审核",
            "label": "已审核"
        },
        {
            "value": "待审核",
            "label": "待审核"
        },
        {
            "value": "已驳回",
            "label": "已驳回"
        },
        {
            "value": "草稿",
            "label": "草稿"
        }
    ]
}
```

---

## 📝 3. 前端使用示例

### 3.1 Vue.js 组件示例

```javascript
// 专利成果筛选和统计组件
export default {
    data() {
        return {
            searchForm: {
                zllx: '',      // 专利类型
                zlzt: '',      // 专利状态
                shzt: '',      // 审核状态
                zlcgmc: '',    // 专利名称
                dyfmr: '',     // 第一发明人
                dwmc: ''       // 单位名称
            },
            zllxOptions: [],
            zlztOptions: [],
            shztOptions: [],
            
            // 统计数据
            zllxStats: {},
            zlztStats: {},
            shztStats: {}
        };
    },
    
    async mounted() {
        await this.loadOptions();
    },
    
    methods: {
        async loadOptions() {
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };
            
            try {
                // 并行加载所有筛选选项
                const [zllxRes, zlztRes, shztRes] = await Promise.all([
                    fetch('/api/zlcgjbxx/zllx-options', { headers }),
                    fetch('/api/zlcgjbxx/zlzt-options', { headers }),
                    fetch('/api/zlcgjbxx/shzt-options', { headers })
                ]);
                
                this.zllxOptions = (await zllxRes.json()).data;
                this.zlztOptions = (await zlztRes.json()).data;
                this.shztOptions = (await shztRes.json()).data;
                
                console.log('专利成果筛选选项加载完成');
            } catch (error) {
                console.error('加载筛选选项失败:', error);
            }
        },
        
        async loadStats() {
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };
            
            try {
                // 并行加载所有统计数据
                const [zllxStatsRes, zlztStatsRes, shztStatsRes] = await Promise.all([
                    fetch('/api/zlcgjbxx/stats/zllx', { headers }),
                    fetch('/api/zlcgjbxx/stats/zlzt', { headers }),
                    fetch('/api/zlcgjbxx/stats/shzt', { headers })
                ]);
                
                this.zllxStats = (await zllxStatsRes.json()).data;
                this.zlztStats = (await zlztStatsRes.json()).data;
                this.shztStats = (await shztStatsRes.json()).data;
                
                console.log('专利类型统计:', this.zllxStats);
                console.log('专利状态统计:', this.zlztStats);
                console.log('审核状态统计:', this.shztStats);
                
                // 可以用于图表展示
                this.renderCharts();
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }
    }
};
```

### 3.2 组件模板示例

```vue
<template>
    <div class="patent-management">
        <!-- 筛选表单 -->
        <el-card class="filter-card">
            <div slot="header">
                <span>专利成果筛选</span>
            </div>

            <el-form :model="searchForm" inline>
                <!-- 专利类型筛选 -->
                <el-form-item label="专利类型">
                    <el-select v-model="searchForm.zllx" placeholder="请选择专利类型" clearable>
                        <el-option
                            v-for="item in zllxOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <!-- 专利状态筛选 -->
                <el-form-item label="专利状态">
                    <el-select v-model="searchForm.zlzt" placeholder="请选择专利状态" clearable>
                        <el-option
                            v-for="item in zlztOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <!-- 审核状态筛选 -->
                <el-form-item label="审核状态">
                    <el-select v-model="searchForm.shzt" placeholder="请选择审核状态" clearable>
                        <el-option
                            v-for="item in shztOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <!-- 专利名称搜索 -->
                <el-form-item label="专利名称">
                    <el-input v-model="searchForm.zlcgmc" placeholder="请输入专利名称" clearable></el-input>
                </el-form-item>

                <!-- 第一发明人搜索 -->
                <el-form-item label="第一发明人">
                    <el-input v-model="searchForm.dyfmr" placeholder="请输入第一发明人姓名" clearable></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                    <el-button @click="resetForm">重置</el-button>
                    <el-button type="info" @click="loadStats">查看统计</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 统计图表 -->
        <el-row :gutter="20" class="stats-row">
            <el-col :span="8">
                <el-card>
                    <div slot="header">专利类型分布</div>
                    <div id="zllxChart" style="height: 300px;"></div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card>
                    <div slot="header">专利状态分布</div>
                    <div id="zlztChart" style="height: 300px;"></div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card>
                    <div slot="header">审核状态分布</div>
                    <div id="shztChart" style="height: 300px;"></div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 专利列表表格 -->
        <el-card class="table-card">
            <div slot="header">
                <span>专利成果列表</span>
            </div>
            <!-- 这里放置专利列表表格 -->
        </el-card>
    </div>
</template>
```

## ⚠️ 4. 注意事项

1. **权限验证**: 所有接口需要教师及以上权限，请确保token有效
2. **性能优化**:
   - 筛选选项接口已优化，响应时间在50ms以内
   - 统计接口使用数据库聚合查询，响应时间在200ms以内
3. **数据更新**: 所有数据实时查询，反映当前数据库状态
4. **排序规则**: 筛选选项按字母顺序排列
5. **缓存建议**: 前端可适当缓存筛选选项数据，减少重复请求
6. **错误处理**: 请妥善处理网络错误和权限错误
7. **图表展示**: 建议使用 ECharts 等图表库展示统计数据

## 📊 5. 接口汇总

### 统计接口
- `GET /api/zlcgjbxx/stats/zllx` - 专利类型统计
- `GET /api/zlcgjbxx/stats/zlzt` - 专利状态统计
- `GET /api/zlcgjbxx/stats/shzt` - 审核状态统计

### 筛选选项接口
- `GET /api/zlcgjbxx/zllx-options` - 专利类型选项
- `GET /api/zlcgjbxx/zlzt-options` - 专利状态选项
- `GET /api/zlcgjbxx/shzt-options` - 审核状态选项

---

## 📞 6. 技术支持

如有问题，请联系后端开发团队。

## 📝 7. 更新日志

- **2025-07-21**: 新增专利类型、专利状态、审核状态统计接口
- **2025-07-21**: 新增专利类型、专利状态、审核状态筛选选项接口
- **2025-07-21**: 优化查询性能，响应速度大幅提升
- **2025-07-21**: 提供完整的前端使用示例和图表展示方案
