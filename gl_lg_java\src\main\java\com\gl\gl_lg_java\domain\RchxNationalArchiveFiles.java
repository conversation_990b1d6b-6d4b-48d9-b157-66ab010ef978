package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 国家/省部级项目归档文件表
 * @TableName t_rchx_national_archive_files
 */
@TableName(value = "t_rchx_national_archive_files")
@Data
public class RchxNationalArchiveFiles implements Serializable {
    
    /**
     * 文件ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 归档ID
     */
    @TableField(value = "archive_id")
    private Long archiveId;

    /**
     * 项目编号
     */
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 文件类别(APPLICATION申报书,CONTRACT合同书,REPORT研究报告,ACHIEVEMENT成果材料,FINANCIAL财务材料,OTHER其他)
     */
    @TableField(value = "file_category")
    private String fileCategory;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 原始文件名
     */
    @TableField(value = "file_original_name")
    private String fileOriginalName;

    /**
     * MinIO文件路径
     */
    @TableField(value = "file_path")
    private String filePath;

    /**
     * 文件大小(字节)
     */
    @TableField(value = "file_size")
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    @TableField(value = "file_type")
    private String fileType;

    /**
     * 文件扩展名
     */
    @TableField(value = "file_extension")
    private String fileExtension;

    /**
     * MinIO存储桶名称
     */
    @TableField(value = "bucket_name")
    private String bucketName;

    /**
     * MinIO对象名称
     */
    @TableField(value = "object_name")
    private String objectName;

    /**
     * 文件MD5值
     */
    @TableField(value = "file_md5")
    private String fileMd5;

    /**
     * 文件描述
     */
    @TableField(value = "file_description")
    private String fileDescription;

    /**
     * 是否必需文件(0否,1是)
     */
    @TableField(value = "is_required")
    private Boolean isRequired;

    /**
     * 是否机密文件(0否,1是)
     */
    @TableField(value = "is_confidential")
    private Boolean isConfidential;

    /**
     * 上传时间
     */
    @TableField(value = "upload_time")
    private LocalDateTime uploadTime;

    /**
     * 上传人职工号
     */
    @TableField(value = "upload_by")
    private String uploadBy;

    /**
     * 上传人姓名
     */
    @TableField(value = "upload_by_name")
    private String uploadByName;

    /**
     * 下载次数
     */
    @TableField(value = "download_count")
    private Integer downloadCount;

    /**
     * 最后下载时间
     */
    @TableField(value = "last_download_time")
    private LocalDateTime lastDownloadTime;

    /**
     * 是否删除(0否,1是)
     */
    @TableField(value = "is_deleted")
    private Boolean isDeleted;

    /**
     * 删除时间
     */
    @TableField(value = "delete_time")
    private LocalDateTime deleteTime;

    /**
     * 删除人职工号
     */
    @TableField(value = "delete_by")
    private String deleteBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
