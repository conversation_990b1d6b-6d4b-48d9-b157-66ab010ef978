package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目申报表实体类
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_rchx_project_application")
public class RchxProjectApplication {

    /**
     * 申报ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目编号
     */
    @TableField("project_code")
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目大类ID
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 项目类别ID
     */
    @TableField("type_id")
    private Integer typeId;

    /**
     * 管理部门ID
     */
    @TableField("dept_id")
    private Integer deptId;

    /**
     * 申报人职工号
     */
    @TableField("applicant_zgh")
    private String applicantZgh;

    /**
     * 申报人姓名
     */
    @TableField("applicant_name")
    private String applicantName;

    /**
     * 填报指南内容
     */
    @TableField("guide_content")
    private String guideContent;

    /**
     * 受理开始时间
     */
    @TableField("accept_start_time")
    private LocalDateTime acceptStartTime;

    /**
     * 受理结束时间
     */
    @TableField("accept_end_time")
    private LocalDateTime acceptEndTime;

    /**
     * 状态(DRAFT草稿,SUBMITTED已提交,REVIEWING审核中,APPROVED通过,REJECTED拒绝)
     */
    @TableField("status")
    private String status;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 审核人职工号
     */
    @TableField("reviewer_zgh")
    private String reviewerZgh;

    /**
     * 审核意见
     */
    @TableField("review_comments")
    private String reviewComments;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人职工号
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人职工号
     */
    @TableField("update_by")
    private String updateBy;
}
