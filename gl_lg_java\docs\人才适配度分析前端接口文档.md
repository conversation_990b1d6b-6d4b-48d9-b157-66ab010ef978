# 人才适配度分析前端接口文档

## 📋 接口概览

| 接口名称 | 方法 | 路径 | 权限 | 功能描述 |
|---------|------|------|------|----------|
| 人才适配度分析 | POST | `/api/talent-matching/analyze` | 学院管理员 | 根据项目需求分析最适合的教师 |
| 教师能力分析 | GET | `/api/talent-matching/teacher-analysis/{teacherId}` | 教师 | 获取教师详细能力报告 |
| 获取预设模板 | GET | `/api/talent-matching/templates` | 教师 | 获取项目需求模板 |
| 快速匹配分析 | POST | `/api/talent-matching/quick-analyze` | 学院管理员 | 使用模板快速匹配 |
| 批量教师评估 | POST | `/api/talent-matching/batch-evaluate` | 学院管理员 | 批量评估教师能力 |
| 分析统计信息 | GET | `/api/talent-matching/statistics` | 学院管理员 | 获取统计数据 |

---

## 🎯 1. 人才适配度分析

### 接口信息
- **URL**: `POST /api/talent-matching/analyze`
- **权限**: 学院管理员
- **Content-Type**: `application/json`

### 请求参数
```typescript
interface ProjectRequirementDTO {
  // 项目基本信息
  projectName: string;              // 项目名称 (必填)
  projectType?: string;             // 项目类型 (基础研究/应用研究/产业化)
  projectDescription?: string;      // 项目描述
  
  // 领域要求
  requiredFields?: string[];        // 要求的研究领域
  requiredXkml?: string[];         // 要求的学科门类 (工学/理学/管理学等)
  requiredYjxk?: string[];         // 要求的一级学科
  projectKeywords?: string[];       // 项目关键词
  
  // 基础要求
  minTitleLevel?: string;          // 最低职称等级 (正高级/副高级/中级/初级)
  minEducation?: string;           // 最低学历 (博士/硕士/本科)
  
  // 权重配置 (四个权重之和必须为100)
  academicWeight: number;          // 学术影响力权重 (0-100)
  fieldWeight: number;             // 领域深耕度权重 (0-100)
  teamWeight: number;              // 团队适配值权重 (0-100)
  practicalWeight: number;         // 成果落地性权重 (0-100)
  
  // 筛选条件
  minAwardCount?: number;          // 最低获奖数量 (默认0)
  minProjectCount?: number;        // 最低项目数量 (默认0)
  requireRecentAwards?: boolean;   // 是否要求近期获奖 (默认false)
  requireCollaboration?: boolean;  // 是否要求合作经验 (默认false)
  excludeTeacherIds?: string[];    // 排除的教师ID列表
  maxResults?: number;             // 最大返回结果数 (默认50)
  minScoreThreshold?: number;      // 最低适配度分数 (默认60)
}
```

### 请求示例
```javascript
const analyzeRequest = {
  projectName: "智能教育平台开发项目",
  projectType: "应用研究",
  projectDescription: "开发基于AI的个性化学习平台",
  requiredXkml: ["工学"],
  requiredYjxk: ["计算机科学与技术"],
  minTitleLevel: "副高级",
  minEducation: "硕士",
  projectKeywords: ["人工智能", "教育技术", "机器学习"],
  academicWeight: 25,
  fieldWeight: 35,
  teamWeight: 25,
  practicalWeight: 15,
  maxResults: 10,
  minScoreThreshold: 70
};

// 发送请求
fetch('/api/talent-matching/analyze', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(analyzeRequest)
});
```

### 响应数据
```typescript
interface TalentMatchingResultDTO {
  // 教师基本信息
  teacherId: string;               // 教师职工号
  teacherName: string;             // 教师姓名
  department: string;              // 所属部门
  title: string;                   // 职称
  titleLevel: string;              // 职称等级
  education: string;               // 最高学历
  
  // 各维度得分
  academicScore: number;           // 学术影响力得分 (0-100)
  fieldScore: number;              // 领域深耕度得分 (0-100)
  teamScore: number;               // 团队适配值得分 (0-100)
  practicalScore: number;          // 成果落地性得分 (0-100)
  totalScore: number;              // 总适配度得分 (0-100)
  ranking: number;                 // 排名
  
  // 推荐信息
  recommendationLevel: 'HIGHLY_RECOMMENDED' | 'RECOMMENDED' | 'CONDITIONAL' | 'NOT_RECOMMENDED';
  recommendationReasons: string[]; // 推荐理由
  riskWarnings: string[];          // 风险预警
  
  // 统计数据
  totalAwards: number;             // 获奖总数
  recentAwards: number;            // 近3年获奖数
  totalProjects: number;           // 项目总数
  collaborativeProjects: number;   // 合作项目数
  researchFields: string[];        // 研究领域
}
```

---

## 👨‍🏫 2. 教师能力分析

### 接口信息
- **URL**: `GET /api/talent-matching/teacher-analysis/{teacherId}`
- **权限**: 教师

### 请求示例
```javascript
// 获取教师T001的能力分析
fetch('/api/talent-matching/teacher-analysis/T001', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 响应数据
```typescript
interface TeacherCapabilityDTO {
  // 基本信息
  teacherId: string;
  teacherName: string;
  department: string;
  title: string;
  titleLevel: string;
  education: string;
  
  // 能力维度得分
  academicInfluenceScore: number;      // 学术影响力得分
  fieldExpertiseScore: number;         // 领域深耕度得分
  teamCompatibilityScore: number;      // 团队适配值得分
  achievementPracticalityScore: number; // 成果落地性得分
  overallScore: number;                // 综合能力得分
  
  // 详细统计
  awardStatistics: {
    totalAwards: number;               // 获奖总数
    nationalAwards: number;            // 国家级获奖
    provincialAwards: number;          // 省级获奖
    recentAwards: number;              // 近3年获奖
    awardTypes: string[];              // 获奖类型
  };
  
  projectStatistics: {
    totalProjects: number;             // 项目总数
    leadingProjects: number;           // 主持项目数
    ongoingProjects: number;           // 进行中项目
    completedProjects: number;         // 已完成项目
  };
  
  collaborationStatistics: {
    collaborativeAwards: number;       // 合作获奖数
    collaborationCount: number;        // 合作次数
    collaborationRate: number;         // 合作率(%)
  };
  
  fieldDistributions: Array<{
    fieldName: string;                 // 领域名称
    achievementCount: number;          // 成果数量
    percentage: number;                // 占比(%)
  }>;
  
  capabilityTags: string[];            // 能力标签
}
```

---

## 📋 3. 获取预设模板

### 接口信息
- **URL**: `GET /api/talent-matching/templates`
- **权限**: 教师

### 请求示例
```javascript
fetch('/api/talent-matching/templates', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 响应数据
```typescript
// 返回预设模板数组
interface ProjectRequirementDTO[] {
  // 基础研究项目模板
  {
    projectName: "基础研究项目模板",
    projectType: "基础研究",
    minTitleLevel: "副高级",
    minEducation: "硕士",
    academicWeight: 40,
    fieldWeight: 30,
    teamWeight: 20,
    practicalWeight: 10
  },
  // 应用研究项目模板
  {
    projectName: "应用研究项目模板",
    projectType: "应用研究",
    minTitleLevel: "副高级",
    minEducation: "硕士",
    academicWeight: 25,
    fieldWeight: 35,
    teamWeight: 25,
    practicalWeight: 15
  },
  // 产业化项目模板
  {
    projectName: "产业化项目模板",
    projectType: "产业化",
    minTitleLevel: "中级",
    minEducation: "本科",
    academicWeight: 15,
    fieldWeight: 25,
    teamWeight: 30,
    practicalWeight: 30
  }
}
```

---

## ⚡ 4. 快速匹配分析

### 接口信息
- **URL**: `POST /api/talent-matching/quick-analyze`
- **权限**: 学院管理员

### 请求参数
```typescript
interface QuickAnalyzeParams {
  templateType: string;      // 模板类型: "基础研究" | "应用研究" | "产业化"
  projectName: string;       // 项目名称
  projectDescription?: string; // 项目描述 (可选)
}
```

### 请求示例
```javascript
// 使用URL参数
const params = new URLSearchParams({
  templateType: '应用研究',
  projectName: 'AI教育创新项目',
  projectDescription: '研究AI在教育中的应用'
});

fetch(`/api/talent-matching/quick-analyze?${params}`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 响应数据
返回与"人才适配度分析"相同的 `TalentMatchingResultDTO[]` 数组

---

## 📊 5. 批量教师评估

### 接口信息
- **URL**: `POST /api/talent-matching/batch-evaluate`
- **权限**: 学院管理员
- **Content-Type**: `application/json`

### 请求参数
```typescript
interface BatchEvaluateRequest {
  teacherIds: string[];      // 教师ID数组
}
```

### 请求示例
```javascript
const batchRequest = {
  teacherIds: ["T001", "T002", "T003", "T004", "T005"]
};

fetch('/api/talent-matching/batch-evaluate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(batchRequest)
});
```

### 响应数据
返回 `TeacherCapabilityDTO[]` 数组，包含所有教师的能力分析

---

## 📈 6. 分析统计信息

### 接口信息
- **URL**: `GET /api/talent-matching/statistics`
- **权限**: 学院管理员

### 请求示例
```javascript
fetch('/api/talent-matching/statistics', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 响应数据
```typescript
interface StatisticsResponse {
  message: string;           // 状态信息
  timestamp: string;         // 时间戳
  // 后续可扩展更多统计数据
}
```

---

## 🎨 前端组件建议

### 1. 项目配置组件
```vue
<template>
  <div class="project-config">
    <!-- 基本信息 -->
    <el-form :model="projectConfig">
      <el-form-item label="项目名称">
        <el-input v-model="projectConfig.projectName" />
      </el-form-item>
      <el-form-item label="项目类型">
        <el-select v-model="projectConfig.projectType">
          <el-option label="基础研究" value="基础研究" />
          <el-option label="应用研究" value="应用研究" />
          <el-option label="产业化" value="产业化" />
        </el-select>
      </el-form-item>
    </el-form>
    
    <!-- 权重配置滑块 -->
    <div class="weight-config">
      <div class="weight-item">
        <label>学术影响力: {{ weights.academic }}%</label>
        <el-slider v-model="weights.academic" :max="100" />
      </div>
      <!-- 其他权重... -->
    </div>
  </div>
</template>
```

### 2. 结果展示组件
```vue
<template>
  <div class="matching-results">
    <!-- 候选人列表 -->
    <el-table :data="candidates">
      <el-table-column prop="ranking" label="排名" width="80" />
      <el-table-column prop="teacherName" label="姓名" />
      <el-table-column prop="department" label="部门" />
      <el-table-column label="适配度">
        <template #default="{ row }">
          <el-progress 
            :percentage="row.totalScore" 
            :color="getScoreColor(row.totalScore)"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
```

## 🔧 错误处理

所有接口都返回统一的响应格式：
```typescript
interface ApiResponse<T> {
  code: number;              // 状态码: 200成功, 其他失败
  message: string;           // 响应消息
  data?: T;                  // 响应数据
}
```

### 常见错误码
- `200`: 成功
- `400`: 请求参数错误 (如权重总和不为100)
- `401`: 未授权 (Token无效)
- `403`: 权限不足
- `500`: 服务器内部错误

### 前端错误处理示例
```javascript
async function analyzeTalent(request) {
  try {
    const response = await fetch('/api/talent-matching/analyze', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(request)
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('分析失败:', error.message);
    // 显示错误提示
  }
}
```
