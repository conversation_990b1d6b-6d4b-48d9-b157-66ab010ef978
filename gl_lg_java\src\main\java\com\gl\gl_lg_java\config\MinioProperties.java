package com.gl.gl_lg_java.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MinIO配置属性类
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@Component
@ConfigurationProperties(prefix = "minio")
public class MinioProperties {

    /**
     * MinIO服务器地址
     */
    private String endpoint;

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 秘密密钥
     */
    private String secretKey;

    /**
     * 默认存储桶名称
     */
    private String bucketName;

    /**
     * 文件访问URL前缀
     */
    private String urlPrefix;

    /**
     * 连接超时时间（秒）
     */
    private Integer connectTimeout;

    /**
     * 写入超时时间（秒）
     */
    private Integer writeTimeout;

    /**
     * 读取超时时间（秒）
     */
    private Integer readTimeout;

    /**
     * 校级项目文件存储桶名称
     */
    private String schoolProjectBucketName = "rchx-school-project";

    /**
     * 预签名URL过期时间（秒）
     */
    private Integer presignedUrlExpiry = 3600;

    /**
     * 最大文件大小（字节）
     */
    private Long maxFileSize = 100 * 1024 * 1024L; // 100MB

    /**
     * 允许的文件类型
     */
    private String[] allowedFileTypes = {
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "image/jpeg",
            "image/png",
            "image/gif",
            "text/plain",
            "application/zip",
            "application/x-rar-compressed"
    };
}
