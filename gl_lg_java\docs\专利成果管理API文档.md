# 专利成果管理API接口文档

## 基础信息

- **基础URL**: `http://localhost:8080/api/zlcgjbxx`
- **认证方式**: JWT Token
- **权限说明**:
  - **教师**: 可以查看专利信息（只能查看自己的）
  - **评审**: 可以查看专利信息和审核状态
  - **学院管理员**: 可以增删改查专利信息
  - **系统管理员**: 拥有所有权限

---

## 1. 查询接口

### 1.1 根据专利成果编号查询

**接口信息**
- **URL**: `GET /api/zlcgjbxx/{zlcgbh}`
- **权限**: 教师及以上

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| zlcgbh | String | Path | 专利成果编号 | ZL202012345678 |

**响应示例**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "zlcgbh": "ZL202012345678",
        "zlcgmc": "一种新型发明专利",
        "dwh": "001",
        "dwmc": "计算机学院",
        "zllx": "发明专利",
        "zlzt": "已授权",
        "shzt": "已审核",
        "dyfmr": "张三",
        "dyfmrzgh": "T001",
        "pzrq": "2020-12-01",
        "sqzlrq": "2020-01-01"
    }
}
```

### 1.2 分页查询专利成果信息

**接口信息**
- **URL**: `POST /api/zlcgjbxx/page`
- **权限**: 教师及以上

**请求参数**
```json
{
    "zlcgbh": "ZL2020",
    "zlcgmc": "发明",
    "dwmc": "计算机",
    "zllx": "发明专利",
    "zlzt": "已授权",
    "shzt": "已审核",
    "dyfmr": "张三",
    "dyfmrzgh": "T001",
    "startDate": "2020-01-01",
    "endDate": "2020-12-31",
    "pageNum": 1,
    "pageSize": 10,
    "orderBy": "cjsj",
    "orderDirection": "desc"
}
```

**响应示例**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "zlcgbh": "ZL202012345678",
                "zlcgmc": "一种新型发明专利",
                "dwmc": "计算机学院",
                "zllx": "发明专利",
                "zlzt": "已授权",
                "shzt": "已审核",
                "dyfmr": "张三",
                "dyfmrzgh": "T001"
            }
        ],
        "total": 100,
        "size": 10,
        "current": 1,
        "pages": 10
    }
}
```

### 1.3 多条件查询（不分页）

**接口信息**
- **URL**: `POST /api/zlcgjbxx/list`
- **权限**: 教师及以上

**请求参数**
```json
{
    "zlcgbh": "ZL2020",
    "zlcgmc": "发明",
    "dwmc": "计算机",
    "zllx": "发明专利",
    "zlzt": "已授权",
    "shzt": "已审核",
    "dyfmr": "张三",
    "dyfmrzgh": "T001",
    "startDate": "2020-01-01",
    "endDate": "2020-12-31"
}
```

### 1.4 根据专利成果名称模糊查询

**接口信息**
- **URL**: `GET /api/zlcgjbxx/name/{zlcgmc}`
- **权限**: 教师及以上

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| zlcgmc | String | Path | 专利成果名称（支持模糊查询） | 发明 |

### 1.5 根据第一发明人职工号查询

**接口信息**
- **URL**: `GET /api/zlcgjbxx/inventor/{dyfmrzgh}`
- **权限**: 教师及以上
- **权限控制**: 教师只能查看自己的专利

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| dyfmrzgh | String | Path | 第一发明人职工号 | T001 |

### 1.6 根据专利类型查询

**接口信息**
- **URL**: `GET /api/zlcgjbxx/type/{zllx}`
- **权限**: 教师及以上

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| zllx | String | Path | 专利类型 | 发明专利 |

### 1.7 根据专利状态查询

**接口信息**
- **URL**: `GET /api/zlcgjbxx/status/{zlzt}`
- **权限**: 教师及以上

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| zlzt | String | Path | 专利状态 | 已授权 |

### 1.8 根据审核状态查询

**接口信息**
- **URL**: `GET /api/zlcgjbxx/audit/{shzt}`
- **权限**: 评审及以上

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| shzt | String | Path | 审核状态 | 已审核 |

---

## 2. 新增接口

### 2.1 新增专利成果信息

**接口信息**
- **URL**: `POST /api/zlcgjbxx`
- **权限**: 学院管理员及以上

**请求参数**
```json
{
    "zlcgbh": "ZL202012345678",
    "zlcgmc": "一种新型发明专利",
    "dwh": "001",
    "dwmc": "计算机学院",
    "zllx": "发明专利",
    "zlzt": "申请中",
    "shzt": "待审核",
    "dyfmr": "张三",
    "dyfmrzgh": "T001",
    "sqzlrq": "2020-01-01",
    "zlnr": "专利内容描述"
}
```

**响应示例**
```json
{
    "code": 200,
    "message": "新增成功",
    "data": null
}
```

### 2.2 批量新增专利成果信息

**接口信息**
- **URL**: `POST /api/zlcgjbxx/batch`
- **权限**: 学院管理员及以上

**请求参数**
```json
[
    {
        "zlcgbh": "ZL202012345678",
        "zlcgmc": "一种新型发明专利",
        "dwmc": "计算机学院",
        "zllx": "发明专利",
        "dyfmr": "张三",
        "dyfmrzgh": "T001"
    },
    {
        "zlcgbh": "ZL202012345679",
        "zlcgmc": "另一种发明专利",
        "dwmc": "计算机学院",
        "zllx": "实用新型",
        "dyfmr": "李四",
        "dyfmrzgh": "T002"
    }
]
```

---

## 3. 更新接口

### 3.1 更新专利成果信息

**接口信息**
- **URL**: `PUT /api/zlcgjbxx`
- **权限**: 学院管理员及以上

**请求参数**
```json
{
    "zlcgbh": "ZL202012345678",
    "zlcgmc": "更新后的专利名称",
    "zlzt": "已授权",
    "shzt": "已审核",
    "pzrq": "2020-12-01"
}
```

### 3.2 批量更新专利成果信息

**接口信息**
- **URL**: `PUT /api/zlcgjbxx/batch`
- **权限**: 学院管理员及以上

**请求参数**
```json
[
    {
        "zlcgbh": "ZL202012345678",
        "zlzt": "已授权",
        "shzt": "已审核"
    },
    {
        "zlcgbh": "ZL202012345679",
        "zlzt": "已授权",
        "shzt": "已审核"
    }
]
```

---

## 4. 删除接口

### 4.1 删除专利成果信息

**接口信息**
- **URL**: `DELETE /api/zlcgjbxx/{zlcgbh}`
- **权限**: 系统管理员

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| zlcgbh | String | Path | 专利成果编号 | ZL202012345678 |

### 4.2 批量删除专利成果信息

**接口信息**
- **URL**: `DELETE /api/zlcgjbxx/batch`
- **权限**: 系统管理员

**请求参数**
```json
["ZL202012345678", "ZL202012345679", "ZL202012345680"]
```

---

## 5. 查询参数说明

### ZlcgjbxxQueryDTO 参数说明

| 参数名 | 类型 | 说明 | 是否必填 | 示例 |
|--------|------|------|----------|------|
| zlcgbh | String | 专利成果编号（模糊查询） | 否 | ZL2020 |
| zlcgmc | String | 专利成果名称（模糊查询） | 否 | 发明 |
| dwh | String | 单位号 | 否 | 001 |
| dwmc | String | 单位名称（模糊查询） | 否 | 计算机 |
| zllx | String | 专利类型 | 否 | 发明专利 |
| zlzt | String | 专利状态 | 否 | 已授权 |
| shzt | String | 审核状态 | 否 | 已审核 |
| dyfmr | String | 第一发明人（模糊查询） | 否 | 张三 |
| dyfmrzgh | String | 第一发明人职工号 | 否 | T001 |
| startDate | String | 开始日期（批准日期） | 否 | 2020-01-01 |
| endDate | String | 结束日期（批准日期） | 否 | 2020-12-31 |
| pageNum | Integer | 页码 | 否 | 1 |
| pageSize | Integer | 页大小 | 否 | 10 |
| orderBy | String | 排序字段 | 否 | cjsj |
| orderDirection | String | 排序方向 | 否 | desc |

---

## 6. 错误响应示例

### 权限不足
```json
{
    "code": 403,
    "message": "权限不足，只能查看自己的专利信息",
    "data": null
}
```

### 参数错误
```json
{
    "code": 500,
    "message": "专利成果编号不能为空",
    "data": null
}
```

### 数据不存在
```json
{
    "code": 500,
    "message": "专利成果信息不存在",
    "data": null
}
```

---

## 7. 使用示例

### 前端Vue.js调用示例

```javascript
// 分页查询专利信息
async getPatentList() {
    const queryData = {
        zlcgmc: '发明',
        zllx: '发明专利',
        pageNum: 1,
        pageSize: 10
    }
    
    const response = await this.$http.post('/api/zlcgjbxx/page', queryData)
    if (response.data.code === 200) {
        this.patentList = response.data.data.records
        this.total = response.data.data.total
    }
}

// 新增专利信息
async createPatent() {
    const patentData = {
        zlcgbh: 'ZL202012345678',
        zlcgmc: '一种新型发明专利',
        dwmc: '计算机学院',
        zllx: '发明专利',
        dyfmr: '张三',
        dyfmrzgh: 'T001'
    }
    
    const response = await this.$http.post('/api/zlcgjbxx', patentData)
    if (response.data.code === 200) {
        this.$message.success('新增成功')
        this.getPatentList() // 刷新列表
    }
}
```
