package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxSchoolProjectCategories;
import com.gl.gl_lg_java.service.RchxSchoolProjectCategoriesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 校级项目类别Controller
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@RestController
@RequestMapping("/api/school-project/categories")
@Api(tags = "校级项目类别管理")
@Slf4j
public class RchxSchoolProjectCategoriesController {

    @Autowired
    private RchxSchoolProjectCategoriesService categoriesService;

    /**
     * 分页查询项目类别（包含统计信息）
     */
    @GetMapping("/page")
    @ApiOperation("分页查询项目类别")
    public Result<IPage<Map<String, Object>>> pageQuery(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("类别名称") @RequestParam(required = false) String categoryName,
            @ApiParam("是否启用") @RequestParam(required = false) Boolean isEnabled) {
        
        try {
            Page<RchxSchoolProjectCategories> page = new Page<>(current, size);
            IPage<Map<String, Object>> result = categoriesService.pageQueryWithStats(page, categoryName, isEnabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目类别失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询启用的项目类别
     */
    @GetMapping("/enabled")
    @ApiOperation("查询启用的项目类别")
    public Result<List<RchxSchoolProjectCategories>> getEnabledCategories() {
        try {
            List<RchxSchoolProjectCategories> categories = categoriesService.getEnabledCategories();
            return Result.success(categories);
        } catch (Exception e) {
            log.error("查询启用的项目类别失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目类别
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询项目类别")
    public Result<RchxSchoolProjectCategories> getById(@PathVariable Integer id) {
        try {
            RchxSchoolProjectCategories category = categoriesService.getById(id);
            if (category == null) {
                return Result.error("项目类别不存在");
            }
            return Result.success(category);
        } catch (Exception e) {
            log.error("查询项目类别失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据类别编码查询项目类别
     */
    @GetMapping("/code/{categoryCode}")
    @ApiOperation("根据类别编码查询项目类别")
    public Result<RchxSchoolProjectCategories> getByCategoryCode(@PathVariable String categoryCode) {
        try {
            RchxSchoolProjectCategories category = categoriesService.getByCategoryCode(categoryCode);
            if (category == null) {
                return Result.error("项目类别不存在");
            }
            return Result.success(category);
        } catch (Exception e) {
            log.error("查询项目类别失败: categoryCode={}", categoryCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建项目类别
     */
    @PostMapping
    @ApiOperation("创建项目类别")
    public Result<String> createCategory(@Valid @RequestBody RchxSchoolProjectCategories category) {
        try {
            // 检查类别编码是否已存在
            if (categoriesService.existsByCategoryCode(category.getCategoryCode(), null)) {
                return Result.error("类别编码已存在: " + category.getCategoryCode());
            }

            boolean success = categoriesService.createCategory(category);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建项目类别失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目类别
     */
    @PutMapping("/{id}")
    @ApiOperation("更新项目类别")
    public Result<String> updateCategory(@PathVariable Integer id, 
                                        @Valid @RequestBody RchxSchoolProjectCategories category) {
        try {
            category.setId(id);
            
            // 检查类别编码是否已存在（排除自己）
            if (categoriesService.existsByCategoryCode(category.getCategoryCode(), id)) {
                return Result.error("类别编码已存在: " + category.getCategoryCode());
            }

            boolean success = categoriesService.updateCategory(category);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新项目类别失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目类别
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除项目类别")
    public Result<String> deleteCategory(@PathVariable Integer id) {
        try {
            boolean success = categoriesService.deleteCategory(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除项目类别失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除项目类别
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除项目类别")
    public Result<String> batchDeleteCategories(@RequestBody List<Integer> ids) {
        try {
            boolean success = categoriesService.batchDeleteCategories(ids);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除项目类别失败: ids={}", ids, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用类别
     */
    @PutMapping("/{id}/status")
    @ApiOperation("启用/禁用类别")
    public Result<String> toggleCategoryStatus(@PathVariable Integer id, 
                                              @RequestParam Boolean isEnabled) {
        try {
            boolean success = categoriesService.toggleCategoryStatus(id, isEnabled);
            if (success) {
                return Result.success(isEnabled ? "启用成功" : "禁用成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("切换项目类别状态失败: id={}, isEnabled={}", id, isEnabled, e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 调整类别排序
     */
    @PutMapping("/{id}/sort")
    @ApiOperation("调整类别排序")
    public Result<String> updateSortOrder(@PathVariable Integer id, 
                                         @RequestParam Integer sortOrder) {
        try {
            boolean success = categoriesService.updateSortOrder(id, sortOrder);
            if (success) {
                return Result.success("排序更新成功");
            } else {
                return Result.error("排序更新失败");
            }
        } catch (Exception e) {
            log.error("更新项目类别排序失败: id={}, sortOrder={}", id, sortOrder, e);
            return Result.error("排序更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取类别统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取类别统计信息")
    public Result<List<Map<String, Object>>> getCategoryStatistics() {
        try {
            List<Map<String, Object>> statistics = categoriesService.getCategoryStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取类别统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据资助金额范围查询类别
     */
    @GetMapping("/funding-range")
    @ApiOperation("根据资助金额范围查询类别")
    public Result<List<RchxSchoolProjectCategories>> getCategoriesByFundingRange(
            @ApiParam("资助金额") @RequestParam BigDecimal fundingAmount) {
        try {
            List<RchxSchoolProjectCategories> categories = 
                categoriesService.getCategoriesByFundingRange(fundingAmount);
            return Result.success(categories);
        } catch (Exception e) {
            log.error("根据资助金额范围查询类别失败: fundingAmount={}", fundingAmount, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 生成类别编码
     */
    @GetMapping("/generate-code")
    @ApiOperation("生成类别编码")
    public Result<String> generateCategoryCode(@RequestParam String categoryName) {
        try {
            String categoryCode = categoriesService.generateCategoryCode(categoryName);
            return Result.success(categoryCode);
        } catch (Exception e) {
            log.error("生成类别编码失败: categoryName={}", categoryName, e);
            return Result.error("生成编码失败: " + e.getMessage());
        }
    }

    /**
     * 导出类别列表
     */
    @GetMapping("/export")
    @ApiOperation("导出类别列表")
    public Result<List<RchxSchoolProjectCategories>> exportCategoryList(
            @ApiParam("是否启用") @RequestParam(required = false) Boolean isEnabled) {
        try {
            List<RchxSchoolProjectCategories> categories = categoriesService.exportCategoryList(isEnabled);
            return Result.success(categories);
        } catch (Exception e) {
            log.error("导出类别列表失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }
}
