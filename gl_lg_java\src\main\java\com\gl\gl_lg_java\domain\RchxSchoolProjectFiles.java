package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 校级项目申报文件实体类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_rchx_school_project_files")
public class RchxSchoolProjectFiles implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申报ID
     */
    @TableField("application_id")
    private Long applicationId;

    /**
     * 申报编号
     */
    @TableField("application_code")
    private String applicationCode;

    /**
     * 文件类别(APPLICATION申报书,BUDGET预算书,RESUME个人简历,ACHIEVEMENT成果证明,PLAN实施方案,OTHER其他)
     */
    @TableField("file_category")
    private String fileCategory;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 原始文件名
     */
    @TableField("file_original_name")
    private String fileOriginalName;

    /**
     * MinIO文件路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件大小(字节)
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 文件扩展名
     */
    @TableField("file_extension")
    private String fileExtension;

    /**
     * MinIO存储桶名称
     */
    @TableField("bucket_name")
    private String bucketName;

    /**
     * MinIO对象名称
     */
    @TableField("object_name")
    private String objectName;

    /**
     * 文件MD5值
     */
    @TableField("file_md5")
    private String fileMd5;

    /**
     * 文件描述
     */
    @TableField("file_description")
    private String fileDescription;

    /**
     * 是否必需文件(0否,1是)
     */
    @TableField("is_required")
    private Boolean isRequired;

    /**
     * 上传时间
     */
    @TableField(value = "upload_time", fill = FieldFill.INSERT)
    private LocalDateTime uploadTime;

    /**
     * 上传人职工号
     */
    @TableField("upload_by")
    private String uploadBy;

    /**
     * 上传人姓名
     */
    @TableField("upload_by_name")
    private String uploadByName;

    /**
     * 下载次数
     */
    @TableField("download_count")
    private Integer downloadCount;

    /**
     * 最后下载时间
     */
    @TableField("last_download_time")
    private LocalDateTime lastDownloadTime;

    /**
     * 是否删除(0否,1是)
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private LocalDateTime deleteTime;

    /**
     * 删除人职工号
     */
    @TableField("delete_by")
    private String deleteBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
