# 剩余CRUD接口快速完成指南

## 当前完成状态

### ✅ 已完成
1. **RchxJzgjbxx** - 教职工基本信息（完整CRUD + API文档）
2. **RchxZlcgjbxx** - 专利成果基本信息（完整CRUD + API文档）
3. **RchxKjxmjbxx** - 科技项目基本信息（完整CRUD）

### 🔄 进行中
4. **RchxHjcgjbxx** - 获奖成果基本信息（Mapper已完成）

### ❌ 待完成
5. **RchxKjlwjbxx** - 科技论文基本信息
6. **RchxJzggzjlxx** - 教职工工作简历信息
7. **RchxXmysxx** - 项目预算信息

## 快速完成方案

### 方案1：使用模板快速生成

我已经为你准备好了标准模板，你只需要：

1. **复制已完成的文件**
2. **批量替换关键字**
3. **调整字段名称**
4. **添加权限控制**

### 方案2：核心功能优先

由于字段太多，建议先实现核心功能：

#### 核心查询功能
- 根据主键查询
- 根据名称模糊查询
- 根据负责人/作者查询
- 分页查询
- 状态查询

#### 核心CRUD功能
- 新增、更新、删除
- 批量操作
- 权限控制

## 获奖成果基本信息快速完成

### 1. 创建查询DTO

```java
// 文件：gl_lg_java/src/main/java/com/gl/gl_lg_java/dto/HjcgjbxxQueryDTO.java
package com.gl.gl_lg_java.dto;

import lombok.Data;

@Data
public class HjcgjbxxQueryDTO {
    private String hjcgbh;        // 获奖成果编号
    private String hjcgmc;        // 获奖成果名称
    private String jlmc;          // 奖励名称
    private String dywcrxm;       // 第一完成人姓名
    private String dywcrzgh;      // 第一完成人职工号
    private String hjjb;          // 获奖级别
    private String shzt;          // 审核状态
    private String dwmc;          // 单位名称
    private String startDate;     // 开始日期
    private String endDate;       // 结束日期
    private Integer pageNum = 1;
    private Integer pageSize = 10;
    private String orderBy = "hjrq";
    private String orderDirection = "desc";
}
```

### 2. 更新Service接口

```java
// 文件：gl_lg_java/src/main/java/com/gl/gl_lg_java/service/RchxHjcgjbxxService.java
// 复制 RchxZlcgjbxxService.java 的内容，然后批量替换：
// RchxZlcgjbxx -> RchxHjcgjbxx
// Zlcgjbxx -> Hjcgjbxx
// zlcgjbxx -> hjcgjbxx
// zlcgbh -> hjcgbh
// 专利成果 -> 获奖成果
```

### 3. 更新Service实现类

```java
// 文件：gl_lg_java/src/main/java/com/gl/gl_lg_java/service/impl/RchxHjcgjbxxServiceImpl.java
// 复制 RchxZlcgjbxxServiceImpl.java 的内容，然后批量替换相同的关键字
```

### 4. 创建Controller

```java
// 文件：gl_lg_java/src/main/java/com/gl/gl_lg_java/controller/HjcgjbxxController.java
// 复制 ZlcgjbxxController.java 的内容，然后批量替换：
// ZlcgjbxxController -> HjcgjbxxController
// /api/zlcgjbxx -> /api/hjcgjbxx
// 专利成果 -> 获奖成果
// 权限控制逻辑：教师只能查看自己的获奖成果
```

## 科技论文基本信息快速完成

### 主键配置
```java
// 需要先查看 RchxKjlwjbxx 实体类确定主键字段
// 可能是 lwid 或 lwbh（论文编号）
```

### 核心字段
- 论文标题 (lwbt)
- 第一作者 (dyzz)
- 期刊名称 (qkmc)
- 发表日期 (fbrq)
- 审核状态 (shzt)

### API路径
- `/api/kjlwjbxx` - 科技论文基本信息

## 教职工工作简历信息快速完成

### 主键配置
```java
// 可能是复合主键：zgh + 序号
// 或者单独的 id 字段
```

### 核心字段
- 职工号 (zgh)
- 工作单位 (gzdw)
- 工作内容 (gznr)
- 开始时间 (kssj)
- 结束时间 (jssj)

### API路径
- `/api/jzggzjlxx` - 教职工工作简历信息

### 权限控制
- 教师：只能查看和修改自己的工作简历
- 学院管理员：可以查看本学院教职工的工作简历
- 系统管理员：可以管理所有工作简历

## 项目预算信息快速完成

### 关联关系
- 与科技项目基本信息关联
- 通过项目ID关联

### 核心字段
- 项目ID (xmid)
- 预算科目 (yskm)
- 预算金额 (ysje)
- 实际支出 (sjzc)

### API路径
- `/api/xmysxx` - 项目预算信息

## 批量替换工具

### VS Code批量替换
1. 打开查找替换 (Ctrl+H)
2. 启用正则表达式
3. 使用以下替换规则：

```
查找：RchxZlcgjbxx
替换：RchxHjcgjbxx

查找：Zlcgjbxx
替换：Hjcgjbxx

查找：zlcgjbxx
替换：hjcgjbxx

查找：zlcgbh
替换：hjcgbh

查找：专利成果
替换：获奖成果

查找：/api/zlcgjbxx
替换：/api/hjcgjbxx
```

## 权限控制模板

```java
// 在Controller中的权限控制模板
String currentUserZgh = (String) request.getAttribute("currentUserZgh");
String currentUserQx = (String) request.getAttribute("currentUserQx");

// 教师只能查看自己的数据
if ("教师".equals(currentUserQx) && result != null && !currentUserZgh.equals(result.get负责人字段())) {
    return Result.error(403, "权限不足，只能查看自己的数据");
}
```

## 测试验证

### 基础测试
1. 启动应用
2. 测试登录接口
3. 测试各个CRUD接口
4. 验证权限控制

### API测试
使用Postman或其他工具测试：
- GET 查询接口
- POST 新增接口
- PUT 更新接口
- DELETE 删除接口

## 完成时间估算

- **获奖成果基本信息**：30分钟
- **科技论文基本信息**：30分钟
- **教职工工作简历信息**：30分钟
- **项目预算信息**：30分钟

总计：2小时可以完成所有剩余的CRUD接口

## 优化建议

1. **先完成核心功能**，后续再完善细节
2. **使用代码生成器**，提高开发效率
3. **统一异常处理**，保证代码质量
4. **完善API文档**，方便前端对接

这样你就可以快速完成所有实体类的CRUD接口了！
