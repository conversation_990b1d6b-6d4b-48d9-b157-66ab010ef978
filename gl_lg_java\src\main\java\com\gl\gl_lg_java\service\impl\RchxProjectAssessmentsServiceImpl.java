package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxProjectAssessments;
import com.gl.gl_lg_java.mapper.RchxProjectAssessmentsMapper;
import com.gl.gl_lg_java.service.RchxProjectAssessmentsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 项目考核管理Service实现类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Slf4j
public class RchxProjectAssessmentsServiceImpl extends ServiceImpl<RchxProjectAssessmentsMapper, RchxProjectAssessments>
        implements RchxProjectAssessmentsService {

    @Autowired
    private RchxProjectAssessmentsMapper assessmentsMapper;

    @Override
    public IPage<RchxProjectAssessments> pageQuery(Page<RchxProjectAssessments> page,
            String projectCode,
            String projectName,
            String assessmentType,
            String assessmentStatus,
            String projectLeaderZgh,
            String assessorZgh,
            String assessmentResult) {
        try {
            return assessmentsMapper.selectPageWithConditions(
                    page, projectCode, projectName, assessmentType,
                    assessmentStatus, projectLeaderZgh, assessorZgh, assessmentResult);
        } catch (Exception e) {
            log.error("分页查询项目考核信息失败", e);
            throw new RuntimeException("分页查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectAssessments> getByProjectId(Long projectId) {
        try {
            return assessmentsMapper.selectByProjectId(projectId);
        } catch (Exception e) {
            log.error("根据项目ID查询考核列表失败: projectId={}", projectId, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectAssessments> getByProjectCode(String projectCode) {
        try {
            return assessmentsMapper.selectByProjectCode(projectCode);
        } catch (Exception e) {
            log.error("根据项目编号查询考核列表失败: projectCode={}", projectCode, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectAssessments> getByAssessmentType(String assessmentType) {
        try {
            return assessmentsMapper.selectByAssessmentType(assessmentType);
        } catch (Exception e) {
            log.error("根据考核类型查询考核列表失败: assessmentType={}", assessmentType, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectAssessments> getByAssessmentStatus(String assessmentStatus) {
        try {
            return assessmentsMapper.selectByAssessmentStatus(assessmentStatus);
        } catch (Exception e) {
            log.error("根据考核状态查询考核列表失败: assessmentStatus={}", assessmentStatus, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectAssessments> getByProjectLeader(String projectLeaderZgh) {
        try {
            return assessmentsMapper.selectByProjectLeader(projectLeaderZgh);
        } catch (Exception e) {
            log.error("根据项目负责人查询考核列表失败: projectLeaderZgh={}", projectLeaderZgh, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateAssessmentScore(Long id, BigDecimal assessmentScore, String assessmentResult,
            String assessmentComments, String updateBy) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int result = assessmentsMapper.updateAssessmentScore(
                    id, assessmentScore, assessmentResult, assessmentComments, now, updateBy);

            if (result > 0) {
                log.info("更新考核得分成功: id={}, score={}", id, assessmentScore);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新考核得分失败: id={}, score={}", id, assessmentScore, e);
            throw new RuntimeException("更新考核得分失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateAssessmentStatus(Long id, String assessmentStatus, String updateBy) {
        try {
            int result = assessmentsMapper.updateAssessmentStatus(id, assessmentStatus, updateBy);

            if (result > 0) {
                log.info("更新考核状态成功: id={}, status={}", id, assessmentStatus);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新考核状态失败: id={}, status={}", id, assessmentStatus, e);
            throw new RuntimeException("更新考核状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean sendAssessmentReminder(Long id) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int result = assessmentsMapper.updateReminderStatus(id, true, now);

            if (result > 0) {
                log.info("发送考核提醒成功: id={}", id);
                // TODO: 实际发送提醒邮件或消息
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("发送考核提醒失败: id={}", id, e);
            throw new RuntimeException("发送考核提醒失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getAssessmentStatistics() {
        try {
            return assessmentsMapper.getAssessmentStatistics();
        } catch (Exception e) {
            log.error("获取考核统计信息失败", e);
            throw new RuntimeException("获取考核统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getAssessmentTypeStatistics() {
        try {
            return assessmentsMapper.getAssessmentTypeStatistics();
        } catch (Exception e) {
            log.error("获取考核类型统计失败", e);
            throw new RuntimeException("获取考核类型统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getProjectLeaderAssessmentStatistics() {
        try {
            return assessmentsMapper.getProjectLeaderAssessmentStatistics();
        } catch (Exception e) {
            log.error("获取项目负责人考核统计失败", e);
            throw new RuntimeException("获取项目负责人考核统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectAssessments> getAssessmentsNearDeadline(Integer days) {
        try {
            return assessmentsMapper.getAssessmentsNearDeadline(days);
        } catch (Exception e) {
            log.error("获取即将到期的考核任务失败: days={}", days, e);
            throw new RuntimeException("获取即将到期的考核任务失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectAssessments> getOverdueAssessments() {
        try {
            return assessmentsMapper.getOverdueAssessments();
        } catch (Exception e) {
            log.error("获取逾期的考核任务失败", e);
            throw new RuntimeException("获取逾期的考核任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean createAssessment(RchxProjectAssessments assessment) {
        try {
            // 生成考核编号
            if (assessment.getAssessmentCode() == null || assessment.getAssessmentCode().isEmpty()) {
                String assessmentCode = generateAssessmentCode(assessment.getAssessmentType());
                assessment.setAssessmentCode(assessmentCode);
            }

            // 检查考核编号是否已存在
            if (existsByAssessmentCode(assessment.getAssessmentCode())) {
                throw new RuntimeException("考核编号已存在: " + assessment.getAssessmentCode());
            }

            // 设置默认值
            if (assessment.getAssessmentStatus() == null) {
                assessment.setAssessmentStatus("PENDING");
            }
            if (assessment.getMaxScore() == null) {
                assessment.setMaxScore(new BigDecimal("100.00"));
            }
            if (assessment.getPassScore() == null) {
                assessment.setPassScore(new BigDecimal("60.00"));
            }
            if (assessment.getIsPublic() == null) {
                assessment.setIsPublic(false);
            }
            if (assessment.getPriorityLevel() == null) {
                assessment.setPriorityLevel("NORMAL");
            }
            if (assessment.getReminderSent() == null) {
                assessment.setReminderSent(false);
            }

            boolean result = save(assessment);
            if (result) {
                log.info("创建考核任务成功: assessmentCode={}", assessment.getAssessmentCode());
            }
            return result;
        } catch (Exception e) {
            log.error("创建考核任务失败: assessmentCode={}", assessment.getAssessmentCode(), e);
            throw new RuntimeException("创建考核任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateAssessment(RchxProjectAssessments assessment) {
        try {
            boolean result = updateById(assessment);
            if (result) {
                log.info("更新考核任务成功: id={}", assessment.getId());
            }
            return result;
        } catch (Exception e) {
            log.error("更新考核任务失败: id={}", assessment.getId(), e);
            throw new RuntimeException("更新考核任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteAssessment(Long id) {
        try {
            boolean result = removeById(id);
            if (result) {
                log.info("删除考核任务成功: id={}", id);
            }
            return result;
        } catch (Exception e) {
            log.error("删除考核任务失败: id={}", id, e);
            throw new RuntimeException("删除考核任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean batchDeleteAssessments(List<Long> ids) {
        try {
            boolean result = removeByIds(ids);
            if (result) {
                log.info("批量删除考核任务成功: ids={}", ids);
            }
            return result;
        } catch (Exception e) {
            log.error("批量删除考核任务失败: ids={}", ids, e);
            throw new RuntimeException("批量删除考核任务失败: " + e.getMessage());
        }
    }

    @Override
    public RchxProjectAssessments getByAssessmentCode(String assessmentCode) {
        try {
            return assessmentsMapper.selectByAssessmentCode(assessmentCode);
        } catch (Exception e) {
            log.error("根据考核编号查询失败: assessmentCode={}", assessmentCode, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public boolean existsByAssessmentCode(String assessmentCode) {
        try {
            return assessmentsMapper.countByAssessmentCode(assessmentCode) > 0;
        } catch (Exception e) {
            log.error("检查考核编号是否存在失败: assessmentCode={}", assessmentCode, e);
            return false;
        }
    }

    @Override
    public String generateAssessmentCode(String assessmentType) {
        try {
            String year = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy"));
            String prefix;

            switch (assessmentType) {
                case "MIDTERM":
                    prefix = "MID";
                    break;
                case "FINAL":
                    prefix = "FINAL";
                    break;
                case "SPECIAL":
                    prefix = "SPEC";
                    break;
                case "ANNUAL":
                    prefix = "ANNUAL";
                    break;
                case "QUARTERLY":
                    prefix = "QUAR";
                    break;
                default:
                    prefix = "ASSESS";
                    break;
            }

            // 查找当年最大编号
            QueryWrapper<RchxProjectAssessments> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("assessment_code", prefix + year)
                    .orderByDesc("assessment_code")
                    .last("LIMIT 1");

            RchxProjectAssessments lastAssessment = getOne(queryWrapper);
            int sequence = 1;

            if (lastAssessment != null && lastAssessment.getAssessmentCode() != null) {
                String lastCode = lastAssessment.getAssessmentCode();
                String sequenceStr = lastCode.substring(lastCode.length() - 3);
                sequence = Integer.parseInt(sequenceStr) + 1;
            }

            return String.format("%s%s%03d", prefix, year, sequence);
        } catch (Exception e) {
            log.error("生成考核编号失败: assessmentType={}", assessmentType, e);
            throw new RuntimeException("生成考核编号失败: " + e.getMessage());
        }
    }

    @Override
    public RchxProjectAssessments getLatestAssessmentByProjectId(Long projectId) {
        try {
            return assessmentsMapper.getLatestAssessmentByProjectId(projectId);
        } catch (Exception e) {
            log.error("获取项目最新考核信息失败: projectId={}", projectId, e);
            throw new RuntimeException("获取项目最新考核信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean startAssessment(Long id, String assessorZgh, String assessorName) {
        try {
            RchxProjectAssessments assessment = new RchxProjectAssessments();
            assessment.setId(id);
            assessment.setAssessmentStatus("ONGOING");
            assessment.setAssessorZgh(assessorZgh);
            assessment.setAssessorName(assessorName);
            assessment.setAssessmentStartTime(LocalDateTime.now());

            boolean result = updateById(assessment);
            if (result) {
                log.info("开始考核成功: id={}, assessor={}", id, assessorName);
            }
            return result;
        } catch (Exception e) {
            log.error("开始考核失败: id={}", id, e);
            throw new RuntimeException("开始考核失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean completeAssessment(Long id, BigDecimal assessmentScore, String assessmentComments,
            String improvementSuggestions, String followUpActions, String updateBy) {
        try {
            RchxProjectAssessments assessment = new RchxProjectAssessments();
            assessment.setId(id);
            assessment.setAssessmentScore(assessmentScore);
            assessment.setAssessmentComments(assessmentComments);
            assessment.setImprovementSuggestions(improvementSuggestions);
            assessment.setFollowUpActions(followUpActions);
            assessment.setAssessmentStatus("COMPLETED");
            assessment.setAssessmentEndTime(LocalDateTime.now());
            assessment.setUpdateBy(updateBy);

            // 根据得分自动判断考核结果
            String assessmentResult;
            if (assessmentScore.compareTo(new BigDecimal("90")) >= 0) {
                assessmentResult = "EXCELLENT";
            } else if (assessmentScore.compareTo(new BigDecimal("80")) >= 0) {
                assessmentResult = "GOOD";
            } else if (assessmentScore.compareTo(new BigDecimal("60")) >= 0) {
                assessmentResult = "PASS";
            } else {
                assessmentResult = "FAIL";
            }
            assessment.setAssessmentResult(assessmentResult);

            boolean result = updateById(assessment);
            if (result) {
                log.info("完成考核成功: id={}, score={}, result={}", id, assessmentScore, assessmentResult);
            }
            return result;
        } catch (Exception e) {
            log.error("完成考核失败: id={}", id, e);
            throw new RuntimeException("完成考核失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean cancelAssessment(Long id, String updateBy) {
        try {
            RchxProjectAssessments assessment = new RchxProjectAssessments();
            assessment.setId(id);
            assessment.setAssessmentStatus("CANCELLED");
            assessment.setUpdateBy(updateBy);

            boolean result = updateById(assessment);
            if (result) {
                log.info("取消考核成功: id={}", id);
            }
            return result;
        } catch (Exception e) {
            log.error("取消考核失败: id={}", id, e);
            throw new RuntimeException("取消考核失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxProjectAssessments> exportAssessmentList(String assessmentType, String assessmentStatus,
            String projectLeaderZgh) {
        try {
            Page<RchxProjectAssessments> page = new Page<>(1, Integer.MAX_VALUE);
            IPage<RchxProjectAssessments> result = pageQuery(
                    page, null, null, assessmentType, assessmentStatus, projectLeaderZgh, null, null);
            return result.getRecords();
        } catch (Exception e) {
            log.error("导出考核列表失败", e);
            throw new RuntimeException("导出考核列表失败: " + e.getMessage());
        }
    }
}
