package com.gl.gl_lg_java.dto;

import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import lombok.Data;

/**
 * 登录响应DTO
 */
@Data
public class LoginResponseDTO {
    
    /**
     * JWT Token
     */
    private String token;
    
    /**
     * Token类型
     */
    private String tokenType = "Bearer";
    
    /**
     * Token过期时间（毫秒）
     */
    private Long expiresIn;
    
    /**
     * 用户信息
     */
    private RchxJzgjbxx userInfo;
    
    public LoginResponseDTO(String token, Long expiresIn, RchxJzgjbxx userInfo) {
        this.token = token;
        this.expiresIn = expiresIn;
        this.userInfo = userInfo;
    }
}
