name: Build and Release JAR

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  release:
    types: [created]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
      
    - name: 检查环境
      run: |
        echo "=== 环境信息 ==="
        echo "当前时间: $(date)"
        echo "系统信息: $(uname -a)"
        echo "Java版本:"
        java -version
        echo "Maven版本:"
        mvn -version
        echo "Git版本:"
        git --version
        echo "当前目录: $(pwd)"
        echo "文件列表:"
        ls -la
        
    - name: 编译项目
      run: |
        echo "=== 开始编译 ==="
        mvn clean compile
        
    - name: 运行测试
      run: |
        echo "=== 运行测试 ==="
        mvn test
        
    - name: 打包JAR
      run: |
        echo "=== 打包JAR ==="
        mvn package -DskipTests
        echo "=== 打包完成，查看结果 ==="
        ls -la target/
        
    - name: 重命名JAR文件
      run: |
        cd target
        # 找到生成的JAR文件并重命名
        JAR_FILE=$(ls *.jar | grep -v original | head -1)
        if [ -n "$JAR_FILE" ]; then
          NEW_NAME="gl-lg-java-$(date +%Y%m%d-%H%M%S).jar"
          cp "$JAR_FILE" "$NEW_NAME"
          echo "JAR文件复制为: $NEW_NAME"
          ls -la *.jar
        else
          echo "未找到JAR文件"
          exit 1
        fi
        
    - name: 上传JAR文件
      uses: actions/upload-artifact@v3
      with:
        name: jar-package
        path: target/*.jar
        retention-days: 30
        
    - name: 显示构建信息
      run: |
        echo "=== 构建完成 ==="
        echo "构建时间: $(date)"
        echo "JAR文件:"
        ls -la target/*.jar
        echo "文件大小:"
        du -h target/*.jar

  release:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'release'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
      
    - name: 打包JAR用于发布
      run: |
        echo "=== 为Release打包JAR ==="
        mvn clean package -DskipTests
        cd target
        JAR_FILE=$(ls *.jar | grep -v original | head -1)
        if [ -n "$JAR_FILE" ]; then
          cp "$JAR_FILE" "gl-lg-java-release.jar"
          echo "Release JAR文件: gl-lg-java-release.jar"
          ls -la gl-lg-java-release.jar
        fi
        
    - name: 上传到Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: target/gl-lg-java-release.jar
        asset_name: gl-lg-java-${{ github.event.release.tag_name }}.jar
        asset_content_type: application/java-archive
