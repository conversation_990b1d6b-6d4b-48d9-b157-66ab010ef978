package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxKjlwjbxx;
import com.gl.gl_lg_java.dto.KjlwjbxxQueryDTO;
import com.gl.gl_lg_java.dto.KjlwStatisticsDTO;
import com.gl.gl_lg_java.mapper.RchxKjlwjbxxMapper;
import com.gl.gl_lg_java.service.RchxKjlwjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_kjlwjbxx(科技论文基本信息)】的数据库操作Service实现
 * @createDate 2025-07-18 11:11:41
 */
@Service
@Slf4j
public class RchxKjlwjbxxServiceImpl extends ServiceImpl<RchxKjlwjbxxMapper, RchxKjlwjbxx>
        implements RchxKjlwjbxxService {

    @Autowired
    private RchxKjlwjbxxMapper kjlwjbxxMapper;

    @Override
    public RchxKjlwjbxx getByLwbh(String lwbh) {
        return kjlwjbxxMapper.findByLwbh(lwbh);
    }

    @Override
    public List<RchxKjlwjbxx> listByLwmcLike(String lwmc) {
        return kjlwjbxxMapper.findByLwmcLike(lwmc);
    }

    @Override
    public List<RchxKjlwjbxx> listByDyzzbh(String dyzzbh) {
        return kjlwjbxxMapper.findByDyzzbh(dyzzbh);
    }

    @Override
    public List<RchxKjlwjbxx> listByDyzzxmLike(String dyzzxm) {
        return kjlwjbxxMapper.findByDyzzxmLike(dyzzxm);
    }

    @Override
    public List<RchxKjlwjbxx> listByTxzzbh(String txzzbh) {
        return kjlwjbxxMapper.findByTxzzbh(txzzbh);
    }

    @Override
    public List<RchxKjlwjbxx> listByKwmcLike(String kwmc) {
        return kjlwjbxxMapper.findByKwmcLike(kwmc);
    }

    @Override
    public List<RchxKjlwjbxx> listByKwlx(String kwlx) {
        return kjlwjbxxMapper.findByKwlx(kwlx);
    }

    @Override
    public List<RchxKjlwjbxx> listByFbfw(String fbfw) {
        return kjlwjbxxMapper.findByFbfw(fbfw);
    }

    @Override
    public List<RchxKjlwjbxx> listByShzt(String shzt) {
        return kjlwjbxxMapper.findByShzt(shzt);
    }

    @Override
    public List<RchxKjlwjbxx> listByFbrqBetween(String startDate, String endDate) {
        return kjlwjbxxMapper.findByFbrqBetween(startDate, endDate);
    }

    @Override
    public List<RchxKjlwjbxx> listByMultiConditions(KjlwjbxxQueryDTO queryDTO) {
        // 使用QueryWrapper替代复杂动态SQL
        QueryWrapper<RchxKjlwjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getLwbh())) {
            queryWrapper.like("lwbh", queryDTO.getLwbh());
        }
        if (StringUtils.hasText(queryDTO.getLwmc())) {
            queryWrapper.like("lwmc", queryDTO.getLwmc());
        }
        if (StringUtils.hasText(queryDTO.getDyzzbh())) {
            queryWrapper.eq("dyzzbh", queryDTO.getDyzzbh());
        }
        if (StringUtils.hasText(queryDTO.getDyzzxm())) {
            queryWrapper.like("dyzzxm", queryDTO.getDyzzxm());
        }
        if (StringUtils.hasText(queryDTO.getTxzzbh())) {
            queryWrapper.eq("txzzbh", queryDTO.getTxzzbh());
        }
        if (StringUtils.hasText(queryDTO.getTxzzxm())) {
            queryWrapper.like("txzzxm", queryDTO.getTxzzxm());
        }
        if (StringUtils.hasText(queryDTO.getKwmc())) {
            queryWrapper.like("kwmc", queryDTO.getKwmc());
        }
        if (StringUtils.hasText(queryDTO.getKwlx())) {
            queryWrapper.eq("kwlx", queryDTO.getKwlx());
        }
        if (StringUtils.hasText(queryDTO.getFbfw())) {
            queryWrapper.eq("fbfw", queryDTO.getFbfw());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getDwmc())) {
            queryWrapper.like("dwmc", queryDTO.getDwmc());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("fbrq", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("fbrq", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("fbrq");

        return kjlwjbxxMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<RchxKjlwjbxx> pageByMultiConditions(KjlwjbxxQueryDTO queryDTO) {
        // 使用MyBatis-Plus的分页功能替代复杂动态SQL
        Page<RchxKjlwjbxx> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<RchxKjlwjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getLwbh())) {
            queryWrapper.like("lwbh", queryDTO.getLwbh());
        }
        if (StringUtils.hasText(queryDTO.getLwmc())) {
            queryWrapper.like("lwmc", queryDTO.getLwmc());
        }
        if (StringUtils.hasText(queryDTO.getDyzzbh())) {
            queryWrapper.eq("dyzzbh", queryDTO.getDyzzbh());
        }
        if (StringUtils.hasText(queryDTO.getDyzzxm())) {
            queryWrapper.like("dyzzxm", queryDTO.getDyzzxm());
        }
        if (StringUtils.hasText(queryDTO.getTxzzbh())) {
            queryWrapper.eq("txzzbh", queryDTO.getTxzzbh());
        }
        if (StringUtils.hasText(queryDTO.getTxzzxm())) {
            queryWrapper.like("txzzxm", queryDTO.getTxzzxm());
        }
        if (StringUtils.hasText(queryDTO.getKwmc())) {
            queryWrapper.like("kwmc", queryDTO.getKwmc());
        }
        if (StringUtils.hasText(queryDTO.getKwlx())) {
            queryWrapper.eq("kwlx", queryDTO.getKwlx());
        }
        if (StringUtils.hasText(queryDTO.getFbfw())) {
            queryWrapper.eq("fbfw", queryDTO.getFbfw());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getDwmc())) {
            queryWrapper.like("dwmc", queryDTO.getDwmc());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("fbrq", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("fbrq", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("fbrq");

        return kjlwjbxxMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveKjlwjbxx(RchxKjlwjbxx kjlwjbxx) {
        try {
            return save(kjlwjbxx);
        } catch (Exception e) {
            log.error("新增科技论文信息失败: {}", e.getMessage());
            throw new RuntimeException("新增科技论文信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByLwbh(RchxKjlwjbxx kjlwjbxx) {
        try {
            return updateById(kjlwjbxx);
        } catch (Exception e) {
            log.error("更新科技论文信息失败: {}", e.getMessage());
            throw new RuntimeException("更新科技论文信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByLwbh(String lwbh) {
        try {
            return removeById(lwbh);
        } catch (Exception e) {
            log.error("删除科技论文信息失败: {}", e.getMessage());
            throw new RuntimeException("删除科技论文信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByLwbhs(List<String> lwbhs) {
        try {
            return removeByIds(lwbhs);
        } catch (Exception e) {
            log.error("批量删除科技论文信息失败: {}", e.getMessage());
            throw new RuntimeException("批量删除科技论文信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchKjlwjbxx(List<RchxKjlwjbxx> kjlwjbxxList) {
        try {
            return saveBatch(kjlwjbxxList);
        } catch (Exception e) {
            log.error("批量新增科技论文信息失败: {}", e.getMessage());
            throw new RuntimeException("批量新增科技论文信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchKjlwjbxx(List<RchxKjlwjbxx> kjlwjbxxList) {
        try {
            return updateBatchById(kjlwjbxxList);
        } catch (Exception e) {
            log.error("批量更新科技论文信息失败: {}", e.getMessage());
            throw new RuntimeException("批量更新科技论文信息失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByKwlx() {
        try {
            log.info("查询刊物类型统计（使用数据库聚合查询）");
            Map<String, Integer> kwlxCounts = convertToIntegerMap(kjlwjbxxMapper.getStatsByKwlx());
            log.info("刊物类型统计结果: {}", kwlxCounts);
            return kwlxCounts;
        } catch (Exception e) {
            log.error("查询刊物类型统计失败: {}", e.getMessage());
            throw new RuntimeException("查询刊物类型统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByShzt() {
        try {
            log.info("查询审核状态统计（使用数据库聚合查询）");
            Map<String, Integer> shztCounts = convertToIntegerMap(kjlwjbxxMapper.getStatsByShzt());
            log.info("审核状态统计结果: {}", shztCounts);
            return shztCounts;
        } catch (Exception e) {
            log.error("查询审核状态统计失败: {}", e.getMessage());
            throw new RuntimeException("查询审核状态统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getKwlxOptions() {
        try {
            log.info("快速获取刊物类型选项列表（不统计数量）");
            List<String> kwlxList = kjlwjbxxMapper.findDistinctKwlx();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String kwlx : kwlxList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", kwlx);
                option.put("label", kwlx);
                options.add(option);
            }

            log.info("刊物类型选项列表快速获取成功，共{}个类型", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取刊物类型选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取刊物类型选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getShztOptions() {
        try {
            log.info("快速获取审核状态选项列表（不统计数量）");
            List<String> shztList = kjlwjbxxMapper.findDistinctShzt();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String shzt : shztList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", shzt);
                option.put("label", shzt);
                options.add(option);
            }

            log.info("审核状态选项列表快速获取成功，共{}个状态", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取审核状态选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取审核状态选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public Long getPersonalCount(String zgh) {
        try {
            log.info("查询教师个人科技论文数量，职工号: {}", zgh);
            Long count = kjlwjbxxMapper.getPersonalCountByZgh(zgh);
            log.info("教师个人科技论文数量查询结果: {}", count);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("查询教师个人科技论文数量失败: {}", e.getMessage());
            throw new RuntimeException("查询教师个人科技论文数量失败: " + e.getMessage());
        }
    }

    /**
     * 转换统计结果为Integer类型的Map
     */
    private Map<String, Integer> convertToIntegerMap(List<Map<String, Object>> statsList) {
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> stats : statsList) {
            String fieldValue = (String) stats.get("fieldValue");
            Object countObj = stats.get("count");
            Integer count = 0;
            if (countObj instanceof Number) {
                count = ((Number) countObj).intValue();
            }
            result.put(fieldValue, count);
        }
        return result;
    }

    @Override
    public KjlwStatisticsDTO.ComprehensiveStatistics getComprehensiveStatistics() {
        try {
            log.info("开始获取科技论文综合统计数据");

            KjlwStatisticsDTO.ComprehensiveStatistics statistics = new KjlwStatisticsDTO.ComprehensiveStatistics();

            // 获取单位统计
            List<KjlwStatisticsDTO.UnitStatistics> unitStats = kjlwjbxxMapper.getUnitStatistics();
            statistics.setUnitStatistics(unitStats);
            log.debug("单位统计数据获取完成，共{}个单位", unitStats.size());

            // 获取发布范围统计
            List<KjlwStatisticsDTO.PublishScopeStatistics> publishScopeStats = kjlwjbxxMapper
                    .getPublishScopeStatistics();
            statistics.setPublishScopeStatistics(publishScopeStats);
            log.debug("发布范围统计数据获取完成，共{}个范围", publishScopeStats.size());

            // 获取学科类别统计
            List<KjlwStatisticsDTO.SubjectCategoryStatistics> subjectCategoryStats = kjlwjbxxMapper
                    .getSubjectCategoryStatistics();
            statistics.setSubjectCategoryStatistics(subjectCategoryStats);
            log.debug("学科类别统计数据获取完成，共{}个类别", subjectCategoryStats.size());

            // 获取总体统计
            Map<String, Object> overallStats = kjlwjbxxMapper.getOverallStatistics();
            if (overallStats != null) {
                statistics.setTotalPapers(((Number) overallStats.get("totalPapers")).longValue());
                statistics.setTotalUnits(((Number) overallStats.get("totalUnits")).longValue());
                statistics.setTotalPublishScopes(((Number) overallStats.get("totalPublishScopes")).longValue());
                statistics.setTotalSubjectCategories(((Number) overallStats.get("totalSubjectCategories")).longValue());
            }

            log.info("科技论文综合统计数据获取完成: 论文总数={}, 单位总数={}, 发布范围总数={}, 学科类别总数={}",
                    statistics.getTotalPapers(), statistics.getTotalUnits(),
                    statistics.getTotalPublishScopes(), statistics.getTotalSubjectCategories());

            return statistics;

        } catch (Exception e) {
            log.error("获取科技论文综合统计数据失败", e);
            throw new RuntimeException("获取统计数据失败: " + e.getMessage());
        }
    }

    // ========== 人才匹配算法专用方法实现 ==========

    @Override
    public boolean hasHighQualityPapers(String teacherZgh) {
        try {
            QueryWrapper<RchxKjlwjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(wrapper -> wrapper.eq("dyzzbh", teacherZgh).or().eq("txzzbh", teacherZgh))
                    .and(wrapper -> wrapper.like("kwlx", "SCI").or().like("kwlx", "EI").or().like("kwlx", "CSSCI"));

            return count(queryWrapper) >= 4; // 根据青年人才要求，需要4篇及以上
        } catch (Exception e) {
            log.error("检查教师{}高质量论文失败: {}", teacherZgh, e.getMessage());
            return false;
        }
    }

    @Override
    public int countByTeacher(String teacherZgh) {
        try {
            QueryWrapper<RchxKjlwjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(wrapper -> wrapper.eq("dyzzbh", teacherZgh).or().eq("txzzbh", teacherZgh));

            return Math.toIntExact(count(queryWrapper));
        } catch (Exception e) {
            log.error("统计教师{}论文总数失败: {}", teacherZgh, e.getMessage());
            return 0;
        }
    }

    @Override
    public int countSciEiByTeacher(String teacherZgh) {
        try {
            QueryWrapper<RchxKjlwjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(wrapper -> wrapper.eq("dyzzbh", teacherZgh).or().eq("txzzbh", teacherZgh))
                    .and(wrapper -> wrapper.like("kwlx", "SCI").or().like("kwlx", "EI"));

            return Math.toIntExact(count(queryWrapper));
        } catch (Exception e) {
            log.error("统计教师{}SCI/EI论文数量失败: {}", teacherZgh, e.getMessage());
            return 0;
        }
    }

    @Override
    public int countFirstAuthorByTeacher(String teacherZgh) {
        try {
            QueryWrapper<RchxKjlwjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dyzzbh", teacherZgh);

            return Math.toIntExact(count(queryWrapper));
        } catch (Exception e) {
            log.error("统计教师{}第一作者论文数量失败: {}", teacherZgh, e.getMessage());
            return 0;
        }
    }
}
