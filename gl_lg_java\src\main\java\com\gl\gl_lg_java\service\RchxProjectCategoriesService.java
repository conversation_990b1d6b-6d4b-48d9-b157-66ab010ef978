package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectCategories;

import java.util.List;

/**
 * 项目大类表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface RchxProjectCategoriesService extends IService<RchxProjectCategories> {

    /**
     * 获取所有启用的项目大类（按排序号排序）
     * 
     * @return 项目大类列表
     */
    List<RchxProjectCategories> getEnabledCategories();

    /**
     * 根据大类编码获取大类信息
     * 
     * @param categoryCode 大类编码
     * @return 项目大类
     */
    RchxProjectCategories getByCategoryCode(String categoryCode);

    /**
     * 启用/禁用项目大类
     * 
     * @param id 大类ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean updateEnabled(Integer id, Boolean enabled);

    /**
     * 检查大类编码是否存在
     * 
     * @param categoryCode 大类编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByCategoryCode(String categoryCode, Integer excludeId);
}
