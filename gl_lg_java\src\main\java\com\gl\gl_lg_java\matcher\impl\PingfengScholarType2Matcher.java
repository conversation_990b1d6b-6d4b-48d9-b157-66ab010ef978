package com.gl.gl_lg_java.matcher.impl;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.matcher.ProjectMatcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;

/**
 * 屏风学者引育计划项目-Ⅱ类匹配算法（简化版）
 */
@Component
@Slf4j
public class PingfengScholarType2Matcher implements ProjectMatcher {
    
    @Override
    public String getProjectCode() {
        return "PFXZ2";
    }
    
    @Override
    public String getProjectName() {
        return "屏风学者引育计划项目-Ⅱ类";
    }
    
    @Override
    public MatchResult matchTeacher(RchxJzgjbxx teacher, AnalysisParams params) {
        log.info("开始分析教师{}的屏风学者Ⅱ类匹配度", teacher.getZgh());
        
        MatchResult result = MatchResult.builder()
            .teacherZgh(teacher.getZgh())
            .teacherName(teacher.getXm())
            .department(teacher.getBm())
            .title(teacher.getZc())
            .createdTime(LocalDateTime.now())
            .strengths(new ArrayList<>())
            .weaknesses(new ArrayList<>())
            .recommendations(new ArrayList<>())
            .build();
        
        try {
            // 简化的匹配逻辑（基于广西E层次标准）
            double score = 60.0; // 基础分
            
            // 基于职称给分（E层次要求相对较低）
            if (teacher.getZc() != null) {
                if (teacher.getZc().contains("教授")) {
                    score += 25.0;
                    result.getStrengths().add("具有教授职称，超出E层次要求");
                } else if (teacher.getZc().contains("副教授")) {
                    score += 15.0;
                    result.getStrengths().add("具有副教授职称，符合E层次要求");
                } else if (teacher.getZc().contains("讲师")) {
                    score += 8.0;
                    result.getStrengths().add("具有讲师职称");
                } else {
                    result.getWeaknesses().add("职称水平有待提升");
                }
            }
            
            // 发展潜力推断
            if (teacher.getBm() != null && !teacher.getBm().isEmpty()) {
                score += 7.0;
                result.getStrengths().add("有明确的学科归属，发展潜力良好");
            }
            
            result.setMatchScore(score);
            result.setIsQualified(score >= params.getMinMatchScore());
            result.calculateMatchLevel();
            
            // 生成建议
            if (result.getIsQualified()) {
                result.getRecommendations().add("建议准备广西E层次人才认定材料");
                result.getRecommendations().add("重点展示发展潜力和学术成果");
            } else {
                result.getRecommendations().add("建议提升学术水平达到广西E层次标准");
                result.getRecommendations().add("加强学术成果积累和发展潜力展示");
            }
            
            log.info("教师{}屏风学者Ⅱ类匹配分析完成，得分: {}", teacher.getZgh(), score);
            
        } catch (Exception e) {
            log.error("教师{}屏风学者Ⅱ类匹配分析失败", teacher.getZgh(), e);
            result.setIsQualified(false);
            result.setReason("分析过程中发生错误: " + e.getMessage());
            result.setMatchScore(0.0);
            result.calculateMatchLevel();
        }
        
        return result;
    }
    
    @Override
    public boolean validateBasicConditions(RchxJzgjbxx teacher) {
        return teacher.getXm() != null && !teacher.getXm().isEmpty() &&
               teacher.getZgh() != null && !teacher.getZgh().isEmpty();
    }
}
