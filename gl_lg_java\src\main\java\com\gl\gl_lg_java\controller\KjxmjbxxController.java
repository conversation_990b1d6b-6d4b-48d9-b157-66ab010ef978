package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gl.gl_lg_java.annotation.RequirePermission;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxKjxmjbxx;
import com.gl.gl_lg_java.dto.KjxmjbxxQueryDTO;
import com.gl.gl_lg_java.dto.KjxmStatisticsDTO;
import com.gl.gl_lg_java.service.RchxKjxmjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
/**
 * 科技项目基本信息控制器
 */
@RestController
@RequestMapping("/api/kjxmjbxx")
@Slf4j
public class KjxmjbxxController {
    
    @Autowired
    private RchxKjxmjbxxService kjxmjbxxService;
    
    /**
     * 根据项目主键查询
     */
    @GetMapping("/{xmid}")
    @RequirePermission("教师")
    public Result<RchxKjxmjbxx> getByXmid(@PathVariable String xmid, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            RchxKjxmjbxx result = kjxmjbxxService.getByXmid(xmid);
            
            // 权限控制：教师只能查看自己负责的项目
            if ("教师".equals(currentUserQx) && result != null && !currentUserZgh.equals(result.getXmfzrh())) {
                return Result.error(403, "权限不足，只能查看自己负责的项目");
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询科技项目信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询科技项目信息
     */
    @PostMapping("/page")
    @RequirePermission("教师")
    public Result<IPage<RchxKjxmjbxx>> getPage(@RequestBody KjxmjbxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己负责的项目
            if ("教师".equals(currentUserQx)) {
                queryDTO.setXmfzrh(currentUserZgh);
            }
            
            IPage<RchxKjxmjbxx> result = kjxmjbxxService.pageByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询科技项目信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 多条件查询科技项目信息
     */
    @PostMapping("/list")
    @RequirePermission("教师")
    public Result<List<RchxKjxmjbxx>> getList(@RequestBody KjxmjbxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己负责的项目
            if ("教师".equals(currentUserQx)) {
                queryDTO.setXmfzrh(currentUserZgh);
            }
            
            List<RchxKjxmjbxx> result = kjxmjbxxService.listByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询科技项目信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据项目名称模糊查询
     */
    @GetMapping("/name/{xmmc}")
    @RequirePermission("学院管理员")
    public Result<List<RchxKjxmjbxx>> getByXmmcLike(@PathVariable String xmmc) {
        try {
            List<RchxKjxmjbxx> result = kjxmjbxxService.listByXmmcLike(xmmc);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据项目名称查询失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据项目负责人号查询
     */
    @GetMapping("/leader/{xmfzrh}")
    @RequirePermission("教师")
    public Result<List<RchxKjxmjbxx>> getByXmfzrh(@PathVariable String xmfzrh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己负责的项目
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(xmfzrh)) {
                return Result.error(403, "权限不足，只能查看自己负责的项目");
            }
            
            List<RchxKjxmjbxx> result = kjxmjbxxService.listByXmfzrh(xmfzrh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据项目负责人查询失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据项目执行状态查询
     */
    @GetMapping("/status/{xmzxzt}")
    @RequirePermission("学院管理员")
    public Result<List<RchxKjxmjbxx>> getByXmzxzt(@PathVariable String xmzxzt) {
        try {
            List<RchxKjxmjbxx> result = kjxmjbxxService.listByXmzxzt(xmzxzt);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据项目执行状态查询失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据审核状态查询
     */
    @GetMapping("/audit/{shzt}")
    @RequirePermission("评审")
    public Result<List<RchxKjxmjbxx>> getByShzt(@PathVariable String shzt) {
        try {
            List<RchxKjxmjbxx> result = kjxmjbxxService.listByShzt(shzt);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据审核状态查询失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增科技项目信息
     */
    @PostMapping
    @RequirePermission("学院管理员")
    public Result<String> create(@RequestBody RchxKjxmjbxx kjxmjbxx) {
        try {
            boolean success = kjxmjbxxService.saveKjxmjbxx(kjxmjbxx);
            if (success) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增科技项目信息失败: {}", e.getMessage());
            return Result.error("新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新科技项目信息
     */
    @PutMapping
    @RequirePermission("学院管理员")
    public Result<String> update(@RequestBody RchxKjxmjbxx kjxmjbxx, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能修改自己负责的项目
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(kjxmjbxx.getXmfzrh())) {
                return Result.error(403, "权限不足，只能修改自己负责的项目");
            }
            
            boolean success = kjxmjbxxService.updateByXmid(kjxmjbxx);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新科技项目信息失败: {}", e.getMessage());
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除科技项目信息
     */
    @DeleteMapping("/{xmid}")
    @RequirePermission("系统管理员")
    public Result<String> delete(@PathVariable String xmid) {
        try {
            boolean success = kjxmjbxxService.removeByXmid(xmid);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除科技项目信息失败: {}", e.getMessage());
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除科技项目信息
     */
    @DeleteMapping("/batch")
    @RequirePermission("系统管理员")
    public Result<String> deleteBatch(@RequestBody List<String> xmids) {
        try {
            boolean success = kjxmjbxxService.removeBatchByXmids(xmids);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除科技项目信息失败: {}", e.getMessage());
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量新增科技项目信息
     */
    @PostMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> createBatch(@RequestBody List<RchxKjxmjbxx> kjxmjbxxList) {
        try {
            boolean success = kjxmjbxxService.saveBatchKjxmjbxx(kjxmjbxxList);
            if (success) {
                return Result.success("批量新增成功");
            } else {
                return Result.error("批量新增失败");
            }
        } catch (Exception e) {
            log.error("批量新增科技项目信息失败: {}", e.getMessage());
            return Result.error("批量新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新科技项目信息
     */
    @PutMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> updateBatch(@RequestBody List<RchxKjxmjbxx> kjxmjbxxList) {
        try {
            boolean success = kjxmjbxxService.updateBatchKjxmjbxx(kjxmjbxxList);
            if (success) {
                return Result.success("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新科技项目信息失败: {}", e.getMessage());
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 按项目级别统计
     */
    @GetMapping("/stats/xmjb")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByXmjb() {
        try {
            log.info("查询项目级别统计");
            Map<String, Integer> stats = kjxmjbxxService.getStatsByXmjb();
            log.info("项目级别统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询项目级别统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按执行状态统计
     */
    @GetMapping("/stats/xmzxzt")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByXmzxzt() {
        try {
            log.info("查询执行状态统计");
            Map<String, Integer> stats = kjxmjbxxService.getStatsByXmzxzt();
            log.info("执行状态统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询执行状态统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按审核状态统计
     */
    @GetMapping("/stats/shzt")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByShzt() {
        try {
            log.info("查询审核状态统计");
            Map<String, Integer> stats = kjxmjbxxService.getStatsByShzt();
            log.info("审核状态统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询审核状态统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目级别选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/xmjb-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getXmjbOptions() {
        try {
            List<Map<String, Object>> options = kjxmjbxxService.getXmjbOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取项目级别选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取执行状态选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/xmzxzt-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getXmzxztOptions() {
        try {
            List<Map<String, Object>> options = kjxmjbxxService.getXmzxztOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取执行状态选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核状态选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/shzt-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getShztOptions() {
        try {
            List<Map<String, Object>> options = kjxmjbxxService.getShztOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取审核状态选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 统计教师个人科技项目数量
     * @param zgh 职工号，可选参数。如果不传则查询当前登录用户
     */
    @GetMapping("/personal-count")
    @RequirePermission("教师")
    public Result<Map<String, Object>> getPersonalCount(
            @RequestParam(value = "zgh", required = false) String zgh,
            HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");

            // 如果没传zgh参数，则查询当前登录用户
            if (zgh == null || zgh.trim().isEmpty()) {
                zgh = currentUserZgh;
            }

            // 权限控制：普通教师只能查询自己的数据
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(zgh)) {
                return Result.error(403, "权限不足，只能查询自己的科技项目数量");
            }

            log.info("查询教师个人科技项目数量，职工号: {}", zgh);

            Long count = kjxmjbxxService.getPersonalCount(zgh);

            Map<String, Object> result = new HashMap<>();
            result.put("zgh", zgh);
            result.put("count", count);
            result.put("type", "科技项目");

            log.info("教师个人科技项目数量查询成功，职工号: {}, 数量: {}", zgh, count);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询教师个人科技项目数量失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取科技项目综合统计数据
     * 包括：单位统计、项目类别统计、负责人TOP10、总体统计
     */
    @GetMapping("/statistics")
    public Result<KjxmStatisticsDTO.ComprehensiveStatistics> getStatistics() {
        try {
            log.info("开始获取科技项目统计数据");

            KjxmStatisticsDTO.ComprehensiveStatistics statistics = kjxmjbxxService.getComprehensiveStatistics();

            log.info("科技项目统计数据获取成功: 项目总数={}, 单位总数={}, 类别总数={}",
                    statistics.getTotalProjects(), statistics.getTotalUnits(), statistics.getTotalCategories());

            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取科技项目统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取单位项目统计（单独接口）
     */
    @GetMapping("/statistics/units")
    public Result<List<KjxmStatisticsDTO.UnitStatistics>> getUnitStatistics() {
        try {
            log.info("开始获取单位项目统计数据");

            KjxmStatisticsDTO.ComprehensiveStatistics statistics = kjxmjbxxService.getComprehensiveStatistics();

            log.info("单位项目统计数据获取成功，共{}个单位", statistics.getUnitStatistics().size());

            return Result.success(statistics.getUnitStatistics());

        } catch (Exception e) {
            log.error("获取单位项目统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目类别统计（单独接口）
     */
    @GetMapping("/statistics/categories")
    public Result<List<KjxmStatisticsDTO.CategoryStatistics>> getCategoryStatistics() {
        try {
            log.info("开始获取项目类别统计数据");

            KjxmStatisticsDTO.ComprehensiveStatistics statistics = kjxmjbxxService.getComprehensiveStatistics();

            log.info("项目类别统计数据获取成功，共{}个类别", statistics.getCategoryStatistics().size());

            return Result.success(statistics.getCategoryStatistics());

        } catch (Exception e) {
            log.error("获取项目类别统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取负责人TOP10统计（单独接口）
     */
    @GetMapping("/statistics/top-leaders")
    public Result<List<KjxmStatisticsDTO.LeaderStatistics>> getTopLeaders() {
        try {
            log.info("开始获取负责人TOP10统计数据");

            KjxmStatisticsDTO.ComprehensiveStatistics statistics = kjxmjbxxService.getComprehensiveStatistics();

            log.info("负责人TOP10统计数据获取成功，共{}个负责人", statistics.getTopLeaders().size());

            return Result.success(statistics.getTopLeaders());

        } catch (Exception e) {
            log.error("获取负责人TOP10统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }
}
