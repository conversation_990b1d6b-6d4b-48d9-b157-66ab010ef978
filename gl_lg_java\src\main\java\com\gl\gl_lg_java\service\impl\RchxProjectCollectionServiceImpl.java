package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxProjectCollection;
import com.gl.gl_lg_java.mapper.RchxProjectCollectionMapper;
import com.gl.gl_lg_java.service.RchxProjectCollectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目选题征集表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@Service
public class RchxProjectCollectionServiceImpl extends ServiceImpl<RchxProjectCollectionMapper, RchxProjectCollection> 
        implements RchxProjectCollectionService {

    @Override
    public IPage<RchxProjectCollection> pageQuery(Page<RchxProjectCollection> page, 
                                                 Integer categoryId, 
                                                 Integer typeId, 
                                                 Integer deptId, 
                                                 String status, 
                                                 String collectionName) {
        QueryWrapper<RchxProjectCollection> queryWrapper = new QueryWrapper<>();
        
        if (categoryId != null) {
            queryWrapper.eq("category_id", categoryId);
        }
        if (typeId != null) {
            queryWrapper.eq("type_id", typeId);
        }
        if (deptId != null) {
            queryWrapper.eq("dept_id", deptId);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        if (StringUtils.hasText(collectionName)) {
            queryWrapper.like("collection_name", collectionName);
        }
        
        queryWrapper.orderByDesc("create_time");
        return page(page, queryWrapper);
    }

    @Override
    public RchxProjectCollection getByCollectionCode(String collectionCode) {
        QueryWrapper<RchxProjectCollection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("collection_code", collectionCode);
        return getOne(queryWrapper);
    }

    @Override
    public List<RchxProjectCollection> getActiveCollections() {
        QueryWrapper<RchxProjectCollection> queryWrapper = new QueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        queryWrapper.eq("status", "PUBLISHED")
                   .le("accept_start_time", now)
                   .ge("accept_end_time", now)
                   .orderByDesc("create_time");
        return list(queryWrapper);
    }

    @Override
    public boolean submitCollection(Long id, String submitBy) {
        try {
            RchxProjectCollection collection = new RchxProjectCollection();
            collection.setId(id);
            collection.setStatus("SUBMITTED");
            collection.setSubmitTime(LocalDateTime.now());
            collection.setUpdateBy(submitBy);
            return updateById(collection);
        } catch (Exception e) {
            log.error("提交征集失败: id={}, submitBy={}", id, submitBy, e);
            return false;
        }
    }

    @Override
    public boolean reviewCollection(Long id, boolean approved, String reviewerZgh, String reviewComments) {
        try {
            RchxProjectCollection collection = new RchxProjectCollection();
            collection.setId(id);
            collection.setStatus(approved ? "APPROVED" : "REJECTED");
            collection.setReviewTime(LocalDateTime.now());
            collection.setReviewerZgh(reviewerZgh);
            collection.setReviewComments(reviewComments);
            collection.setUpdateBy(reviewerZgh);
            return updateById(collection);
        } catch (Exception e) {
            log.error("审核征集失败: id={}, approved={}, reviewerZgh={}", id, approved, reviewerZgh, e);
            return false;
        }
    }

    @Override
    public boolean publishCollection(Long id) {
        try {
            RchxProjectCollection collection = new RchxProjectCollection();
            collection.setId(id);
            collection.setStatus("PUBLISHED");
            return updateById(collection);
        } catch (Exception e) {
            log.error("发布征集失败: id={}", id, e);
            return false;
        }
    }

    @Override
    public boolean closeCollection(Long id) {
        try {
            RchxProjectCollection collection = new RchxProjectCollection();
            collection.setId(id);
            collection.setStatus("CLOSED");
            return updateById(collection);
        } catch (Exception e) {
            log.error("关闭征集失败: id={}", id, e);
            return false;
        }
    }

    @Override
    public boolean existsByCollectionCode(String collectionCode, Long excludeId) {
        QueryWrapper<RchxProjectCollection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("collection_code", collectionCode);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) > 0;
    }
}
