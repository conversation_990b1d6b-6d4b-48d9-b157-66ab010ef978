package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.domain.RchxSchoolProjectFiles;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 校级项目申报文件Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Mapper
public interface RchxSchoolProjectFilesMapper extends BaseMapper<RchxSchoolProjectFiles> {

    /**
     * 分页查询文件信息
     */
    @Select({
        "<script>",
        "SELECT f.*, a.project_name, a.applicant_name ",
        "FROM t_rchx_school_project_files f ",
        "LEFT JOIN t_rchx_school_project_applications a ON f.application_id = a.id ",
        "WHERE f.is_deleted = 0 ",
        "<if test='applicationId != null'>",
        "AND f.application_id = #{applicationId} ",
        "</if>",
        "<if test='applicationCode != null and applicationCode != \"\"'>",
        "AND f.application_code = #{applicationCode} ",
        "</if>",
        "<if test='fileCategory != null and fileCategory != \"\"'>",
        "AND f.file_category = #{fileCategory} ",
        "</if>",
        "<if test='uploadBy != null and uploadBy != \"\"'>",
        "AND f.upload_by = #{uploadBy} ",
        "</if>",
        "<if test='isRequired != null'>",
        "AND f.is_required = #{isRequired} ",
        "</if>",
        "ORDER BY f.upload_time DESC",
        "</script>"
    })
    IPage<Map<String, Object>> selectPageWithDetails(
            Page<RchxSchoolProjectFiles> page,
            @Param("applicationId") Long applicationId,
            @Param("applicationCode") String applicationCode,
            @Param("fileCategory") String fileCategory,
            @Param("uploadBy") String uploadBy,
            @Param("isRequired") Boolean isRequired
    );

    /**
     * 根据申报ID查询文件列表
     */
    @Select("SELECT * FROM t_rchx_school_project_files " +
            "WHERE application_id = #{applicationId} AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxSchoolProjectFiles> selectByApplicationId(@Param("applicationId") Long applicationId);

    /**
     * 根据申报编号查询文件列表
     */
    @Select("SELECT * FROM t_rchx_school_project_files " +
            "WHERE application_code = #{applicationCode} AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxSchoolProjectFiles> selectByApplicationCode(@Param("applicationCode") String applicationCode);

    /**
     * 根据文件类别查询文件列表
     */
    @Select("SELECT * FROM t_rchx_school_project_files " +
            "WHERE application_id = #{applicationId} AND file_category = #{fileCategory} " +
            "AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxSchoolProjectFiles> selectByCategory(@Param("applicationId") Long applicationId,
                                                  @Param("fileCategory") String fileCategory);

    /**
     * 根据上传人查询文件列表
     */
    @Select("SELECT * FROM t_rchx_school_project_files " +
            "WHERE upload_by = #{uploadBy} AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxSchoolProjectFiles> selectByUploader(@Param("uploadBy") String uploadBy);

    /**
     * 查询必需文件列表
     */
    @Select("SELECT * FROM t_rchx_school_project_files " +
            "WHERE application_id = #{applicationId} AND is_required = 1 " +
            "AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxSchoolProjectFiles> selectRequiredFiles(@Param("applicationId") Long applicationId);

    /**
     * 根据文件MD5查询（用于重复文件检测）
     */
    @Select("SELECT * FROM t_rchx_school_project_files " +
            "WHERE file_md5 = #{fileMd5} AND is_deleted = 0")
    List<RchxSchoolProjectFiles> selectByFileMd5(@Param("fileMd5") String fileMd5);

    /**
     * 更新下载次数和最后下载时间
     */
    @Update("UPDATE t_rchx_school_project_files SET " +
            "download_count = download_count + 1, " +
            "last_download_time = #{lastDownloadTime} " +
            "WHERE id = #{id}")
    int updateDownloadInfo(@Param("id") Long id, @Param("lastDownloadTime") LocalDateTime lastDownloadTime);

    /**
     * 逻辑删除文件
     */
    @Update("UPDATE t_rchx_school_project_files SET " +
            "is_deleted = 1, " +
            "delete_time = #{deleteTime}, " +
            "delete_by = #{deleteBy} " +
            "WHERE id = #{id}")
    int logicalDeleteFile(@Param("id") Long id,
                         @Param("deleteTime") LocalDateTime deleteTime,
                         @Param("deleteBy") String deleteBy);

    /**
     * 批量逻辑删除文件
     */
    @Update({
        "<script>",
        "UPDATE t_rchx_school_project_files SET ",
        "is_deleted = 1, ",
        "delete_time = #{deleteTime}, ",
        "delete_by = #{deleteBy} ",
        "WHERE id IN ",
        "<foreach collection='ids' item='id' open='(' separator=',' close=')'>",
        "#{id}",
        "</foreach>",
        "</script>"
    })
    int batchLogicalDeleteFiles(@Param("ids") List<Long> ids,
                               @Param("deleteTime") LocalDateTime deleteTime,
                               @Param("deleteBy") String deleteBy);

    /**
     * 获取文件统计信息
     */
    @Select({
        "SELECT ",
        "COUNT(*) AS total_files, ",
        "SUM(file_size) AS total_size, ",
        "SUM(CASE WHEN is_required = 1 THEN 1 ELSE 0 END) AS required_files, ",
        "SUM(download_count) AS total_downloads, ",
        "COUNT(DISTINCT upload_by) AS total_uploaders, ",
        "COUNT(DISTINCT application_id) AS total_applications ",
        "FROM t_rchx_school_project_files ",
        "WHERE is_deleted = 0"
    })
    Map<String, Object> getFileStatistics();

    /**
     * 获取文件类别统计
     */
    @Select({
        "SELECT ",
        "file_category, ",
        "COUNT(*) AS file_count, ",
        "SUM(file_size) AS total_size, ",
        "SUM(download_count) AS total_downloads ",
        "FROM t_rchx_school_project_files ",
        "WHERE is_deleted = 0 ",
        "GROUP BY file_category ",
        "ORDER BY file_count DESC"
    })
    List<Map<String, Object>> getFileCategoryStatistics();

    /**
     * 获取申报文件统计
     */
    @Select({
        "SELECT ",
        "f.application_code, ",
        "a.project_name, ",
        "a.applicant_name, ",
        "COUNT(*) AS file_count, ",
        "SUM(f.file_size) AS total_size, ",
        "SUM(f.download_count) AS total_downloads ",
        "FROM t_rchx_school_project_files f ",
        "LEFT JOIN t_rchx_school_project_applications a ON f.application_id = a.id ",
        "WHERE f.is_deleted = 0 ",
        "GROUP BY f.application_id, f.application_code, a.project_name, a.applicant_name ",
        "ORDER BY file_count DESC"
    })
    List<Map<String, Object>> getApplicationFileStatistics();

    /**
     * 获取上传人文件统计
     */
    @Select({
        "SELECT ",
        "upload_by, ",
        "upload_by_name, ",
        "COUNT(*) AS file_count, ",
        "SUM(file_size) AS total_size ",
        "FROM t_rchx_school_project_files ",
        "WHERE is_deleted = 0 ",
        "GROUP BY upload_by, upload_by_name ",
        "ORDER BY file_count DESC"
    })
    List<Map<String, Object>> getUploaderFileStatistics();

    /**
     * 获取最近上传的文件
     */
    @Select("SELECT * FROM t_rchx_school_project_files " +
            "WHERE is_deleted = 0 " +
            "ORDER BY upload_time DESC " +
            "LIMIT #{limit}")
    List<RchxSchoolProjectFiles> getRecentUploadedFiles(@Param("limit") Integer limit);

    /**
     * 获取热门下载文件
     */
    @Select("SELECT * FROM t_rchx_school_project_files " +
            "WHERE is_deleted = 0 AND download_count > 0 " +
            "ORDER BY download_count DESC " +
            "LIMIT #{limit}")
    List<RchxSchoolProjectFiles> getPopularDownloadFiles(@Param("limit") Integer limit);

    /**
     * 检查申报的必需文件是否完整
     */
    @Select({
        "SELECT ",
        "CASE WHEN COUNT(CASE WHEN file_category = 'APPLICATION' THEN 1 END) > 0 THEN 1 ELSE 0 END AS has_application, ",
        "CASE WHEN COUNT(CASE WHEN file_category = 'BUDGET' THEN 1 END) > 0 THEN 1 ELSE 0 END AS has_budget, ",
        "CASE WHEN COUNT(CASE WHEN file_category = 'RESUME' THEN 1 END) > 0 THEN 1 ELSE 0 END AS has_resume, ",
        "COUNT(*) AS total_files ",
        "FROM t_rchx_school_project_files ",
        "WHERE application_id = #{applicationId} AND is_deleted = 0"
    })
    Map<String, Object> checkRequiredFiles(@Param("applicationId") Long applicationId);

    /**
     * 检查文件名是否存在
     */
    @Select("SELECT COUNT(*) FROM t_rchx_school_project_files " +
            "WHERE file_name = #{fileName} AND application_id = #{applicationId} " +
            "AND is_deleted = 0")
    int countByFileName(@Param("fileName") String fileName, @Param("applicationId") Long applicationId);
}
