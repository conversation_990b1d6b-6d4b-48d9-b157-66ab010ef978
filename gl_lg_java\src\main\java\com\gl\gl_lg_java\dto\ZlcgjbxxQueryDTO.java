package com.gl.gl_lg_java.dto;

import lombok.Data;

/**
 * 专利成果基本信息查询DTO
 */
@Data
public class ZlcgjbxxQueryDTO {
    
    /**
     * 专利成果编号
     */
    private String zlcgbh;
    
    /**
     * 专利成果名称
     */
    private String zlcgmc;
    
    /**
     * 单位号
     */
    private String dwh;
    
    /**
     * 单位名称
     */
    private String dwmc;
    
    /**
     * 专利类型
     */
    private String zllx;
    
    /**
     * 专利状态
     */
    private String zlzt;
    
    /**
     * 审核状态
     */
    private String shzt;
    
    /**
     * 第一发明人
     */
    private String dyfmr;
    
    /**
     * 第一发明人职工号
     */
    private String dyfmrzgh;
    
    /**
     * 开始日期（批准日期）
     */
    private String startDate;
    
    /**
     * 结束日期（批准日期）
     */
    private String endDate;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String orderBy = "cjsj";
    
    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "desc";
}
