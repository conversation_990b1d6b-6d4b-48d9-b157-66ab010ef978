package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectApplication;

import java.util.List;

/**
 * 项目申报表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface RchxProjectApplicationService extends IService<RchxProjectApplication> {

    /**
     * 分页查询项目申报
     * 
     * @param page 分页参数
     * @param categoryId 项目大类ID
     * @param typeId 项目类别ID
     * @param deptId 管理部门ID
     * @param status 状态
     * @param projectName 项目名称（模糊查询）
     * @param applicantZgh 申报人职工号
     * @return 分页结果
     */
    IPage<RchxProjectApplication> pageQuery(Page<RchxProjectApplication> page, 
                                           Integer categoryId, 
                                           Integer typeId, 
                                           Integer deptId, 
                                           String status, 
                                           String projectName,
                                           String applicantZgh);

    /**
     * 根据项目编号获取申报信息
     * 
     * @param projectCode 项目编号
     * @return 项目申报
     */
    RchxProjectApplication getByProjectCode(String projectCode);

    /**
     * 根据申报人获取申报列表
     * 
     * @param applicantZgh 申报人职工号
     * @return 申报列表
     */
    List<RchxProjectApplication> getByApplicant(String applicantZgh);

    /**
     * 提交申报
     * 
     * @param id 申报ID
     * @param submitBy 提交人
     * @return 是否成功
     */
    boolean submitApplication(Long id, String submitBy);

    /**
     * 审核申报
     * 
     * @param id 申报ID
     * @param approved 是否通过
     * @param reviewerZgh 审核人职工号
     * @param reviewComments 审核意见
     * @return 是否成功
     */
    boolean reviewApplication(Long id, boolean approved, String reviewerZgh, String reviewComments);

    /**
     * 检查项目编号是否存在
     * 
     * @param projectCode 项目编号
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByProjectCode(String projectCode, Long excludeId);

    /**
     * 获取申报统计信息
     * 
     * @param deptId 部门ID（可选）
     * @param categoryId 大类ID（可选）
     * @return 统计信息
     */
    Object getApplicationStatistics(Integer deptId, Integer categoryId);
}
