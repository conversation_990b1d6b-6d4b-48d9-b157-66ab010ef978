-- =====================================================
-- 国家/省部级项目归档管理系统数据库表结构
-- 创建时间: 2025-01-04
-- 说明: 专门用于国家级和省部级项目的归档管理，支持MinIO文件存储
-- =====================================================

USE glrchx;

-- =====================================================
-- 1. 项目级别字典表（国家/省部级专用）
-- =====================================================

-- 1.1 国家/省部级项目级别表
DROP TABLE IF EXISTS `t_rchx_national_project_levels`;
CREATE TABLE `t_rchx_national_project_levels` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '级别ID',
  `level_code` varchar(50) NOT NULL COMMENT '级别编码',
  `level_name` varchar(100) NOT NULL COMMENT '级别名称',
  `level_type` varchar(20) NOT NULL COMMENT '级别类型(NATIONAL国家级,PROVINCIAL省部级,MUNICIPAL市级)',
  `level_weight` int NOT NULL DEFAULT '0' COMMENT '级别权重(数值越大级别越高)',
  `funding_range_min` decimal(15,2) COMMENT '资助金额下限(万元)',
  `funding_range_max` decimal(15,2) COMMENT '资助金额上限(万元)',
  `archive_requirement` text COMMENT '归档要求说明',
  `description` text COMMENT '级别描述',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序号',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0禁用,1启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_code` (`level_code`),
  KEY `idx_level_type_weight` (`level_type`, `level_weight`),
  KEY `idx_sort_enabled` (`sort_order`, `is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国家/省部级项目级别表';

-- =====================================================
-- 2. 项目归档主表
-- =====================================================

-- 2.1 国家/省部级项目归档主表
DROP TABLE IF EXISTS `t_rchx_national_project_archive`;
CREATE TABLE `t_rchx_national_project_archive` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '归档ID',
  `archive_code` varchar(50) NOT NULL COMMENT '归档编号',
  `project_code` varchar(50) NOT NULL COMMENT '项目编号',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `level_id` int NOT NULL COMMENT '项目级别ID',
  `dept_id` int NOT NULL COMMENT '所属部门ID',
  `project_leader_zgh` varchar(50) NOT NULL COMMENT '项目负责人职工号',
  `project_leader_name` varchar(50) NOT NULL COMMENT '项目负责人姓名',
  `project_leader_phone` varchar(20) COMMENT '项目负责人电话',
  `project_leader_email` varchar(100) COMMENT '项目负责人邮箱',
  `project_leader_title` varchar(100) COMMENT '项目负责人职称',
  `project_start_date` date NOT NULL COMMENT '项目开始日期',
  `project_end_date` date NOT NULL COMMENT '项目结束日期',
  `project_duration_months` int COMMENT '项目周期(月)',
  `project_budget` decimal(15,2) COMMENT '项目预算(万元)',
  `actual_funding` decimal(15,2) COMMENT '实际资助金额(万元)',
  `funding_source` varchar(200) COMMENT '资助来源',
  `project_description` longtext COMMENT '项目描述',
  `achievement_summary` longtext COMMENT '成果总结',
  `keywords` varchar(500) COMMENT '关键词(逗号分隔)',
  `research_field` varchar(200) COMMENT '研究领域',
  `cooperation_units` text COMMENT '合作单位',
  `archive_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '归档状态(PENDING待归档,ARCHIVED已归档,REVIEWING审核中,APPROVED审核通过,REJECTED审核拒绝,INVALID失效)',
  `archive_time` datetime COMMENT '归档时间',
  `archive_by` varchar(50) COMMENT '归档人职工号',
  `archive_by_name` varchar(50) COMMENT '归档人姓名',
  `review_time` datetime COMMENT '审核时间',
  `reviewer_zgh` varchar(50) COMMENT '审核人职工号',
  `reviewer_name` varchar(50) COMMENT '审核人姓名',
  `review_comments` text COMMENT '审核意见',
  `review_score` decimal(5,2) COMMENT '审核评分',
  `source_type` varchar(20) DEFAULT 'MANUAL' COMMENT '来源类型(MANUAL手动录入,IMPORT从业务系统导入)',
  `source_business_id` bigint COMMENT '来源业务ID(关联申报/结项等表)',
  `priority_level` varchar(20) DEFAULT 'NORMAL' COMMENT '优先级(HIGH高,NORMAL普通,LOW低)',
  `confidentiality_level` varchar(20) DEFAULT 'PUBLIC' COMMENT '保密级别(PUBLIC公开,INTERNAL内部,CONFIDENTIAL机密,SECRET秘密)',
  `archive_deadline` datetime COMMENT '归档截止时间',
  `reminder_sent` tinyint(1) DEFAULT '0' COMMENT '是否已发送提醒(0否,1是)',
  `last_reminder_time` datetime COMMENT '最后提醒时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人职工号',
  `update_by` varchar(50) COMMENT '更新人职工号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_archive_code` (`archive_code`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_level_dept` (`level_id`, `dept_id`),
  KEY `idx_archive_status_time` (`archive_status`, `archive_time`),
  KEY `idx_leader` (`project_leader_zgh`),
  KEY `idx_archive_deadline` (`archive_deadline`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_project_dates` (`project_start_date`, `project_end_date`),
  CONSTRAINT `fk_national_archive_level` FOREIGN KEY (`level_id`) REFERENCES `t_rchx_national_project_levels` (`id`),
  CONSTRAINT `fk_national_archive_dept` FOREIGN KEY (`dept_id`) REFERENCES `t_rchx_departments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国家/省部级项目归档主表';

-- =====================================================
-- 3. 归档文件管理表
-- =====================================================

-- 3.1 国家/省部级项目归档文件表
DROP TABLE IF EXISTS `t_rchx_national_archive_files`;
CREATE TABLE `t_rchx_national_archive_files` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `archive_id` bigint NOT NULL COMMENT '归档ID',
  `project_code` varchar(50) NOT NULL COMMENT '项目编号',
  `file_category` varchar(50) NOT NULL COMMENT '文件类别(APPLICATION申报书,CONTRACT合同书,REPORT研究报告,ACHIEVEMENT成果材料,FINANCIAL财务材料,OTHER其他)',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT 'MinIO文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL COMMENT '文件MIME类型',
  `file_extension` varchar(10) NOT NULL COMMENT '文件扩展名',
  `bucket_name` varchar(100) NOT NULL DEFAULT 'rchx-archive' COMMENT 'MinIO存储桶名称',
  `object_name` varchar(500) NOT NULL COMMENT 'MinIO对象名称',
  `file_md5` varchar(32) COMMENT '文件MD5值',
  `file_description` text COMMENT '文件描述',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必需文件(0否,1是)',
  `is_confidential` tinyint(1) DEFAULT '0' COMMENT '是否机密文件(0否,1是)',
  `upload_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `upload_by` varchar(50) NOT NULL COMMENT '上传人职工号',
  `upload_by_name` varchar(50) NOT NULL COMMENT '上传人姓名',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `last_download_time` datetime COMMENT '最后下载时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0否,1是)',
  `delete_time` datetime COMMENT '删除时间',
  `delete_by` varchar(50) COMMENT '删除人职工号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_archive_category` (`archive_id`, `file_category`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_bucket_object` (`bucket_name`, `object_name`),
  KEY `idx_deleted` (`is_deleted`),
  CONSTRAINT `fk_archive_files_archive` FOREIGN KEY (`archive_id`) REFERENCES `t_rchx_national_project_archive` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国家/省部级项目归档文件表';

-- =====================================================
-- 4. 索引优化
-- =====================================================

-- 4.1 复合索引优化
CREATE INDEX idx_archive_complex_query ON t_rchx_national_project_archive
(level_id, dept_id, archive_status, archive_time DESC);

CREATE INDEX idx_archive_leader_status ON t_rchx_national_project_archive
(project_leader_zgh, archive_status, create_time DESC);

CREATE INDEX idx_files_archive_category ON t_rchx_national_archive_files
(archive_id, file_category, is_deleted, upload_time DESC);

-- =====================================================
-- 5. 视图定义
-- =====================================================

-- 5.1 国家/省部级项目归档概览视图
CREATE OR REPLACE VIEW v_national_project_archive_overview AS
SELECT
    npa.id,
    npa.archive_code,
    npa.project_code,
    npa.project_name,
    npl.level_name AS project_level,
    npl.level_type,
    d.dept_name AS department,
    npa.project_leader_name,
    npa.project_leader_phone,
    npa.project_leader_email,
    npa.project_start_date,
    npa.project_end_date,
    npa.project_budget,
    npa.actual_funding,
    npa.archive_status,
    npa.archive_time,
    npa.archive_by_name,
    npa.review_time,
    npa.reviewer_name,
    npa.review_score,
    npa.confidentiality_level,
    npa.create_time,
    -- 统计文件数量
    COALESCE(file_stats.total_files, 0) AS total_files,
    COALESCE(file_stats.required_files, 0) AS required_files,
    COALESCE(file_stats.confidential_files, 0) AS confidential_files
FROM t_rchx_national_project_archive npa
LEFT JOIN t_rchx_national_project_levels npl ON npa.level_id = npl.id
LEFT JOIN t_rchx_departments d ON npa.dept_id = d.id
LEFT JOIN (
    SELECT
        archive_id,
        COUNT(*) AS total_files,
        SUM(CASE WHEN is_required = 1 THEN 1 ELSE 0 END) AS required_files,
        SUM(CASE WHEN is_confidential = 1 THEN 1 ELSE 0 END) AS confidential_files
    FROM t_rchx_national_archive_files
    WHERE is_deleted = 0
    GROUP BY archive_id
) file_stats ON npa.id = file_stats.archive_id;

-- =====================================================
-- 6. 示例数据插入
-- =====================================================

-- 6.1 插入项目级别示例数据
INSERT INTO t_rchx_national_project_levels (level_code, level_name, level_type, level_weight, funding_range_min, funding_range_max, archive_requirement, description, sort_order) VALUES
('NATIONAL_KEY', '国家重点研发计划', 'NATIONAL', 100, 100.00, 1000.00, '需提交完整的研究报告、成果材料、财务报告等', '国家级重点研发项目', 1),
('NATIONAL_NATURAL', '国家自然科学基金', 'NATIONAL', 90, 20.00, 300.00, '需提交研究报告、学术论文、专利等成果材料', '国家自然科学基金项目', 2),
('NATIONAL_SOCIAL', '国家社会科学基金', 'NATIONAL', 85, 15.00, 80.00, '需提交研究报告、专著、调研报告等', '国家社会科学基金项目', 3),
('PROVINCIAL_KEY', '省重点研发计划', 'PROVINCIAL', 70, 50.00, 200.00, '需提交研究报告、成果材料等', '省级重点研发项目', 4),
('PROVINCIAL_NATURAL', '省自然科学基金', 'PROVINCIAL', 60, 10.00, 50.00, '需提交研究报告、论文等', '省级自然科学基金项目', 5),
('MUNICIPAL_KEY', '市重点科技计划', 'MUNICIPAL', 40, 5.00, 30.00, '需提交项目总结报告', '市级重点科技项目', 6);

-- 6.2 插入归档项目示例数据（需要先确保部门表有数据）
-- 注意：这里假设部门表已有数据，实际使用时需要根据实际部门ID调整
INSERT INTO t_rchx_national_project_archive (
    archive_code, project_code, project_name, level_id, dept_id,
    project_leader_zgh, project_leader_name, project_leader_phone, project_leader_email, project_leader_title,
    project_start_date, project_end_date, project_duration_months, project_budget, actual_funding, funding_source,
    project_description, achievement_summary, keywords, research_field, cooperation_units,
    archive_status, archive_time, archive_by, archive_by_name,
    source_type, priority_level, confidentiality_level, archive_deadline,
    create_by
) VALUES
('ARCH2024001', 'PROJ2024001', '基于人工智能的智能制造关键技术研究', 1, 1,
'202401001', '张三', '13800138001', '<EMAIL>', '教授',
'2024-01-01', '2026-12-31', 36, 500.00, 500.00, '国家重点研发计划',
'本项目旨在研究基于人工智能的智能制造关键技术，包括智能感知、智能决策、智能控制等方面。',
'项目已完成预期目标，发表SCI论文10篇，申请发明专利5项，培养博士生3名。',
'人工智能,智能制造,机器学习,工业4.0', '智能制造', '清华大学,华为技术有限公司',
'ARCHIVED', '2024-12-01 10:00:00', '202401002', '李四',
'IMPORT', 'HIGH', 'INTERNAL', '2024-12-31 23:59:59',
'202401002'
),
('ARCH2024002', 'PROJ2024002', '新能源汽车电池管理系统优化研究', 2, 2,
'202401003', '王五', '13800138002', '<EMAIL>', '副教授',
'2024-03-01', '2027-02-28', 36, 80.00, 80.00, '国家自然科学基金',
'研究新能源汽车电池管理系统的优化算法，提高电池使用效率和安全性。',
'项目进展顺利，已完成理论研究和仿真验证，正在进行实验验证。',
'新能源汽车,电池管理,优化算法,能源效率', '新能源技术', '比亚迪股份有限公司',
'REVIEWING', NULL, NULL, NULL,
'MANUAL', 'NORMAL', 'PUBLIC', '2025-03-31 23:59:59',
'202401003'
);

-- =====================================================
-- 7. 常用查询语句示例
-- =====================================================

-- 7.1 查询所有国家级项目归档情况
/*
SELECT
    v.project_code,
    v.project_name,
    v.project_level,
    v.department,
    v.project_leader_name,
    v.archive_status,
    v.archive_time,
    v.total_files
FROM v_national_project_archive_overview v
WHERE v.level_type = 'NATIONAL'
ORDER BY v.archive_time DESC;
*/

-- 7.2 查询待归档的项目（按截止时间排序）
/*
SELECT
    archive_code,
    project_name,
    project_leader_name,
    archive_deadline,
    DATEDIFF(archive_deadline, NOW()) AS days_remaining
FROM t_rchx_national_project_archive
WHERE archive_status = 'PENDING'
    AND archive_deadline IS NOT NULL
ORDER BY archive_deadline ASC;
*/

-- 7.3 查询某个部门的项目归档统计
/*
SELECT
    d.dept_name,
    COUNT(*) AS total_projects,
    SUM(CASE WHEN npa.archive_status = 'ARCHIVED' THEN 1 ELSE 0 END) AS archived_count,
    SUM(CASE WHEN npa.archive_status = 'PENDING' THEN 1 ELSE 0 END) AS pending_count,
    SUM(CASE WHEN npa.archive_status = 'REVIEWING' THEN 1 ELSE 0 END) AS reviewing_count,
    ROUND(SUM(CASE WHEN npa.archive_status = 'ARCHIVED' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS archive_rate
FROM t_rchx_national_project_archive npa
LEFT JOIN t_rchx_departments d ON npa.dept_id = d.id
GROUP BY npa.dept_id, d.dept_name
ORDER BY archive_rate DESC;
*/

-- 7.4 查询项目负责人的项目归档情况
/*
SELECT
    project_leader_name,
    project_leader_zgh,
    COUNT(*) AS total_projects,
    SUM(CASE WHEN archive_status = 'ARCHIVED' THEN 1 ELSE 0 END) AS archived_projects,
    GROUP_CONCAT(DISTINCT project_name SEPARATOR '; ') AS project_list
FROM t_rchx_national_project_archive
GROUP BY project_leader_zgh, project_leader_name
HAVING total_projects > 1
ORDER BY total_projects DESC;
*/

-- =====================================================
-- 8. 存储过程
-- =====================================================

-- 8.1 自动生成归档编号的存储过程
DELIMITER //
CREATE PROCEDURE sp_generate_archive_code(
    IN p_level_type VARCHAR(20),
    OUT p_archive_code VARCHAR(50)
)
BEGIN
    DECLARE v_year VARCHAR(4);
    DECLARE v_prefix VARCHAR(10);
    DECLARE v_sequence INT DEFAULT 1;
    DECLARE v_max_code VARCHAR(50);

    -- 获取当前年份
    SET v_year = YEAR(NOW());

    -- 根据级别类型设置前缀
    CASE p_level_type
        WHEN 'NATIONAL' THEN SET v_prefix = 'NAT';
        WHEN 'PROVINCIAL' THEN SET v_prefix = 'PROV';
        WHEN 'MUNICIPAL' THEN SET v_prefix = 'MUN';
        ELSE SET v_prefix = 'ARCH';
    END CASE;

    -- 查找当年最大编号
    SELECT MAX(archive_code) INTO v_max_code
    FROM t_rchx_national_project_archive
    WHERE archive_code LIKE CONCAT(v_prefix, v_year, '%');

    -- 计算下一个序号
    IF v_max_code IS NOT NULL THEN
        SET v_sequence = CAST(SUBSTRING(v_max_code, -3) AS UNSIGNED) + 1;
    END IF;

    -- 生成新的归档编号
    SET p_archive_code = CONCAT(v_prefix, v_year, LPAD(v_sequence, 3, '0'));
END //
DELIMITER ;

-- =====================================================
-- 9. 触发器
-- =====================================================

-- 9.1 归档时间自动设置触发器
DELIMITER //
CREATE TRIGGER tr_archive_time_update
    BEFORE UPDATE ON t_rchx_national_project_archive
    FOR EACH ROW
BEGIN
    -- 当状态从非ARCHIVED变为ARCHIVED时，自动设置归档时间
    IF OLD.archive_status != 'ARCHIVED' AND NEW.archive_status = 'ARCHIVED' THEN
        SET NEW.archive_time = NOW();
    END IF;

    -- 当状态从非APPROVED变为APPROVED时，自动设置审核时间
    IF OLD.archive_status != 'APPROVED' AND NEW.archive_status = 'APPROVED' THEN
        SET NEW.review_time = NOW();
    END IF;
END //
DELIMITER ;

-- =====================================================
-- 10. 权限设置建议
-- =====================================================

/*
-- 创建归档管理员角色
CREATE ROLE 'archive_admin';
GRANT SELECT, INSERT, UPDATE, DELETE ON glrchx.t_rchx_national_project_archive TO 'archive_admin';
GRANT SELECT, INSERT, UPDATE, DELETE ON glrchx.t_rchx_national_archive_files TO 'archive_admin';
GRANT SELECT ON glrchx.t_rchx_national_project_levels TO 'archive_admin';
GRANT SELECT ON glrchx.t_rchx_departments TO 'archive_admin';

-- 创建归档查询角色
CREATE ROLE 'archive_viewer';
GRANT SELECT ON glrchx.v_national_project_archive_overview TO 'archive_viewer';
GRANT SELECT ON glrchx.t_rchx_national_project_archive TO 'archive_viewer';
GRANT SELECT ON glrchx.t_rchx_national_archive_files TO 'archive_viewer';
*/

-- =====================================================
-- 说明文档
-- =====================================================

/*
国家/省部级项目归档管理系统数据库设计说明

1. 核心表结构：
   - t_rchx_national_project_levels: 项目级别字典表
   - t_rchx_national_project_archive: 项目归档主表
   - t_rchx_national_archive_files: 归档文件管理表

2. 主要功能：
   - 支持国家级、省部级、市级项目的分级管理
   - 完整的归档状态流程管理
   - 集成MinIO文件存储系统
   - 支持文件分类和权限控制
   - 提供丰富的查询视图和统计功能

3. 文件存储：
   - 使用MinIO对象存储
   - 支持文件分类管理
   - 提供文件MD5校验
   - 支持文件下载统计

4. 索引优化：
   - 针对常用查询场景设计复合索引
   - 支持高效的分页查询
   - 优化文件检索性能

5. 扩展功能：
   - 自动生成归档编号
   - 归档提醒机制
   - 审核流程管理
   - 统计报表支持
*/
