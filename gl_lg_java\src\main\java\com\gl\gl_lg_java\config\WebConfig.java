package com.gl.gl_lg_java.config;

import com.gl.gl_lg_java.interceptor.JwtInterceptor;
import com.gl.gl_lg_java.interceptor.PermissionInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类 - 包含JWT拦截器、权限拦截器和跨域配置
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtInterceptor jwtInterceptor;

    @Autowired
    private PermissionInterceptor permissionInterceptor;

    /**
     * 配置拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // JWT拦截器 - 验证Token
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/api/**")  // 拦截所有API请求
                .excludePathPatterns(
                    "/api/auth/login",           // 登录接口不拦截
                    "/api/auth/refresh",         // 刷新Token接口不拦截
                    "/api/auth/forgot-password", // 忘记密码接口不拦截
                    "/api/auth/reset-password",  // 重置密码接口不拦截
                    "/api/test/**"               // 所有test开头的测试接口不拦截
                )
                .order(1);  // 优先级1

        // 权限拦截器 - 验证权限
        registry.addInterceptor(permissionInterceptor)
                .addPathPatterns("/api/**")  // 拦截所有API请求
                .excludePathPatterns(
                    "/api/auth/login",           // 登录接口不拦截
                    "/api/auth/refresh",         // 刷新Token接口不拦截
                    "/api/auth/me",              // 获取用户信息不拦截
                    "/api/auth/logout",          // 登出接口不拦截
                    "/api/auth/forgot-password", // 忘记密码接口不拦截
                    "/api/auth/reset-password",  // 重置密码接口不拦截
                    "/api/test/**"               // 所有test开头的测试接口不拦截
                )
                .order(2);  // 优先级2，在JWT拦截器之后
    }
    
    /**
     * 配置跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")  // 允许所有域名
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")  // 允许的HTTP方法
                .allowedHeaders("*")  // 允许所有请求头
                .allowCredentials(true)  // 允许携带凭证
                .maxAge(3600);  // 预检请求缓存时间（秒）
    }
}
