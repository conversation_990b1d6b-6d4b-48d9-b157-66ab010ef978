package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxXmysxx;
import com.gl.gl_lg_java.dto.XmysxxQueryDTO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_rchx_xmysxx(项目预算信息)】的数据库操作Service
* @createDate 2025-07-18 11:11:41
*/
public interface RchxXmysxxService extends IService<RchxXmysxx> {

    /**
     * 根据项目编号查询
     */
    List<RchxXmysxx> listByXmbh(String xmbh);

    /**
     * 根据项目名称模糊查询
     */
    List<RchxXmysxx> listByXmmcLike(String xmmc);

    /**
     * 根据凭单号查询
     */
    List<RchxXmysxx> listByPdh(String pdh);

    /**
     * 根据经办人模糊查询
     */
    List<RchxXmysxx> listByJbrLike(String jbr);

    /**
     * 根据报销人模糊查询
     */
    List<RchxXmysxx> listByBxrLike(String bxr);

    /**
     * 根据创建人工号查询
     */
    List<RchxXmysxx> listByCjrgh(String cjrgh);

    /**
     * 根据创建人姓名模糊查询
     */
    List<RchxXmysxx> listByCjrxmLike(String cjrxm);

    /**
     * 根据审核状态查询
     */
    List<RchxXmysxx> listByShzt(String shzt);

    /**
     * 根据支出金额范围查询
     */
    List<RchxXmysxx> listByZcjeBetween(String startAmount, String endAmount);

    /**
     * 根据支出时间范围查询
     */
    List<RchxXmysxx> listByZcsjBetween(String startDate, String endDate);

    /**
     * 多条件查询
     */
    List<RchxXmysxx> listByMultiConditions(XmysxxQueryDTO queryDTO);

    /**
     * 分页多条件查询
     */
    IPage<RchxXmysxx> pageByMultiConditions(XmysxxQueryDTO queryDTO);

    /**
     * 新增项目预算信息
     */
    boolean saveXmysxx(RchxXmysxx xmysxx);

    /**
     * 更新项目预算信息
     */
    boolean updateXmysxx(RchxXmysxx xmysxx);

    /**
     * 根据项目编号删除项目预算信息
     */
    boolean removeByXmbh(String xmbh);

    /**
     * 批量删除项目预算信息
     */
    boolean removeBatchByXmbhs(List<String> xmbhs);

    /**
     * 批量新增项目预算信息
     */
    boolean saveBatchXmysxx(List<RchxXmysxx> xmysxxList);

    /**
     * 批量更新项目预算信息
     */
    boolean updateBatchXmysxx(List<RchxXmysxx> xmysxxList);
}
