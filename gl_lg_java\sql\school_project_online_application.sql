-- =====================================================
-- 校级项目在线申报系统数据库表结构
-- 创建时间: 2025-01-04
-- 说明: 专门用于校级项目的在线申报管理，支持MinIO文件存储
-- =====================================================

USE glrchx;

-- =====================================================
-- 1. 校级项目类别字典表
-- =====================================================

-- 1.1 校级项目类别表
DROP TABLE IF EXISTS `t_rchx_school_project_categories`;
CREATE TABLE `t_rchx_school_project_categories` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '类别ID',
  `category_code` varchar(50) NOT NULL COMMENT '类别编码',
  `category_name` varchar(100) NOT NULL COMMENT '类别名称',
  `funding_range_min` decimal(10,2) COMMENT '资助金额下限(万元)',
  `funding_range_max` decimal(10,2) COMMENT '资助金额上限(万元)',
  `application_requirements` text COMMENT '申报要求说明',
  `evaluation_criteria` text COMMENT '评审标准',
  `project_duration_months` int COMMENT '项目周期(月)',
  `description` text COMMENT '类别描述',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序号',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0禁用,1启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`category_code`),
  KEY `idx_sort_enabled` (`sort_order`, `is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='校级项目类别表';

-- =====================================================
-- 2. 校级项目在线申报主表
-- =====================================================

-- 2.1 校级项目在线申报表
DROP TABLE IF EXISTS `t_rchx_school_project_applications`;
CREATE TABLE `t_rchx_school_project_applications` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '申报ID',
  `application_code` varchar(50) NOT NULL COMMENT '申报编号',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `category_id` int NOT NULL COMMENT '项目类别ID',
  `dept_id` int NOT NULL COMMENT '申报人所属部门ID',
  `applicant_zgh` varchar(50) NOT NULL COMMENT '申报人职工号',
  `applicant_name` varchar(50) NOT NULL COMMENT '申报人姓名',
  `applicant_phone` varchar(20) COMMENT '申报人电话',
  `applicant_email` varchar(100) COMMENT '申报人邮箱',
  `applicant_title` varchar(100) COMMENT '申报人职称',
  `funding_amount` decimal(10,2) NOT NULL COMMENT '申请资助资金(万元)',
  `project_description` longtext COMMENT '项目描述',
  `application_reason` text COMMENT '申报理由',
  `expected_outcomes` text COMMENT '预期成果',
  `implementation_plan` text COMMENT '实施方案',
  `budget_plan` text COMMENT '经费预算',
  `team_members` text COMMENT '团队成员信息',
  `application_deadline` datetime NOT NULL COMMENT '申报截止日期',
  `project_start_date` date COMMENT '项目开始日期',
  `project_end_date` date COMMENT '项目结束日期',
  `status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态(DRAFT草稿,SUBMITTED已提交,REVIEWING审核中,APPROVED通过,REJECTED拒绝,WITHDRAWN撤回)',
  `application_status` varchar(20) NOT NULL DEFAULT 'PREPARING' COMMENT '申报状态(PREPARING准备中,SUBMITTED已提交,UNDER_REVIEW初审中,EXPERT_REVIEW专家评审,APPROVED已通过,REJECTED已拒绝)',
  `submit_time` datetime COMMENT '提交时间',
  `review_start_time` datetime COMMENT '审核开始时间',
  `review_end_time` datetime COMMENT '审核结束时间',
  `reviewer_zgh` varchar(50) COMMENT '审核人职工号',
  `reviewer_name` varchar(50) COMMENT '审核人姓名',
  `review_comments` text COMMENT '审核意见',
  `review_score` decimal(5,2) COMMENT '评审得分',
  `approval_amount` decimal(10,2) COMMENT '批准资助金额(万元)',
  `priority_level` varchar(20) DEFAULT 'NORMAL' COMMENT '优先级(HIGH高,NORMAL普通,LOW低)',
  `is_online_application` tinyint(1) DEFAULT '1' COMMENT '是否在线申报(0否,1是)',
  `application_round` varchar(50) COMMENT '申报批次',
  `keywords` varchar(500) COMMENT '关键词(逗号分隔)',
  `research_field` varchar(200) COMMENT '研究领域',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人职工号',
  `update_by` varchar(50) COMMENT '更新人职工号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_application_code` (`application_code`),
  KEY `idx_category_dept` (`category_id`, `dept_id`),
  KEY `idx_applicant` (`applicant_zgh`),
  KEY `idx_status_application_status` (`status`, `application_status`),
  KEY `idx_application_deadline` (`application_deadline`),
  KEY `idx_submit_time` (`submit_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_funding_amount` (`funding_amount`),
  CONSTRAINT `fk_school_application_category` FOREIGN KEY (`category_id`) REFERENCES `t_rchx_school_project_categories` (`id`),
  CONSTRAINT `fk_school_application_dept` FOREIGN KEY (`dept_id`) REFERENCES `t_rchx_departments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='校级项目在线申报表';

-- =====================================================
-- 3. 校级项目申报文件管理表
-- =====================================================

-- 3.1 校级项目申报文件表
DROP TABLE IF EXISTS `t_rchx_school_project_files`;
CREATE TABLE `t_rchx_school_project_files` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `application_id` bigint NOT NULL COMMENT '申报ID',
  `application_code` varchar(50) NOT NULL COMMENT '申报编号',
  `file_category` varchar(50) NOT NULL COMMENT '文件类别(APPLICATION申报书,BUDGET预算书,RESUME个人简历,ACHIEVEMENT成果证明,PLAN实施方案,OTHER其他)',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT 'MinIO文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL COMMENT '文件MIME类型',
  `file_extension` varchar(10) NOT NULL COMMENT '文件扩展名',
  `bucket_name` varchar(100) NOT NULL DEFAULT 'rchx-school-project' COMMENT 'MinIO存储桶名称',
  `object_name` varchar(500) NOT NULL COMMENT 'MinIO对象名称',
  `file_md5` varchar(32) COMMENT '文件MD5值',
  `file_description` text COMMENT '文件描述',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必需文件(0否,1是)',
  `upload_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `upload_by` varchar(50) NOT NULL COMMENT '上传人职工号',
  `upload_by_name` varchar(50) NOT NULL COMMENT '上传人姓名',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `last_download_time` datetime COMMENT '最后下载时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0否,1是)',
  `delete_time` datetime COMMENT '删除时间',
  `delete_by` varchar(50) COMMENT '删除人职工号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_application_category` (`application_id`, `file_category`),
  KEY `idx_application_code` (`application_code`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_bucket_object` (`bucket_name`, `object_name`),
  KEY `idx_deleted` (`is_deleted`),
  CONSTRAINT `fk_school_files_application` FOREIGN KEY (`application_id`) REFERENCES `t_rchx_school_project_applications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='校级项目申报文件表';

-- =====================================================
-- 4. 索引优化
-- =====================================================

-- 4.1 复合索引优化
CREATE INDEX idx_school_application_complex_query ON t_rchx_school_project_applications
(category_id, dept_id, status, application_status, application_deadline DESC);

CREATE INDEX idx_school_application_applicant_status ON t_rchx_school_project_applications
(applicant_zgh, status, create_time DESC);

CREATE INDEX idx_school_files_application_category ON t_rchx_school_project_files
(application_id, file_category, is_deleted, upload_time DESC);

-- =====================================================
-- 5. 视图定义
-- =====================================================

-- 5.1 校级项目申报概览视图
CREATE OR REPLACE VIEW v_school_project_application_overview AS
SELECT
    spa.id,
    spa.application_code,
    spa.project_name,
    spc.category_name AS project_category,
    d.dept_name AS department,
    spa.applicant_name,
    spa.applicant_phone,
    spa.applicant_email,
    spa.applicant_title,
    spa.funding_amount,
    spa.approval_amount,
    spa.application_deadline,
    spa.project_start_date,
    spa.project_end_date,
    spa.status,
    spa.application_status,
    spa.submit_time,
    spa.review_end_time,
    spa.reviewer_name,
    spa.review_score,
    spa.priority_level,
    spa.application_round,
    spa.create_time,
    -- 统计文件数量
    COALESCE(file_stats.total_files, 0) AS total_files,
    COALESCE(file_stats.required_files, 0) AS required_files
FROM t_rchx_school_project_applications spa
LEFT JOIN t_rchx_school_project_categories spc ON spa.category_id = spc.id
LEFT JOIN t_rchx_departments d ON spa.dept_id = d.id
LEFT JOIN (
    SELECT
        application_id,
        COUNT(*) AS total_files,
        SUM(CASE WHEN is_required = 1 THEN 1 ELSE 0 END) AS required_files
    FROM t_rchx_school_project_files
    WHERE is_deleted = 0
    GROUP BY application_id
) file_stats ON spa.id = file_stats.application_id;

-- =====================================================
-- 6. 示例数据插入
-- =====================================================

-- 6.1 插入校级项目类别示例数据
INSERT INTO t_rchx_school_project_categories (category_code, category_name, funding_range_min, funding_range_max, application_requirements, evaluation_criteria, project_duration_months, description, sort_order) VALUES
('SCHOOL_TEACHING', '教学改革项目', 1.00, 5.00, '需提交教学改革方案、预期效果等材料', '教学效果、创新性、可推广性', 12, '支持教学方法、课程体系等方面的改革创新', 1),
('SCHOOL_RESEARCH', '科研启动项目', 2.00, 10.00, '需提交研究方案、文献综述、预期成果等', '学术价值、创新性、可行性', 24, '支持青年教师开展科学研究工作', 2),
('SCHOOL_INNOVATION', '创新创业项目', 1.00, 8.00, '需提交项目计划书、市场分析、团队介绍等', '创新性、市场前景、团队能力', 18, '支持师生创新创业实践活动', 3),
('SCHOOL_SOCIAL', '社会服务项目', 0.50, 3.00, '需提交服务方案、合作协议等材料', '社会效益、服务质量、可持续性', 12, '支持为社会提供专业技术服务', 4),
('SCHOOL_CULTURE', '校园文化项目', 0.50, 2.00, '需提交活动方案、预算明细等', '文化价值、参与度、影响力', 6, '支持校园文化建设和品牌推广', 5);

-- 6.2 插入申报项目示例数据（需要先确保部门表有数据）
-- 注意：这里假设部门表已有数据，实际使用时需要根据实际部门ID调整
INSERT INTO t_rchx_school_project_applications (
    application_code, project_name, category_id, dept_id,
    applicant_zgh, applicant_name, applicant_phone, applicant_email, applicant_title,
    funding_amount, project_description, application_reason, expected_outcomes,
    implementation_plan, budget_plan, team_members,
    application_deadline, project_start_date, project_end_date,
    status, application_status, submit_time,
    priority_level, application_round, keywords, research_field,
    create_by
) VALUES
('SCHOOL2024001', '基于翻转课堂的程序设计课程教学改革', 1, 1,
'202401001', '张教授', '13800138001', '<EMAIL>', '副教授',
3.00, '本项目旨在通过翻转课堂模式改革程序设计课程教学，提高学生学习效果和编程能力。',
'传统教学模式存在学生参与度不高、实践能力不足等问题，需要通过教学改革提升教学质量。',
'预期提高学生编程能力30%，课程满意度达到90%以上，形成可推广的教学模式。',
'第一阶段：课程设计和资源准备；第二阶段：试点实施；第三阶段：效果评估和推广。',
'设备费1万元，资料费0.5万元，差旅费0.5万元，其他费用1万元。',
'项目负责人：张教授；团队成员：李讲师、王助教；学生助手：3名研究生。',
'2024-12-31 23:59:59', '2024-03-01', '2025-02-28',
'SUBMITTED', 'UNDER_REVIEW', '2024-11-15 14:30:00',
'HIGH', '2024年第二批', '翻转课堂,程序设计,教学改革', '计算机教育',
'202401001'
),
('SCHOOL2024002', '新型纳米材料的制备与应用研究', 2, 2,
'202401002', '李博士', '13800138002', '<EMAIL>', '讲师',
8.00, '研究新型纳米材料的制备工艺和应用前景，为相关产业发展提供技术支撑。',
'纳米材料是当前材料科学的前沿领域，具有重要的科学价值和应用前景。',
'预期发表SCI论文2-3篇，申请发明专利1-2项，培养研究生2名。',
'采用化学合成法制备纳米材料，通过表征分析优化工艺参数，开展应用性能测试。',
'设备费4万元，材料费2万元，测试费1万元，其他费用1万元。',
'项目负责人：李博士；合作导师：王教授；研究生：2名。',
'2024-12-31 23:59:59', '2024-04-01', '2026-03-31',
'APPROVED', 'APPROVED', '2024-10-20 09:15:00',
'NORMAL', '2024年第一批', '纳米材料,制备工艺,应用研究', '材料科学',
'202401002'
);

-- =====================================================
-- 7. 常用查询语句示例
-- =====================================================

-- 7.1 查询所有在线申报项目的概览信息
/*
SELECT
    v.application_code,
    v.project_name,
    v.project_category,
    v.department,
    v.applicant_name,
    v.funding_amount,
    v.approval_amount,
    v.status,
    v.application_status,
    v.application_deadline,
    v.total_files
FROM v_school_project_application_overview v
WHERE v.is_online_application = 1
ORDER BY v.application_deadline DESC, v.create_time DESC;
*/

-- 7.2 查询即将截止的申报项目
/*
SELECT
    application_code,
    project_name,
    applicant_name,
    funding_amount,
    application_deadline,
    DATEDIFF(application_deadline, NOW()) AS days_remaining
FROM t_rchx_school_project_applications
WHERE status IN ('DRAFT', 'SUBMITTED')
    AND application_deadline > NOW()
    AND DATEDIFF(application_deadline, NOW()) <= 7
ORDER BY application_deadline ASC;
*/

-- 7.3 查询各类别项目申报统计
/*
SELECT
    spc.category_name,
    COUNT(*) AS total_applications,
    SUM(CASE WHEN spa.status = 'APPROVED' THEN 1 ELSE 0 END) AS approved_count,
    SUM(CASE WHEN spa.status = 'SUBMITTED' THEN 1 ELSE 0 END) AS submitted_count,
    SUM(CASE WHEN spa.status = 'DRAFT' THEN 1 ELSE 0 END) AS draft_count,
    SUM(spa.funding_amount) AS total_funding_requested,
    SUM(spa.approval_amount) AS total_funding_approved,
    ROUND(SUM(CASE WHEN spa.status = 'APPROVED' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS approval_rate
FROM t_rchx_school_project_applications spa
LEFT JOIN t_rchx_school_project_categories spc ON spa.category_id = spc.id
GROUP BY spa.category_id, spc.category_name
ORDER BY total_applications DESC;
*/

-- 7.4 查询申报人的项目申报情况
/*
SELECT
    applicant_name,
    applicant_zgh,
    COUNT(*) AS total_applications,
    SUM(CASE WHEN status = 'APPROVED' THEN 1 ELSE 0 END) AS approved_applications,
    SUM(funding_amount) AS total_funding_requested,
    SUM(approval_amount) AS total_funding_approved,
    GROUP_CONCAT(DISTINCT project_name SEPARATOR '; ') AS project_list
FROM t_rchx_school_project_applications
GROUP BY applicant_zgh, applicant_name
HAVING total_applications > 0
ORDER BY total_applications DESC, total_funding_approved DESC;
*/

-- =====================================================
-- 8. 存储过程
-- =====================================================

-- 8.1 自动生成申报编号的存储过程
DELIMITER //
CREATE PROCEDURE sp_generate_school_application_code(
    IN p_category_code VARCHAR(50),
    OUT p_application_code VARCHAR(50)
)
BEGIN
    DECLARE v_year VARCHAR(4);
    DECLARE v_prefix VARCHAR(10);
    DECLARE v_sequence INT DEFAULT 1;
    DECLARE v_max_code VARCHAR(50);

    -- 获取当前年份
    SET v_year = YEAR(NOW());

    -- 根据类别设置前缀
    CASE p_category_code
        WHEN 'SCHOOL_TEACHING' THEN SET v_prefix = 'TEACH';
        WHEN 'SCHOOL_RESEARCH' THEN SET v_prefix = 'RESEARCH';
        WHEN 'SCHOOL_INNOVATION' THEN SET v_prefix = 'INNOV';
        WHEN 'SCHOOL_SOCIAL' THEN SET v_prefix = 'SOCIAL';
        WHEN 'SCHOOL_CULTURE' THEN SET v_prefix = 'CULTURE';
        ELSE SET v_prefix = 'SCHOOL';
    END CASE;

    -- 查找当年最大编号
    SELECT MAX(application_code) INTO v_max_code
    FROM t_rchx_school_project_applications
    WHERE application_code LIKE CONCAT(v_prefix, v_year, '%');

    -- 计算下一个序号
    IF v_max_code IS NOT NULL THEN
        SET v_sequence = CAST(SUBSTRING(v_max_code, -3) AS UNSIGNED) + 1;
    END IF;

    -- 生成新的申报编号
    SET p_application_code = CONCAT(v_prefix, v_year, LPAD(v_sequence, 3, '0'));
END //
DELIMITER ;

-- =====================================================
-- 9. 触发器
-- =====================================================

-- 9.1 申报状态自动更新触发器
DELIMITER //
CREATE TRIGGER tr_school_application_status_update
    BEFORE UPDATE ON t_rchx_school_project_applications
    FOR EACH ROW
BEGIN
    -- 当状态从DRAFT变为SUBMITTED时，自动设置提交时间和申报状态
    IF OLD.status = 'DRAFT' AND NEW.status = 'SUBMITTED' THEN
        SET NEW.submit_time = NOW();
        SET NEW.application_status = 'SUBMITTED';
    END IF;

    -- 当状态变为REVIEWING时，自动设置审核开始时间和申报状态
    IF OLD.status != 'REVIEWING' AND NEW.status = 'REVIEWING' THEN
        SET NEW.review_start_time = NOW();
        SET NEW.application_status = 'UNDER_REVIEW';
    END IF;

    -- 当状态变为APPROVED时，自动设置审核结束时间和申报状态
    IF OLD.status != 'APPROVED' AND NEW.status = 'APPROVED' THEN
        SET NEW.review_end_time = NOW();
        SET NEW.application_status = 'APPROVED';
    END IF;

    -- 当状态变为REJECTED时，自动设置审核结束时间和申报状态
    IF OLD.status != 'REJECTED' AND NEW.status = 'REJECTED' THEN
        SET NEW.review_end_time = NOW();
        SET NEW.application_status = 'REJECTED';
    END IF;
END //
DELIMITER ;

-- =====================================================
-- 10. 权限设置建议
-- =====================================================

/*
-- 创建申报管理员角色
CREATE ROLE 'school_project_admin';
GRANT SELECT, INSERT, UPDATE, DELETE ON glrchx.t_rchx_school_project_applications TO 'school_project_admin';
GRANT SELECT, INSERT, UPDATE, DELETE ON glrchx.t_rchx_school_project_files TO 'school_project_admin';
GRANT SELECT ON glrchx.t_rchx_school_project_categories TO 'school_project_admin';
GRANT SELECT ON glrchx.t_rchx_departments TO 'school_project_admin';

-- 创建申报用户角色
CREATE ROLE 'school_project_applicant';
GRANT SELECT, INSERT, UPDATE ON glrchx.t_rchx_school_project_applications TO 'school_project_applicant';
GRANT SELECT, INSERT, UPDATE, DELETE ON glrchx.t_rchx_school_project_files TO 'school_project_applicant';
GRANT SELECT ON glrchx.v_school_project_application_overview TO 'school_project_applicant';

-- 创建查询角色
CREATE ROLE 'school_project_viewer';
GRANT SELECT ON glrchx.v_school_project_application_overview TO 'school_project_viewer';
GRANT SELECT ON glrchx.t_rchx_school_project_applications TO 'school_project_viewer';
*/

-- =====================================================
-- 说明文档
-- =====================================================

/*
校级项目在线申报系统数据库设计说明

1. 核心表结构：
   - t_rchx_school_project_categories: 校级项目类别字典表
   - t_rchx_school_project_applications: 校级项目在线申报主表
   - t_rchx_school_project_files: 校级项目申报文件管理表

2. 主要功能：
   - 支持多种校级项目类别的在线申报
   - 完整的申报状态流程管理
   - 集成MinIO文件存储系统
   - 支持文件分类和上传管理
   - 提供丰富的查询视图和统计功能

3. 核心字段覆盖：
   - 项目名称：project_name
   - 项目类别：category_id (关联校级项目类别表)
   - 资助资金：funding_amount
   - 申报截止日期：application_deadline
   - 状态：status (草稿、已提交、审核中、通过、拒绝等)
   - 申报状态：application_status (更细分的申报流程状态)

4. 文件存储：
   - 使用MinIO对象存储
   - 支持申报文件分类管理
   - 提供文件MD5校验
   - 支持文件下载统计

5. 索引优化：
   - 针对常用查询场景设计复合索引
   - 支持高效的分页查询
   - 优化文件检索性能

6. 扩展功能：
   - 自动生成申报编号
   - 申报状态自动更新
   - 审核流程管理
   - 统计报表支持
*/
