package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 国家/省部级项目级别表
 * @TableName t_rchx_national_project_levels
 */
@TableName(value = "t_rchx_national_project_levels")
@Data
public class RchxNationalProjectLevels implements Serializable {
    
    /**
     * 级别ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 级别编码
     */
    @TableField(value = "level_code")
    private String levelCode;

    /**
     * 级别名称
     */
    @TableField(value = "level_name")
    private String levelName;

    /**
     * 级别类型(NATIONAL国家级,PROVINCIAL省部级,MUNICIPAL市级)
     */
    @TableField(value = "level_type")
    private String levelType;

    /**
     * 级别权重(数值越大级别越高)
     */
    @TableField(value = "level_weight")
    private Integer levelWeight;

    /**
     * 资助金额下限(万元)
     */
    @TableField(value = "funding_range_min")
    private BigDecimal fundingRangeMin;

    /**
     * 资助金额上限(万元)
     */
    @TableField(value = "funding_range_max")
    private BigDecimal fundingRangeMax;

    /**
     * 归档要求说明
     */
    @TableField(value = "archive_requirement")
    private String archiveRequirement;

    /**
     * 级别描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 排序号
     */
    @TableField(value = "sort_order")
    private Integer sortOrder;

    /**
     * 是否启用(0禁用,1启用)
     */
    @TableField(value = "is_enabled")
    private Boolean isEnabled;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
