package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 项目预算信息
 * @TableName t_rchx_xmysxx
 */
@TableName(value ="t_rchx_xmysxx")
@Data
public class RchxXmysxx implements Serializable {
    /**
     * 项目编号
     */
    @TableId(value = "xmbh", type = IdType.INPUT)
    private String xmbh;

    /**
     * 项目名称
     */
    @TableField(value = "xmmc")
    private String xmmc;

    /**
     * 支出金额
     */
    @TableField(value = "zcje")
    private String zcje;

    /**
     * 支出时间
     */
    @TableField(value = "zcsj")
    private String zcsj;

    /**
     * 凭单号
     */
    @TableField(value = "pdh")
    private String pdh;

    /**
     * 经办人
     */
    @TableField(value = "jbr")
    private String jbr;

    /**
     * 报销人
     */
    @TableField(value = "bxr")
    private String bxr;

    /**
     * 创建人工号
     */
    @TableField(value = "cjrgh")
    private String cjrgh;

    /**
     * 创建人姓名
     */
    @TableField(value = "cjrxm")
    private String cjrxm;

    /**
     * 创建时间
     */
    @TableField(value = "cjsj")
    private String cjsj;

    /**
     * 审核状态码
     */
    @TableField(value = "shztm")
    private String shztm;

    /**
     * 审核状态
     */
    @TableField(value = "shzt")
    private String shzt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RchxXmysxx other = (RchxXmysxx) that;
        return (this.getXmbh() == null ? other.getXmbh() == null : this.getXmbh().equals(other.getXmbh()))
            && (this.getXmmc() == null ? other.getXmmc() == null : this.getXmmc().equals(other.getXmmc()))
            && (this.getZcje() == null ? other.getZcje() == null : this.getZcje().equals(other.getZcje()))
            && (this.getZcsj() == null ? other.getZcsj() == null : this.getZcsj().equals(other.getZcsj()))
            && (this.getPdh() == null ? other.getPdh() == null : this.getPdh().equals(other.getPdh()))
            && (this.getJbr() == null ? other.getJbr() == null : this.getJbr().equals(other.getJbr()))
            && (this.getBxr() == null ? other.getBxr() == null : this.getBxr().equals(other.getBxr()))
            && (this.getCjrgh() == null ? other.getCjrgh() == null : this.getCjrgh().equals(other.getCjrgh()))
            && (this.getCjrxm() == null ? other.getCjrxm() == null : this.getCjrxm().equals(other.getCjrxm()))
            && (this.getCjsj() == null ? other.getCjsj() == null : this.getCjsj().equals(other.getCjsj()))
            && (this.getShztm() == null ? other.getShztm() == null : this.getShztm().equals(other.getShztm()))
            && (this.getShzt() == null ? other.getShzt() == null : this.getShzt().equals(other.getShzt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getXmbh() == null) ? 0 : getXmbh().hashCode());
        result = prime * result + ((getXmmc() == null) ? 0 : getXmmc().hashCode());
        result = prime * result + ((getZcje() == null) ? 0 : getZcje().hashCode());
        result = prime * result + ((getZcsj() == null) ? 0 : getZcsj().hashCode());
        result = prime * result + ((getPdh() == null) ? 0 : getPdh().hashCode());
        result = prime * result + ((getJbr() == null) ? 0 : getJbr().hashCode());
        result = prime * result + ((getBxr() == null) ? 0 : getBxr().hashCode());
        result = prime * result + ((getCjrgh() == null) ? 0 : getCjrgh().hashCode());
        result = prime * result + ((getCjrxm() == null) ? 0 : getCjrxm().hashCode());
        result = prime * result + ((getCjsj() == null) ? 0 : getCjsj().hashCode());
        result = prime * result + ((getShztm() == null) ? 0 : getShztm().hashCode());
        result = prime * result + ((getShzt() == null) ? 0 : getShzt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", xmbh=").append(xmbh);
        sb.append(", xmmc=").append(xmmc);
        sb.append(", zcje=").append(zcje);
        sb.append(", zcsj=").append(zcsj);
        sb.append(", pdh=").append(pdh);
        sb.append(", jbr=").append(jbr);
        sb.append(", bxr=").append(bxr);
        sb.append(", cjrgh=").append(cjrgh);
        sb.append(", cjrxm=").append(cjrxm);
        sb.append(", cjsj=").append(cjsj);
        sb.append(", shztm=").append(shztm);
        sb.append(", shzt=").append(shzt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}