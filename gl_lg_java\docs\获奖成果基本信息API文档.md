# 获奖成果基本信息 API 文档

## 基础信息

- **模块名称**: 获奖成果基本信息管理
- **基础路径**: `/api/hjcgjbxx`
- **数据表**: `t_rchx_hjcgjbxx`
- **实体类**: `RchxHjcgjbxx`

## 权限说明

- **教师**: 只能查看自己的获奖成果信息
- **学院管理员**: 可以查看所有获奖成果信息
- **评审**: 可以查看审核相关信息

## API 接口列表

### 1. 根据获奖成果编号查询

**接口地址**: `GET /api/hjcgjbxx/{hjcgbh}`

**权限要求**: 教师

**请求参数**:
- `hjcgbh` (路径参数): 获奖成果编号

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "hjcgbh": "HJ2024001",
    "hjcgmc": "基于人工智能的教学系统",
    "jlmc": "国家教学成果奖",
    "jldj": "一等奖",
    "hjjb": "国家级",
    "dywcrxm": "张三",
    "dywcrzgh": "T001",
    "hjrq": "2024-01-15",
    "shzt": "已审核"
  }
}
```

### 2. 分页查询获奖成果信息

**接口地址**: `POST /api/hjcgjbxx/page`

**权限要求**: 教师

**请求体**:
```json
{
  "hjcgbh": "HJ2024",
  "hjcgmc": "教学系统",
  "jlmc": "教学成果奖",
  "jldj": "一等奖",
  "dywcrxm": "张三",
  "dywcrzgh": "T001",
  "hjjb": "国家级",
  "shzt": "已审核",
  "dwmc": "计算机学院",
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "pageNum": 1,
  "pageSize": 10,
  "orderBy": "hjrq",
  "orderDirection": "desc"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "hjcgbh": "HJ2024001",
        "hjcgmc": "基于人工智能的教学系统",
        "jlmc": "国家教学成果奖",
        "jldj": "一等奖",
        "hjjb": "国家级",
        "dywcrxm": "张三",
        "dywcrzgh": "T001",
        "hjrq": "2024-01-15",
        "shzt": "已审核"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 3. 多条件查询获奖成果信息

**接口地址**: `POST /api/hjcgjbxx/list`

**权限要求**: 教师

**请求体**: 同分页查询，但不返回分页信息

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "hjcgbh": "HJ2024001",
      "hjcgmc": "基于人工智能的教学系统",
      "jlmc": "国家教学成果奖",
      "jldj": "一等奖",
      "hjjb": "国家级",
      "dywcrxm": "张三",
      "dywcrzgh": "T001",
      "hjrq": "2024-01-15",
      "shzt": "已审核"
    }
  ]
}
```

### 4. 根据获奖成果名称模糊查询

**接口地址**: `GET /api/hjcgjbxx/name/{hjcgmc}`

**权限要求**: 学院管理员

**请求参数**:
- `hjcgmc` (路径参数): 获奖成果名称关键字

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "hjcgbh": "HJ2024001",
      "hjcgmc": "基于人工智能的教学系统",
      "jlmc": "国家教学成果奖",
      "jldj": "一等奖"
    }
  ]
}
```

### 5. 根据第一完成人职工号查询

**接口地址**: `GET /api/hjcgjbxx/winner/{dywcrzgh}`

**权限要求**: 教师

**请求参数**:
- `dywcrzgh` (路径参数): 第一完成人职工号

### 6. 根据奖励名称模糊查询

**接口地址**: `GET /api/hjcgjbxx/award/{jlmc}`

**权限要求**: 学院管理员

**请求参数**:
- `jlmc` (路径参数): 奖励名称关键字

### 7. 根据获奖级别查询

**接口地址**: `GET /api/hjcgjbxx/level/{hjjb}`

**权限要求**: 学院管理员

**请求参数**:
- `hjjb` (路径参数): 获奖级别（如：国家级、省级、市级）

### 8. 根据奖励等级查询 ⭐ 新增

**接口地址**: `GET /api/hjcgjbxx/grade/{jldj}`

**权限要求**: 学院管理员

**请求参数**:
- `jldj` (路径参数): 奖励等级（如：一等奖、二等奖、三等奖）

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "hjcgbh": "HJ2024001",
      "hjcgmc": "基于人工智能的教学系统",
      "jlmc": "国家教学成果奖",
      "jldj": "一等奖",
      "hjjb": "国家级",
      "dywcrxm": "张三",
      "dywcrzgh": "T001",
      "hjrq": "2024-01-15"
    }
  ]
}
```

### 9. 根据审核状态查询

**接口地址**: `GET /api/hjcgjbxx/audit/{shzt}`

**权限要求**: 评审

**请求参数**:
- `shzt` (路径参数): 审核状态（如：待审核、已审核、已驳回）

## 统计接口

### 1. 按奖励等级统计

**接口地址**: `GET /api/hjcgjbxx/stats/jldj`

**权限要求**: 教师

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "一等奖": 5,
    "二等奖": 12,
    "三等奖": 8,
    "优秀奖": 3
  }
}
```

### 2. 按获奖级别统计

**接口地址**: `GET /api/hjcgjbxx/stats/hjjb`

**权限要求**: 教师

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "国家级": 3,
    "省级": 15,
    "市级": 10
  }
}
```

### 3. 按审核状态统计

**接口地址**: `GET /api/hjcgjbxx/stats/shzt`

**权限要求**: 教师

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "已审核": 20,
    "待审核": 5,
    "已驳回": 3
  }
}
```

### 4. 获取总数统计

**接口地址**: `GET /api/hjcgjbxx/stats/total`

**权限要求**: 教师

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalCount": 28,
    "timestamp": 1642752000000,
    "shztStats": {
      "已审核": 20,
      "待审核": 5,
      "已驳回": 3
    },
    "hjjbStats": {
      "国家级": 3,
      "省级": 15,
      "市级": 10
    },
    "jldjStats": {
      "一等奖": 5,
      "二等奖": 12,
      "三等奖": 8,
      "优秀奖": 3
    }
  }
}
```

## 查询参数说明

### HjcgjbxxQueryDTO 查询参数

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| hjcgbh | String | 获奖成果编号（模糊查询） | "HJ2024" |
| hjcgmc | String | 获奖成果名称（模糊查询） | "教学系统" |
| jlmc | String | 奖励名称（模糊查询） | "教学成果奖" |
| jldj | String | 奖励等级（精确查询）⭐ 新增 | "一等奖" |
| dywcrxm | String | 第一完成人姓名（模糊查询） | "张三" |
| dywcrzgh | String | 第一完成人职工号（精确查询） | "T001" |
| hjjb | String | 获奖级别（精确查询） | "国家级" |
| shzt | String | 审核状态（精确查询） | "已审核" |
| dwmc | String | 单位名称（模糊查询） | "计算机学院" |
| startDate | String | 获奖日期开始 | "2024-01-01" |
| endDate | String | 获奖日期结束 | "2024-12-31" |
| pageNum | Integer | 页码 | 1 |
| pageSize | Integer | 页大小 | 10 |
| orderBy | String | 排序字段 | "hjrq" |
| orderDirection | String | 排序方向 | "desc" |

## 常见奖励等级值

- 一等奖
- 二等奖  
- 三等奖
- 优秀奖
- 特等奖
- 鼓励奖

## 常见获奖级别值

- 国家级
- 省级
- 市级
- 校级
- 院级

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 403 | 权限不足 |
| 404 | 数据不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. **权限控制**: 教师只能查看自己的获奖成果（dywcrzgh匹配当前用户职工号）
2. **日期格式**: 所有日期字段使用 "YYYY-MM-DD" 格式
3. **模糊查询**: 名称类字段支持模糊查询，会自动添加 % 通配符
4. **精确查询**: 状态、等级类字段使用精确匹配
5. **新增功能**: jldj（奖励等级）字段现已支持查询和筛选
