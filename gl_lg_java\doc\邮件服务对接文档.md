# 邮件服务对接文档

## 概述
本文档描述了科研管理系统邮件服务的前后端对接接口，包括文本邮件发送、HTML邮件发送和批量邮件发送功能。

**重要说明：** 前端只需调用统一的邮件接口，后端会根据配置自动选择邮件服务商（QQ邮箱、宝塔邮局等），对前端完全透明。

## 接口列表

### 1. 发送自定义邮件

**接口地址：** `POST /api/email/send`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数：**
```json
{
  "to": "<EMAIL>",
  "subject": "邮件主题",
  "content": "邮件内容"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| to | String | 是 | 收件人邮箱地址 |
| subject | String | 是 | 邮件主题 |
| content | String | 是 | 邮件内容 |

**响应示例：**

成功响应：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "邮件发送成功"
}
```

失败响应：
```json
{
  "code": 500,
  "message": "邮件发送失败，请检查网络连接",
  "data": null
}
```

---

### 2. 发送HTML邮件

**接口地址：** `POST /api/email/send-html`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数：**
```json
{
  "to": "<EMAIL>",
  "subject": "HTML邮件主题",
  "content": "<h1>HTML邮件内容</h1><p>这是一封HTML格式的邮件</p>"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| to | String | 是 | 收件人邮箱地址 |
| subject | String | 是 | 邮件主题 |
| content | String | 是 | 邮件内容（HTML格式） |

**响应示例：**

成功响应：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "HTML邮件发送成功"
}
```

失败响应：
```json
{
  "code": 500,
  "message": "HTML邮件发送失败，请检查网络连接",
  "data": null
}
```

---

### 3. 批量发送邮件

**接口地址：** `POST /api/email/batch-send`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数：**
```json
{
  "toList": [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ],
  "subject": "批量邮件主题",
  "content": "批量邮件内容"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| toList | Array | 是 | 收件人邮箱地址列表 |
| subject | String | 是 | 邮件主题 |
| content | String | 是 | 邮件内容 |

**响应示例：**

成功响应：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "批量发送完成，成功: 3, 失败: 0"
}
```

部分失败响应：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "批量发送完成，成功: 2, 失败: 1"
}
```

全部失败响应：
```json
{
  "code": 500,
  "message": "所有邮件发送失败",
  "data": null
}
```

---

## 前端实现示例

### 1. 发送自定义邮件
```javascript
// 发送自定义邮件
async sendCustomEmail() {
  const emailData = {
    to: this.recipientEmail,
    subject: this.emailSubject,
    content: this.emailContent
  };
  
  try {
    const response = await this.$http.post('/api/email/send', emailData, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.code === 200) {
      this.$message.success('邮件发送成功');
      this.resetForm();
    } else {
      this.$message.error(response.data.message);
    }
  } catch (error) {
    this.$message.error('发送失败，请稍后重试');
  }
}
```

### 2. 发送HTML邮件
```javascript
// 发送HTML邮件
async sendHtmlEmail() {
  const emailData = {
    to: this.recipientEmail,
    subject: this.emailSubject,
    content: this.htmlContent  // HTML格式内容
  };

  try {
    const response = await this.$http.post('/api/email/send-html', emailData, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.code === 200) {
      this.$message.success('HTML邮件发送成功');
      this.resetForm();
    } else {
      this.$message.error(response.data.message);
    }
  } catch (error) {
    this.$message.error('发送失败，请稍后重试');
  }
}
```

### 3. 批量发送邮件
```javascript
// 批量发送邮件
async sendBatchEmails() {
  const batchData = {
    toList: this.recipientList, // ['<EMAIL>', '<EMAIL>']
    subject: this.batchSubject,
    content: this.batchContent
  };
  
  try {
    const response = await this.$http.post('/api/email/batch-send', batchData, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.code === 200) {
      this.$message.success(response.data.data);
    } else {
      this.$message.error(response.data.message);
    }
  } catch (error) {
    this.$message.error('批量发送失败，请稍后重试');
  }
}
```

---

## Vue.js 完整组件示例

```vue
<template>
  <div class="email-service">
    <el-tabs v-model="activeTab">
      <!-- 自定义邮件 -->
      <el-tab-pane label="发送邮件" name="custom">
        <el-form>
          <el-form-item label="收件人">
            <el-input v-model="customEmail.to" placeholder="请输入收件人邮箱" />
          </el-form-item>
          <el-form-item label="主题">
            <el-input v-model="customEmail.subject" placeholder="请输入邮件主题" />
          </el-form-item>
          <el-form-item label="内容">
            <el-input 
              v-model="customEmail.content" 
              type="textarea" 
              :rows="6"
              placeholder="请输入邮件内容" 
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="sendCustomEmail">发送邮件</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <!-- 批量邮件 -->
      <el-tab-pane label="批量发送" name="batch">
        <el-form>
          <el-form-item label="收件人列表">
            <el-input 
              v-model="batchEmailText" 
              type="textarea" 
              :rows="3"
              placeholder="请输入邮箱地址，每行一个" 
            />
          </el-form-item>
          <el-form-item label="主题">
            <el-input v-model="batchEmail.subject" placeholder="请输入邮件主题" />
          </el-form-item>
          <el-form-item label="内容">
            <el-input 
              v-model="batchEmail.content" 
              type="textarea" 
              :rows="6"
              placeholder="请输入邮件内容" 
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="sendBatchEmails">批量发送</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'custom',
      customEmail: {
        to: '',
        subject: '',
        content: ''
      },
      batchEmail: {
        subject: '',
        content: ''
      },
      batchEmailText: ''
    }
  },
  computed: {
    token() {
      return localStorage.getItem('token');
    }
  },
  methods: {
    // 发送自定义邮件
    async sendCustomEmail() {
      if (!this.customEmail.to.trim()) {
        this.$message.error('请输入收件人邮箱');
        return;
      }
      if (!this.customEmail.subject.trim()) {
        this.$message.error('请输入邮件主题');
        return;
      }
      if (!this.customEmail.content.trim()) {
        this.$message.error('请输入邮件内容');
        return;
      }
      
      try {
        const response = await this.$http.post('/api/email/send', this.customEmail, {
          headers: { 
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.data.code === 200) {
          this.$message.success('邮件发送成功');
          this.customEmail = { to: '', subject: '', content: '' };
        } else {
          this.$message.error(response.data.message);
        }
      } catch (error) {
        this.$message.error('发送失败，请稍后重试');
      }
    },
    
    // 批量发送邮件
    async sendBatchEmails() {
      if (!this.batchEmailText.trim()) {
        this.$message.error('请输入收件人邮箱列表');
        return;
      }
      if (!this.batchEmail.subject.trim()) {
        this.$message.error('请输入邮件主题');
        return;
      }
      if (!this.batchEmail.content.trim()) {
        this.$message.error('请输入邮件内容');
        return;
      }
      
      const toList = this.batchEmailText.split('\n')
        .map(email => email.trim())
        .filter(email => email.length > 0);
      
      if (toList.length === 0) {
        this.$message.error('请输入有效的邮箱地址');
        return;
      }
      
      const batchData = {
        toList: toList,
        subject: this.batchEmail.subject,
        content: this.batchEmail.content
      };
      
      try {
        const response = await this.$http.post('/api/email/batch-send', batchData, {
          headers: { 
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.data.code === 200) {
          this.$message.success(response.data.data);
          this.batchEmail = { subject: '', content: '' };
          this.batchEmailText = '';
        } else {
          this.$message.error(response.data.message);
        }
      } catch (error) {
        this.$message.error('批量发送失败，请稍后重试');
      }
    }
  }
}
</script>
```

---

## 注意事项

1. **认证要求**：所有邮件接口都需要JWT Token认证
2. **邮箱验证**：系统会自动验证邮箱格式的有效性
3. **错误处理**：请妥善处理网络错误和服务器错误
4. **用户体验**：建议在发送邮件时显示loading状态

---

## 联系方式
如有技术问题，请联系后端开发团队。
