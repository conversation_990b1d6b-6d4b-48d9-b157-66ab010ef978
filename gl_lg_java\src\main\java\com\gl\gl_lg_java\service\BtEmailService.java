package com.gl.gl_lg_java.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * 宝塔邮局系统邮件服务
 */
@Service
@Slf4j
public class BtEmailService {

    @Value("${bt.mail.host:mail.159email.shop}")
    private String mailHost;

    @Value("${bt.mail.from}")
    private String mailFrom;

    @Value("${bt.mail.password}")
    private String mailPassword;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 发送简单文本邮件
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送结果
     */
    public boolean sendSimpleEmail(String to, String subject, String content) {
        return sendEmail(to, subject, content, "plain");
    }

    /**
     * 发送HTML邮件
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content HTML邮件内容
     * @return 发送结果
     */
    public boolean sendHtmlEmail(String to, String subject, String content) {
        return sendEmail(to, subject, content, "html");
    }

    /**
     * 发送邮件核心方法
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param subtype 邮件类型 (plain/html)
     * @return 发送结果
     */
    private boolean sendEmail(String to, String subject, String content, String subtype) {
        try {
            String url = String.format("http://%s/mail_sys/send_mail_http.json", mailHost);
            
            // 构建请求参数
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("mail_from", mailFrom);
            params.add("password", mailPassword);
            params.add("mail_to", to);
            params.add("subject", subject);
            params.add("content", content);
            params.add("subtype", subtype);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

            log.info("发送宝塔邮局邮件: {} -> {}, 主题: {}", mailFrom, to, subject);

            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.info("宝塔邮局响应: {}", responseBody);
                
                // 检查响应内容是否包含成功标识
                if (responseBody != null &&
                    (responseBody.contains("\"status\":true") ||
                     responseBody.contains("\"code\":0") ||
                     responseBody.contains("\"status\": true"))) {
                    log.info("宝塔邮局邮件发送成功: {} -> {}", mailFrom, to);
                    return true;
                } else {
                    log.error("宝塔邮局邮件发送失败: {}", responseBody);
                    return false;
                }
            } else {
                log.error("宝塔邮局HTTP请求失败: {}", response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            log.error("宝塔邮局邮件发送异常: {} -> {}, 错误: {}", mailFrom, to, e.getMessage());
            return false;
        }
    }

    /**
     * 发送密码重置邮件
     * @param email 收件人邮箱
     * @param userName 用户姓名
     * @param verificationCode 验证码
     * @return 发送结果
     */
    public boolean sendPasswordResetEmail(String email, String userName, String verificationCode) {
        String subject = "人才画像管理系统 - 密码重置验证码";
        String content = buildPasswordResetEmailContent(userName, verificationCode);
        
        return sendSimpleEmail(email, subject, content);
    }

    /**
     * 构建密码重置邮件内容
     */
    private String buildPasswordResetEmailContent(String userName, String verificationCode) {
        StringBuilder content = new StringBuilder();
        content.append("尊敬的 ").append(userName != null ? userName : "用户").append("：\n\n");
        content.append("您好！您正在申请人才画像管理系统的登录密码。\n\n");
        content.append("您的密码重置验证码是：").append(verificationCode).append("\n\n");
        content.append("验证码有效期为15分钟，请及时使用。\n\n");
        content.append("安全提示：\n");
        content.append("• 请勿将验证码告诉他人\n");
        content.append("• 如果这不是您本人的操作，请忽略此邮件\n");
        content.append("• 验证码仅可使用一次\n\n");
        content.append("如有疑问，请联系系统管理员。\n\n");
        content.append("人才画像管理系统\n");
        content.append(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")));
        
        return content.toString();
    }

    /**
     * 批量发送邮件
     * @param toList 收件人列表
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送结果统计 [成功数, 失败数]
     */
    public int[] sendBatchEmails(java.util.List<String> toList, String subject, String content) {
        int successCount = 0;
        int failCount = 0;

        for (String email : toList) {
            if (email != null && !email.trim().isEmpty()) {
                boolean success = sendSimpleEmail(email.trim(), subject, content);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
                
                // 批量发送时添加短暂延迟，避免频率过高
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            } else {
                failCount++;
            }
        }

        log.info("批量邮件发送完成: 成功={}, 失败={}", successCount, failCount);
        return new int[]{successCount, failCount};
    }
}
