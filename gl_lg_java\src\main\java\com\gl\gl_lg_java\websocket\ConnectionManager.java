package com.gl.gl_lg_java.websocket;

import com.gl.gl_lg_java.service.NotificationRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket连接管理器
 * 管理教师的WebSocket连接状态
 */
@Component
@Slf4j
public class ConnectionManager {

    @Autowired
    private NotificationRedisService notificationRedisService;

    /**
     * 存储职工号与WebSocket会话的映射
     * Key: 职工号(zgh)
     * Value: WebSocketSession
     */
    private final Map<String, WebSocketSession> connections = new ConcurrentHashMap<>();

    /**
     * 存储会话ID与职工号的映射（用于快速查找）
     * Key: 会话ID
     * Value: 职工号(zgh)
     */
    private final Map<String, String> sessionToZgh = new ConcurrentHashMap<>();

    /**
     * 添加连接
     * @param zgh 职工号
     * @param session WebSocket会话
     */
    public void addConnection(String zgh, WebSocketSession session) {
        try {
            // 如果该用户已有连接，先关闭旧连接
            removeConnection(zgh);

            // 添加新连接
            connections.put(zgh, session);
            sessionToZgh.put(session.getId(), zgh);

            // 在Redis中设置在线状态
            notificationRedisService.setTeacherOnline(zgh, session.getId());

            log.info("添加WebSocket连接: zgh={}, sessionId={}, 当前连接数: {}", 
                    zgh, session.getId(), connections.size());

        } catch (Exception e) {
            log.error("添加WebSocket连接失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }

    /**
     * 移除连接
     * @param zgh 职工号
     */
    public void removeConnection(String zgh) {
        try {
            WebSocketSession session = connections.remove(zgh);
            if (session != null) {
                sessionToZgh.remove(session.getId());
                
                // 关闭会话
                if (session.isOpen()) {
                    session.close();
                }

                // 从Redis中移除在线状态
                notificationRedisService.removeTeacherOnline(zgh);

                log.info("移除WebSocket连接: zgh={}, sessionId={}, 当前连接数: {}", 
                        zgh, session.getId(), connections.size());
            }
        } catch (Exception e) {
            log.error("移除WebSocket连接失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }

    /**
     * 根据会话ID移除连接
     * @param sessionId 会话ID
     */
    public void removeConnectionBySessionId(String sessionId) {
        try {
            String zgh = sessionToZgh.remove(sessionId);
            if (zgh != null) {
                connections.remove(zgh);
                notificationRedisService.removeTeacherOnline(zgh);
                
                log.info("根据会话ID移除WebSocket连接: sessionId={}, zgh={}, 当前连接数: {}", 
                        sessionId, zgh, connections.size());
            }
        } catch (Exception e) {
            log.error("根据会话ID移除WebSocket连接失败: sessionId={}, error={}", sessionId, e.getMessage());
        }
    }

    /**
     * 获取连接
     * @param zgh 职工号
     * @return WebSocket会话
     */
    public WebSocketSession getConnection(String zgh) {
        return connections.get(zgh);
    }

    /**
     * 检查用户是否在线
     * @param zgh 职工号
     * @return 是否在线
     */
    public boolean isOnline(String zgh) {
        WebSocketSession session = connections.get(zgh);
        return session != null && session.isOpen();
    }

    /**
     * 获取在线用户数量
     * @return 在线用户数量
     */
    public int getOnlineCount() {
        return connections.size();
    }

    /**
     * 获取所有在线用户的职工号
     * @return 在线用户职工号集合
     */
    public java.util.Set<String> getOnlineUsers() {
        return connections.keySet();
    }

    /**
     * 根据会话ID获取职工号
     * @param sessionId 会话ID
     * @return 职工号
     */
    public String getZghBySessionId(String sessionId) {
        return sessionToZgh.get(sessionId);
    }

    /**
     * 清理所有连接
     */
    public void clearAllConnections() {
        try {
            for (Map.Entry<String, WebSocketSession> entry : connections.entrySet()) {
                String zgh = entry.getKey();
                WebSocketSession session = entry.getValue();
                
                if (session.isOpen()) {
                    session.close();
                }
                
                notificationRedisService.removeTeacherOnline(zgh);
            }
            
            connections.clear();
            sessionToZgh.clear();
            
            log.info("清理所有WebSocket连接完成");
        } catch (Exception e) {
            log.error("清理所有WebSocket连接失败: {}", e.getMessage());
        }
    }

    /**
     * 获取连接统计信息
     * @return 统计信息
     */
    public Map<String, Object> getConnectionStats() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("totalConnections", connections.size());
        stats.put("onlineUsers", getOnlineUsers());
        stats.put("timestamp", System.currentTimeMillis());
        return stats;
    }
}
