package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 通知类型表
 * @TableName t_rchx_notification_types
 */
@TableName(value = "t_rchx_notification_types")
@Data
public class RchxNotificationTypes implements Serializable {
    
    /**
     * 类型ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型编码
     */
    @TableField(value = "type_code")
    private String typeCode;

    /**
     * 类型名称
     */
    @TableField(value = "type_name")
    private String typeName;

    /**
     * 图标
     */
    @TableField(value = "icon")
    private String icon;

    /**
     * 颜色
     */
    @TableField(value = "color")
    private String color;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 获取默认图标
     */
    public String getIconOrDefault() {
        return icon != null ? icon : "bell";
    }

    /**
     * 获取默认颜色
     */
    public String getColorOrDefault() {
        return color != null ? color : "#1890ff";
    }
}
