package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxJzggzjlxx;
import com.gl.gl_lg_java.dto.JzggzjlxxQueryDTO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_rchx_jzggzjlxx(教职工工作简历信息)】的数据库操作Service
* @createDate 2025-07-18 11:11:41
*/
public interface RchxJzggzjlxxService extends IService<RchxJzggzjlxx> {

    /**
     * 根据职工号查询
     */
    List<RchxJzggzjlxx> listByZgh(String zgh);

    /**
     * 根据工作单位模糊查询
     */
    List<RchxJzggzjlxx> listByGzdwLike(String gzdw);

    /**
     * 根据工作内容模糊查询
     */
    List<RchxJzggzjlxx> listByGznrLike(String gznr);

    /**
     * 根据曾任职务模糊查询
     */
    List<RchxJzggzjlxx> listByCrzwLike(String crzw);

    /**
     * 根据从事专业模糊查询
     */
    List<RchxJzggzjlxx> listByCszyLike(String cszy);

    /**
     * 根据工作证明人模糊查询
     */
    List<RchxJzggzjlxx> listByGzzmrLike(String gzzmr);

    /**
     * 根据工作所在地查询
     */
    List<RchxJzggzjlxx> listByGzszd(String gzszd);

    /**
     * 根据工作起始日期范围查询
     */
    List<RchxJzggzjlxx> listByGzqsrqBetween(String startDate, String endDate);

    /**
     * 多条件查询
     */
    List<RchxJzggzjlxx> listByMultiConditions(JzggzjlxxQueryDTO queryDTO);

    /**
     * 分页多条件查询
     */
    IPage<RchxJzggzjlxx> pageByMultiConditions(JzggzjlxxQueryDTO queryDTO);

    /**
     * 新增工作简历信息
     */
    boolean saveJzggzjlxx(RchxJzggzjlxx jzggzjlxx);

    /**
     * 更新工作简历信息
     */
    boolean updateJzggzjlxx(RchxJzggzjlxx jzggzjlxx);

    /**
     * 根据职工号删除工作简历信息
     */
    boolean removeByZgh(String zgh);

    /**
     * 批量删除工作简历信息
     */
    boolean removeBatchByZghs(List<String> zghs);

    /**
     * 批量新增工作简历信息
     */
    boolean saveBatchJzggzjlxx(List<RchxJzggzjlxx> jzggzjlxxList);

    /**
     * 批量更新工作简历信息
     */
    boolean updateBatchJzggzjlxx(List<RchxJzggzjlxx> jzggzjlxxList);
}
