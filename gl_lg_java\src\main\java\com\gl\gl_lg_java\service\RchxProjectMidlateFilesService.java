package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectMidlateFiles;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 项目中后期文件管理Service接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface RchxProjectMidlateFilesService extends IService<RchxProjectMidlateFiles> {

    /**
     * 分页查询文件信息
     * 
     * @param page 分页参数
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param projectCode 项目编号
     * @param fileCategory 文件类别
     * @param uploadBy 上传人
     * @param isRequired 是否必需
     * @param isConfidential 是否机密
     * @return 分页结果
     */
    IPage<RchxProjectMidlateFiles> pageQuery(Page<RchxProjectMidlateFiles> page,
                                             String businessType,
                                             Long businessId,
                                             String projectCode,
                                             String fileCategory,
                                             String uploadBy,
                                             Boolean isRequired,
                                             Boolean isConfidential);

    /**
     * 根据业务类型和业务ID查询文件列表
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 文件列表
     */
    List<RchxProjectMidlateFiles> getByBusiness(String businessType, Long businessId);

    /**
     * 根据项目编号查询文件列表
     * 
     * @param projectCode 项目编号
     * @return 文件列表
     */
    List<RchxProjectMidlateFiles> getByProjectCode(String projectCode);

    /**
     * 根据文件类别查询文件列表
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param fileCategory 文件类别
     * @return 文件列表
     */
    List<RchxProjectMidlateFiles> getByCategory(String businessType, Long businessId, String fileCategory);

    /**
     * 根据上传人查询文件列表
     * 
     * @param uploadBy 上传人
     * @return 文件列表
     */
    List<RchxProjectMidlateFiles> getByUploader(String uploadBy);

    /**
     * 查询机密文件列表
     * 
     * @return 机密文件列表
     */
    List<RchxProjectMidlateFiles> getConfidentialFiles();

    /**
     * 查询必需文件列表
     * 
     * @return 必需文件列表
     */
    List<RchxProjectMidlateFiles> getRequiredFiles();

    /**
     * 上传文件
     * 
     * @param file 文件
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param projectCode 项目编号
     * @param fileCategory 文件类别
     * @param fileDescription 文件描述
     * @param isRequired 是否必需
     * @param isConfidential 是否机密
     * @param versionNumber 版本号
     * @param uploadBy 上传人
     * @param uploadByName 上传人姓名
     * @return 文件信息
     */
    RchxProjectMidlateFiles uploadFile(MultipartFile file,
                                      String businessType,
                                      Long businessId,
                                      String projectCode,
                                      String fileCategory,
                                      String fileDescription,
                                      Boolean isRequired,
                                      Boolean isConfidential,
                                      String versionNumber,
                                      String uploadBy,
                                      String uploadByName);

    /**
     * 下载文件
     * 
     * @param fileId 文件ID
     * @return 文件字节数组
     */
    byte[] downloadFile(Long fileId);

    /**
     * 获取文件下载URL（预签名URL）
     * 
     * @param fileId 文件ID
     * @return 下载URL
     */
    String getFileDownloadUrl(Long fileId);

    /**
     * 删除文件（逻辑删除）
     * 
     * @param fileId 文件ID
     * @param deleteBy 删除人
     * @return 是否成功
     */
    boolean deleteFile(Long fileId, String deleteBy);

    /**
     * 批量删除文件（逻辑删除）
     * 
     * @param fileIds 文件ID列表
     * @param deleteBy 删除人
     * @return 是否成功
     */
    boolean batchDeleteFiles(List<Long> fileIds, String deleteBy);

    /**
     * 获取文件统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getFileStatistics();

    /**
     * 获取文件类别统计
     * 
     * @return 文件类别统计
     */
    List<Map<String, Object>> getFileCategoryStatistics();

    /**
     * 获取业务类型文件统计
     * 
     * @return 业务类型文件统计
     */
    List<Map<String, Object>> getBusinessTypeFileStatistics();

    /**
     * 获取项目文件统计
     * 
     * @return 项目文件统计
     */
    List<Map<String, Object>> getProjectFileStatistics();

    /**
     * 获取上传人文件统计
     * 
     * @return 上传人文件统计
     */
    List<Map<String, Object>> getUploaderFileStatistics();

    /**
     * 获取最近上传的文件
     * 
     * @param limit 限制数量
     * @return 最近上传的文件列表
     */
    List<RchxProjectMidlateFiles> getRecentUploadedFiles(Integer limit);

    /**
     * 获取热门下载文件
     * 
     * @param limit 限制数量
     * @return 热门下载文件列表
     */
    List<RchxProjectMidlateFiles> getPopularDownloadFiles(Integer limit);

    /**
     * 检查文件是否重复（基于MD5）
     * 
     * @param fileMd5 文件MD5
     * @return 重复文件列表
     */
    List<RchxProjectMidlateFiles> checkDuplicateFiles(String fileMd5);

    /**
     * 更新文件信息
     * 
     * @param fileId 文件ID
     * @param fileDescription 文件描述
     * @param isRequired 是否必需
     * @param isConfidential 是否机密
     * @param versionNumber 版本号
     * @return 是否成功
     */
    boolean updateFileInfo(Long fileId, String fileDescription, Boolean isRequired, 
                          Boolean isConfidential, String versionNumber);

    /**
     * 替换文件（上传新版本）
     * 
     * @param fileId 原文件ID
     * @param newFile 新文件
     * @param versionNumber 版本号
     * @param uploadBy 上传人
     * @param uploadByName 上传人姓名
     * @return 新文件信息
     */
    RchxProjectMidlateFiles replaceFile(Long fileId, MultipartFile newFile, String versionNumber, 
                                       String uploadBy, String uploadByName);

    /**
     * 获取文件版本历史
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param originalFileName 原始文件名
     * @return 版本历史列表
     */
    List<RchxProjectMidlateFiles> getFileVersionHistory(String businessType, Long businessId, String originalFileName);

    /**
     * 检查文件名是否存在
     * 
     * @param fileName 文件名
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 是否存在
     */
    boolean existsByFileName(String fileName, String businessType, Long businessId);

    /**
     * 生成唯一文件名
     * 
     * @param originalFileName 原始文件名
     * @param businessType 业务类型
     * @return 唯一文件名
     */
    String generateUniqueFileName(String originalFileName, String businessType);

    /**
     * 计算文件MD5
     * 
     * @param file 文件
     * @return MD5值
     */
    String calculateFileMd5(MultipartFile file);

    /**
     * 验证文件类型
     * 
     * @param file 文件
     * @param allowedTypes 允许的文件类型
     * @return 是否有效
     */
    boolean validateFileType(MultipartFile file, List<String> allowedTypes);

    /**
     * 验证文件大小
     * 
     * @param file 文件
     * @param maxSizeInMB 最大大小（MB）
     * @return 是否有效
     */
    boolean validateFileSize(MultipartFile file, Long maxSizeInMB);
}
