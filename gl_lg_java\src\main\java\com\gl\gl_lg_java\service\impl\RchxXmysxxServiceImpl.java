package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxXmysxx;
import com.gl.gl_lg_java.dto.XmysxxQueryDTO;
import com.gl.gl_lg_java.mapper.RchxXmysxxMapper;
import com.gl.gl_lg_java.service.RchxXmysxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_rchx_xmysxx(项目预算信息)】的数据库操作Service实现
* @createDate 2025-07-18 11:11:41
*/
@Service
@Slf4j
public class RchxXmysxxServiceImpl extends ServiceImpl<RchxXmysxxMapper, RchxXmysxx>
    implements RchxXmysxxService{

    @Autowired
    private RchxXmysxxMapper xmysxxMapper;

    @Override
    public List<RchxXmysxx> listByXmbh(String xmbh) {
        return xmysxxMapper.findByXmbh(xmbh);
    }

    @Override
    public List<RchxXmysxx> listByXmmcLike(String xmmc) {
        return xmysxxMapper.findByXmmcLike(xmmc);
    }

    @Override
    public List<RchxXmysxx> listByPdh(String pdh) {
        return xmysxxMapper.findByPdh(pdh);
    }

    @Override
    public List<RchxXmysxx> listByJbrLike(String jbr) {
        return xmysxxMapper.findByJbrLike(jbr);
    }

    @Override
    public List<RchxXmysxx> listByBxrLike(String bxr) {
        return xmysxxMapper.findByBxrLike(bxr);
    }

    @Override
    public List<RchxXmysxx> listByCjrgh(String cjrgh) {
        return xmysxxMapper.findByCjrgh(cjrgh);
    }

    @Override
    public List<RchxXmysxx> listByCjrxmLike(String cjrxm) {
        return xmysxxMapper.findByCjrxmLike(cjrxm);
    }

    @Override
    public List<RchxXmysxx> listByShzt(String shzt) {
        return xmysxxMapper.findByShzt(shzt);
    }

    @Override
    public List<RchxXmysxx> listByZcjeBetween(String startAmount, String endAmount) {
        return xmysxxMapper.findByZcjeBetween(startAmount, endAmount);
    }

    @Override
    public List<RchxXmysxx> listByZcsjBetween(String startDate, String endDate) {
        return xmysxxMapper.findByZcsjBetween(startDate, endDate);
    }

    @Override
    public List<RchxXmysxx> listByMultiConditions(XmysxxQueryDTO queryDTO) {
        // 使用QueryWrapper替代复杂动态SQL
        QueryWrapper<RchxXmysxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getXmbh())) {
            queryWrapper.like("xmbh", queryDTO.getXmbh());
        }
        if (StringUtils.hasText(queryDTO.getXmmc())) {
            queryWrapper.like("xmmc", queryDTO.getXmmc());
        }
        if (StringUtils.hasText(queryDTO.getPdh())) {
            queryWrapper.eq("pdh", queryDTO.getPdh());
        }
        if (StringUtils.hasText(queryDTO.getJbr())) {
            queryWrapper.like("jbr", queryDTO.getJbr());
        }
        if (StringUtils.hasText(queryDTO.getBxr())) {
            queryWrapper.like("bxr", queryDTO.getBxr());
        }
        if (StringUtils.hasText(queryDTO.getCjrgh())) {
            queryWrapper.eq("cjrgh", queryDTO.getCjrgh());
        }
        if (StringUtils.hasText(queryDTO.getCjrxm())) {
            queryWrapper.like("cjrxm", queryDTO.getCjrxm());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getStartAmount())) {
            queryWrapper.ge("zcje", queryDTO.getStartAmount());
        }
        if (StringUtils.hasText(queryDTO.getEndAmount())) {
            queryWrapper.le("zcje", queryDTO.getEndAmount());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("zcsj", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("zcsj", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("zcsj");

        return xmysxxMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<RchxXmysxx> pageByMultiConditions(XmysxxQueryDTO queryDTO) {
        // 使用MyBatis-Plus的分页功能替代复杂动态SQL
        Page<RchxXmysxx> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<RchxXmysxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getXmbh())) {
            queryWrapper.like("xmbh", queryDTO.getXmbh());
        }
        if (StringUtils.hasText(queryDTO.getXmmc())) {
            queryWrapper.like("xmmc", queryDTO.getXmmc());
        }
        if (StringUtils.hasText(queryDTO.getPdh())) {
            queryWrapper.eq("pdh", queryDTO.getPdh());
        }
        if (StringUtils.hasText(queryDTO.getJbr())) {
            queryWrapper.like("jbr", queryDTO.getJbr());
        }
        if (StringUtils.hasText(queryDTO.getBxr())) {
            queryWrapper.like("bxr", queryDTO.getBxr());
        }
        if (StringUtils.hasText(queryDTO.getCjrgh())) {
            queryWrapper.eq("cjrgh", queryDTO.getCjrgh());
        }
        if (StringUtils.hasText(queryDTO.getCjrxm())) {
            queryWrapper.like("cjrxm", queryDTO.getCjrxm());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getStartAmount())) {
            queryWrapper.ge("zcje", queryDTO.getStartAmount());
        }
        if (StringUtils.hasText(queryDTO.getEndAmount())) {
            queryWrapper.le("zcje", queryDTO.getEndAmount());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("zcsj", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("zcsj", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("zcsj");

        return xmysxxMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveXmysxx(RchxXmysxx xmysxx) {
        try {
            return save(xmysxx);
        } catch (Exception e) {
            log.error("新增项目预算信息失败: {}", e.getMessage());
            throw new RuntimeException("新增项目预算信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateXmysxx(RchxXmysxx xmysxx) {
        try {
            return updateById(xmysxx);
        } catch (Exception e) {
            log.error("更新项目预算信息失败: {}", e.getMessage());
            throw new RuntimeException("更新项目预算信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByXmbh(String xmbh) {
        try {
            return removeById(xmbh);
        } catch (Exception e) {
            log.error("删除项目预算信息失败: {}", e.getMessage());
            throw new RuntimeException("删除项目预算信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByXmbhs(List<String> xmbhs) {
        try {
            return removeByIds(xmbhs);
        } catch (Exception e) {
            log.error("批量删除项目预算信息失败: {}", e.getMessage());
            throw new RuntimeException("批量删除项目预算信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchXmysxx(List<RchxXmysxx> xmysxxList) {
        try {
            return saveBatch(xmysxxList);
        } catch (Exception e) {
            log.error("批量新增项目预算信息失败: {}", e.getMessage());
            throw new RuntimeException("批量新增项目预算信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchXmysxx(List<RchxXmysxx> xmysxxList) {
        try {
            return updateBatchById(xmysxxList);
        } catch (Exception e) {
            log.error("批量更新项目预算信息失败: {}", e.getMessage());
            throw new RuntimeException("批量更新项目预算信息失败: " + e.getMessage());
        }
    }
}
