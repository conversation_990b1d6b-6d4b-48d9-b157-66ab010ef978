package com.gl.gl_lg_java.dto;

import lombok.Data;
import java.util.List;

/**
 * 专利成果统计数据传输对象
 */
@Data
public class ZlcgStatisticsDTO {

    /**
     * 单位统计信息
     */
    @Data
    public static class UnitStatistics {
        private String dwmc;        // 单位名称
        private Long patentCount;   // 专利数量
    }

    /**
     * 专利类型统计信息
     */
    @Data
    public static class PatentTypeStatistics {
        private String zllx;        // 专利类型
        private Long patentCount;   // 专利数量
    }

    /**
     * 第一发明人统计信息
     */
    @Data
    public static class FirstInventorStatistics {
        private String dyfmr;       // 第一发明人姓名
        private Long patentCount;   // 专利数量
        private String dwmc;        // 所属单位
    }

    /**
     * 综合统计结果
     */
    @Data
    public static class ComprehensiveStatistics {
        // 单位统计 - 所有单位及其专利数量
        private List<UnitStatistics> unitStatistics;
        
        // 专利类型统计 - 所有专利类型及其专利数量
        private List<PatentTypeStatistics> patentTypeStatistics;
        
        // 第一发明人TOP10 - 专利数量最多的前10个第一发明人
        private List<FirstInventorStatistics> topFirstInventors;
        
        // 总体统计
        private Long totalPatents;      // 专利总数
        private Long totalUnits;        // 单位总数
        private Long totalPatentTypes;  // 专利类型总数
    }
}
