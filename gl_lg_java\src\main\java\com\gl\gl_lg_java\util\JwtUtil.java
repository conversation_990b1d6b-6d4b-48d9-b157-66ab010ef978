package com.gl.gl_lg_java.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;

/**
 * JWT工具类
 */
@Component
@Slf4j
public class JwtUtil {
    
    // JWT密钥 - 必须至少64字节（512位）用于HS512算法
    private static final String SECRET_KEY = "gl_lg_java_secret_key_for_jwt_token_generation_2024_this_key_must_be_at_least_64_bytes_long_for_hs512_algorithm_security_requirements";
    
    // Token过期时间：1天（24小时）
    private static final long EXPIRATION_TIME = 24 * 60 * 60 * 1000L; // 1天
    
    // Token前缀
    private static final String TOKEN_PREFIX = "Bearer ";
    
    // 密钥对象
    private final SecretKey key;
    
    public JwtUtil() {
        // 确保密钥长度足够
        if (SECRET_KEY.getBytes().length < 64) {
            throw new IllegalArgumentException("JWT密钥长度必须至少64字节用于HS512算法");
        }
        this.key = Keys.hmacShaKeyFor(SECRET_KEY.getBytes());
    }
    
    /**
     * 生成JWT Token
     */
    public String generateToken(String zgh, String qx) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + EXPIRATION_TIME);
        
        return Jwts.builder()
                .setSubject(zgh)                    // 主题（职工号）
                .claim("qx", qx)                    // 权限
                .setIssuedAt(now)                   // 签发时间
                .setExpiration(expiryDate)          // 过期时间
                .signWith(key, SignatureAlgorithm.HS512)  // 签名算法
                .compact();
    }
    
    /**
     * 从Token中获取职工号
     */
    public String getZghFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getSubject();
        } catch (Exception e) {
            log.error("从Token中获取职工号失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 从Token中获取权限
     */
    public String getQxFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.get("qx", String.class);
        } catch (Exception e) {
            log.error("从Token中获取权限失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证Token是否有效
     */
    public boolean validateToken(String token) {
        try {
            parseToken(token);
            return true;
        } catch (ExpiredJwtException e) {
            log.warn("Token已过期: {}", e.getMessage());
            return false;
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的Token: {}", e.getMessage());
            return false;
        } catch (MalformedJwtException e) {
            log.warn("Token格式错误: {}", e.getMessage());
            return false;
        } catch (SecurityException e) {
            log.warn("Token签名验证失败: {}", e.getMessage());
            return false;
        } catch (IllegalArgumentException e) {
            log.warn("Token参数错误: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取Token过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getExpiration();
        } catch (Exception e) {
            log.error("获取Token过期时间失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查Token是否即将过期（1小时内）
     */
    public boolean isTokenExpiringSoon(String token) {
        try {
            Date expirationDate = getExpirationDateFromToken(token);
            if (expirationDate == null) {
                return true;
            }
            
            Date now = new Date();
            long timeUntilExpiration = expirationDate.getTime() - now.getTime();
            
            // 如果1小时内过期，返回true
            return timeUntilExpiration < (60 * 60 * 1000L);
        } catch (Exception e) {
            log.error("检查Token过期时间失败: {}", e.getMessage());
            return true;
        }
    }
    
    /**
     * 解析Token
     */
    private Claims parseToken(String token) {
        // 移除Bearer前缀
        if (token.startsWith(TOKEN_PREFIX)) {
            token = token.substring(TOKEN_PREFIX.length());
        }
        
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }
    
    /**
     * 获取Token过期时间（毫秒）
     */
    public long getExpirationTime() {
        return EXPIRATION_TIME;
    }
}
