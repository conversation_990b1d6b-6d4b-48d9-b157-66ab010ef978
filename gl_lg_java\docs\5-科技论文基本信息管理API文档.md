# 科技论文基本信息管理API文档

## 基础信息
- **模块名称**: 科技论文基本信息管理
- **基础路径**: `/api/kjlwjbxx`
- **权限要求**: 需要JWT Token认证
- **数据格式**: JSON

## 统一响应格式

```json
{
    "code": 200,           // 状态码：200-成功，500-失败，401-未授权，403-权限不足
    "message": "操作成功",  // 响应消息
    "data": {}            // 响应数据（可能为null）
}
```

## 接口列表

### 1. 根据论文编号查询

**接口地址**: `GET /api/kjlwjbxx/{lwbh}`

**权限要求**: 教师+

**路径参数**:
- `lwbh` (string, 必填): 论文编号

**请求示例**:
```
GET /api/kjlwjbxx/LW2025001
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "lwbh": "LW2025001",
        "lwmc": "基于深度学习的图像识别算法研究",
        "dyzzbh": "T001",
        "dyzzxm": "张三",
        "txzzbh": "T001",
        "txzzxm": "张三",
        "kwmc": "计算机学报",
        "kwlx": "核心期刊",
        "fbfw": "国内",
        "fbrq": "2025-03-15",
        "shzt": "已审核",
        "dwmc": "计算机学院",
        "yxzs": "10",
        "qkh": "2025年第3期",
        "cjsj": "2025-07-18 10:00:00",
        "gxsj": "2025-07-18 10:00:00"
    }
}
```

### 2. 分页查询科技论文信息

**接口地址**: `POST /api/kjlwjbxx/page`

**权限要求**: 教师+

**请求体**:
```json
{
    "lwbh": "LW2025",               // 论文编号（可选）
    "lwmc": "深度学习",             // 论文名称（可选）
    "dyzzbh": "T001",               // 第一作者编号（可选）
    "dyzzxm": "张三",               // 第一作者姓名（可选）
    "txzzbh": "T001",               // 通讯作者编号（可选）
    "txzzxm": "张三",               // 通讯作者姓名（可选）
    "kwmc": "计算机学报",           // 刊物名称（可选）
    "kwlx": "核心期刊",             // 刊物类型（可选）
    "fbfw": "国内",                 // 发表范围（可选）
    "shzt": "已审核",               // 审核状态（可选）
    "dwmc": "计算机学院",           // 单位名称（可选）
    "startDate": "2025-01-01",      // 发表日期开始（可选）
    "endDate": "2025-12-31",        // 发表日期结束（可选）
    "pageNum": 1,                   // 页码（默认1）
    "pageSize": 10,                 // 页大小（默认10）
    "orderBy": "fbrq",              // 排序字段（默认fbrq）
    "orderDirection": "desc"        // 排序方向（默认desc）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "records": [
            {
                "lwbh": "LW2025001",
                "lwmc": "基于深度学习的图像识别算法研究",
                "dyzzbh": "T001",
                "dyzzxm": "张三",
                "txzzbh": "T001",
                "txzzxm": "张三",
                "kwmc": "计算机学报",
                "kwlx": "核心期刊",
                "fbfw": "国内",
                "fbrq": "2025-03-15",
                "shzt": "已审核",
                "dwmc": "计算机学院",
                "yxzs": "10",
                "qkh": "2025年第3期",
                "cjsj": "2025-07-18 10:00:00",
                "gxsj": "2025-07-18 10:00:00"
            }
        ],
        "total": 40,
        "size": 10,
        "current": 1,
        "pages": 4
    }
}
```

### 3. 多条件查询科技论文信息

**接口地址**: `POST /api/kjlwjbxx/list`

**权限要求**: 教师+

**请求体**: 同分页查询，但不返回分页信息

### 4. 根据论文名称模糊查询

**接口地址**: `GET /api/kjlwjbxx/name/{lwmc}`

**权限要求**: 学院管理员+

**路径参数**:
- `lwmc` (string, 必填): 论文名称关键字

### 5. 根据第一作者编号查询

**接口地址**: `GET /api/kjlwjbxx/author/{dyzzbh}`

**权限要求**: 教师+

**路径参数**:
- `dyzzbh` (string, 必填): 第一作者编号

### 6. 根据通讯作者编号查询

**接口地址**: `GET /api/kjlwjbxx/correspondent/{txzzbh}`

**权限要求**: 教师+

**路径参数**:
- `txzzbh` (string, 必填): 通讯作者编号

### 7. 根据刊物类型查询

**接口地址**: `GET /api/kjlwjbxx/type/{kwlx}`

**权限要求**: 学院管理员+

**路径参数**:
- `kwlx` (string, 必填): 刊物类型

### 8. 根据审核状态查询

**接口地址**: `GET /api/kjlwjbxx/audit/{shzt}`

**权限要求**: 评审+

**路径参数**:
- `shzt` (string, 必填): 审核状态

### 9. 新增科技论文信息

**接口地址**: `POST /api/kjlwjbxx`

**权限要求**: 学院管理员+

**请求体**:
```json
{
    "lwbh": "LW2025002",                        // 论文编号（必填）
    "lwmc": "机器学习在自然语言处理中的应用",   // 论文名称（必填）
    "dyzzbh": "T002",                           // 第一作者编号（必填）
    "dyzzxm": "李四",                           // 第一作者姓名（必填）
    "txzzbh": "T002",                           // 通讯作者编号（可选）
    "txzzxm": "李四",                           // 通讯作者姓名（可选）
    "kwmc": "软件学报",                         // 刊物名称（必填）
    "kwlx": "核心期刊",                         // 刊物类型（必填）
    "fbfw": "国内",                             // 发表范围（必填）
    "fbrq": "2025-04-20",                       // 发表日期（必填）
    "shzt": "待审核",                           // 审核状态（必填）
    "dwmc": "计算机学院",                       // 单位名称（可选）
    "yxzs": "8",                                // 页码字数（可选）
    "qkh": "2025年第4期",                       // 期刊号（可选）
    "lwzy": "本文研究了机器学习在自然语言处理中的应用方法"  // 论文摘要（可选）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "新增成功",
    "data": "新增成功"
}
```

### 10. 更新科技论文信息

**接口地址**: `PUT /api/kjlwjbxx`

**权限要求**: 学院管理员+（教师可修改自己的论文）

**请求体**: 同新增，但lwbh不可修改

### 11. 删除科技论文信息

**接口地址**: `DELETE /api/kjlwjbxx/{lwbh}`

**权限要求**: 系统管理员

**路径参数**:
- `lwbh` (string, 必填): 论文编号

### 12. 批量新增科技论文信息

**接口地址**: `POST /api/kjlwjbxx/batch`

**权限要求**: 学院管理员+

**请求体**:
```json
[
    {
        "lwbh": "LW2025003",
        "lwmc": "区块链技术在数据安全中的应用研究",
        "dyzzbh": "T003",
        "dyzzxm": "王五",
        "txzzbh": "T003",
        "txzzxm": "王五",
        "kwmc": "信息安全学报",
        "kwlx": "核心期刊",
        "fbfw": "国内",
        "fbrq": "2025-05-10",
        "shzt": "待审核",
        "dwmc": "计算机学院",
        "yxzs": "12",
        "qkh": "2025年第5期"
    }
]
```

### 13. 批量更新科技论文信息

**接口地址**: `PUT /api/kjlwjbxx/batch`

**权限要求**: 学院管理员+

### 14. 批量删除科技论文信息

**接口地址**: `DELETE /api/kjlwjbxx/batch`

**权限要求**: 系统管理员

**请求体**:
```json
["LW2025001", "LW2025002", "LW2025003"]
```

## 刊物类型说明

### 国内期刊
- **核心期刊**: 中文核心期刊（北大核心）
- **CSCD期刊**: 中国科学引文数据库期刊
- **CSSCI期刊**: 中文社会科学引文索引期刊
- **普通期刊**: 其他正规学术期刊

### 国际期刊
- **SCI期刊**: Science Citation Index期刊
- **EI期刊**: Engineering Index期刊
- **SSCI期刊**: Social Sciences Citation Index期刊
- **A&HCI期刊**: Arts & Humanities Citation Index期刊

### 会议论文
- **国际会议**: 国际学术会议论文
- **国内会议**: 国内学术会议论文
- **顶级会议**: CCF A类会议论文

## 发表范围说明

- **国内**: 在国内期刊或会议发表
- **国际**: 在国际期刊或会议发表
- **港澳台**: 在港澳台地区期刊发表

## 审核状态说明

- **待审核**: 等待内部审核
- **审核中**: 正在进行内部审核
- **已审核**: 内部审核通过
- **审核不通过**: 内部审核未通过

## 影响因子说明

- **高影响因子**: IF > 5.0
- **中等影响因子**: 1.0 < IF <= 5.0
- **低影响因子**: IF <= 1.0
- **无影响因子**: 未被SCI收录

## 权限说明

### 数据权限
- **教师**: 只能查看和修改自己的论文（第一作者或通讯作者）
- **评审**: 可以查看论文信息，进行论文评审
- **学院管理员**: 可以管理本学院的论文
- **系统管理员**: 可以管理所有论文

### 操作权限
- **查询**: 教师+
- **新增**: 学院管理员+
- **修改**: 学院管理员+（教师可修改自己的论文）
- **删除**: 系统管理员

## 错误码说明

- **200**: 操作成功
- **401**: 未授权（Token无效或过期）
- **403**: 权限不足
- **500**: 服务器内部错误

## 注意事项

1. 所有接口都需要在请求头中携带JWT Token
2. 教师权限的用户只能操作自己的论文（第一作者或通讯作者）
3. 论文编号必须唯一
4. 批量操作支持事务，失败时会回滚
5. 分页查询默认按发表日期倒序排列
6. 日期格式统一使用 yyyy-MM-dd
7. 刊物类型和发表范围有固定的枚举值
8. 论文需要提供相关发表证明材料
9. 支持多作者论文，但权限控制基于第一作者和通讯作者
