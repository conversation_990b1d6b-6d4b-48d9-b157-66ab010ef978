package com.gl.gl_lg_java.dto;

import lombok.Data;

/**
 * 教职工基本信息查询DTO
 */
@Data
public class JzgjbxxQueryDTO {
    
    /**
     * 职工号
     */
    private String zgh;
    
    /**
     * 姓名
     */
    private String xm;
    
    /**
     * 身份证件号
     */
    private String sfzjh;
    
    /**
     * 性别
     */
    private String xb;
    
    /**
     * 部门
     */
    private String bm;
    
    /**
     * 职称
     */
    private String zc;
    
    /**
     * 权限
     */
    private String qx;
    
    /**
     * 电话
     */
    private String dh;
    
    /**
     * 电子信箱
     */
    private String dzxx;
    
    /**
     * 学历
     */
    private String zgxl;
    
    /**
     * 学位
     */
    private String zgxw;
    
    /**
     * 职务级别
     */
    private String zwjb;
    
    /**
     * 政治面貌
     */
    private String zzmm;
    
    /**
     * 民族
     */
    private String mz;
    
    /**
     * 籍贯
     */
    private String jg;
    
    /**
     * 国家地区
     */
    private String gjdq;

    /**
     * 当前状态
     */
    private String dqzt;

    /**
     * 年龄段（35岁以下、35-45岁、45岁以上）
     */
    private String ageRange;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String orderBy = "zgh";
    
    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "asc";
}
