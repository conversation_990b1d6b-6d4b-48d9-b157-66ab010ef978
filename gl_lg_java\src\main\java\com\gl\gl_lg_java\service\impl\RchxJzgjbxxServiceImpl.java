package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.dto.JzgjbxxQueryDTO;
import com.gl.gl_lg_java.mapper.RchxJzgjbxxMapper;
import com.gl.gl_lg_java.service.RchxJzgjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_rchx_jzgjbxx(教职工基本信息)】的数据库操作Service实现
* @createDate 2025-07-18 11:11:41
*/
@Service
@Slf4j
public class RchxJzgjbxxServiceImpl extends ServiceImpl<RchxJzgjbxxMapper, RchxJzgjbxx>
    implements RchxJzgjbxxService{

    @Autowired
    private RchxJzgjbxxMapper jzgjbxxMapper;

    @Override
    public RchxJzgjbxx getByZgh(String zgh) {
        return jzgjbxxMapper.findByZgh(zgh);
    }

    @Override
    public RchxJzgjbxx getByAccount(String account) {
        return jzgjbxxMapper.findByAccount(account);
    }

    @Override
    public List<RchxJzgjbxx> listByXmLike(String xm) {
        return jzgjbxxMapper.findByXmLike(xm);
    }

    @Override
    public List<RchxJzgjbxx> listByBm(String bm) {
        return jzgjbxxMapper.findByBm(bm);
    }

    @Override
    public List<RchxJzgjbxx> listByBmLike(String bm) {
        return jzgjbxxMapper.findByBmLike(bm);
    }

    @Override
    public List<RchxJzgjbxx> listByZc(String zc) {
        return jzgjbxxMapper.findByZc(zc);
    }

    @Override
    public List<RchxJzgjbxx> listByQx(String qx) {
        return jzgjbxxMapper.findByQx(qx);
    }

    @Override
    public List<RchxJzgjbxx> listByXb(String xb) {
        return jzgjbxxMapper.findByXb(xb);
    }

    @Override
    public List<RchxJzgjbxx> listByZgxl(String zgxl) {
        return jzgjbxxMapper.findByZgxl(zgxl);
    }

    @Override
    public List<RchxJzgjbxx> listByZgxw(String zgxw) {
        return jzgjbxxMapper.findByZgxw(zgxw);
    }

    @Override
    public List<RchxJzgjbxx> listByMultiConditions(JzgjbxxQueryDTO queryDTO) {
        // 使用QueryWrapper替代复杂动态SQL
        QueryWrapper<RchxJzgjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getZgh())) {
            queryWrapper.like("zgh", queryDTO.getZgh());
        }
        if (StringUtils.hasText(queryDTO.getXm())) {
            queryWrapper.like("xm", queryDTO.getXm());
        }
        if (StringUtils.hasText(queryDTO.getSfzjh())) {
            queryWrapper.like("sfzjh", queryDTO.getSfzjh());
        }
        if (StringUtils.hasText(queryDTO.getXb())) {
            queryWrapper.eq("xb", queryDTO.getXb());
        }
        if (StringUtils.hasText(queryDTO.getBm())) {
            queryWrapper.like("bm", queryDTO.getBm());
        }
        if (StringUtils.hasText(queryDTO.getZc())) {
            queryWrapper.like("zc", queryDTO.getZc());
        }
        if (StringUtils.hasText(queryDTO.getQx())) {
            queryWrapper.eq("qx", queryDTO.getQx());
        }
        if (StringUtils.hasText(queryDTO.getDh())) {
            queryWrapper.like("dh", queryDTO.getDh());
        }
        if (StringUtils.hasText(queryDTO.getDzxx())) {
            queryWrapper.like("dzxx", queryDTO.getDzxx());
        }
        if (StringUtils.hasText(queryDTO.getZgxl())) {
            queryWrapper.eq("zgxl", queryDTO.getZgxl());
        }
        if (StringUtils.hasText(queryDTO.getZgxw())) {
            queryWrapper.eq("zgxw", queryDTO.getZgxw());
        }
        if (StringUtils.hasText(queryDTO.getZwjb())) {
            queryWrapper.eq("zwjb", queryDTO.getZwjb());
        }
        if (StringUtils.hasText(queryDTO.getZzmm())) {
            queryWrapper.eq("zzmm", queryDTO.getZzmm());
        }
        if (StringUtils.hasText(queryDTO.getMz())) {
            queryWrapper.eq("mz", queryDTO.getMz());
        }
        if (StringUtils.hasText(queryDTO.getJg())) {
            queryWrapper.like("jg", queryDTO.getJg());
        }
        if (StringUtils.hasText(queryDTO.getGjdq())) {
            queryWrapper.eq("gjdq", queryDTO.getGjdq());
        }
        if (StringUtils.hasText(queryDTO.getDqzt())) {
            queryWrapper.eq("dqzt", queryDTO.getDqzt());
        }

        // 年龄段筛选
        if (StringUtils.hasText(queryDTO.getAgeRange())) {
            String ageRange = queryDTO.getAgeRange();
            if ("35岁以下".equals(ageRange)) {
                queryWrapper.apply("TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) < 35");
            } else if ("35-45岁".equals(ageRange)) {
                queryWrapper.apply("TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) BETWEEN 35 AND 45");
            } else if ("45岁以上".equals(ageRange)) {
                queryWrapper.apply("TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) > 45");
            }
        }

        // 排序
        queryWrapper.orderByAsc("zgh");

        return jzgjbxxMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<RchxJzgjbxx> pageByMultiConditions(JzgjbxxQueryDTO queryDTO) {
        // 使用MyBatis-Plus的分页功能替代复杂动态SQL
        Page<RchxJzgjbxx> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<RchxJzgjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getZgh())) {
            queryWrapper.like("zgh", queryDTO.getZgh());
        }
        if (StringUtils.hasText(queryDTO.getXm())) {
            queryWrapper.like("xm", queryDTO.getXm());
        }
        if (StringUtils.hasText(queryDTO.getSfzjh())) {
            queryWrapper.like("sfzjh", queryDTO.getSfzjh());
        }
        if (StringUtils.hasText(queryDTO.getXb())) {
            queryWrapper.eq("xb", queryDTO.getXb());
        }
        if (StringUtils.hasText(queryDTO.getBm())) {
            queryWrapper.like("bm", queryDTO.getBm());
        }
        if (StringUtils.hasText(queryDTO.getZc())) {
            queryWrapper.like("zc", queryDTO.getZc());
        }
        if (StringUtils.hasText(queryDTO.getQx())) {
            queryWrapper.eq("qx", queryDTO.getQx());
        }
        if (StringUtils.hasText(queryDTO.getDh())) {
            queryWrapper.like("dh", queryDTO.getDh());
        }
        if (StringUtils.hasText(queryDTO.getDzxx())) {
            queryWrapper.like("dzxx", queryDTO.getDzxx());
        }
        if (StringUtils.hasText(queryDTO.getZgxl())) {
            queryWrapper.eq("zgxl", queryDTO.getZgxl());
        }
        if (StringUtils.hasText(queryDTO.getZgxw())) {
            queryWrapper.eq("zgxw", queryDTO.getZgxw());
        }
        if (StringUtils.hasText(queryDTO.getZwjb())) {
            queryWrapper.eq("zwjb", queryDTO.getZwjb());
        }
        if (StringUtils.hasText(queryDTO.getZzmm())) {
            queryWrapper.eq("zzmm", queryDTO.getZzmm());
        }
        if (StringUtils.hasText(queryDTO.getMz())) {
            queryWrapper.eq("mz", queryDTO.getMz());
        }
        if (StringUtils.hasText(queryDTO.getJg())) {
            queryWrapper.like("jg", queryDTO.getJg());
        }
        if (StringUtils.hasText(queryDTO.getGjdq())) {
            queryWrapper.eq("gjdq", queryDTO.getGjdq());
        }
        if (StringUtils.hasText(queryDTO.getDqzt())) {
            queryWrapper.eq("dqzt", queryDTO.getDqzt());
        }

        // 年龄段筛选
        if (StringUtils.hasText(queryDTO.getAgeRange())) {
            String ageRange = queryDTO.getAgeRange();
            if ("35岁以下".equals(ageRange)) {
                queryWrapper.apply("TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) < 35");
            } else if ("35-45岁".equals(ageRange)) {
                queryWrapper.apply("TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) BETWEEN 35 AND 45");
            } else if ("45岁以上".equals(ageRange)) {
                queryWrapper.apply("TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) > 45");
            }
        }

        // 排序
        queryWrapper.orderByAsc("zgh");

        return jzgjbxxMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveJzgjbxx(RchxJzgjbxx jzgjbxx) {
        try {
            return save(jzgjbxx);
        } catch (Exception e) {
            log.error("新增教职工信息失败: {}", e.getMessage());
            throw new RuntimeException("新增教职工信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByZgh(RchxJzgjbxx jzgjbxx) {
        try {
            return updateById(jzgjbxx);
        } catch (Exception e) {
            log.error("更新教职工信息失败: {}", e.getMessage());
            throw new RuntimeException("更新教职工信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByZgh(String zgh) {
        try {
            return removeById(zgh);
        } catch (Exception e) {
            log.error("删除教职工信息失败: {}", e.getMessage());
            throw new RuntimeException("删除教职工信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByZghs(List<String> zghs) {
        try {
            return removeByIds(zghs);
        } catch (Exception e) {
            log.error("批量删除教职工信息失败: {}", e.getMessage());
            throw new RuntimeException("批量删除教职工信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchJzgjbxx(List<RchxJzgjbxx> jzgjbxxList) {
        try {
            return saveBatch(jzgjbxxList);
        } catch (Exception e) {
            log.error("批量新增教职工信息失败: {}", e.getMessage());
            throw new RuntimeException("批量新增教职工信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchJzgjbxx(List<RchxJzgjbxx> jzgjbxxList) {
        try {
            return updateBatchById(jzgjbxxList);
        } catch (Exception e) {
            log.error("批量更新教职工信息失败: {}", e.getMessage());
            throw new RuntimeException("批量更新教职工信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getDepartmentOptions() {
        try {
            log.info("快速获取部门选项列表（不统计数量）");
            List<String> departments = jzgjbxxMapper.findDistinctDepartments();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String department : departments) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", department);
                option.put("label", department);
                options.add(option);
            }

            log.info("部门选项列表快速获取成功，共{}个部门", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取部门选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取部门选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getDqztOptions() {
        try {
            log.info("快速获取当前状态选项列表（不统计数量）");
            List<String> dqztList = jzgjbxxMapper.findDistinctDqzt();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String dqzt : dqztList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", dqzt);
                option.put("label", dqzt);
                options.add(option);
            }

            log.info("当前状态选项列表快速获取成功，共{}个状态", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取当前状态选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取当前状态选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getStatsByZc() {
        try {
            log.info("查询职称统计（包含具体教师信息）");

            // 获取统计数据
            List<Map<String, Object>> stats = jzgjbxxMapper.getStatsByZc();
            Map<String, Object> statistics = new HashMap<>();
            for (Map<String, Object> stat : stats) {
                statistics.put((String) stat.get("fieldValue"), stat.get("count"));
            }

            // 获取所有教师信息并按职称分组
            List<RchxJzgjbxx> allTeachers = list();

            // 按职称分组教师信息
            Map<String, List<RchxJzgjbxx>> groupedTeachers = new HashMap<>();

            for (RchxJzgjbxx teacher : allTeachers) {
                String zc = teacher.getZc();
                if (zc == null || zc.trim().isEmpty()) {
                    zc = "NULL或空";
                }

                if (!groupedTeachers.containsKey(zc)) {
                    groupedTeachers.put(zc, new ArrayList<>());
                }
                groupedTeachers.get(zc).add(teacher);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("statistics", statistics);  // 统计数据
            result.put("details", groupedTeachers);  // 具体教师信息

            log.info("职称统计结果: 统计数据={}, 详细信息包含{}个职称", statistics, groupedTeachers.size());
            return result;
        } catch (Exception e) {
            log.error("查询职称统计失败: {}", e.getMessage());
            throw new RuntimeException("查询职称统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getZcOptions() {
        try {
            log.info("快速获取职称选项列表（不统计数量）");
            List<String> zcList = jzgjbxxMapper.findDistinctZc();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String zc : zcList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", zc);
                option.put("label", zc);
                options.add(option);
            }

            log.info("职称选项列表快速获取成功，共{}个职称", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取职称选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取职称选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByZcdj() {
        try {
            log.info("查询职称等级统计（使用数据库聚合查询）");
            Map<String, Integer> zcdjCounts = convertToIntegerMap(jzgjbxxMapper.getStatsByZcdj());
            log.info("职称等级统计结果: {}", zcdjCounts);
            return zcdjCounts;
        } catch (Exception e) {
            log.error("查询职称等级统计失败: {}", e.getMessage());
            throw new RuntimeException("查询职称等级统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getStatsByZcdjWithTeachers() {
        try {
            log.info("查询职称等级统计（包含具体教师信息）");

            // 获取统计数据
            List<Map<String, Object>> stats = jzgjbxxMapper.getStatsByZcdj();
            Map<String, Object> statistics = new HashMap<>();
            for (Map<String, Object> stat : stats) {
                statistics.put((String) stat.get("fieldValue"), stat.get("count"));
            }

            // 获取所有教师信息并按职称等级分组
            List<RchxJzgjbxx> allTeachers = list();

            // 按职称等级分组教师信息
            Map<String, List<RchxJzgjbxx>> groupedTeachers = new HashMap<>();

            for (RchxJzgjbxx teacher : allTeachers) {
                String zcdj = teacher.getZcdj();
                if (zcdj == null || zcdj.trim().isEmpty()) {
                    zcdj = "NULL或空";
                }

                groupedTeachers.computeIfAbsent(zcdj, k -> new ArrayList<>()).add(teacher);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("statistics", statistics);
            result.put("teachersByZcdj", groupedTeachers);

            log.info("职称等级统计（包含教师信息）查询成功，统计数据: {}", statistics);
            return result;
        } catch (Exception e) {
            log.error("查询职称等级统计（包含教师信息）失败: {}", e.getMessage());
            throw new RuntimeException("查询职称等级统计（包含教师信息）失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByZcCount() {
        try {
            log.info("查询职称统计（仅统计数据，使用数据库聚合查询）");
            Map<String, Integer> zcCounts = convertToIntegerMap(jzgjbxxMapper.getStatsByZc());
            log.info("职称统计结果: {}", zcCounts);
            return zcCounts;
        } catch (Exception e) {
            log.error("查询职称统计失败: {}", e.getMessage());
            throw new RuntimeException("查询职称统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByAge() {
        try {
            log.info("查询年龄段统计（基于出生日期计算）");

            // 获取所有有出生日期的教师
            List<RchxJzgjbxx> teachers = jzgjbxxMapper.selectList(
                new QueryWrapper<RchxJzgjbxx>()
                    .isNotNull("csrq")
                    .ne("csrq", "")
            );

            Map<String, Integer> ageStats = new LinkedHashMap<>();
            ageStats.put("35岁以下", 0);
            ageStats.put("35-45岁", 0);
            ageStats.put("45岁以上", 0);
            ageStats.put("年龄未知", 0);

            int currentYear = java.time.LocalDate.now().getYear();

            for (RchxJzgjbxx teacher : teachers) {
                String csrq = teacher.getCsrq();
                if (csrq == null || csrq.trim().isEmpty()) {
                    ageStats.put("年龄未知", ageStats.get("年龄未知") + 1);
                    continue;
                }

                try {
                    // 解析出生日期，支持多种格式
                    int birthYear = parseBirthYear(csrq);
                    int age = currentYear - birthYear;

                    if (age < 35) {
                        ageStats.put("35岁以下", ageStats.get("35岁以下") + 1);
                    } else if (age <= 45) {
                        ageStats.put("35-45岁", ageStats.get("35-45岁") + 1);
                    } else {
                        ageStats.put("45岁以上", ageStats.get("45岁以上") + 1);
                    }
                } catch (Exception e) {
                    log.warn("解析出生日期失败: {}, 教师: {}", csrq, teacher.getXm());
                    ageStats.put("年龄未知", ageStats.get("年龄未知") + 1);
                }
            }

            log.info("年龄段统计结果: {}", ageStats);
            return ageStats;
        } catch (Exception e) {
            log.error("查询年龄段统计失败: {}", e.getMessage());
            throw new RuntimeException("查询年龄段统计失败: " + e.getMessage());
        }
    }

    /**
     * 解析出生年份，支持多种日期格式
     */
    private int parseBirthYear(String birthDate) {
        if (birthDate == null || birthDate.trim().isEmpty()) {
            throw new IllegalArgumentException("出生日期为空");
        }

        birthDate = birthDate.trim();

        // 尝试不同的日期格式
        String[] patterns = {
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "yyyy.MM.dd",
            "yyyy-M-d",
            "yyyy/M/d",
            "yyyy.M.d"
        };

        for (String pattern : patterns) {
            try {
                java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern(pattern);
                java.time.LocalDate date = java.time.LocalDate.parse(birthDate, formatter);
                return date.getYear();
            } catch (Exception ignored) {
                // 继续尝试下一个格式
            }
        }

        // 如果是纯年份格式（4位数字）
        if (birthDate.matches("\\d{4}")) {
            return Integer.parseInt(birthDate);
        }

        // 如果包含年份，尝试提取前4位数字
        if (birthDate.length() >= 4 && birthDate.substring(0, 4).matches("\\d{4}")) {
            return Integer.parseInt(birthDate.substring(0, 4));
        }

        throw new IllegalArgumentException("无法解析出生日期格式: " + birthDate);
    }

    @Override
    public Map<String, Integer> getStatsByZgxlcc() {
        try {
            log.info("查询最高学历层次统计（使用数据库聚合查询）");
            Map<String, Integer> zgxlccCounts = convertToIntegerMap(jzgjbxxMapper.getStatsByZgxlcc());
            log.info("最高学历层次统计结果: {}", zgxlccCounts);
            return zgxlccCounts;
        } catch (Exception e) {
            log.error("查询最高学历层次统计失败: {}", e.getMessage());
            throw new RuntimeException("查询最高学历层次统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getZcdjOptions() {
        try {
            log.info("快速获取职称等级选项列表（不统计数量）");
            List<String> zcdjList = jzgjbxxMapper.findDistinctZcdj();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String zcdj : zcdjList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", zcdj);
                option.put("label", zcdj);
                options.add(option);
            }

            log.info("职称等级选项列表快速获取成功，共{}个等级", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取职称等级选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取职称等级选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getZgxlccOptions() {
        try {
            log.info("快速获取最高学历层次选项列表（不统计数量）");
            List<String> zgxlccList = jzgjbxxMapper.findDistinctZgxlcc();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String zgxlcc : zgxlccList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", zgxlcc);
                option.put("label", zgxlcc);
                options.add(option);
            }

            log.info("最高学历层次选项列表快速获取成功，共{}个层次", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取最高学历层次选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取最高学历层次选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<RchxJzgjbxx> getPageByAgeRange(String ageRange, Integer pageNum, Integer pageSize) {
        try {
            log.info("按年龄段分页查询教师信息: ageRange={}, pageNum={}, pageSize={}", ageRange, pageNum, pageSize);

            // 设置默认值
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 20;
            }

            // 创建分页对象
            Page<RchxJzgjbxx> page = new Page<>(pageNum, pageSize);

            // 执行分页查询
            IPage<RchxJzgjbxx> result = jzgjbxxMapper.findPageByAgeRange(page, ageRange);

            log.info("年龄段分页查询完成: 年龄段={}, 总记录数={}, 当前页={}, 每页大小={}",
                    ageRange != null ? ageRange : "全部", result.getTotal(), result.getCurrent(), result.getSize());

            return result;
        } catch (Exception e) {
            log.error("按年龄段分页查询教师信息失败: {}", e.getMessage());
            throw new RuntimeException("按年龄段分页查询教师信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getAgeRangeOptions() {
        try {
            log.info("获取年龄段选项列表");

            // 固定的年龄段选项
            List<Map<String, Object>> options = new ArrayList<>();

            Map<String, Object> option1 = new HashMap<>();
            option1.put("value", "35岁以下");
            option1.put("label", "35岁以下");
            options.add(option1);

            Map<String, Object> option2 = new HashMap<>();
            option2.put("value", "35-45岁");
            option2.put("label", "35-45岁");
            options.add(option2);

            Map<String, Object> option3 = new HashMap<>();
            option3.put("value", "45岁以上");
            option3.put("label", "45岁以上");
            options.add(option3);

            log.info("年龄段选项列表获取成功，共{}个年龄段", options.size());
            return options;
        } catch (Exception e) {
            log.error("获取年龄段选项列表失败: {}", e.getMessage());
            throw new RuntimeException("获取年龄段选项列表失败: " + e.getMessage());
        }
    }



    @Override
    public Map<String, Object> getTotalCount() {
        try {
            log.info("开始获取教职工总数统计");

            // 获取总数
            long totalCount = this.count();

            // 按性别统计
            QueryWrapper<RchxJzgjbxx> maleWrapper = new QueryWrapper<>();
            maleWrapper.eq("xb", "男");
            long maleCount = this.count(maleWrapper);

            QueryWrapper<RchxJzgjbxx> femaleWrapper = new QueryWrapper<>();
            femaleWrapper.eq("xb", "女");
            long femaleCount = this.count(femaleWrapper);

            // 按当前状态统计
            QueryWrapper<RchxJzgjbxx> activeWrapper = new QueryWrapper<>();
            activeWrapper.eq("dqzt", "在岗");
            long activeCount = this.count(activeWrapper);

            QueryWrapper<RchxJzgjbxx> retiredWrapper = new QueryWrapper<>();
            retiredWrapper.eq("dqzt", "退休");
            long retiredCount = this.count(retiredWrapper);

            // 按权限统计
            QueryWrapper<RchxJzgjbxx> teacherWrapper = new QueryWrapper<>();
            teacherWrapper.eq("qx", "教师");
            long teacherCount = this.count(teacherWrapper);

            QueryWrapper<RchxJzgjbxx> adminWrapper = new QueryWrapper<>();
            adminWrapper.eq("qx", "学院管理员");
            long adminCount = this.count(adminWrapper);

            QueryWrapper<RchxJzgjbxx> sysAdminWrapper = new QueryWrapper<>();
            sysAdminWrapper.eq("qx", "系统管理员");
            long sysAdminCount = this.count(sysAdminWrapper);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("timestamp", System.currentTimeMillis());

            // 性别统计
            Map<String, Long> genderStats = new HashMap<>();
            genderStats.put("male", maleCount);
            genderStats.put("female", femaleCount);
            genderStats.put("other", totalCount - maleCount - femaleCount);
            result.put("genderStats", genderStats);

            // 状态统计
            Map<String, Long> statusStats = new HashMap<>();
            statusStats.put("active", activeCount);
            statusStats.put("retired", retiredCount);
            statusStats.put("other", totalCount - activeCount - retiredCount);
            result.put("statusStats", statusStats);

            // 权限统计
            Map<String, Long> permissionStats = new HashMap<>();
            permissionStats.put("teacher", teacherCount);
            permissionStats.put("admin", adminCount);
            permissionStats.put("sysAdmin", sysAdminCount);
            permissionStats.put("other", totalCount - teacherCount - adminCount - sysAdminCount);
            result.put("permissionStats", permissionStats);

            log.info("教职工总数统计获取成功，总数: {}", totalCount);
            return result;
        } catch (Exception e) {
            log.error("获取教职工总数统计失败: {}", e.getMessage());
            throw new RuntimeException("获取教职工总数统计失败: " + e.getMessage());
        }
    }

    /**
     * 转换统计结果为Integer类型的Map
     */
    private Map<String, Integer> convertToIntegerMap(List<Map<String, Object>> statsList) {
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> stats : statsList) {
            String fieldValue = (String) stats.get("fieldValue");
            Object countObj = stats.get("count");
            Integer count = 0;
            if (countObj instanceof Number) {
                count = ((Number) countObj).intValue();
            }
            result.put(fieldValue, count);
        }
        return result;
    }
}
