package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 获奖成果基本信息
 * @TableName t_rchx_hjcgjbxx
 */
@TableName(value ="t_rchx_hjcgjbxx")
@Data
public class RchxHjcgjbxx implements Serializable {
    /**
     * 获奖成果编号
     */
    @TableId(value = "hjcgbh", type = IdType.INPUT)
    private String hjcgbh;

    /**
     * 获奖成果名称
     */
    @TableField(value = "hjcgmc")
    private String hjcgmc;

    /**
     * 奖励名称
     */
    @TableField(value = "jlmc")
    private String jlmc;

    /**
     * 奖励简介
     */
    @TableField(value = "jljj")
    private String jljj;

    /**
     * 所属项目编号
     */
    @TableField(value = "ssxmbh")
    private String ssxmbh;

    /**
     * 项目来源码
     */
    @TableField(value = "xmlym")
    private String xmlym;

    /**
     * 项目来源
     */
    @TableField(value = "xmly")
    private String xmly;

    /**
     * 单位号
     */
    @TableField(value = "dwh")
    private String dwh;

    /**
     * 单位名称
     */
    @TableField(value = "dwmc")
    private String dwmc;

    /**
     * 学科门类码
     */
    @TableField(value = "xkmlm")
    private String xkmlm;

    /**
     * 学科门类
     */
    @TableField(value = "xkml")
    private String xkml;

    /**
     * 学科领域码
     */
    @TableField(value = "xklym")
    private String xklym;

    /**
     * 成果获奖类别码
     */
    @TableField(value = "cghjlbm")
    private String cghjlbm;

    /**
     * 科技奖类别码
     */
    @TableField(value = "kjjlbm")
    private String kjjlbm;

    /**
     * 奖励等级码
     */
    @TableField(value = "jldjm")
    private String jldjm;

    /**
     * 奖励等级
     */
    @TableField(value = "jldj")
    private String jldj;

    /**
     * 获奖级别码
     */
    @TableField(value = "hjjbm")
    private String hjjbm;

    /**
     * 获奖级别
     */
    @TableField(value = "hjjb")
    private String hjjb;

    /**
     * 获奖日期
     */
    @TableField(value = "hjrq")
    private String hjrq;

    /**
     * 成果形式码
     */
    @TableField(value = "cgxsm")
    private String cgxsm;

    /**
     * 成果形式
     */
    @TableField(value = "cgxs")
    private String cgxs;

    /**
     * 证书号
     */
    @TableField(value = "zsh")
    private String zsh;

    /**
     * 总作者数
     */
    @TableField(value = "zzzs")
    private String zzzs;

    /**
     * 是否合作
     */
    @TableField(value = "sfhz")
    private String sfhz;

    /**
     * 单位排名码
     */
    @TableField(value = "dwpmm")
    private String dwpmm;

    /**
     * 单位排名
     */
    @TableField(value = "dwpm")
    private String dwpm;

    /**
     * 颁奖单位
     */
    @TableField(value = "bjdw")
    private String bjdw;

    /**
     * 教研室编号
     */
    @TableField(value = "jysbh")
    private String jysbh;

    /**
     * 教研室名称
     */
    @TableField(value = "jysmc")
    private String jysmc;

    /**
     * 一级学科码
     */
    @TableField(value = "yjxkm")
    private String yjxkm;

    /**
     * 一级学科
     */
    @TableField(value = "yjxk")
    private String yjxk;

    /**
     * 奖励批准号
     */
    @TableField(value = "jlpzh")
    private String jlpzh;

    /**
     * 审核状态码
     */
    @TableField(value = "shztm")
    private String shztm;

    /**
     * 审核状态
     */
    @TableField(value = "shzt")
    private String shzt;

    /**
     * 第一完成人姓名
     */
    @TableField(value = "dywcrxm")
    private String dywcrxm;

    /**
     * 第一完成人职称码
     */
    @TableField(value = "dywcrzcm")
    private String dywcrzcm;

    /**
     * 第一完成人职称
     */
    @TableField(value = "dywcrzc")
    private String dywcrzc;

    /**
     * 第一完成人性别码
     */
    @TableField(value = "dywcrxbm")
    private String dywcrxbm;

    /**
     * 第一完成人性别
     */
    @TableField(value = "dywcrxb")
    private String dywcrxb;

    /**
     * 第一完成人学历码
     */
    @TableField(value = "dywcrxlm")
    private String dywcrxlm;

    /**
     * 第一完成人学历
     */
    @TableField(value = "dywcrxl")
    private String dywcrxl;

    /**
     * 第一完成人学位码
     */
    @TableField(value = "dywcrxwm")
    private String dywcrxwm;

    /**
     * 第一完成人学位
     */
    @TableField(value = "dywcrxw")
    private String dywcrxw;

    /**
     * 第一完成人职工号
     */
    @TableField(value = "dywcrzgh")
    private String dywcrzgh;

    /**
     * 操作时间
     */
    @TableField(value = "czsj")
    private String czsj;

    /**
     * 第一完成人编号
     */
    @TableField(value = "dywcrbh")
    private String dywcrbh;

    /**
     * 第一完成人类型码
     */
    @TableField(value = "dywcrlxm")
    private String dywcrlxm;

    /**
     * 第一完成人类型
     */
    @TableField(value = "dywcrlx")
    private String dywcrlx;

    /**
     * 完成单位
     */
    @TableField(value = "wcdw")
    private String wcdw;

    /**
     * 附件
     */
    @TableField(value = "fj")
    private String fj;

    /**
     * 主键码
     */
    @TableField(value = "id")
    private String id;

    /**
     * 
     */
    @TableField(value = "tstamp")
    private String tstamp;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RchxHjcgjbxx other = (RchxHjcgjbxx) that;
        return (this.getHjcgbh() == null ? other.getHjcgbh() == null : this.getHjcgbh().equals(other.getHjcgbh()))
            && (this.getHjcgmc() == null ? other.getHjcgmc() == null : this.getHjcgmc().equals(other.getHjcgmc()))
            && (this.getJlmc() == null ? other.getJlmc() == null : this.getJlmc().equals(other.getJlmc()))
            && (this.getJljj() == null ? other.getJljj() == null : this.getJljj().equals(other.getJljj()))
            && (this.getSsxmbh() == null ? other.getSsxmbh() == null : this.getSsxmbh().equals(other.getSsxmbh()))
            && (this.getXmlym() == null ? other.getXmlym() == null : this.getXmlym().equals(other.getXmlym()))
            && (this.getXmly() == null ? other.getXmly() == null : this.getXmly().equals(other.getXmly()))
            && (this.getDwh() == null ? other.getDwh() == null : this.getDwh().equals(other.getDwh()))
            && (this.getDwmc() == null ? other.getDwmc() == null : this.getDwmc().equals(other.getDwmc()))
            && (this.getXkmlm() == null ? other.getXkmlm() == null : this.getXkmlm().equals(other.getXkmlm()))
            && (this.getXkml() == null ? other.getXkml() == null : this.getXkml().equals(other.getXkml()))
            && (this.getXklym() == null ? other.getXklym() == null : this.getXklym().equals(other.getXklym()))
            && (this.getCghjlbm() == null ? other.getCghjlbm() == null : this.getCghjlbm().equals(other.getCghjlbm()))
            && (this.getKjjlbm() == null ? other.getKjjlbm() == null : this.getKjjlbm().equals(other.getKjjlbm()))
            && (this.getJldjm() == null ? other.getJldjm() == null : this.getJldjm().equals(other.getJldjm()))
            && (this.getJldj() == null ? other.getJldj() == null : this.getJldj().equals(other.getJldj()))
            && (this.getHjjbm() == null ? other.getHjjbm() == null : this.getHjjbm().equals(other.getHjjbm()))
            && (this.getHjjb() == null ? other.getHjjb() == null : this.getHjjb().equals(other.getHjjb()))
            && (this.getHjrq() == null ? other.getHjrq() == null : this.getHjrq().equals(other.getHjrq()))
            && (this.getCgxsm() == null ? other.getCgxsm() == null : this.getCgxsm().equals(other.getCgxsm()))
            && (this.getCgxs() == null ? other.getCgxs() == null : this.getCgxs().equals(other.getCgxs()))
            && (this.getZsh() == null ? other.getZsh() == null : this.getZsh().equals(other.getZsh()))
            && (this.getZzzs() == null ? other.getZzzs() == null : this.getZzzs().equals(other.getZzzs()))
            && (this.getSfhz() == null ? other.getSfhz() == null : this.getSfhz().equals(other.getSfhz()))
            && (this.getDwpmm() == null ? other.getDwpmm() == null : this.getDwpmm().equals(other.getDwpmm()))
            && (this.getDwpm() == null ? other.getDwpm() == null : this.getDwpm().equals(other.getDwpm()))
            && (this.getBjdw() == null ? other.getBjdw() == null : this.getBjdw().equals(other.getBjdw()))
            && (this.getJysbh() == null ? other.getJysbh() == null : this.getJysbh().equals(other.getJysbh()))
            && (this.getJysmc() == null ? other.getJysmc() == null : this.getJysmc().equals(other.getJysmc()))
            && (this.getYjxkm() == null ? other.getYjxkm() == null : this.getYjxkm().equals(other.getYjxkm()))
            && (this.getYjxk() == null ? other.getYjxk() == null : this.getYjxk().equals(other.getYjxk()))
            && (this.getJlpzh() == null ? other.getJlpzh() == null : this.getJlpzh().equals(other.getJlpzh()))
            && (this.getShztm() == null ? other.getShztm() == null : this.getShztm().equals(other.getShztm()))
            && (this.getShzt() == null ? other.getShzt() == null : this.getShzt().equals(other.getShzt()))
            && (this.getDywcrxm() == null ? other.getDywcrxm() == null : this.getDywcrxm().equals(other.getDywcrxm()))
            && (this.getDywcrzcm() == null ? other.getDywcrzcm() == null : this.getDywcrzcm().equals(other.getDywcrzcm()))
            && (this.getDywcrzc() == null ? other.getDywcrzc() == null : this.getDywcrzc().equals(other.getDywcrzc()))
            && (this.getDywcrxbm() == null ? other.getDywcrxbm() == null : this.getDywcrxbm().equals(other.getDywcrxbm()))
            && (this.getDywcrxb() == null ? other.getDywcrxb() == null : this.getDywcrxb().equals(other.getDywcrxb()))
            && (this.getDywcrxlm() == null ? other.getDywcrxlm() == null : this.getDywcrxlm().equals(other.getDywcrxlm()))
            && (this.getDywcrxl() == null ? other.getDywcrxl() == null : this.getDywcrxl().equals(other.getDywcrxl()))
            && (this.getDywcrxwm() == null ? other.getDywcrxwm() == null : this.getDywcrxwm().equals(other.getDywcrxwm()))
            && (this.getDywcrxw() == null ? other.getDywcrxw() == null : this.getDywcrxw().equals(other.getDywcrxw()))
            && (this.getDywcrzgh() == null ? other.getDywcrzgh() == null : this.getDywcrzgh().equals(other.getDywcrzgh()))
            && (this.getCzsj() == null ? other.getCzsj() == null : this.getCzsj().equals(other.getCzsj()))
            && (this.getDywcrbh() == null ? other.getDywcrbh() == null : this.getDywcrbh().equals(other.getDywcrbh()))
            && (this.getDywcrlxm() == null ? other.getDywcrlxm() == null : this.getDywcrlxm().equals(other.getDywcrlxm()))
            && (this.getDywcrlx() == null ? other.getDywcrlx() == null : this.getDywcrlx().equals(other.getDywcrlx()))
            && (this.getWcdw() == null ? other.getWcdw() == null : this.getWcdw().equals(other.getWcdw()))
            && (this.getFj() == null ? other.getFj() == null : this.getFj().equals(other.getFj()))
            && (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTstamp() == null ? other.getTstamp() == null : this.getTstamp().equals(other.getTstamp()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getHjcgbh() == null) ? 0 : getHjcgbh().hashCode());
        result = prime * result + ((getHjcgmc() == null) ? 0 : getHjcgmc().hashCode());
        result = prime * result + ((getJlmc() == null) ? 0 : getJlmc().hashCode());
        result = prime * result + ((getJljj() == null) ? 0 : getJljj().hashCode());
        result = prime * result + ((getSsxmbh() == null) ? 0 : getSsxmbh().hashCode());
        result = prime * result + ((getXmlym() == null) ? 0 : getXmlym().hashCode());
        result = prime * result + ((getXmly() == null) ? 0 : getXmly().hashCode());
        result = prime * result + ((getDwh() == null) ? 0 : getDwh().hashCode());
        result = prime * result + ((getDwmc() == null) ? 0 : getDwmc().hashCode());
        result = prime * result + ((getXkmlm() == null) ? 0 : getXkmlm().hashCode());
        result = prime * result + ((getXkml() == null) ? 0 : getXkml().hashCode());
        result = prime * result + ((getXklym() == null) ? 0 : getXklym().hashCode());
        result = prime * result + ((getCghjlbm() == null) ? 0 : getCghjlbm().hashCode());
        result = prime * result + ((getKjjlbm() == null) ? 0 : getKjjlbm().hashCode());
        result = prime * result + ((getJldjm() == null) ? 0 : getJldjm().hashCode());
        result = prime * result + ((getJldj() == null) ? 0 : getJldj().hashCode());
        result = prime * result + ((getHjjbm() == null) ? 0 : getHjjbm().hashCode());
        result = prime * result + ((getHjjb() == null) ? 0 : getHjjb().hashCode());
        result = prime * result + ((getHjrq() == null) ? 0 : getHjrq().hashCode());
        result = prime * result + ((getCgxsm() == null) ? 0 : getCgxsm().hashCode());
        result = prime * result + ((getCgxs() == null) ? 0 : getCgxs().hashCode());
        result = prime * result + ((getZsh() == null) ? 0 : getZsh().hashCode());
        result = prime * result + ((getZzzs() == null) ? 0 : getZzzs().hashCode());
        result = prime * result + ((getSfhz() == null) ? 0 : getSfhz().hashCode());
        result = prime * result + ((getDwpmm() == null) ? 0 : getDwpmm().hashCode());
        result = prime * result + ((getDwpm() == null) ? 0 : getDwpm().hashCode());
        result = prime * result + ((getBjdw() == null) ? 0 : getBjdw().hashCode());
        result = prime * result + ((getJysbh() == null) ? 0 : getJysbh().hashCode());
        result = prime * result + ((getJysmc() == null) ? 0 : getJysmc().hashCode());
        result = prime * result + ((getYjxkm() == null) ? 0 : getYjxkm().hashCode());
        result = prime * result + ((getYjxk() == null) ? 0 : getYjxk().hashCode());
        result = prime * result + ((getJlpzh() == null) ? 0 : getJlpzh().hashCode());
        result = prime * result + ((getShztm() == null) ? 0 : getShztm().hashCode());
        result = prime * result + ((getShzt() == null) ? 0 : getShzt().hashCode());
        result = prime * result + ((getDywcrxm() == null) ? 0 : getDywcrxm().hashCode());
        result = prime * result + ((getDywcrzcm() == null) ? 0 : getDywcrzcm().hashCode());
        result = prime * result + ((getDywcrzc() == null) ? 0 : getDywcrzc().hashCode());
        result = prime * result + ((getDywcrxbm() == null) ? 0 : getDywcrxbm().hashCode());
        result = prime * result + ((getDywcrxb() == null) ? 0 : getDywcrxb().hashCode());
        result = prime * result + ((getDywcrxlm() == null) ? 0 : getDywcrxlm().hashCode());
        result = prime * result + ((getDywcrxl() == null) ? 0 : getDywcrxl().hashCode());
        result = prime * result + ((getDywcrxwm() == null) ? 0 : getDywcrxwm().hashCode());
        result = prime * result + ((getDywcrxw() == null) ? 0 : getDywcrxw().hashCode());
        result = prime * result + ((getDywcrzgh() == null) ? 0 : getDywcrzgh().hashCode());
        result = prime * result + ((getCzsj() == null) ? 0 : getCzsj().hashCode());
        result = prime * result + ((getDywcrbh() == null) ? 0 : getDywcrbh().hashCode());
        result = prime * result + ((getDywcrlxm() == null) ? 0 : getDywcrlxm().hashCode());
        result = prime * result + ((getDywcrlx() == null) ? 0 : getDywcrlx().hashCode());
        result = prime * result + ((getWcdw() == null) ? 0 : getWcdw().hashCode());
        result = prime * result + ((getFj() == null) ? 0 : getFj().hashCode());
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTstamp() == null) ? 0 : getTstamp().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", hjcgbh=").append(hjcgbh);
        sb.append(", hjcgmc=").append(hjcgmc);
        sb.append(", jlmc=").append(jlmc);
        sb.append(", jljj=").append(jljj);
        sb.append(", ssxmbh=").append(ssxmbh);
        sb.append(", xmlym=").append(xmlym);
        sb.append(", xmly=").append(xmly);
        sb.append(", dwh=").append(dwh);
        sb.append(", dwmc=").append(dwmc);
        sb.append(", xkmlm=").append(xkmlm);
        sb.append(", xkml=").append(xkml);
        sb.append(", xklym=").append(xklym);
        sb.append(", cghjlbm=").append(cghjlbm);
        sb.append(", kjjlbm=").append(kjjlbm);
        sb.append(", jldjm=").append(jldjm);
        sb.append(", jldj=").append(jldj);
        sb.append(", hjjbm=").append(hjjbm);
        sb.append(", hjjb=").append(hjjb);
        sb.append(", hjrq=").append(hjrq);
        sb.append(", cgxsm=").append(cgxsm);
        sb.append(", cgxs=").append(cgxs);
        sb.append(", zsh=").append(zsh);
        sb.append(", zzzs=").append(zzzs);
        sb.append(", sfhz=").append(sfhz);
        sb.append(", dwpmm=").append(dwpmm);
        sb.append(", dwpm=").append(dwpm);
        sb.append(", bjdw=").append(bjdw);
        sb.append(", jysbh=").append(jysbh);
        sb.append(", jysmc=").append(jysmc);
        sb.append(", yjxkm=").append(yjxkm);
        sb.append(", yjxk=").append(yjxk);
        sb.append(", jlpzh=").append(jlpzh);
        sb.append(", shztm=").append(shztm);
        sb.append(", shzt=").append(shzt);
        sb.append(", dywcrxm=").append(dywcrxm);
        sb.append(", dywcrzcm=").append(dywcrzcm);
        sb.append(", dywcrzc=").append(dywcrzc);
        sb.append(", dywcrxbm=").append(dywcrxbm);
        sb.append(", dywcrxb=").append(dywcrxb);
        sb.append(", dywcrxlm=").append(dywcrxlm);
        sb.append(", dywcrxl=").append(dywcrxl);
        sb.append(", dywcrxwm=").append(dywcrxwm);
        sb.append(", dywcrxw=").append(dywcrxw);
        sb.append(", dywcrzgh=").append(dywcrzgh);
        sb.append(", czsj=").append(czsj);
        sb.append(", dywcrbh=").append(dywcrbh);
        sb.append(", dywcrlxm=").append(dywcrlxm);
        sb.append(", dywcrlx=").append(dywcrlx);
        sb.append(", wcdw=").append(wcdw);
        sb.append(", fj=").append(fj);
        sb.append(", id=").append(id);
        sb.append(", tstamp=").append(tstamp);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}