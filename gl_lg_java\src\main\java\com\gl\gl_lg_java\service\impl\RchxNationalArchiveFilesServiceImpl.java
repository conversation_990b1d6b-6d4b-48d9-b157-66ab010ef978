package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.config.MinioProperties;
import com.gl.gl_lg_java.domain.RchxNationalArchiveFiles;
import com.gl.gl_lg_java.mapper.RchxNationalArchiveFilesMapper;
import com.gl.gl_lg_java.service.RchxNationalArchiveFilesService;
import io.minio.GetObjectArgs;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_national_archive_files(国家/省部级项目归档文件表)】的数据库操作Service实现
 * @createDate 2025-01-04 10:00:00
 */
@Service
@Slf4j
public class RchxNationalArchiveFilesServiceImpl
        extends ServiceImpl<RchxNationalArchiveFilesMapper, RchxNationalArchiveFiles>
        implements RchxNationalArchiveFilesService {

    @Autowired
    private RchxNationalArchiveFilesMapper filesMapper;

    @Autowired
    private MinioClient minioClient;


    @Autowired
    private MinioProperties minioProperties;

    // 允许的文件类型
    private static final Set<String> ALLOWED_FILE_TYPES = Set.of(
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            "txt", "jpg", "jpeg", "png", "gif", "zip", "rar");

    // 文件大小限制（100MB）
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024;

    @Override
    public List<RchxNationalArchiveFiles> listByArchiveId(Long archiveId) {
        return filesMapper.findByArchiveId(archiveId);
    }

    @Override
    public List<RchxNationalArchiveFiles> listByProjectCode(String projectCode) {
        return filesMapper.findByProjectCode(projectCode);
    }

    @Override
    public List<RchxNationalArchiveFiles> listByFileCategory(String fileCategory) {
        return filesMapper.findByFileCategory(fileCategory);
    }

    @Override
    public List<RchxNationalArchiveFiles> listByArchiveIdAndCategory(Long archiveId, String fileCategory) {
        return filesMapper.findByArchiveIdAndCategory(archiveId, fileCategory);
    }

    @Override
    public List<RchxNationalArchiveFiles> listByFileNameLike(String fileName) {
        return filesMapper.findByFileNameLike(fileName);
    }

    @Override
    public List<RchxNationalArchiveFiles> listByUploadBy(String uploadBy) {
        return filesMapper.findByUploadBy(uploadBy);
    }

    @Override
    public List<RchxNationalArchiveFiles> listByUploadTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return filesMapper.findByUploadTimeRange(startTime, endTime);
    }

    @Override
    public List<RchxNationalArchiveFiles> listRequiredFiles() {
        return filesMapper.findRequiredFiles();
    }

    @Override
    public List<RchxNationalArchiveFiles> listConfidentialFiles() {
        return filesMapper.findConfidentialFiles();
    }

    @Override
    public RchxNationalArchiveFiles getByMinioInfo(String bucketName, String objectName) {
        return filesMapper.findByMinioInfo(bucketName, objectName);
    }

    @Override
    public List<RchxNationalArchiveFiles> listByFileMd5(String fileMd5) {
        return filesMapper.findByFileMd5(fileMd5);
    }

    @Override
    public List<Map<String, Object>> countByFileCategory() {
        return filesMapper.countByFileCategory();
    }

    @Override
    public List<Map<String, Object>> countByArchiveId() {
        return filesMapper.countByArchiveId();
    }

    @Override
    public Map<String, Object> getFileStatistics() {
        return filesMapper.getFileStatistics();
    }

    @Override
    @Transactional
    public RchxNationalArchiveFiles uploadFile(Long archiveId, String projectCode, String fileCategory,
            MultipartFile file, String uploadBy, String uploadByName,
            String description, Boolean isRequired, Boolean isConfidential) {
        try {
            // 验证文件
            if (!isFileTypeAllowed(getFileExtension(file.getOriginalFilename()))) {
                throw new RuntimeException("不支持的文件类型");
            }

            if (!isFileSizeAllowed(file.getSize())) {
                throw new RuntimeException("文件大小超出限制");
            }

            // 计算文件MD5
            String fileMd5 = calculateFileMd5(file);

            // 检查重复文件
            if (isFileDuplicate(fileMd5)) {
                log.warn("检测到重复文件: {}", file.getOriginalFilename());
            }

            // 生成文件存储路径
            String filePath = generateFilePath(projectCode, fileCategory, file.getOriginalFilename());
            String objectName = UUID.randomUUID().toString() + "_" + file.getOriginalFilename();

            // 创建文件记录
            RchxNationalArchiveFiles archiveFile = new RchxNationalArchiveFiles();
            archiveFile.setArchiveId(archiveId);
            archiveFile.setProjectCode(projectCode);
            archiveFile.setFileCategory(fileCategory);
            archiveFile.setFileName(file.getOriginalFilename());
            archiveFile.setFileOriginalName(file.getOriginalFilename());
            archiveFile.setFilePath(filePath);
            archiveFile.setFileSize(file.getSize());
            archiveFile.setFileType(file.getContentType());
            archiveFile.setFileExtension(getFileExtension(file.getOriginalFilename()));
            archiveFile.setBucketName("rchx-archive");
            archiveFile.setObjectName(objectName);
            archiveFile.setFileMd5(fileMd5);
            archiveFile.setFileDescription(description);
            archiveFile.setIsRequired(isRequired != null ? isRequired : false);
            archiveFile.setIsConfidential(isConfidential != null ? isConfidential : false);
            archiveFile.setUploadTime(LocalDateTime.now());
            archiveFile.setUploadBy(uploadBy);
            archiveFile.setUploadByName(uploadByName);
            archiveFile.setDownloadCount(0);
            archiveFile.setIsDeleted(false);

            // 上传文件到MinIO，设置公共读取权限
            Map<String, String> headers = new HashMap<>();
            headers.put("x-amz-acl", "public-read");

            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(archiveFile.getBucketName())
                    .object(archiveFile.getObjectName())
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .headers(headers)
                    .build());

            // 保存文件记录
            save(archiveFile);

            log.info("文件上传成功: {}", file.getOriginalFilename());
            return archiveFile;

        } catch (Exception e) {
            log.error("文件上传失败: {}", file.getOriginalFilename(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public List<RchxNationalArchiveFiles> batchUploadFiles(Long archiveId, String projectCode, String fileCategory,
            List<MultipartFile> files, String uploadBy, String uploadByName,
            Boolean isRequired, Boolean isConfidential) {
        List<RchxNationalArchiveFiles> uploadedFiles = new ArrayList<>();

        for (MultipartFile file : files) {
            try {
                RchxNationalArchiveFiles uploadedFile = uploadFile(archiveId, projectCode, fileCategory,
                        file, uploadBy, uploadByName, null, isRequired, isConfidential);
                uploadedFiles.add(uploadedFile);
            } catch (Exception e) {
                log.error("批量上传文件失败: {}", file.getOriginalFilename(), e);
                // 继续处理其他文件
            }
        }

        return uploadedFiles;
    }

    @Override
    public byte[] downloadFile(Long fileId) {
        try {
            RchxNationalArchiveFiles file = getById(fileId);
            if (file == null || file.getIsDeleted()) {
                throw new RuntimeException("文件不存在");
            }

            // 更新下载统计
            filesMapper.updateDownloadInfo(fileId);

            // 从MinIO下载文件
            try (InputStream inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(file.getBucketName())
                            .object(file.getObjectName())
                            .build())) {

                return inputStream.readAllBytes();
            }

        } catch (Exception e) {
            log.error("文件下载失败: fileId={}", fileId, e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    @Override
    public String getFileDownloadUrl(Long fileId) {
        try {
            RchxNationalArchiveFiles file = getById(fileId);
            if (file == null || file.getIsDeleted()) {
                throw new RuntimeException("文件不存在");
            }

            // 生成MinIO的预签名下载URL，有效期7天
            String presignedUrl = minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(file.getBucketName())
                            .object(file.getObjectName())
                            .expiry(7, TimeUnit.DAYS)
                            .build());

            return presignedUrl;

        } catch (Exception e) {
            log.error("获取文件下载URL失败: fileId={}", fileId, e);
            throw new RuntimeException("获取文件下载URL失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteFile(Long fileId, String deleteBy) {
        try {
            return filesMapper.logicalDeleteFile(fileId, deleteBy) > 0;
        } catch (Exception e) {
            log.error("删除文件失败: fileId={}", fileId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean batchDeleteFiles(List<Long> fileIds, String deleteBy) {
        try {
            for (Long fileId : fileIds) {
                filesMapper.logicalDeleteFile(fileId, deleteBy);
            }
            return true;
        } catch (Exception e) {
            log.error("批量删除文件失败: fileIds={}", fileIds, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean physicalDeleteFile(Long fileId) {
        try {
            RchxNationalArchiveFiles file = getById(fileId);
            if (file != null) {
                // 从MinIO删除文件
                // minioService.deleteFile(file.getBucketName(), file.getObjectName());

                // 从数据库删除记录
                removeById(fileId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("物理删除文件失败: fileId={}", fileId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateFileInfo(Long fileId, String fileName, String description, Boolean isRequired,
            Boolean isConfidential) {
        try {
            RchxNationalArchiveFiles file = getById(fileId);
            if (file != null) {
                file.setFileName(fileName);
                file.setFileDescription(description);
                file.setIsRequired(isRequired);
                file.setIsConfidential(isConfidential);
                return updateById(file);
            }
            return false;
        } catch (Exception e) {
            log.error("更新文件信息失败: fileId={}", fileId, e);
            return false;
        }
    }

    @Override
    public boolean isFileDuplicate(String fileMd5) {
        return !listByFileMd5(fileMd5).isEmpty();
    }

    @Override
    public boolean isFileTypeAllowed(String fileExtension) {
        return ALLOWED_FILE_TYPES.contains(fileExtension.toLowerCase());
    }

    @Override
    public boolean isFileSizeAllowed(Long fileSize) {
        return fileSize <= MAX_FILE_SIZE;
    }

    @Override
    public Map<String, Object> checkArchiveFileCompleteness(Long archiveId) {
        List<RchxNationalArchiveFiles> files = listByArchiveId(archiveId);

        Map<String, Object> result = new HashMap<>();
        result.put("totalFiles", files.size());
        result.put("requiredFiles", files.stream().filter(RchxNationalArchiveFiles::getIsRequired).count());
        result.put("confidentialFiles", files.stream().filter(RchxNationalArchiveFiles::getIsConfidential).count());

        // 按文件类别统计
        Map<String, Long> categoryCount = new HashMap<>();
        files.forEach(file -> {
            categoryCount.merge(file.getFileCategory(), 1L, Long::sum);
        });
        result.put("categoryCount", categoryCount);

        return result;
    }

    @Override
    public String generateFilePath(String projectCode, String fileCategory, String originalFileName) {
        String year = String.valueOf(LocalDateTime.now().getYear());
        String month = String.format("%02d", LocalDateTime.now().getMonthValue());
        return String.format("archive/%s/%s/%s/%s", year, month, projectCode, fileCategory);
    }

    @Override
    public String calculateFileMd5(MultipartFile file) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(file.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("计算文件MD5失败", e);
            return null;
        }
    }

    @Override
    public boolean hasFileAccessPermission(String userZgh, Long fileId) {
        // 简单权限检查：上传人可以访问
        QueryWrapper<RchxNationalArchiveFiles> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", fileId)
                .eq("upload_by", userZgh)
                .eq("is_deleted", false);
        return count(queryWrapper) > 0;
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
}
