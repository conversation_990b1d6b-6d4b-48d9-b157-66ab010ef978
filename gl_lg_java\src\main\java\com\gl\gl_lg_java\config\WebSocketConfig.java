package com.gl.gl_lg_java.config;

import com.gl.gl_lg_java.websocket.TeacherNotificationHandler;
import com.gl.gl_lg_java.websocket.JwtWebSocketInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置类
 * 用于教师通知系统的WebSocket连接配置
 */
@Configuration
@EnableWebSocket
@Slf4j
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private TeacherNotificationHandler teacherNotificationHandler;

    @Autowired
    private JwtWebSocketInterceptor jwtWebSocketInterceptor;

    @Value("${teacher-notification.websocket.endpoint:/ws/teacher-notifications}")
    private String endpoint;

    @Value("${teacher-notification.websocket.allowed-origins:*}")
    private String allowedOrigins;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册教师通知WebSocket处理器
        registry.addHandler(teacherNotificationHandler, endpoint)
                .addInterceptors(jwtWebSocketInterceptor)
                .setAllowedOrigins(allowedOrigins);

        log.info("WebSocket配置完成: endpoint={}, allowedOrigins={}", endpoint, allowedOrigins);
    }
}
