# 教师管理系统实时通知技术方案

## 1. 技术栈选择

### 1.1 后端技术（基于现有系统）
- **Spring Boot 2.x** - 主框架（已有）
- **Spring WebSocket** - WebSocket支持（新增）
- **MyBatis Plus** - 数据库ORM（已有）
- **MySQL 8.0** - 主数据库（已有）
- **Redis 6.0+** - 缓存和会话存储（新增）
- **JWT认证** - 用户认证（已有）
- **Jackson** - JSON序列化（已有）
- **Spring Task** - 定时任务（新增）

### 1.2 前端技术
- **Vue.js** - 前端框架
- **WebSocket API** - 浏览器WebSocket
- **Element UI** - UI组件库
- **Axios** - HTTP请求（已有）

### 1.3 开发工具
- **Maven** - 项目构建（已有）
- **Postman** - API测试
- **WebSocket测试工具** - 连接测试

## 2. 数据库设计（基于现有教师系统）

### 2.1 通知表 (t_rchx_notifications)
```sql
CREATE TABLE `t_rchx_notifications` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `zgh` varchar(50) NOT NULL COMMENT '接收教师职工号',
  `title` varchar(200) NOT NULL COMMENT '通知标题',
  `content` text COMMENT '通知内容',
  `type` varchar(50) NOT NULL COMMENT '通知类型',
  `related_id` varchar(100) COMMENT '关联业务ID',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读(0未读,1已读)',
  `priority` tinyint(1) DEFAULT '1' COMMENT '优先级(1普通,2重要,3紧急)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `read_time` datetime COMMENT '阅读时间',
  `expire_time` datetime COMMENT '过期时间',
  `sender_zgh` varchar(50) COMMENT '发送者职工号',
  PRIMARY KEY (`id`),
  KEY `idx_zgh` (`zgh`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_type` (`type`),
  KEY `idx_zgh_read_time` (`zgh`, `is_read`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师通知表';
```

### 2.2 通知类型枚举（基于教师系统业务）
```sql
-- 教师系统通知类型
-- teacher_info_updated: 教师信息更新通知
-- permission_changed: 权限变更通知
-- system_maintenance: 系统维护通知
-- data_import_result: 数据导入结果通知
-- password_reset_success: 密码重置成功通知
-- account_locked: 账户锁定通知
-- batch_operation_result: 批量操作结果通知
```

### 2.3 索引优化
```sql
-- 基于教师系统的索引设计
CREATE INDEX idx_zgh_read_time ON t_rchx_notifications(zgh, is_read, create_time);
CREATE INDEX idx_type_time ON t_rchx_notifications(type, create_time);
CREATE INDEX idx_sender_time ON t_rchx_notifications(sender_zgh, create_time);
```

## 3. 系统架构设计（集成现有教师系统）

### 3.1 整体架构
```
前端Vue应用 ←→ WebSocket连接 ←→ Spring Boot教师系统
                                    ↓
                              通知服务层
                                ↙        ↘
                    Redis缓存层              MySQL数据库(glrchx)
                  (连接状态/未读数)              ↓
                                    教师表(t_rchx_jzgjbxx) + 通知表(t_rchx_notifications)
```

### 3.2 核心组件（基于现有系统）

#### 3.2.1 WebSocket配置类
- **WebSocketConfig** - WebSocket配置，集成JWT认证
- **TeacherWebSocketHandler** - 教师消息处理器
- **JwtWebSocketInterceptor** - JWT连接拦截器

#### 3.2.2 通知服务层
- **TeacherNotificationService** - 教师通知业务服务
- **WebSocketNotificationService** - WebSocket推送服务
- **TeacherConnectionManager** - 教师连接管理器

#### 3.2.3 集成现有服务
- **RchxJzgjbxxService** - 复用现有教师服务
- **AuthService** - 复用现有认证服务
- **JwtUtil** - 复用现有JWT工具

## 4. 依赖配置（基于现有系统）

### 4.1 新增Maven依赖 (pom.xml)
```xml
<!-- WebSocket支持 - 新增 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-websocket</artifactId>
</dependency>

<!-- Redis支持 - 新增 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- 定时任务支持 - 新增 -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-context-support</artifactId>
</dependency>

<!-- 现有依赖保持不变 -->
<!-- MyBatis Plus、MySQL、JWT等已有依赖继续使用 -->
```

### 4.2 配置文件扩展 (application.yml)
```yaml
# 现有配置保持不变
server:
  port: 8080

spring:
  datasource:
    url: jdbc:mysql://**************:3306/glrchx?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
    username: glrchx
    password: glrchx
    driver-class-name: com.mysql.cj.jdbc.Driver

  # 新增Redis配置
  redis:
    host: localhost
    port: 6379
    database: 2  # 使用数据库2，避免与其他应用冲突
    timeout: 3000ms
    password: # 如果Redis设置了密码
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: -1ms

# 新增WebSocket配置
websocket:
  endpoint: /ws/teacher-notifications
  allowed-origins: "*"
  heartbeat-interval: 30000
  max-connections: 500

# 新增通知系统配置
teacher-notification:
  # 批量推送大小
  batch-size: 50
  # 重试次数
  retry-times: 3
  # 过期时间(天)
  expire-days: 30
  # 是否启用桌面通知
  desktop-notification: true
  # 通知保留数量（每个用户）
  max-notifications-per-user: 100
  # Redis缓存配置
  redis:
    # 连接状态缓存时间(秒)
    connection-expire: 3600
    # 未读数量缓存时间(秒)
    unread-count-expire: 1800

# 定时任务配置
spring:
  task:
    scheduling:
      pool:
        size: 3
      thread-name-prefix: teacher-notification-task-
```

## 5. 核心功能模块（基于教师系统业务）

### 5.1 WebSocket连接管理
- **JWT认证连接** - 基于现有JWT Token建立WebSocket连接
- **教师连接映射** - 使用ConcurrentHashMap存储职工号(zgh)与连接的映射
- **权限验证** - 连接时验证教师权限和身份
- **心跳检测** - 定期检测连接状态，自动清理断开连接
- **多设备支持** - 同一教师可在多设备同时在线

### 5.2 通知创建与推送（教师系统场景）
- **教师信息变更通知** - 管理员修改教师信息后自动通知
- **权限变更通知** - 教师权限调整后实时通知
- **批量操作结果** - 批量导入/更新教师信息的结果通知
- **系统维护通知** - 系统维护前的提醒通知
- **密码重置通知** - 密码重置成功的确认通知

### 5.3 权限控制（基于现有权限系统）
- **教师权限** - 只能接收与自己相关的通知
- **学院管理员** - 可接收本学院相关的管理通知
- **系统管理员** - 可接收所有系统级通知
- **通知发送权限** - 只有管理员可发送通知给其他用户

### 5.4 业务集成点
- **教师CRUD操作** - 集成现有的教师增删改查接口
- **用户认证流程** - 复用现有的JWT认证机制
- **权限验证** - 使用现有的PermissionUtil工具类

## 6. 前端实现要点（教师系统界面集成）

### 6.1 WebSocket客户端（集成JWT认证）
```javascript
// 教师系统WebSocket连接
class TeacherNotificationClient {
  constructor(jwtToken) {
    this.token = jwtToken;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const wsUrl = `ws://localhost:8080/ws/teacher-notifications?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);
    // 连接处理逻辑
  }
}
```

### 6.2 UI组件设计（基于教师系统界面）
- **顶部导航栏集成** - 在现有导航栏添加通知铃铛图标
- **权限相关通知** - 根据教师权限显示不同类型通知
- **教师信息变更提醒** - 个人信息被修改时的实时提醒
- **系统消息面板** - 系统维护、公告等重要消息
- **通知历史记录** - 查看历史通知记录

### 6.3 状态管理（集成现有用户状态）
- **用户信息集成** - 复用现有的用户登录状态
- **权限相关通知** - 根据用户权限过滤通知内容
- **通知分类显示** - 按通知类型分类显示
- **离线消息同步** - 用户重新登录时同步离线期间的通知

## 7. 性能优化策略（基于教师系统规模）

### 7.1 数据库优化（基于3000+教师数据）
- **职工号索引** - 基于zgh字段的高效索引
- **复合索引** - (zgh, is_read, create_time)复合索引优化查询
- **分页查询** - 通知列表分页，每页20条
- **定期清理** - 每月清理30天前的已读通知
- **连接池优化** - 复用现有HikariCP连接池配置

### 7.2 Redis缓存策略
- **连接状态缓存** - Redis存储在线教师的WebSocket连接状态
- **未读数量缓存** - Redis缓存每个教师的未读通知数量，减少数据库查询
- **热点数据缓存** - 缓存最近的通知数据，提高查询性能
- **分布式会话** - 支持多实例部署时的连接状态共享

### 7.3 推送优化（教师系统场景）
- **权限过滤** - 推送前根据权限过滤接收者
- **批量推送** - 同一学院的教师批量推送相同通知
- **异步处理** - 使用@Async注解异步处理通知推送
- **连接检查** - 从Redis快速检查WebSocket连接状态

### 7.4 Redis具体应用场景

#### 7.4.1 连接状态管理
```
Key: teacher:online:{zgh}
Value: {sessionId, connectTime, lastHeartbeat}
TTL: 1小时（心跳更新）
```

#### 7.4.2 未读通知计数
```
Key: teacher:unread:{zgh}
Value: 未读通知数量
TTL: 30分钟（查询时更新）
```

#### 7.4.3 热点通知缓存
```
Key: notification:hot:{id}
Value: 通知详情JSON
TTL: 1小时（减少数据库查询）
```

#### 7.4.4 推送队列
```
Key: notification:queue
Value: 待推送通知列表
用途: 异步推送，提高响应速度
```

## 8. 安全考虑（基于现有安全机制）

### 8.1 连接安全（复用JWT认证）
- **JWT Token验证** - 复用现有的JwtUtil工具类验证连接
- **权限控制** - 基于现有PermissionEnum进行权限验证
- **职工号验证** - 确保用户只能接收发给自己职工号的通知
- **连接超时** - 设置合理的连接超时时间

### 8.2 数据安全（基于现有安全策略）
- **MyBatis Plus防护** - 复用现有的参数化查询防护
- **权限分级** - 不同权限用户接收不同级别的通知
- **敏感信息过滤** - 通知内容不包含密码等敏感信息
- **日志记录** - 记录通知发送和接收的审计日志

## 9. 监控与日志（集成现有日志系统）

### 9.1 连接监控（基于教师系统）
- **在线教师统计** - 实时统计在线教师数量
- **权限分布监控** - 监控不同权限用户的在线情况
- **连接状态检查** - 定期检查WebSocket连接健康状态
- **性能指标** - 监控通知推送延迟和成功率

### 9.2 业务监控（教师系统场景）
- **通知类型统计** - 统计各类教师通知的发送数量
- **权限相关统计** - 统计不同权限用户的通知接收情况
- **教师活跃度** - 统计教师通知查看率和响应时间
- **系统负载** - 监控WebSocket连接对系统资源的影响

### 9.3 日志记录（复用现有日志配置）
- **连接日志** - 记录教师WebSocket连接/断开，包含职工号和权限
- **推送日志** - 记录通知推送详情，包含发送者和接收者职工号
- **权限日志** - 记录权限验证和访问控制日志
- **错误日志** - 记录WebSocket和通知系统的异常信息

## 10. 部署要求（基于现有环境）

### 10.1 服务器要求（现有环境扩展）
- **CPU** - 现有配置基础上无需额外要求
- **内存** - 现有8GB内存足够支持WebSocket连接
- **存储** - 复用现有MySQL数据库存储
- **网络** - 现有网络环境支持WebSocket协议

### 10.2 软件环境（复用现有环境）
- **Java** - JDK 11（已有）
- **MySQL** - 8.0（已有，数据库：glrchx）
- **Redis** - 6.0+（新增，建议部署在同一服务器）
- **Spring Boot** - 2.x（已有）
- **Maven** - 项目构建工具（已有）

### 10.3 部署架构（基于现有架构）
```
教师浏览器 → WebSocket连接 → Spring Boot教师系统 → MySQL(glrchx)
                                    ↓              ↓
                              现有JWT认证      Redis缓存
                                    ↓              ↓
                              新增通知服务 ←→ 连接状态/未读数
```

### 10.4 配置建议（基于现有配置）
- **HikariCP连接池** - 复用现有的数据库连接池配置
- **WebSocket配置** - 设置合理的连接超时时间
- **内存管理** - 定期清理断开的WebSocket连接

## 11. 测试策略（基于教师系统）

### 11.1 单元测试
- **通知服务测试** - 测试教师通知创建和推送逻辑
- **WebSocket测试** - 测试基于JWT的连接管理功能
- **权限测试** - 测试不同权限用户的通知接收权限

### 11.2 集成测试
- **教师系统集成** - 测试与现有教师CRUD操作的集成
- **JWT认证集成** - 测试WebSocket与JWT认证的集成
- **权限系统集成** - 测试与现有权限系统的集成

### 11.3 测试工具
- **JUnit** - 单元测试框架（现有）
- **Mockito** - Mock测试（现有）
- **Postman** - API测试（现有）
- **浏览器开发者工具** - WebSocket连接测试

## 12. 实施计划（基于教师系统）

### 12.1 开发阶段（6-7周）
1. **第一阶段（1-2周）** - 数据库扩展和WebSocket基础集成
2. **第二阶段（2-3周）** - JWT认证集成和权限控制
3. **第三阶段（2周）** - 教师业务集成和前端界面
4. **第四阶段（1周）** - 测试优化和文档更新

### 12.2 测试阶段（2周）
1. **功能测试** - 各权限用户的通知功能测试
2. **集成测试** - 与现有教师系统的集成测试
3. **性能测试** - 3000+教师数据的性能测试
4. **用户验收** - 教师用户的使用体验测试

### 12.3 上线部署（1周）
1. **环境准备** - 复用现有生产环境 + 部署Redis服务
2. **数据库更新** - 在glrchx数据库中创建通知表
3. **Redis配置** - 配置Redis服务和持久化
4. **应用部署** - 更新现有Spring Boot应用
5. **功能验证** - 生产环境功能验证

## 13. 总结

本实时通知系统技术方案完全基于现有的教师管理系统设计，具有以下特点：

### 13.1 系统集成度高
- **复用现有架构** - 基于现有Spring Boot + MyBatis Plus + MySQL架构
- **复用认证系统** - 完全复用现有JWT认证和权限系统
- **复用数据模型** - 基于现有教师表(t_rchx_jzgjbxx)的职工号(zgh)和权限(qx)字段

### 13.2 业务契合度高
- **教师系统场景** - 针对教师信息管理、权限变更等具体业务场景
- **权限体系一致** - 与现有的教师、评审、学院管理员、系统管理员权限完全一致
- **用户体验友好** - 基于现有用户界面和操作习惯设计

### 13.3 技术风险可控
- **渐进式集成** - 不影响现有系统功能，可独立开关
- **性能显著提升** - Redis缓存大幅提升查询性能和用户体验
- **扩展性强** - Redis支持分布式部署，便于将来系统扩展
- **开发周期短** - 预计6-7周完成开发，2周测试，1周上线

### 13.4 Redis带来的核心优势
- **性能提升10倍** - 未读通知查询从数据库查询变为Redis查询
- **实时性更强** - 连接状态实时更新，推送更精准
- **并发能力强** - Redis支持高并发读写，适合3000+用户同时在线
- **数据可靠性** - Redis持久化保证重要数据不丢失

---

**文档版本**: v2.0（教师系统定制版）
**创建时间**: 2025-07-24
**更新时间**: 2025-07-24
**维护人员**: 教师系统开发团队
