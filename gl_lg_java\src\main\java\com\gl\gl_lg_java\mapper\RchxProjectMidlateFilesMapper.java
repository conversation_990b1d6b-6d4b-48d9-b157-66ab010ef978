package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.domain.RchxProjectMidlateFiles;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 项目中后期文件管理Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Mapper
public interface RchxProjectMidlateFilesMapper extends BaseMapper<RchxProjectMidlateFiles> {

    /**
     * 分页查询文件信息
     */
    @Select({
        "<script>",
        "SELECT * FROM t_rchx_project_midlate_files ",
        "WHERE is_deleted = 0 ",
        "<if test='businessType != null and businessType != \"\"'>",
        "AND business_type = #{businessType} ",
        "</if>",
        "<if test='businessId != null'>",
        "AND business_id = #{businessId} ",
        "</if>",
        "<if test='projectCode != null and projectCode != \"\"'>",
        "AND project_code = #{projectCode} ",
        "</if>",
        "<if test='fileCategory != null and fileCategory != \"\"'>",
        "AND file_category = #{fileCategory} ",
        "</if>",
        "<if test='uploadBy != null and uploadBy != \"\"'>",
        "AND upload_by = #{uploadBy} ",
        "</if>",
        "<if test='isRequired != null'>",
        "AND is_required = #{isRequired} ",
        "</if>",
        "<if test='isConfidential != null'>",
        "AND is_confidential = #{isConfidential} ",
        "</if>",
        "ORDER BY upload_time DESC",
        "</script>"
    })
    IPage<RchxProjectMidlateFiles> selectPageWithConditions(
            Page<RchxProjectMidlateFiles> page,
            @Param("businessType") String businessType,
            @Param("businessId") Long businessId,
            @Param("projectCode") String projectCode,
            @Param("fileCategory") String fileCategory,
            @Param("uploadBy") String uploadBy,
            @Param("isRequired") Boolean isRequired,
            @Param("isConfidential") Boolean isConfidential
    );

    /**
     * 根据业务类型和业务ID查询文件列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_files " +
            "WHERE business_type = #{businessType} AND business_id = #{businessId} " +
            "AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxProjectMidlateFiles> selectByBusiness(@Param("businessType") String businessType,
                                                   @Param("businessId") Long businessId);

    /**
     * 根据项目编号查询文件列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_files " +
            "WHERE project_code = #{projectCode} AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxProjectMidlateFiles> selectByProjectCode(@Param("projectCode") String projectCode);

    /**
     * 根据文件类别查询文件列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_files " +
            "WHERE business_type = #{businessType} AND business_id = #{businessId} " +
            "AND file_category = #{fileCategory} AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxProjectMidlateFiles> selectByCategory(@Param("businessType") String businessType,
                                                   @Param("businessId") Long businessId,
                                                   @Param("fileCategory") String fileCategory);

    /**
     * 根据上传人查询文件列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_files " +
            "WHERE upload_by = #{uploadBy} AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxProjectMidlateFiles> selectByUploader(@Param("uploadBy") String uploadBy);

    /**
     * 查询机密文件列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_files " +
            "WHERE is_confidential = 1 AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxProjectMidlateFiles> selectConfidentialFiles();

    /**
     * 查询必需文件列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_files " +
            "WHERE is_required = 1 AND is_deleted = 0 " +
            "ORDER BY upload_time DESC")
    List<RchxProjectMidlateFiles> selectRequiredFiles();

    /**
     * 根据文件MD5查询（用于重复文件检测）
     */
    @Select("SELECT * FROM t_rchx_project_midlate_files " +
            "WHERE file_md5 = #{fileMd5} AND is_deleted = 0")
    List<RchxProjectMidlateFiles> selectByFileMd5(@Param("fileMd5") String fileMd5);

    /**
     * 更新下载次数和最后下载时间
     */
    @Update("UPDATE t_rchx_project_midlate_files SET " +
            "download_count = download_count + 1, " +
            "last_download_time = #{lastDownloadTime} " +
            "WHERE id = #{id}")
    int updateDownloadInfo(@Param("id") Long id, @Param("lastDownloadTime") LocalDateTime lastDownloadTime);

    /**
     * 逻辑删除文件
     */
    @Update("UPDATE t_rchx_project_midlate_files SET " +
            "is_deleted = 1, " +
            "delete_time = #{deleteTime}, " +
            "delete_by = #{deleteBy} " +
            "WHERE id = #{id}")
    int logicalDeleteFile(@Param("id") Long id,
                         @Param("deleteTime") LocalDateTime deleteTime,
                         @Param("deleteBy") String deleteBy);

    /**
     * 批量逻辑删除文件
     */
    @Update({
        "<script>",
        "UPDATE t_rchx_project_midlate_files SET ",
        "is_deleted = 1, ",
        "delete_time = #{deleteTime}, ",
        "delete_by = #{deleteBy} ",
        "WHERE id IN ",
        "<foreach collection='ids' item='id' open='(' separator=',' close=')'>",
        "#{id}",
        "</foreach>",
        "</script>"
    })
    int batchLogicalDeleteFiles(@Param("ids") List<Long> ids,
                               @Param("deleteTime") LocalDateTime deleteTime,
                               @Param("deleteBy") String deleteBy);

    /**
     * 获取文件统计信息
     */
    @Select({
        "SELECT ",
        "COUNT(*) AS totalFiles, ",
        "SUM(file_size) AS totalSize, ",
        "SUM(CASE WHEN is_required = 1 THEN 1 ELSE 0 END) AS requiredFiles, ",
        "SUM(CASE WHEN is_confidential = 1 THEN 1 ELSE 0 END) AS confidentialFiles, ",
        "SUM(download_count) AS totalDownloads, ",
        "COUNT(DISTINCT upload_by) AS totalUploaders ",
        "FROM t_rchx_project_midlate_files ",
        "WHERE is_deleted = 0"
    })
    Map<String, Object> getFileStatistics();

    /**
     * 获取文件类别统计
     */
    @Select({
        "SELECT ",
        "file_category AS fileCategory, ",
        "COUNT(*) AS fileCount, ",
        "SUM(file_size) AS totalSize, ",
        "SUM(download_count) AS totalDownloads ",
        "FROM t_rchx_project_midlate_files ",
        "WHERE is_deleted = 0 ",
        "GROUP BY file_category ",
        "ORDER BY fileCount DESC"
    })
    List<Map<String, Object>> getFileCategoryStatistics();

    /**
     * 获取业务类型文件统计
     */
    @Select({
        "SELECT ",
        "business_type AS businessType, ",
        "COUNT(*) AS fileCount, ",
        "SUM(file_size) AS totalSize ",
        "FROM t_rchx_project_midlate_files ",
        "WHERE is_deleted = 0 ",
        "GROUP BY business_type ",
        "ORDER BY fileCount DESC"
    })
    List<Map<String, Object>> getBusinessTypeFileStatistics();

    /**
     * 获取项目文件统计
     */
    @Select({
        "SELECT ",
        "project_code AS projectCode, ",
        "COUNT(*) AS fileCount, ",
        "SUM(file_size) AS totalSize, ",
        "SUM(download_count) AS totalDownloads ",
        "FROM t_rchx_project_midlate_files ",
        "WHERE is_deleted = 0 ",
        "GROUP BY project_code ",
        "ORDER BY fileCount DESC"
    })
    List<Map<String, Object>> getProjectFileStatistics();

    /**
     * 获取上传人文件统计
     */
    @Select({
        "SELECT ",
        "upload_by AS uploadBy, ",
        "upload_by_name AS uploadByName, ",
        "COUNT(*) AS fileCount, ",
        "SUM(file_size) AS totalSize ",
        "FROM t_rchx_project_midlate_files ",
        "WHERE is_deleted = 0 ",
        "GROUP BY upload_by, upload_by_name ",
        "ORDER BY fileCount DESC"
    })
    List<Map<String, Object>> getUploaderFileStatistics();

    /**
     * 获取最近上传的文件
     */
    @Select("SELECT * FROM t_rchx_project_midlate_files " +
            "WHERE is_deleted = 0 " +
            "ORDER BY upload_time DESC " +
            "LIMIT #{limit}")
    List<RchxProjectMidlateFiles> getRecentUploadedFiles(@Param("limit") Integer limit);

    /**
     * 获取热门下载文件
     */
    @Select("SELECT * FROM t_rchx_project_midlate_files " +
            "WHERE is_deleted = 0 AND download_count > 0 " +
            "ORDER BY download_count DESC " +
            "LIMIT #{limit}")
    List<RchxProjectMidlateFiles> getPopularDownloadFiles(@Param("limit") Integer limit);

    /**
     * 检查文件名是否存在
     */
    @Select("SELECT COUNT(*) FROM t_rchx_project_midlate_files " +
            "WHERE file_name = #{fileName} AND business_type = #{businessType} " +
            "AND business_id = #{businessId} AND is_deleted = 0")
    int countByFileName(@Param("fileName") String fileName,
                       @Param("businessType") String businessType,
                       @Param("businessId") Long businessId);
}
