package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxSchoolProjectCategories;
import com.gl.gl_lg_java.mapper.RchxSchoolProjectCategoriesMapper;
import com.gl.gl_lg_java.service.RchxSchoolProjectCategoriesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 校级项目类别Service实现类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Slf4j
public class RchxSchoolProjectCategoriesServiceImpl extends ServiceImpl<RchxSchoolProjectCategoriesMapper, RchxSchoolProjectCategories>
        implements RchxSchoolProjectCategoriesService {

    @Autowired
    private RchxSchoolProjectCategoriesMapper categoriesMapper;

    @Override
    public IPage<Map<String, Object>> pageQueryWithStats(Page<RchxSchoolProjectCategories> page,
                                                         String categoryName,
                                                         Boolean isEnabled) {
        try {
            return categoriesMapper.selectPageWithStats(page, categoryName, isEnabled);
        } catch (Exception e) {
            log.error("分页查询项目类别失败", e);
            throw new RuntimeException("分页查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectCategories> getEnabledCategories() {
        try {
            return categoriesMapper.selectEnabledCategories();
        } catch (Exception e) {
            log.error("查询启用的项目类别失败", e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public RchxSchoolProjectCategories getByCategoryCode(String categoryCode) {
        try {
            return categoriesMapper.selectByCategoryCode(categoryCode);
        } catch (Exception e) {
            log.error("根据类别编码查询失败: categoryCode={}", categoryCode, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public boolean existsByCategoryCode(String categoryCode, Integer excludeId) {
        try {
            return categoriesMapper.countByCategoryCode(categoryCode, excludeId) > 0;
        } catch (Exception e) {
            log.error("检查类别编码是否存在失败: categoryCode={}", categoryCode, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean createCategory(RchxSchoolProjectCategories category) {
        try {
            // 检查类别编码是否已存在
            if (existsByCategoryCode(category.getCategoryCode(), null)) {
                throw new RuntimeException("类别编码已存在: " + category.getCategoryCode());
            }

            // 设置排序号
            if (category.getSortOrder() == null) {
                Integer maxSortOrder = categoriesMapper.getMaxSortOrder();
                category.setSortOrder(maxSortOrder + 1);
            }

            // 设置默认值
            if (category.getIsEnabled() == null) {
                category.setIsEnabled(true);
            }

            boolean result = save(category);
            if (result) {
                log.info("创建项目类别成功: categoryCode={}", category.getCategoryCode());
            }
            return result;
        } catch (Exception e) {
            log.error("创建项目类别失败: categoryCode={}", category.getCategoryCode(), e);
            throw new RuntimeException("创建项目类别失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateCategory(RchxSchoolProjectCategories category) {
        try {
            // 检查类别编码是否已存在（排除自己）
            if (existsByCategoryCode(category.getCategoryCode(), category.getId())) {
                throw new RuntimeException("类别编码已存在: " + category.getCategoryCode());
            }

            boolean result = updateById(category);
            if (result) {
                log.info("更新项目类别成功: id={}", category.getId());
            }
            return result;
        } catch (Exception e) {
            log.error("更新项目类别失败: id={}", category.getId(), e);
            throw new RuntimeException("更新项目类别失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteCategory(Integer id) {
        try {
            boolean result = removeById(id);
            if (result) {
                log.info("删除项目类别成功: id={}", id);
            }
            return result;
        } catch (Exception e) {
            log.error("删除项目类别失败: id={}", id, e);
            throw new RuntimeException("删除项目类别失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean batchDeleteCategories(List<Integer> ids) {
        try {
            boolean result = removeByIds(ids);
            if (result) {
                log.info("批量删除项目类别成功: ids={}", ids);
            }
            return result;
        } catch (Exception e) {
            log.error("批量删除项目类别失败: ids={}", ids, e);
            throw new RuntimeException("批量删除项目类别失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean toggleCategoryStatus(Integer id, Boolean isEnabled) {
        try {
            RchxSchoolProjectCategories category = new RchxSchoolProjectCategories();
            category.setId(id);
            category.setIsEnabled(isEnabled);
            
            boolean result = updateById(category);
            if (result) {
                log.info("切换项目类别状态成功: id={}, isEnabled={}", id, isEnabled);
            }
            return result;
        } catch (Exception e) {
            log.error("切换项目类别状态失败: id={}, isEnabled={}", id, isEnabled, e);
            throw new RuntimeException("切换项目类别状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateSortOrder(Integer id, Integer sortOrder) {
        try {
            RchxSchoolProjectCategories category = new RchxSchoolProjectCategories();
            category.setId(id);
            category.setSortOrder(sortOrder);
            
            boolean result = updateById(category);
            if (result) {
                log.info("更新项目类别排序成功: id={}, sortOrder={}", id, sortOrder);
            }
            return result;
        } catch (Exception e) {
            log.error("更新项目类别排序失败: id={}, sortOrder={}", id, sortOrder, e);
            throw new RuntimeException("更新项目类别排序失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getCategoryStatistics() {
        try {
            return categoriesMapper.getCategoryStatistics();
        } catch (Exception e) {
            log.error("获取类别统计信息失败", e);
            throw new RuntimeException("获取类别统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectCategories> getCategoriesByFundingRange(BigDecimal fundingAmount) {
        try {
            return categoriesMapper.selectByFundingRange(fundingAmount);
        } catch (Exception e) {
            log.error("根据资助金额范围查询类别失败: fundingAmount={}", fundingAmount, e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public String generateCategoryCode(String categoryName) {
        try {
            // 生成格式: CAT + 年份 + 3位序号
            String year = new SimpleDateFormat("yyyy").format(new Date());
            
            // 查找当年最大编号
            QueryWrapper<RchxSchoolProjectCategories> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("category_code", "CAT" + year)
                       .orderByDesc("category_code")
                       .last("LIMIT 1");
            
            RchxSchoolProjectCategories lastCategory = getOne(queryWrapper);
            int sequence = 1;
            
            if (lastCategory != null && lastCategory.getCategoryCode() != null) {
                String lastCode = lastCategory.getCategoryCode();
                String sequenceStr = lastCode.substring(lastCode.length() - 3);
                sequence = Integer.parseInt(sequenceStr) + 1;
            }
            
            return String.format("CAT%s%03d", year, sequence);
        } catch (Exception e) {
            log.error("生成类别编码失败: categoryName={}", categoryName, e);
            throw new RuntimeException("生成类别编码失败: " + e.getMessage());
        }
    }

    @Override
    public List<RchxSchoolProjectCategories> exportCategoryList(Boolean isEnabled) {
        try {
            QueryWrapper<RchxSchoolProjectCategories> queryWrapper = new QueryWrapper<>();
            if (isEnabled != null) {
                queryWrapper.eq("is_enabled", isEnabled);
            }
            queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");
            
            return list(queryWrapper);
        } catch (Exception e) {
            log.error("导出类别列表失败", e);
            throw new RuntimeException("导出类别列表失败: " + e.getMessage());
        }
    }
}
