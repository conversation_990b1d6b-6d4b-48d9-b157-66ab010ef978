package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 科技论文基本信息
 * @TableName t_rchx_kjlwjbxx
 */
@TableName(value ="t_rchx_kjlwjbxx")
@Data
public class RchxKjlwjbxx implements Serializable {
    /**
     * 论文编号
     */
    @TableId(value = "lwbh", type = IdType.INPUT)
    private String lwbh;

    /**
     * 论文名称
     */
    @TableField(value = "lwmc")
    private String lwmc;

    /**
     * 论文摘要
     */
    @TableField(value = "lwzy")
    private String lwzy;

    /**
     * 单位号
     */
    @TableField(value = "dwh")
    private String dwh;

    /**
     * 单位名称
     */
    @TableField(value = "dwmc")
    private String dwmc;

    /**
     * 教研室编号
     */
    @TableField(value = "jysbh")
    private String jysbh;

    /**
     * 教研室名称
     */
    @TableField(value = "jysmc")
    private String jysmc;

    /**
     * 项目来源码
     */
    @TableField(value = "xmlym")
    private String xmlym;

    /**
     * 项目来源
     */
    @TableField(value = "xmly")
    private String xmly;

    /**
     * 学科分类码
     */
    @TableField(value = "xkflm")
    private String xkflm;

    /**
     * 学科分类
     */
    @TableField(value = "xkfl")
    private String xkfl;

    /**
     * 学科类别码
     */
    @TableField(value = "xklbm")
    private String xklbm;

    /**
     * 学科类别
     */
    @TableField(value = "xklb")
    private String xklb;

    /**
     * 论文形式码
     */
    @TableField(value = "lwxsm")
    private String lwxsm;

    /**
     * 论文形式
     */
    @TableField(value = "lwxs")
    private String lwxs;

    /**
     * 发表范围码
     */
    @TableField(value = "fbfwm")
    private String fbfwm;

    /**
     * 发表范围
     */
    @TableField(value = "fbfw")
    private String fbfw;

    /**
     * 发表日期
     */
    @TableField(value = "fbrq")
    private String fbrq;

    /**
     * 论文历史收录编号
     */
    @TableField(value = "lwlsslbh")
    private String lwlsslbh;

    /**
     * 刊物编号
     */
    @TableField(value = "kwbh")
    private String kwbh;

    /**
     * 刊物名称
     */
    @TableField(value = "kwmc")
    private String kwmc;

    /**
     * 刊物类型码
     */
    @TableField(value = "kwlxm")
    private String kwlxm;

    /**
     * 刊物类型
     */
    @TableField(value = "kwlx")
    private String kwlx;

    /**
     * 刊物级别码
     */
    @TableField(value = "kwjbm")
    private String kwjbm;

    /**
     * 出版单位
     */
    @TableField(value = "cbdw")
    private String cbdw;

    /**
     * ISSN
     */
    @TableField(value = "issn")
    private String issn;

    /**
     * CN号
     */
    @TableField(value = "cnh")
    private String cnh;

    /**
     * 
     */
    @TableField(value = "zs")
    private String zs;

    /**
     * 是否译文
     */
    @TableField(value = "sfyw")
    private String sfyw;

    /**
     * 著作人数
     */
    @TableField(value = "zzrs")
    private String zzrs;

    /**
     * 第一作者编号
     */
    @TableField(value = "dyzzbh")
    private String dyzzbh;

    /**
     * 第一作者姓名
     */
    @TableField(value = "dyzzxm")
    private String dyzzxm;

    /**
     * 所属单位
     */
    @TableField(value = "ssdw")
    private String ssdw;

    /**
     * 通讯作者编号
     */
    @TableField(value = "txzzbh")
    private String txzzbh;

    /**
     * 通讯作者姓名
     */
    @TableField(value = "txzzxm")
    private String txzzxm;

    /**
     * 创建者编号
     */
    @TableField(value = "cjzbh")
    private String cjzbh;

    /**
     * 创建者姓名
     */
    @TableField(value = "cjzxm")
    private String cjzxm;

    /**
     * 创建时间
     */
    @TableField(value = "cjsj")
    private String cjsj;

    /**
     * 审核状态码
     */
    @TableField(value = "shztm")
    private String shztm;

    /**
     * 审核状态
     */
    @TableField(value = "shzt")
    private String shzt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RchxKjlwjbxx other = (RchxKjlwjbxx) that;
        return (this.getLwbh() == null ? other.getLwbh() == null : this.getLwbh().equals(other.getLwbh()))
            && (this.getLwmc() == null ? other.getLwmc() == null : this.getLwmc().equals(other.getLwmc()))
            && (this.getLwzy() == null ? other.getLwzy() == null : this.getLwzy().equals(other.getLwzy()))
            && (this.getDwh() == null ? other.getDwh() == null : this.getDwh().equals(other.getDwh()))
            && (this.getDwmc() == null ? other.getDwmc() == null : this.getDwmc().equals(other.getDwmc()))
            && (this.getJysbh() == null ? other.getJysbh() == null : this.getJysbh().equals(other.getJysbh()))
            && (this.getJysmc() == null ? other.getJysmc() == null : this.getJysmc().equals(other.getJysmc()))
            && (this.getXmlym() == null ? other.getXmlym() == null : this.getXmlym().equals(other.getXmlym()))
            && (this.getXmly() == null ? other.getXmly() == null : this.getXmly().equals(other.getXmly()))
            && (this.getXkflm() == null ? other.getXkflm() == null : this.getXkflm().equals(other.getXkflm()))
            && (this.getXkfl() == null ? other.getXkfl() == null : this.getXkfl().equals(other.getXkfl()))
            && (this.getXklbm() == null ? other.getXklbm() == null : this.getXklbm().equals(other.getXklbm()))
            && (this.getXklb() == null ? other.getXklb() == null : this.getXklb().equals(other.getXklb()))
            && (this.getLwxsm() == null ? other.getLwxsm() == null : this.getLwxsm().equals(other.getLwxsm()))
            && (this.getLwxs() == null ? other.getLwxs() == null : this.getLwxs().equals(other.getLwxs()))
            && (this.getFbfwm() == null ? other.getFbfwm() == null : this.getFbfwm().equals(other.getFbfwm()))
            && (this.getFbfw() == null ? other.getFbfw() == null : this.getFbfw().equals(other.getFbfw()))
            && (this.getFbrq() == null ? other.getFbrq() == null : this.getFbrq().equals(other.getFbrq()))
            && (this.getLwlsslbh() == null ? other.getLwlsslbh() == null : this.getLwlsslbh().equals(other.getLwlsslbh()))
            && (this.getKwbh() == null ? other.getKwbh() == null : this.getKwbh().equals(other.getKwbh()))
            && (this.getKwmc() == null ? other.getKwmc() == null : this.getKwmc().equals(other.getKwmc()))
            && (this.getKwlxm() == null ? other.getKwlxm() == null : this.getKwlxm().equals(other.getKwlxm()))
            && (this.getKwlx() == null ? other.getKwlx() == null : this.getKwlx().equals(other.getKwlx()))
            && (this.getKwjbm() == null ? other.getKwjbm() == null : this.getKwjbm().equals(other.getKwjbm()))
            && (this.getCbdw() == null ? other.getCbdw() == null : this.getCbdw().equals(other.getCbdw()))
            && (this.getIssn() == null ? other.getIssn() == null : this.getIssn().equals(other.getIssn()))
            && (this.getCnh() == null ? other.getCnh() == null : this.getCnh().equals(other.getCnh()))
            && (this.getZs() == null ? other.getZs() == null : this.getZs().equals(other.getZs()))
            && (this.getSfyw() == null ? other.getSfyw() == null : this.getSfyw().equals(other.getSfyw()))
            && (this.getZzrs() == null ? other.getZzrs() == null : this.getZzrs().equals(other.getZzrs()))
            && (this.getDyzzbh() == null ? other.getDyzzbh() == null : this.getDyzzbh().equals(other.getDyzzbh()))
            && (this.getDyzzxm() == null ? other.getDyzzxm() == null : this.getDyzzxm().equals(other.getDyzzxm()))
            && (this.getSsdw() == null ? other.getSsdw() == null : this.getSsdw().equals(other.getSsdw()))
            && (this.getTxzzbh() == null ? other.getTxzzbh() == null : this.getTxzzbh().equals(other.getTxzzbh()))
            && (this.getTxzzxm() == null ? other.getTxzzxm() == null : this.getTxzzxm().equals(other.getTxzzxm()))
            && (this.getCjzbh() == null ? other.getCjzbh() == null : this.getCjzbh().equals(other.getCjzbh()))
            && (this.getCjzxm() == null ? other.getCjzxm() == null : this.getCjzxm().equals(other.getCjzxm()))
            && (this.getCjsj() == null ? other.getCjsj() == null : this.getCjsj().equals(other.getCjsj()))
            && (this.getShztm() == null ? other.getShztm() == null : this.getShztm().equals(other.getShztm()))
            && (this.getShzt() == null ? other.getShzt() == null : this.getShzt().equals(other.getShzt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getLwbh() == null) ? 0 : getLwbh().hashCode());
        result = prime * result + ((getLwmc() == null) ? 0 : getLwmc().hashCode());
        result = prime * result + ((getLwzy() == null) ? 0 : getLwzy().hashCode());
        result = prime * result + ((getDwh() == null) ? 0 : getDwh().hashCode());
        result = prime * result + ((getDwmc() == null) ? 0 : getDwmc().hashCode());
        result = prime * result + ((getJysbh() == null) ? 0 : getJysbh().hashCode());
        result = prime * result + ((getJysmc() == null) ? 0 : getJysmc().hashCode());
        result = prime * result + ((getXmlym() == null) ? 0 : getXmlym().hashCode());
        result = prime * result + ((getXmly() == null) ? 0 : getXmly().hashCode());
        result = prime * result + ((getXkflm() == null) ? 0 : getXkflm().hashCode());
        result = prime * result + ((getXkfl() == null) ? 0 : getXkfl().hashCode());
        result = prime * result + ((getXklbm() == null) ? 0 : getXklbm().hashCode());
        result = prime * result + ((getXklb() == null) ? 0 : getXklb().hashCode());
        result = prime * result + ((getLwxsm() == null) ? 0 : getLwxsm().hashCode());
        result = prime * result + ((getLwxs() == null) ? 0 : getLwxs().hashCode());
        result = prime * result + ((getFbfwm() == null) ? 0 : getFbfwm().hashCode());
        result = prime * result + ((getFbfw() == null) ? 0 : getFbfw().hashCode());
        result = prime * result + ((getFbrq() == null) ? 0 : getFbrq().hashCode());
        result = prime * result + ((getLwlsslbh() == null) ? 0 : getLwlsslbh().hashCode());
        result = prime * result + ((getKwbh() == null) ? 0 : getKwbh().hashCode());
        result = prime * result + ((getKwmc() == null) ? 0 : getKwmc().hashCode());
        result = prime * result + ((getKwlxm() == null) ? 0 : getKwlxm().hashCode());
        result = prime * result + ((getKwlx() == null) ? 0 : getKwlx().hashCode());
        result = prime * result + ((getKwjbm() == null) ? 0 : getKwjbm().hashCode());
        result = prime * result + ((getCbdw() == null) ? 0 : getCbdw().hashCode());
        result = prime * result + ((getIssn() == null) ? 0 : getIssn().hashCode());
        result = prime * result + ((getCnh() == null) ? 0 : getCnh().hashCode());
        result = prime * result + ((getZs() == null) ? 0 : getZs().hashCode());
        result = prime * result + ((getSfyw() == null) ? 0 : getSfyw().hashCode());
        result = prime * result + ((getZzrs() == null) ? 0 : getZzrs().hashCode());
        result = prime * result + ((getDyzzbh() == null) ? 0 : getDyzzbh().hashCode());
        result = prime * result + ((getDyzzxm() == null) ? 0 : getDyzzxm().hashCode());
        result = prime * result + ((getSsdw() == null) ? 0 : getSsdw().hashCode());
        result = prime * result + ((getTxzzbh() == null) ? 0 : getTxzzbh().hashCode());
        result = prime * result + ((getTxzzxm() == null) ? 0 : getTxzzxm().hashCode());
        result = prime * result + ((getCjzbh() == null) ? 0 : getCjzbh().hashCode());
        result = prime * result + ((getCjzxm() == null) ? 0 : getCjzxm().hashCode());
        result = prime * result + ((getCjsj() == null) ? 0 : getCjsj().hashCode());
        result = prime * result + ((getShztm() == null) ? 0 : getShztm().hashCode());
        result = prime * result + ((getShzt() == null) ? 0 : getShzt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", lwbh=").append(lwbh);
        sb.append(", lwmc=").append(lwmc);
        sb.append(", lwzy=").append(lwzy);
        sb.append(", dwh=").append(dwh);
        sb.append(", dwmc=").append(dwmc);
        sb.append(", jysbh=").append(jysbh);
        sb.append(", jysmc=").append(jysmc);
        sb.append(", xmlym=").append(xmlym);
        sb.append(", xmly=").append(xmly);
        sb.append(", xkflm=").append(xkflm);
        sb.append(", xkfl=").append(xkfl);
        sb.append(", xklbm=").append(xklbm);
        sb.append(", xklb=").append(xklb);
        sb.append(", lwxsm=").append(lwxsm);
        sb.append(", lwxs=").append(lwxs);
        sb.append(", fbfwm=").append(fbfwm);
        sb.append(", fbfw=").append(fbfw);
        sb.append(", fbrq=").append(fbrq);
        sb.append(", lwlsslbh=").append(lwlsslbh);
        sb.append(", kwbh=").append(kwbh);
        sb.append(", kwmc=").append(kwmc);
        sb.append(", kwlxm=").append(kwlxm);
        sb.append(", kwlx=").append(kwlx);
        sb.append(", kwjbm=").append(kwjbm);
        sb.append(", cbdw=").append(cbdw);
        sb.append(", issn=").append(issn);
        sb.append(", cnh=").append(cnh);
        sb.append(", zs=").append(zs);
        sb.append(", sfyw=").append(sfyw);
        sb.append(", zzrs=").append(zzrs);
        sb.append(", dyzzbh=").append(dyzzbh);
        sb.append(", dyzzxm=").append(dyzzxm);
        sb.append(", ssdw=").append(ssdw);
        sb.append(", txzzbh=").append(txzzbh);
        sb.append(", txzzxm=").append(txzzxm);
        sb.append(", cjzbh=").append(cjzbh);
        sb.append(", cjzxm=").append(cjzxm);
        sb.append(", cjsj=").append(cjsj);
        sb.append(", shztm=").append(shztm);
        sb.append(", shzt=").append(shzt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}