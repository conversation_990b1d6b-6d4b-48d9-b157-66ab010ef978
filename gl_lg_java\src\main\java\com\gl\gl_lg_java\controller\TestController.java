package com.gl.gl_lg_java.controller;

import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.service.RchxJzgjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试控制器 - 专门用于数据统计和测试功能
 * 所有接口都以test开头，无需权限验证
 */
@RestController
@RequestMapping("/api/test")
@Slf4j
public class TestController {

    @Autowired
    private RchxJzgjbxxService jzgjbxxService;

    /**
     * 测试接口 - 查询所有部门统计
     */
    @GetMapping("/departments")
    public Result<Map<String, Integer>> getDepartments() {
        try {
            log.info("查询所有部门统计");
            List<RchxJzgjbxx> allRecords = jzgjbxxService.list();
            
            // 统计每个部门的人数
            Map<String, Integer> departmentCounts = new HashMap<>();
            for (RchxJzgjbxx record : allRecords) {
                String bm = record.getBm();
                String key = (bm == null || bm.trim().isEmpty()) ? "NULL或空" : bm;
                departmentCounts.put(key, departmentCounts.getOrDefault(key, 0) + 1);
            }
            
            log.info("部门统计结果: {}", departmentCounts);
            return Result.success(departmentCounts);
        } catch (Exception e) {
            log.error("查询部门统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口 - 查询所有当前状态统计
     */
    @GetMapping("/dqzt-status")
    public Result<Map<String, Integer>> getDqztStatus() {
        try {
            log.info("查询所有当前状态统计");
            List<RchxJzgjbxx> allRecords = jzgjbxxService.list();
            
            // 统计每个当前状态的人数
            Map<String, Integer> dqztCounts = new HashMap<>();
            for (RchxJzgjbxx record : allRecords) {
                String dqzt = record.getDqzt();
                String key = (dqzt == null || dqzt.trim().isEmpty()) ? "NULL或空" : dqzt;
                dqztCounts.put(key, dqztCounts.getOrDefault(key, 0) + 1);
            }
            
            log.info("当前状态统计结果: {}", dqztCounts);
            return Result.success(dqztCounts);
        } catch (Exception e) {
            log.error("查询当前状态统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口 - 查询所有权限统计
     */
    @GetMapping("/permissions")
    public Result<Map<String, Integer>> getPermissions() {
        try {
            log.info("查询所有权限统计");
            List<RchxJzgjbxx> allRecords = jzgjbxxService.list();
            
            // 统计每个权限的人数
            Map<String, Integer> permissionCounts = new HashMap<>();
            for (RchxJzgjbxx record : allRecords) {
                String qx = record.getQx();
                String key = (qx == null || qx.trim().isEmpty()) ? "NULL或空" : qx;
                permissionCounts.put(key, permissionCounts.getOrDefault(key, 0) + 1);
            }
            
            log.info("权限统计结果: {}", permissionCounts);
            return Result.success(permissionCounts);
        } catch (Exception e) {
            log.error("查询权限统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口 - 查询所有职称统计
     */
    @GetMapping("/titles")
    public Result<Map<String, Integer>> getTitles() {
        try {
            log.info("查询所有职称统计");
            List<RchxJzgjbxx> allRecords = jzgjbxxService.list();
            
            // 统计每个职称的人数
            Map<String, Integer> titleCounts = new HashMap<>();
            for (RchxJzgjbxx record : allRecords) {
                String zc = record.getZc();
                String key = (zc == null || zc.trim().isEmpty()) ? "NULL或空" : zc;
                titleCounts.put(key, titleCounts.getOrDefault(key, 0) + 1);
            }
            
            log.info("职称统计结果: {}", titleCounts);
            return Result.success(titleCounts);
        } catch (Exception e) {
            log.error("查询职称统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口 - 数据库连接测试
     */
    @GetMapping("/db-connection")
    public Result<Map<String, Object>> testDbConnection() {
        try {
            log.info("测试数据库连接");
            long totalCount = jzgjbxxService.count();

            Map<String, Object> result = new HashMap<>();
            result.put("status", "连接正常");
            result.put("totalRecords", totalCount);
            result.put("timestamp", System.currentTimeMillis());
            result.put("hotReload", "热更新测试成功！");  // 添加这行测试热更新

            log.info("数据库连接测试成功，总记录数: {}", totalCount);
            return Result.success(result);
        } catch (Exception e) {
            log.error("数据库连接测试失败: {}", e.getMessage());
            return Result.error("数据库连接失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门选项列表 - 用于前端筛选
     */
    @GetMapping("/department-options")
    public Result<List<Map<String, Object>>> getDepartmentOptions() {
        try {
            log.info("获取部门选项列表");
            List<RchxJzgjbxx> allRecords = jzgjbxxService.list();

            // 统计每个部门的人数
            Map<String, Integer> departmentCounts = new HashMap<>();
            for (RchxJzgjbxx record : allRecords) {
                String bm = record.getBm();
                if (bm != null && !bm.trim().isEmpty()) {
                    departmentCounts.put(bm, departmentCounts.getOrDefault(bm, 0) + 1);
                }
            }

            // 转换为前端需要的格式，按人数降序排列
            List<Map<String, Object>> options = new ArrayList<>();
            departmentCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> {
                    Map<String, Object> option = new HashMap<>();
                    option.put("value", entry.getKey());
                    option.put("label", entry.getKey() + " (" + entry.getValue() + "人)");
                    option.put("count", entry.getValue());
                    options.add(option);
                });

            log.info("部门选项列表获取成功，共{}个部门", options.size());
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取部门选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前状态选项列表 - 用于前端筛选
     */
    @GetMapping("/dqzt-options")
    public Result<List<Map<String, Object>>> getDqztOptions() {
        try {
            log.info("获取当前状态选项列表");
            List<RchxJzgjbxx> allRecords = jzgjbxxService.list();

            // 统计每个状态的人数
            Map<String, Integer> dqztCounts = new HashMap<>();
            for (RchxJzgjbxx record : allRecords) {
                String dqzt = record.getDqzt();
                if (dqzt != null && !dqzt.trim().isEmpty()) {
                    dqztCounts.put(dqzt, dqztCounts.getOrDefault(dqzt, 0) + 1);
                }
            }

            // 转换为前端需要的格式，按人数降序排列
            List<Map<String, Object>> options = new ArrayList<>();
            dqztCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> {
                    Map<String, Object> option = new HashMap<>();
                    option.put("value", entry.getKey());
                    option.put("label", entry.getKey() + " (" + entry.getValue() + "人)");
                    option.put("count", entry.getValue());
                    options.add(option);
                });

            log.info("当前状态选项列表获取成功，共{}个状态", options.size());
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取当前状态选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }
}
