package com.gl.gl_lg_java.service.impl;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.AnalysisResult;
import com.gl.gl_lg_java.dto.AnalysisSummary;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.matcher.ProjectMatcher;
import com.gl.gl_lg_java.matcher.ProjectMatcherFactory;
import com.gl.gl_lg_java.service.RchxJzgjbxxService;
import com.gl.gl_lg_java.service.SchoolProjectMatchingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 校级项目匹配分析服务实现
 */
@Service
@Slf4j
public class SchoolProjectMatchingServiceImpl implements SchoolProjectMatchingService {

    @Autowired
    private ProjectMatcherFactory matcherFactory;

    @Autowired
    private RchxJzgjbxxService teacherService;

    @Override
    public AnalysisResult analyzeProjectMatching(String projectCode, AnalysisParams params) {
        log.info("开始执行校级项目匹配分析: projectCode={}, params={}", projectCode, params);

        String analysisId = generateAnalysisId();

        try {
            // 1. 验证项目代码
            if (!matcherFactory.isSupported(projectCode)) {
                throw new IllegalArgumentException("不支持的项目类型: " + projectCode);
            }

            // 2. 获取项目匹配器
            ProjectMatcher matcher = matcherFactory.getMatcher(projectCode);

            // 3. 获取所有教师
            List<RchxJzgjbxx> teachers = teacherService.list();
            log.info("获取到{}位教师信息", teachers.size());

            // 4. 执行匹配分析
            List<MatchResult> results = new ArrayList<>();
            int analyzedCount = 0;
            int qualifiedCount = 0;
            double totalScore = 0.0;

            for (RchxJzgjbxx teacher : teachers) {
                try {
                    MatchResult result = matcher.matchTeacher(teacher, params);
                    analyzedCount++;

                    if (result.getMatchScore() != null) {
                        totalScore += result.getMatchScore();
                    }

                    if (result.getIsQualified() != null && result.getIsQualified()) {
                        qualifiedCount++;
                    }

                    // 只保留达到最低分数要求的结果
                    if (result.getMatchScore() != null &&
                            result.getMatchScore() >= params.getMinMatchScore()) {
                        results.add(result);
                    }

                } catch (Exception e) {
                    log.error("教师{}匹配分析失败", teacher.getZgh(), e);
                }
            }

            // 5. 排序和限制结果数量
            results.sort((a, b) -> Double.compare(
                    b.getMatchScore() != null ? b.getMatchScore() : 0.0,
                    a.getMatchScore() != null ? a.getMatchScore() : 0.0));

            if (results.size() > params.getMaxResults()) {
                results = results.subList(0, params.getMaxResults());
            }

            // 6. 计算统计信息
            Map<String, Integer> levelCounts = calculateLevelCounts(results);

            // 7. 构建分析结果
            AnalysisResult analysisResult = AnalysisResult.builder()
                    .analysisId(analysisId)
                    .projectCode(projectCode)
                    .projectName(matcher.getProjectName())
                    .status("COMPLETED")
                    .summary(AnalysisSummary.builder()
                            .totalTeachers(teachers.size())
                            .analyzedTeachers(analyzedCount)
                            .qualifiedTeachers(qualifiedCount)
                            .averageScore(analyzedCount > 0 ? totalScore / analyzedCount : 0.0)
                            .highMatchCount(levelCounts.get("HIGH"))
                            .mediumMatchCount(levelCounts.get("MEDIUM"))
                            .lowMatchCount(levelCounts.get("LOW"))
                            .build())
                    .results(results)
                    .analysisTime(LocalDateTime.now())
                    .completedTime(LocalDateTime.now())
                    .build();

            log.info("校级项目匹配分析完成: analysisId={}, 符合条件教师数={}",
                    analysisId, results.size());

            return analysisResult;

        } catch (Exception e) {
            log.error("校级项目匹配分析失败: projectCode={}", projectCode, e);
            throw new RuntimeException("匹配分析失败: " + e.getMessage(), e);
        }
    }

    @Override
    public AnalysisResult getAnalysisResults(String analysisId) {
        // 简化实现，实际应用中可能需要从缓存或数据库中获取
        log.warn("getAnalysisResults方法暂未实现持久化，analysisId: {}", analysisId);
        return null;
    }

    @Override
    public List<Map<String, Object>> getAllSchoolProjects() {
        Map<String, String> supportedProjects = matcherFactory.getSupportedProjects();

        List<Map<String, Object>> projects = new ArrayList<>();

        // 按照预定义顺序排列项目
        String[] projectOrder = { "CXTD", "FWGL", "LJRC", "PFXZ1", "PFXZ2", "PFXZ3", "QNRC", "SHFW" };

        for (String projectCode : projectOrder) {
            if (supportedProjects.containsKey(projectCode)) {
                Map<String, Object> project = new HashMap<>();
                project.put("projectCode", projectCode);
                project.put("projectName", supportedProjects.get(projectCode));
                project.put("isEnabled", true);
                project.put("category", getProjectCategory(projectCode));
                projects.add(project);
            }
        }

        return projects;
    }

    @Override
    public MatchResult analyzeSingleTeacher(String teacherZgh, String projectCode, AnalysisParams params) {
        log.info("开始分析单个教师匹配情况: teacherZgh={}, projectCode={}", teacherZgh, projectCode);

        try {
            // 1. 获取教师信息
            RchxJzgjbxx teacher = teacherService.getByZgh(teacherZgh);
            if (teacher == null) {
                throw new IllegalArgumentException("教师不存在: " + teacherZgh);
            }

            // 2. 获取项目匹配器
            ProjectMatcher matcher = matcherFactory.getMatcher(projectCode);

            // 3. 执行匹配分析
            MatchResult result = matcher.matchTeacher(teacher, params);

            log.info("单个教师匹配分析完成: teacherZgh={}, score={}",
                    teacherZgh, result.getMatchScore());

            return result;

        } catch (Exception e) {
            log.error("单个教师匹配分析失败: teacherZgh={}, projectCode={}",
                    teacherZgh, projectCode, e);
            throw new RuntimeException("匹配分析失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<MatchResult> analyzeTeacherForAllProjects(String teacherZgh, AnalysisParams params) {
        log.info("开始分析教师对所有项目的匹配情况: teacherZgh={}", teacherZgh);

        try {
            // 1. 获取教师信息
            RchxJzgjbxx teacher = teacherService.getByZgh(teacherZgh);
            if (teacher == null) {
                throw new IllegalArgumentException("教师不存在: " + teacherZgh);
            }

            // 2. 获取所有支持的项目
            Map<String, String> supportedProjects = matcherFactory.getSupportedProjects();

            // 3. 逐一分析每个项目
            List<MatchResult> results = new ArrayList<>();

            for (String projectCode : supportedProjects.keySet()) {
                try {
                    ProjectMatcher matcher = matcherFactory.getMatcher(projectCode);
                    MatchResult result = matcher.matchTeacher(teacher, params);
                    results.add(result);
                } catch (Exception e) {
                    log.error("教师{}对项目{}的匹配分析失败", teacherZgh, projectCode, e);
                }
            }

            // 4. 按匹配分数排序
            results.sort((a, b) -> Double.compare(
                    b.getMatchScore() != null ? b.getMatchScore() : 0.0,
                    a.getMatchScore() != null ? a.getMatchScore() : 0.0));

            log.info("教师对所有项目的匹配分析完成: teacherZgh={}, 项目数={}",
                    teacherZgh, results.size());

            return results;

        } catch (Exception e) {
            log.error("教师对所有项目的匹配分析失败: teacherZgh={}", teacherZgh, e);
            throw new RuntimeException("匹配分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成分析ID
     */
    private String generateAnalysisId() {
        return "ANALYSIS_" + System.currentTimeMillis() + "_" +
                String.format("%03d", new Random().nextInt(1000));
    }

    /**
     * 计算各匹配等级的数量
     */
    private Map<String, Integer> calculateLevelCounts(List<MatchResult> results) {
        Map<String, Integer> counts = new HashMap<>();
        counts.put("HIGH", 0);
        counts.put("MEDIUM", 0);
        counts.put("LOW", 0);
        counts.put("VERY_LOW", 0);

        for (MatchResult result : results) {
            String level = result.getMatchLevel();
            if (level != null) {
                counts.put(level, counts.getOrDefault(level, 0) + 1);
            }
        }

        return counts;
    }

    /**
     * 获取项目分类
     */
    private String getProjectCategory(String projectCode) {
        switch (projectCode) {
            case "CXTD":
                return "团队类";
            case "FWGL":
                return "管理类";
            case "LJRC":
                return "领军类";
            case "PFXZ1":
            case "PFXZ2":
            case "PFXZ3":
                return "屏风学者";
            case "QNRC":
                return "青年类";
            case "SHFW":
                return "服务类";
            default:
                return "其他";
        }
    }
}
