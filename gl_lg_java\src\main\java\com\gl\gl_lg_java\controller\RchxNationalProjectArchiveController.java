package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxNationalProjectArchive;
import com.gl.gl_lg_java.service.RchxNationalProjectArchiveService;
import lombok.extern.slf4j.Slf4j;
import com.gl.gl_lg_java.mapper.RchxNationalProjectArchiveMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 国家/省部级项目归档管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@RestController
@RequestMapping("/api/national-project-archive")
@Slf4j
public class RchxNationalProjectArchiveController {

    @Autowired
    private RchxNationalProjectArchiveService archiveService;

    // Mapper已移除，直接使用Service层

    /**
     * 分页查询项目归档
     */
    @PostMapping("/page")
    public Result<IPage<RchxNationalProjectArchive>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String projectCode,
            @RequestParam(required = false) String leaderName,
            @RequestParam(required = false) String archiveStatus,
            @RequestParam(required = false) Integer levelId,
            @RequestParam(required = false) Integer deptId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        try {
            Page<RchxNationalProjectArchive> page = new Page<>(current, size);

            // 使用QueryWrapper构建查询条件
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<RchxNationalProjectArchive> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();

            if (projectName != null && !projectName.isEmpty()) {
                queryWrapper.like("project_name", projectName);
            }
            if (projectCode != null && !projectCode.isEmpty()) {
                queryWrapper.like("project_code", projectCode);
            }
            if (leaderName != null && !leaderName.isEmpty()) {
                queryWrapper.like("project_leader_name", leaderName);
            }
            if (archiveStatus != null && !archiveStatus.isEmpty()) {
                queryWrapper.eq("archive_status", archiveStatus);
            }
            if (levelId != null) {
                queryWrapper.eq("level_id", levelId);
            }
            if (deptId != null) {
                queryWrapper.eq("dept_id", deptId);
            }
            if (startDate != null) {
                queryWrapper.ge("project_start_date", startDate);
            }
            if (endDate != null) {
                queryWrapper.le("project_end_date", endDate);
            }

            queryWrapper.orderByDesc("create_time");

            IPage<RchxNationalProjectArchive> result = archiveService.page(page, queryWrapper);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目归档失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目归档
     */
    @GetMapping("/{id}")
    public Result<RchxNationalProjectArchive> getById(@PathVariable Long id) {
        try {
            RchxNationalProjectArchive archive = archiveService.getById(id);
            if (archive != null) {
                return Result.success(archive);
            } else {
                return Result.error("项目归档不存在");
            }
        } catch (Exception e) {
            log.error("根据ID查询项目归档失败: {}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据归档编号查询
     */
    @GetMapping("/code/{archiveCode}")
    public Result<RchxNationalProjectArchive> getByArchiveCode(@PathVariable String archiveCode) {
        try {
            RchxNationalProjectArchive archive = archiveService.getByArchiveCode(archiveCode);
            if (archive != null) {
                return Result.success(archive);
            } else {
                return Result.error("项目归档不存在");
            }
        } catch (Exception e) {
            log.error("根据归档编号查询失败: {}", archiveCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目编号查询
     */
    @GetMapping("/project/{projectCode}")
    public Result<RchxNationalProjectArchive> getByProjectCode(@PathVariable String projectCode) {
        try {
            RchxNationalProjectArchive archive = archiveService.getByProjectCode(projectCode);
            if (archive != null) {
                return Result.success(archive);
            } else {
                return Result.error("项目归档不存在");
            }
        } catch (Exception e) {
            log.error("根据项目编号查询失败: {}", projectCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据归档状态查询
     */
    @GetMapping("/status/{archiveStatus}")
    public Result<List<RchxNationalProjectArchive>> getByArchiveStatus(@PathVariable String archiveStatus) {
        try {
            List<RchxNationalProjectArchive> archives = archiveService.listByArchiveStatus(archiveStatus);
            return Result.success(archives);
        } catch (Exception e) {
            log.error("根据归档状态查询失败: {}", archiveStatus, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目负责人查询
     */
    @GetMapping("/leader/{leaderZgh}")
    public Result<List<RchxNationalProjectArchive>> getByProjectLeader(@PathVariable String leaderZgh) {
        try {
            List<RchxNationalProjectArchive> archives = archiveService.listByProjectLeaderZgh(leaderZgh);
            return Result.success(archives);
        } catch (Exception e) {
            log.error("根据项目负责人查询失败: {}", leaderZgh, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询即将到期的项目
     */
    @GetMapping("/pending/deadline")
    public Result<List<RchxNationalProjectArchive>> getPendingByDeadline(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime deadline) {
        try {
            if (deadline == null) {
                deadline = LocalDateTime.now().plusDays(7); // 默认查询7天内到期的
            }
            List<RchxNationalProjectArchive> archives = archiveService.listPendingByDeadline(deadline);
            return Result.success(archives);
        } catch (Exception e) {
            log.error("查询即将到期的项目失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目归档概览
     */
    @GetMapping("/{id}/overview")
    public Result<Map<String, Object>> getArchiveOverview(@PathVariable Long id) {
        try {
            Map<String, Object> overview = archiveService.getArchiveOverview(id);
            return Result.success(overview);
        } catch (Exception e) {
            log.error("获取项目归档概览失败: {}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目归档
     */
    @PostMapping
    public Result<String> create(@RequestBody RchxNationalProjectArchive archive) {
        try {
            // 验证归档编号唯一性
            if (archive.getArchiveCode() != null &&
                    !archiveService.isArchiveCodeUnique(archive.getArchiveCode(), null)) {
                return Result.error("归档编号已存在");
            }

            // 验证项目编号唯一性
            if (!archiveService.isProjectCodeUnique(archive.getProjectCode(), null)) {
                return Result.error("项目编号已存在");
            }

            // 自动生成归档编号（如果未提供）
            if (archive.getArchiveCode() == null || archive.getArchiveCode().isEmpty()) {
                // 需要先查询级别类型
                String levelType = "NATIONAL"; // 默认值，实际应该根据levelId查询
                archive.setArchiveCode(archiveService.generateArchiveCode(levelType));
            }

            // 设置默认值
            if (archive.getArchiveStatus() == null) {
                archive.setArchiveStatus("PENDING");
            }
            if (archive.getSourceType() == null) {
                archive.setSourceType("MANUAL");
            }
            if (archive.getPriorityLevel() == null) {
                archive.setPriorityLevel("NORMAL");
            }
            if (archive.getConfidentialityLevel() == null) {
                archive.setConfidentialityLevel("PUBLIC");
            }
            if (archive.getReminderSent() == null) {
                archive.setReminderSent(false);
            }

            boolean success = archiveService.save(archive);
            if (success) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增项目归档失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目归档
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Long id, @RequestBody RchxNationalProjectArchive archive) {
        try {
            // 验证归档编号唯一性
            if (archive.getArchiveCode() != null &&
                    !archiveService.isArchiveCodeUnique(archive.getArchiveCode(), id)) {
                return Result.error("归档编号已存在");
            }

            // 验证项目编号唯一性
            if (archive.getProjectCode() != null &&
                    !archiveService.isProjectCodeUnique(archive.getProjectCode(), id)) {
                return Result.error("项目编号已存在");
            }

            archive.setId(id);
            boolean success = archiveService.updateById(archive);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新项目归档失败: {}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目归档
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        try {
            boolean success = archiveService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除项目归档失败: {}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除项目归档
     */
    @DeleteMapping("/batch")
    public Result<String> batchDelete(@RequestBody List<Long> ids) {
        try {
            boolean success = archiveService.removeByIds(ids);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除项目归档失败: {}", ids, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 提交归档
     */
    @PutMapping("/{id}/submit")
    public Result<String> submitArchive(@PathVariable Long id,
            @RequestParam String archiveBy,
            @RequestParam String archiveByName) {
        try {
            boolean success = archiveService.submitArchive(id, archiveBy, archiveByName);
            if (success) {
                return Result.success("提交归档成功");
            } else {
                return Result.error("提交归档失败");
            }
        } catch (Exception e) {
            log.error("提交归档失败: {}", id, e);
            return Result.error("提交归档失败: " + e.getMessage());
        }
    }

    /**
     * 审核归档
     */
    @PutMapping("/{id}/review")
    public Result<String> reviewArchive(@PathVariable Long id,
            @RequestParam String reviewerZgh,
            @RequestParam String reviewerName,
            @RequestParam String reviewComments,
            @RequestParam BigDecimal reviewScore,
            @RequestParam String archiveStatus) {
        try {
            boolean success = archiveService.reviewArchive(id, reviewerZgh, reviewerName,
                    reviewComments, reviewScore, archiveStatus);
            if (success) {
                return Result.success("审核完成");
            } else {
                return Result.error("审核失败");
            }
        } catch (Exception e) {
            log.error("审核归档失败: {}", id, e);
            return Result.error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 设置归档截止时间
     */
    @PutMapping("/{id}/deadline")
    public Result<String> setArchiveDeadline(@PathVariable Long id,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime deadline) {
        try {
            boolean success = archiveService.setArchiveDeadline(id, deadline);
            if (success) {
                return Result.success("设置截止时间成功");
            } else {
                return Result.error("设置截止时间失败");
            }
        } catch (Exception e) {
            log.error("设置归档截止时间失败: {}", id, e);
            return Result.error("设置截止时间失败: " + e.getMessage());
        }
    }

    /**
     * 发送归档提醒
     */
    @PutMapping("/{id}/reminder")
    public Result<String> sendArchiveReminder(@PathVariable Long id) {
        try {
            boolean success = archiveService.sendArchiveReminder(id);
            if (success) {
                return Result.success("发送提醒成功");
            } else {
                return Result.error("发送提醒失败");
            }
        } catch (Exception e) {
            log.error("发送归档提醒失败: {}", id, e);
            return Result.error("发送提醒失败: " + e.getMessage());
        }
    }

    /**
     * 统计各状态的项目数量
     */
    @GetMapping("/statistics/status")
    public Result<List<Map<String, Object>>> countByArchiveStatus() {
        try {
            List<Map<String, Object>> statistics = archiveService.countByArchiveStatus();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("统计归档状态数量失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 统计各级别的项目数量
     */
    @GetMapping("/statistics/level")
    public Result<List<Map<String, Object>>> countByLevel() {
        try {
            List<Map<String, Object>> statistics = archiveService.countByLevel();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("统计级别数量失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 统计各部门的项目数量
     */
    @GetMapping("/statistics/department")
    public Result<List<Map<String, Object>>> countByDepartment() {
        try {
            List<Map<String, Object>> statistics = archiveService.countByDepartment();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("统计部门数量失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取归档进度统计
     */
    @GetMapping("/statistics/progress")
    public Result<Map<String, Object>> getArchiveProgress() {
        try {
            Map<String, Object> progress = archiveService.getArchiveProgress();
            return Result.success(progress);
        } catch (Exception e) {
            log.error("获取归档进度统计失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }
}
