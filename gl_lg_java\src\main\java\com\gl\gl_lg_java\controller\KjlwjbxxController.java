package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gl.gl_lg_java.annotation.RequirePermission;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxKjlwjbxx;
import com.gl.gl_lg_java.dto.KjlwjbxxQueryDTO;
import com.gl.gl_lg_java.dto.KjlwStatisticsDTO;
import com.gl.gl_lg_java.service.RchxKjlwjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
/**
 * 科技论文基本信息控制器
 */
@RestController
@RequestMapping("/api/kjlwjbxx")
@Slf4j
public class KjlwjbxxController {
    
    @Autowired
    private RchxKjlwjbxxService kjlwjbxxService;
    
    /**
     * 根据论文编号查询
     */
    @GetMapping("/{lwbh}")
    @RequirePermission("教师")
    public Result<RchxKjlwjbxx> getByLwbh(@PathVariable String lwbh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            RchxKjlwjbxx result = kjlwjbxxService.getByLwbh(lwbh);
            
            // 权限控制：教师只能查看自己的论文
            if ("教师".equals(currentUserQx) && result != null && 
                !currentUserZgh.equals(result.getDyzzbh()) && 
                !currentUserZgh.equals(result.getTxzzbh())) {
                return Result.error(403, "权限不足，只能查看自己的论文");
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询科技论文信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询科技论文信息
     */
    @PostMapping("/page")
    @RequirePermission("教师")
    public Result<IPage<RchxKjlwjbxx>> getPage(@RequestBody KjlwjbxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的论文
            if ("教师".equals(currentUserQx)) {
                queryDTO.setDyzzbh(currentUserZgh);
                queryDTO.setTxzzbh(currentUserZgh);
            }
            
            IPage<RchxKjlwjbxx> result = kjlwjbxxService.pageByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询科技论文信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 多条件查询科技论文信息
     */
    @PostMapping("/list")
    @RequirePermission("教师")
    public Result<List<RchxKjlwjbxx>> getList(@RequestBody KjlwjbxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的论文
            if ("教师".equals(currentUserQx)) {
                queryDTO.setDyzzbh(currentUserZgh);
                queryDTO.setTxzzbh(currentUserZgh);
            }
            
            List<RchxKjlwjbxx> result = kjlwjbxxService.listByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询科技论文信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据论文名称模糊查询
     */
    @GetMapping("/name/{lwmc}")
    @RequirePermission("学院管理员")
    public Result<List<RchxKjlwjbxx>> getByLwmcLike(@PathVariable String lwmc) {
        try {
            List<RchxKjlwjbxx> result = kjlwjbxxService.listByLwmcLike(lwmc);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据论文名称查询失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据第一作者编号查询
     */
    @GetMapping("/author/{dyzzbh}")
    @RequirePermission("教师")
    public Result<List<RchxKjlwjbxx>> getByDyzzbh(@PathVariable String dyzzbh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的论文
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(dyzzbh)) {
                return Result.error(403, "权限不足，只能查看自己的论文");
            }
            
            List<RchxKjlwjbxx> result = kjlwjbxxService.listByDyzzbh(dyzzbh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据第一作者查询科技论文信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据通讯作者编号查询
     */
    @GetMapping("/correspondent/{txzzbh}")
    @RequirePermission("教师")
    public Result<List<RchxKjlwjbxx>> getByTxzzbh(@PathVariable String txzzbh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的论文
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(txzzbh)) {
                return Result.error(403, "权限不足，只能查看自己的论文");
            }
            
            List<RchxKjlwjbxx> result = kjlwjbxxService.listByTxzzbh(txzzbh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据通讯作者查询科技论文信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据刊物类型查询
     */
    @GetMapping("/type/{kwlx}")
    @RequirePermission("学院管理员")
    public Result<List<RchxKjlwjbxx>> getByKwlx(@PathVariable String kwlx) {
        try {
            List<RchxKjlwjbxx> result = kjlwjbxxService.listByKwlx(kwlx);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据刊物类型查询科技论文信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据审核状态查询
     */
    @GetMapping("/audit/{shzt}")
    @RequirePermission("评审")
    public Result<List<RchxKjlwjbxx>> getByShzt(@PathVariable String shzt) {
        try {
            List<RchxKjlwjbxx> result = kjlwjbxxService.listByShzt(shzt);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据审核状态查询科技论文信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增科技论文信息
     */
    @PostMapping
    @RequirePermission("学院管理员")
    public Result<String> create(@RequestBody RchxKjlwjbxx kjlwjbxx) {
        try {
            boolean success = kjlwjbxxService.saveKjlwjbxx(kjlwjbxx);
            if (success) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增科技论文信息失败: {}", e.getMessage());
            return Result.error("新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新科技论文信息
     */
    @PutMapping
    @RequirePermission("学院管理员")
    public Result<String> update(@RequestBody RchxKjlwjbxx kjlwjbxx, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能修改自己的论文
            if ("教师".equals(currentUserQx) && 
                !currentUserZgh.equals(kjlwjbxx.getDyzzbh()) && 
                !currentUserZgh.equals(kjlwjbxx.getTxzzbh())) {
                return Result.error(403, "权限不足，只能修改自己的论文");
            }
            
            boolean success = kjlwjbxxService.updateByLwbh(kjlwjbxx);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新科技论文信息失败: {}", e.getMessage());
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除科技论文信息
     */
    @DeleteMapping("/{lwbh}")
    @RequirePermission("系统管理员")
    public Result<String> delete(@PathVariable String lwbh) {
        try {
            boolean success = kjlwjbxxService.removeByLwbh(lwbh);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除科技论文信息失败: {}", e.getMessage());
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除科技论文信息
     */
    @DeleteMapping("/batch")
    @RequirePermission("系统管理员")
    public Result<String> deleteBatch(@RequestBody List<String> lwbhs) {
        try {
            boolean success = kjlwjbxxService.removeBatchByLwbhs(lwbhs);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除科技论文信息失败: {}", e.getMessage());
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量新增科技论文信息
     */
    @PostMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> createBatch(@RequestBody List<RchxKjlwjbxx> kjlwjbxxList) {
        try {
            boolean success = kjlwjbxxService.saveBatchKjlwjbxx(kjlwjbxxList);
            if (success) {
                return Result.success("批量新增成功");
            } else {
                return Result.error("批量新增失败");
            }
        } catch (Exception e) {
            log.error("批量新增科技论文信息失败: {}", e.getMessage());
            return Result.error("批量新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新科技论文信息
     */
    @PutMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> updateBatch(@RequestBody List<RchxKjlwjbxx> kjlwjbxxList) {
        try {
            boolean success = kjlwjbxxService.updateBatchKjlwjbxx(kjlwjbxxList);
            if (success) {
                return Result.success("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新科技论文信息失败: {}", e.getMessage());
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 按刊物类型统计
     */
    @GetMapping("/stats/kwlx")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByKwlx() {
        try {
            log.info("查询刊物类型统计");
            Map<String, Integer> stats = kjlwjbxxService.getStatsByKwlx();
            log.info("刊物类型统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询刊物类型统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按审核状态统计
     */
    @GetMapping("/stats/shzt")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByShzt() {
        try {
            log.info("查询审核状态统计");
            Map<String, Integer> stats = kjlwjbxxService.getStatsByShzt();
            log.info("审核状态统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询审核状态统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取刊物类型选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/kwlx-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getKwlxOptions() {
        try {
            List<Map<String, Object>> options = kjlwjbxxService.getKwlxOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取刊物类型选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核状态选项列表 - 用于前端筛选（快速版本，不统计数量）
     */
    @GetMapping("/shzt-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getShztOptions() {
        try {
            List<Map<String, Object>> options = kjlwjbxxService.getShztOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取审核状态选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 统计教师个人科技论文数量
     * @param zgh 职工号，可选参数。如果不传则查询当前登录用户
     */
    @GetMapping("/personal-count")
    @RequirePermission("教师")
    public Result<Map<String, Object>> getPersonalCount(
            @RequestParam(value = "zgh", required = false) String zgh,
            HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");

            // 如果没传zgh参数，则查询当前登录用户
            if (zgh == null || zgh.trim().isEmpty()) {
                zgh = currentUserZgh;
            }

            // 权限控制：普通教师只能查询自己的数据
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(zgh)) {
                return Result.error(403, "权限不足，只能查询自己的科技论文数量");
            }

            log.info("查询教师个人科技论文数量，职工号: {}", zgh);

            Long count = kjlwjbxxService.getPersonalCount(zgh);

            Map<String, Object> result = new HashMap<>();
            result.put("zgh", zgh);
            result.put("count", count);
            result.put("type", "科技论文");

            log.info("教师个人科技论文数量查询成功，职工号: {}, 数量: {}", zgh, count);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询教师个人科技论文数量失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取科技论文综合统计数据
     * 包括：单位统计、发布范围统计、学科类别统计、总体统计
     */
    @GetMapping("/statistics")
    public Result<KjlwStatisticsDTO.ComprehensiveStatistics> getStatistics() {
        try {
            log.info("开始获取科技论文统计数据");

            KjlwStatisticsDTO.ComprehensiveStatistics statistics = kjlwjbxxService.getComprehensiveStatistics();

            log.info("科技论文统计数据获取成功: 论文总数={}, 单位总数={}, 发布范围总数={}, 学科类别总数={}",
                    statistics.getTotalPapers(), statistics.getTotalUnits(),
                    statistics.getTotalPublishScopes(), statistics.getTotalSubjectCategories());

            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取科技论文统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取单位论文统计（单独接口）
     */
    @GetMapping("/statistics/units")
    public Result<List<KjlwStatisticsDTO.UnitStatistics>> getUnitStatistics() {
        try {
            log.info("开始获取单位论文统计数据");

            KjlwStatisticsDTO.ComprehensiveStatistics statistics = kjlwjbxxService.getComprehensiveStatistics();

            log.info("单位论文统计数据获取成功，共{}个单位", statistics.getUnitStatistics().size());

            return Result.success(statistics.getUnitStatistics());

        } catch (Exception e) {
            log.error("获取单位论文统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取发布范围统计（单独接口）
     */
    @GetMapping("/statistics/publish-scopes")
    public Result<List<KjlwStatisticsDTO.PublishScopeStatistics>> getPublishScopeStatistics() {
        try {
            log.info("开始获取发布范围统计数据");

            KjlwStatisticsDTO.ComprehensiveStatistics statistics = kjlwjbxxService.getComprehensiveStatistics();

            log.info("发布范围统计数据获取成功，共{}个范围", statistics.getPublishScopeStatistics().size());

            return Result.success(statistics.getPublishScopeStatistics());

        } catch (Exception e) {
            log.error("获取发布范围统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取学科类别统计（单独接口）
     */
    @GetMapping("/statistics/subject-categories")
    public Result<List<KjlwStatisticsDTO.SubjectCategoryStatistics>> getSubjectCategoryStatistics() {
        try {
            log.info("开始获取学科类别统计数据");

            KjlwStatisticsDTO.ComprehensiveStatistics statistics = kjlwjbxxService.getComprehensiveStatistics();

            log.info("学科类别统计数据获取成功，共{}个类别", statistics.getSubjectCategoryStatistics().size());

            return Result.success(statistics.getSubjectCategoryStatistics());

        } catch (Exception e) {
            log.error("获取学科类别统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }
}
