package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxKjlwjbxx;
import com.gl.gl_lg_java.dto.KjlwjbxxQueryDTO;
import com.gl.gl_lg_java.dto.KjlwStatisticsDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_kjlwjbxx(科技论文基本信息)】的数据库操作Service
 * @createDate 2025-07-18 11:11:41
 */
public interface RchxKjlwjbxxService extends IService<RchxKjlwjbxx> {

    /**
     * 根据论文编号查询
     */
    RchxKjlwjbxx getByLwbh(String lwbh);

    /**
     * 根据论文名称模糊查询
     */
    List<RchxKjlwjbxx> listByLwmcLike(String lwmc);

    /**
     * 根据第一作者编号查询
     */
    List<RchxKjlwjbxx> listByDyzzbh(String dyzzbh);

    /**
     * 根据第一作者姓名模糊查询
     */
    List<RchxKjlwjbxx> listByDyzzxmLike(String dyzzxm);

    /**
     * 根据通讯作者编号查询
     */
    List<RchxKjlwjbxx> listByTxzzbh(String txzzbh);

    /**
     * 根据刊物名称模糊查询
     */
    List<RchxKjlwjbxx> listByKwmcLike(String kwmc);

    /**
     * 根据刊物类型查询
     */
    List<RchxKjlwjbxx> listByKwlx(String kwlx);

    /**
     * 根据发表范围查询
     */
    List<RchxKjlwjbxx> listByFbfw(String fbfw);

    /**
     * 根据审核状态查询
     */
    List<RchxKjlwjbxx> listByShzt(String shzt);

    /**
     * 根据发表日期范围查询
     */
    List<RchxKjlwjbxx> listByFbrqBetween(String startDate, String endDate);

    /**
     * 多条件查询
     */
    List<RchxKjlwjbxx> listByMultiConditions(KjlwjbxxQueryDTO queryDTO);

    /**
     * 分页多条件查询
     */
    IPage<RchxKjlwjbxx> pageByMultiConditions(KjlwjbxxQueryDTO queryDTO);

    /**
     * 新增科技论文信息
     */
    boolean saveKjlwjbxx(RchxKjlwjbxx kjlwjbxx);

    /**
     * 根据编号更新科技论文信息
     */
    boolean updateByLwbh(RchxKjlwjbxx kjlwjbxx);

    /**
     * 根据编号删除科技论文信息
     */
    boolean removeByLwbh(String lwbh);

    /**
     * 批量删除科技论文信息
     */
    boolean removeBatchByLwbhs(List<String> lwbhs);

    /**
     * 批量新增科技论文信息
     */
    boolean saveBatchKjlwjbxx(List<RchxKjlwjbxx> kjlwjbxxList);

    /**
     * 批量更新科技论文信息
     */
    boolean updateBatchKjlwjbxx(List<RchxKjlwjbxx> kjlwjbxxList);

    /**
     * 按刊物类型统计
     */
    Map<String, Integer> getStatsByKwlx();

    /**
     * 按审核状态统计
     */
    Map<String, Integer> getStatsByShzt();

    /**
     * 获取刊物类型选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getKwlxOptions();

    /**
     * 获取审核状态选项列表（用于前端筛选）- 快速版本，不统计数量
     */
    List<Map<String, Object>> getShztOptions();

    /**
     * 统计教师个人科技论文数量
     */
    Long getPersonalCount(String zgh);

    /**
     * 获取科技论文综合统计数据
     */
    KjlwStatisticsDTO.ComprehensiveStatistics getComprehensiveStatistics();

    // ========== 人才匹配算法专用方法 ==========

    /**
     * 检查教师是否有高质量论文
     */
    boolean hasHighQualityPapers(String teacherZgh);

    /**
     * 统计教师论文总数
     */
    int countByTeacher(String teacherZgh);

    /**
     * 统计教师SCI/EI论文数量
     */
    int countSciEiByTeacher(String teacherZgh);

    /**
     * 统计教师第一作者论文数量
     */
    int countFirstAuthorByTeacher(String teacherZgh);
}
