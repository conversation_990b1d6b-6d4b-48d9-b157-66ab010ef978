# 教职工管理系统 API 接口文档

## 基础信息

- **基础URL**: `http://localhost:8080`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Token
- **Token有效期**: 24小时（1天）

## 统一响应格式

所有接口都返回统一的响应格式：

```json
{
    "code": 200,           // 状态码：200-成功，500-失败，401-未授权
    "message": "操作成功",  // 响应消息
    "data": {}            // 响应数据（可能为null）
}
```

## 认证说明

### JWT Token使用
- 除了登录接口外，所有API都需要在请求头中携带JWT Token
- Header格式：`Authorization: Bearer {token}`
- Token有效期：24小时
- Token过期后需要重新登录或使用刷新接口

### 权限说明
- **教师**：只能查看和修改自己的信息
- **评审**：只能进行评审相关操作
- **学院管理员**：只能管理本学院的教职工信息
- **系统管理员**：拥有所有权限，可以执行任何操作

---

# 认证接口

## 1. 用户登录

### 接口信息
- **URL**: `POST /api/auth/login`
- **描述**: 用户登录获取JWT Token，支持四种账号类型登录

### 支持的账号类型
1. **职工号**：如 `admin`、`T001`、`T002` 等
2. **手机号**：如 `***********`、`***********` 等
3. **身份证号**：如 `110101199001011234` 等
4. **邮箱**：如 `<EMAIL>`、`<EMAIL>` 等

### 请求参数
| 参数名 | 类型 | 位置 | 说明 | 是否必填 | 示例 |
|--------|------|------|------|----------|------|
| account | String | Body | 账号（支持职工号/手机号/身份证号/邮箱） | 是 | admin |
| password | String | Body | 密码 | 是 | admin |

### 请求体示例

#### 使用职工号登录
```json
{
    "account": "admin",
    "password": "admin"
}
```

#### 使用手机号登录
```json
{
    "account": "***********",
    "password": "123456"
}
```

#### 使用身份证号登录
```json
{
    "account": "110101199001011234",
    "password": "123456"
}
```

#### 使用邮箱登录
```json
{
    "account": "<EMAIL>",
    "password": "admin"
}
```

### 成功响应示例
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsInF4Ijoi57O757uf566h55CG5ZGYIiwiaWF0IjoxNzA5NTQ0MDAwLCJleHAiOjE3MDk2MzA0MDB9.xxx",
        "tokenType": "Bearer",
        "expiresIn": ********,
        "userInfo": {
            "zgh": "admin",
            "xm": "系统管理员",
            "sfzjh": "admin",
            "dh": "admin",
            "dzxx": "<EMAIL>",
            "zc": "系统管理员",
            "xb": "男",
            "pass": "admin",
            "bm": "系统管理部",
            "qx": "系统管理员"
        }
    }
}
```

### 失败响应示例

#### 账号为空
```json
{
    "code": 500,
    "message": "账号不能为空",
    "data": null
}
```

#### 密码为空
```json
{
    "code": 500,
    "message": "密码不能为空",
    "data": null
}
```

#### 账号或密码错误
```json
{
    "code": 500,
    "message": "账号或密码错误",
    "data": null
}
```

---

## 2. 获取当前用户信息

### 接口信息
- **URL**: `GET /api/auth/me`
- **描述**: 根据Token获取当前登录用户信息

### 请求参数
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| Authorization | String | Header | Bearer {token} | Bearer eyJhbGciOiJIUzUxMiJ9... |

### 成功响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "zgh": "admin",
        "xm": "系统管理员",
        "sfzjh": "admin",
        "dh": "admin",
        "dzxx": "<EMAIL>",
        "zc": "系统管理员",
        "xb": "男",
        "pass": "admin",
        "bm": "系统管理部",
        "qx": "系统管理员"
    }
}
```

### 失败响应示例
```json
{
    "code": 401,
    "message": "Token无效或已过期",
    "data": null
}
```

---

## 3. 刷新Token

### 接口信息
- **URL**: `POST /api/auth/refresh`
- **描述**: 刷新JWT Token，延长登录时间

### 请求参数
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| Authorization | String | Header | Bearer {token} | Bearer eyJhbGciOiJIUzUxMiJ9... |

### 成功响应示例
```json
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsInF4Ijoi57O757uf566h55CG5ZGYIiwiaWF0IjoxNzA5NTQ0MDAwLCJleHAiOjE3MDk2MzA0MDB9.xxx",
        "tokenType": "Bearer",
        "expiresIn": ********,
        "userInfo": {
            "zgh": "admin",
            "xm": "系统管理员",
            "qx": "系统管理员"
        }
    }
}
```

---

## 4. 用户登出

### 接口信息
- **URL**: `POST /api/auth/logout`
- **描述**: 用户登出，清除登录状态

### 请求参数
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| Authorization | String | Header | Bearer {token} | Bearer eyJhbGciOiJIUzUxMiJ9... |

### 成功响应示例
```json
{
    "code": 200,
    "message": "登出成功",
    "data": null
}
```

---

## 错误响应示例

### 参数错误
```json
{
    "code": 500,
    "message": "账号不能为空",
    "data": null
}
```

### Token相关错误
```json
{
    "code": 401,
    "message": "Token无效或已过期，请重新登录",
    "data": null
}
```

### 服务器错误
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "data": null
}
```

---

## 注意事项

### 1. 账号类型说明
- **职工号**：系统内唯一标识，如 `admin`、`T001` 等
- **手机号**：11位数字，如 `***********`
- **身份证号**：18位身份证号码，如 `110101199001011234`
- **邮箱**：标准邮箱格式，如 `<EMAIL>`

### 2. 登录流程
1. 用户输入账号（职工号/手机号/身份证号/邮箱）和密码
2. 系统自动识别账号类型并查找对应用户
3. 验证密码正确性
4. 生成JWT Token并返回用户信息
5. 前端保存Token并设置请求头

### 3. Token管理
- **存储位置**：localStorage
- **有效期**：24小时
- **使用方式**：请求头 `Authorization: Bearer {token}`
- **过期处理**：自动跳转登录页

### 4. 安全建议
- 生产环境建议使用HTTPS
- 定期刷新Token延长登录时间
- 登出时清除所有本地存储
- 密码传输前可考虑加密

### 5. 默认测试账号
- **职工号**：admin
- **密码**：admin
- **权限**：系统管理员
- **邮箱**：<EMAIL>

### 6. 权限类型说明
系统共有四种权限类型：
- **教师**：普通教师权限，只能查看个人信息
- **评审**：评审人员权限，只能进行评审操作
- **学院管理员**：学院管理权限，只能管理学院事务
- **系统管理员**：最高管理权限，拥有所有功能

权限采用独立制，每种权限只能访问自己对应的功能，只有系统管理员拥有所有权限。
