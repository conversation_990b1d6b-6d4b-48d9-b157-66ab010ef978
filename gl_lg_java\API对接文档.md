# 校级项目人才匹配系统 API对接文档

**基础URL**: `http://localhost:8080`

## 1. 获取项目列表

**接口**: `GET /api/school-project-matching/projects`

**响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "projectCode": "QNRC",
      "projectName": "青年人才未来工程项目",
      "category": "青年类",
      "isEnabled": true
    }
  ]
}
```

## 2. 执行匹配分析

**接口**: `POST /api/school-project-matching/analyze`

**入参**:
```json
{
  "projectCode": "QNRC",
  "analysisParams": {
    "timeRange": "5",
    "minMatchScore": 60,
    "maxResults": 100,
    "includeDetails": true
  }
}
```

**响应**:
```json
{
  "code": 200,
  "data": {
    "analysisId": "ANALYSIS_xxx",
    "projectCode": "QNRC",
    "projectName": "青年人才未来工程项目",
    "status": "COMPLETED",
    "summary": {
      "totalTeachers": 100,
      "qualifiedTeachers": 25,
      "averageScore": 72.5,
      "highMatchCount": 8,
      "mediumMatchCount": 12,
      "lowMatchCount": 5
    },
    "results": [
      {
        "teacherZgh": "202301001",
        "teacherName": "张三",
        "department": "计算机学院",
        "title": "副教授",
        "age": 35,
        "matchScore": 85.5,
        "matchLevel": "HIGH",
        "isQualified": true,
        "strengths": ["具有副教授职称"],
        "weaknesses": [],
        "recommendations": ["建议尽快准备申报材料"]
      }
    ]
  }
}
```

## 3. 快速分析

**接口**: `GET /api/school-project-matching/quick-analyze?projectCode=QNRC&minScore=60&maxResults=20`

**响应**: 同上

## 4. 单个教师分析

**接口**: `POST /api/school-project-matching/analyze/teacher/{teacherZgh}?projectCode=QNRC`

**入参**:
```json
{
  "timeRange": "5",
  "includeDetails": true
}
```

**响应**:
```json
{
  "code": 200,
  "data": {
    "teacherZgh": "202301001",
    "teacherName": "张三",
    "matchScore": 85.5,
    "matchLevel": "HIGH",
    "isQualified": true,
    "strengths": ["具有副教授职称"],
    "recommendations": ["建议尽快准备申报材料"]
  }
}
```

## 5. 教师全项目分析

**接口**: `POST /api/school-project-matching/analyze/teacher/{teacherZgh}/all-projects`

**入参**: 同上

**响应**: 返回该教师对所有8个项目的匹配结果数组

## 6. 获取分析结果

**接口**: `GET /api/school-project-matching/results/{analysisId}`

**响应**: 根据分析ID获取之前的分析结果，格式同接口2

## 项目代码对照

| 代码 | 项目名称 |
|-----|---------|
| QNRC | 青年人才未来工程项目 |
| LJRC | 领军人才引育计划项目 |
| CXTD | 创新团队支持计划项目 |
| FWGL | 服务管理人才支持计划项目 |
| PFXZ1 | 屏风学者引育计划项目-Ⅰ类 |
| PFXZ2 | 屏风学者引育计划项目-Ⅱ类 |
| PFXZ3 | 屏风学者引育计划项目-Ⅲ类 |
| SHFW | 社会服务人才培育计划项目 |

## 匹配等级

| 等级 | 分数 | 标识 |
|-----|------|------|
| 高匹配 | 85-100 | HIGH |
| 中匹配 | 70-84 | MEDIUM |
| 低匹配 | 60-69 | LOW |
| 极低匹配 | 0-59 | VERY_LOW |

## 错误码

- 200: 成功
- 400: 参数错误
- 404: 资源不存在
- 500: 服务器错误

## 前端调用示例

```javascript
// 获取项目列表
const projects = await fetch('/api/school-project-matching/projects')
  .then(res => res.json());

// 执行分析
const result = await fetch('/api/school-project-matching/analyze', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    projectCode: 'QNRC',
    analysisParams: { minMatchScore: 60, maxResults: 50 }
  })
}).then(res => res.json());

// 快速分析
const quickResult = await fetch('/api/school-project-matching/quick-analyze?projectCode=QNRC&minScore=60')
  .then(res => res.json());
```
