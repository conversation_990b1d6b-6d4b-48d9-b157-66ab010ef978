package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.domain.RchxSchoolProjectApplications;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 校级项目申报Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Mapper
public interface RchxSchoolProjectApplicationsMapper extends BaseMapper<RchxSchoolProjectApplications> {

    /**
     * 分页查询申报信息（包含关联信息）
     */
    @Select({
        "<script>",
        "SELECT a.*, ",
        "c.category_name, ",
        "c.funding_range_min, ",
        "c.funding_range_max, ",
        "d.dept_name ",
        "FROM t_rchx_school_project_applications a ",
        "LEFT JOIN t_rchx_school_project_categories c ON a.category_id = c.id ",
        "LEFT JOIN t_rchx_departments d ON a.dept_id = d.id ",
        "WHERE 1=1 ",
        "<if test='applicationCode != null and applicationCode != \"\"'>",
        "AND a.application_code LIKE CONCAT('%', #{applicationCode}, '%') ",
        "</if>",
        "<if test='projectName != null and projectName != \"\"'>",
        "AND a.project_name LIKE CONCAT('%', #{projectName}, '%') ",
        "</if>",
        "<if test='status != null and status != \"\"'>",
        "AND a.status = #{status} ",
        "</if>",
        "<if test='applicationStatus != null and applicationStatus != \"\"'>",
        "AND a.application_status = #{applicationStatus} ",
        "</if>",
        "<if test='categoryId != null'>",
        "AND a.category_id = #{categoryId} ",
        "</if>",
        "<if test='deptId != null'>",
        "AND a.dept_id = #{deptId} ",
        "</if>",
        "<if test='applicantZgh != null and applicantZgh != \"\"'>",
        "AND a.applicant_zgh = #{applicantZgh} ",
        "</if>",
        "<if test='applicationRound != null and applicationRound != \"\"'>",
        "AND a.application_round = #{applicationRound} ",
        "</if>",
        "ORDER BY a.create_time DESC",
        "</script>"
    })
    IPage<Map<String, Object>> selectPageWithDetails(
            Page<RchxSchoolProjectApplications> page,
            @Param("applicationCode") String applicationCode,
            @Param("projectName") String projectName,
            @Param("status") String status,
            @Param("applicationStatus") String applicationStatus,
            @Param("categoryId") Integer categoryId,
            @Param("deptId") Integer deptId,
            @Param("applicantZgh") String applicantZgh,
            @Param("applicationRound") String applicationRound
    );

    /**
     * 根据申报编号查询
     */
    @Select("SELECT * FROM t_rchx_school_project_applications " +
            "WHERE application_code = #{applicationCode}")
    RchxSchoolProjectApplications selectByApplicationCode(@Param("applicationCode") String applicationCode);

    /**
     * 检查申报编号是否存在
     */
    @Select("SELECT COUNT(*) FROM t_rchx_school_project_applications " +
            "WHERE application_code = #{applicationCode}")
    int countByApplicationCode(@Param("applicationCode") String applicationCode);

    /**
     * 根据申报人查询申报列表
     */
    @Select("SELECT * FROM t_rchx_school_project_applications " +
            "WHERE applicant_zgh = #{applicantZgh} " +
            "ORDER BY create_time DESC")
    List<RchxSchoolProjectApplications> selectByApplicant(@Param("applicantZgh") String applicantZgh);

    /**
     * 根据部门查询申报列表
     */
    @Select("SELECT * FROM t_rchx_school_project_applications " +
            "WHERE dept_id = #{deptId} " +
            "ORDER BY create_time DESC")
    List<RchxSchoolProjectApplications> selectByDepartment(@Param("deptId") Integer deptId);

    /**
     * 根据状态查询申报列表
     */
    @Select("SELECT * FROM t_rchx_school_project_applications " +
            "WHERE status = #{status} " +
            "ORDER BY create_time DESC")
    List<RchxSchoolProjectApplications> selectByStatus(@Param("status") String status);

    /**
     * 更新申报状态
     */
    @Update("UPDATE t_rchx_school_project_applications SET " +
            "status = #{status}, " +
            "application_status = #{applicationStatus}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int updateStatus(@Param("id") Long id,
                    @Param("status") String status,
                    @Param("applicationStatus") String applicationStatus,
                    @Param("updateBy") String updateBy);

    /**
     * 提交申报
     */
    @Update("UPDATE t_rchx_school_project_applications SET " +
            "status = 'SUBMITTED', " +
            "application_status = 'SUBMITTED', " +
            "submit_time = #{submitTime}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int submitApplication(@Param("id") Long id,
                         @Param("submitTime") LocalDateTime submitTime,
                         @Param("updateBy") String updateBy);

    /**
     * 开始审核
     */
    @Update("UPDATE t_rchx_school_project_applications SET " +
            "status = 'REVIEWING', " +
            "application_status = 'UNDER_REVIEW', " +
            "review_start_time = #{reviewStartTime}, " +
            "reviewer_zgh = #{reviewerZgh}, " +
            "reviewer_name = #{reviewerName}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int startReview(@Param("id") Long id,
                   @Param("reviewStartTime") LocalDateTime reviewStartTime,
                   @Param("reviewerZgh") String reviewerZgh,
                   @Param("reviewerName") String reviewerName,
                   @Param("updateBy") String updateBy);

    /**
     * 完成审核
     */
    @Update("UPDATE t_rchx_school_project_applications SET " +
            "status = #{status}, " +
            "application_status = #{applicationStatus}, " +
            "review_end_time = #{reviewEndTime}, " +
            "review_comments = #{reviewComments}, " +
            "review_score = #{reviewScore}, " +
            "approval_amount = #{approvalAmount}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int completeReview(@Param("id") Long id,
                      @Param("status") String status,
                      @Param("applicationStatus") String applicationStatus,
                      @Param("reviewEndTime") LocalDateTime reviewEndTime,
                      @Param("reviewComments") String reviewComments,
                      @Param("reviewScore") BigDecimal reviewScore,
                      @Param("approvalAmount") BigDecimal approvalAmount,
                      @Param("updateBy") String updateBy);

    /**
     * 获取申报统计信息
     */
    @Select({
        "SELECT ",
        "COUNT(*) AS total_applications, ",
        "SUM(CASE WHEN status = 'DRAFT' THEN 1 ELSE 0 END) AS draft_count, ",
        "SUM(CASE WHEN status = 'SUBMITTED' THEN 1 ELSE 0 END) AS submitted_count, ",
        "SUM(CASE WHEN status = 'REVIEWING' THEN 1 ELSE 0 END) AS reviewing_count, ",
        "SUM(CASE WHEN status = 'APPROVED' THEN 1 ELSE 0 END) AS approved_count, ",
        "SUM(CASE WHEN status = 'REJECTED' THEN 1 ELSE 0 END) AS rejected_count, ",
        "SUM(funding_amount) AS total_funding_requested, ",
        "SUM(approval_amount) AS total_funding_approved, ",
        "AVG(review_score) AS avg_review_score, ",
        "COUNT(DISTINCT applicant_zgh) AS unique_applicants ",
        "FROM t_rchx_school_project_applications"
    })
    Map<String, Object> getApplicationStatistics();

    /**
     * 获取部门申报统计
     */
    @Select({
        "SELECT ",
        "d.dept_name, ",
        "a.dept_id, ",
        "COUNT(*) AS application_count, ",
        "SUM(CASE WHEN a.status = 'APPROVED' THEN 1 ELSE 0 END) AS approved_count, ",
        "SUM(a.funding_amount) AS total_funding_requested, ",
        "SUM(a.approval_amount) AS total_funding_approved, ",
        "AVG(a.review_score) AS avg_review_score ",
        "FROM t_rchx_school_project_applications a ",
        "LEFT JOIN t_rchx_departments d ON a.dept_id = d.id ",
        "GROUP BY a.dept_id, d.dept_name ",
        "ORDER BY application_count DESC"
    })
    List<Map<String, Object>> getDepartmentStatistics();

    /**
     * 获取即将到期的申报
     */
    @Select("SELECT * FROM t_rchx_school_project_applications " +
            "WHERE status IN ('DRAFT', 'SUBMITTED') " +
            "AND application_deadline BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY) " +
            "ORDER BY application_deadline ASC")
    List<RchxSchoolProjectApplications> getApplicationsNearDeadline(@Param("days") Integer days);

    /**
     * 获取逾期未提交的申报
     */
    @Select("SELECT * FROM t_rchx_school_project_applications " +
            "WHERE status = 'DRAFT' " +
            "AND application_deadline < NOW() " +
            "ORDER BY application_deadline ASC")
    List<RchxSchoolProjectApplications> getOverdueApplications();

    /**
     * 根据申报批次查询
     */
    @Select("SELECT * FROM t_rchx_school_project_applications " +
            "WHERE application_round = #{applicationRound} " +
            "ORDER BY create_time DESC")
    List<RchxSchoolProjectApplications> selectByApplicationRound(@Param("applicationRound") String applicationRound);

    /**
     * 获取申报批次列表
     */
    @Select("SELECT DISTINCT application_round " +
            "FROM t_rchx_school_project_applications " +
            "WHERE application_round IS NOT NULL " +
            "ORDER BY application_round DESC")
    List<String> getApplicationRounds();
}
