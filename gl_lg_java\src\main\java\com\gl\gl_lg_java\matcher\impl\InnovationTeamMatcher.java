package com.gl.gl_lg_java.matcher.impl;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.matcher.ProjectMatcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;

/**
 * 创新团队支持计划项目匹配算法（简化版）
 */
@Component
@Slf4j
public class InnovationTeamMatcher implements ProjectMatcher {
    
    @Override
    public String getProjectCode() {
        return "CXTD";
    }
    
    @Override
    public String getProjectName() {
        return "创新团队支持计划项目";
    }
    
    @Override
    public MatchResult matchTeacher(RchxJzgjbxx teacher, AnalysisParams params) {
        log.info("开始分析教师{}的创新团队匹配度", teacher.getZgh());
        
        MatchResult result = MatchResult.builder()
            .teacherZgh(teacher.getZgh())
            .teacherName(teacher.getXm())
            .department(teacher.getBm())
            .title(teacher.getZc())
            .createdTime(LocalDateTime.now())
            .strengths(new ArrayList<>())
            .weaknesses(new ArrayList<>())
            .recommendations(new ArrayList<>())
            .build();
        
        try {
            // 简化的匹配逻辑
            double score = 70.0; // 基础分
            
            // 基于职称给分
            if (teacher.getZc() != null) {
                if (teacher.getZc().contains("教授")) {
                    score += 15.0;
                    result.getStrengths().add("具有教授职称");
                } else if (teacher.getZc().contains("副教授")) {
                    score += 10.0;
                    result.getStrengths().add("具有副教授职称");
                } else if (teacher.getZc().contains("讲师")) {
                    score += 5.0;
                    result.getStrengths().add("具有讲师职称");
                }
            }
            
            // 基于部门给分（团队协作能力推断）
            if (teacher.getBm() != null && !teacher.getBm().isEmpty()) {
                score += 5.0;
                result.getStrengths().add("有明确的部门归属");
            }
            
            result.setMatchScore(score);
            result.setIsQualified(score >= params.getMinMatchScore());
            result.calculateMatchLevel();
            
            // 生成建议
            if (result.getIsQualified()) {
                result.getRecommendations().add("建议加强团队协作能力展示");
                result.getRecommendations().add("完善团队建设相关材料");
            } else {
                result.getRecommendations().add("建议提升学术水平和团队协作经验");
            }
            
            log.info("教师{}创新团队匹配分析完成，得分: {}", teacher.getZgh(), score);
            
        } catch (Exception e) {
            log.error("教师{}创新团队匹配分析失败", teacher.getZgh(), e);
            result.setIsQualified(false);
            result.setReason("分析过程中发生错误: " + e.getMessage());
            result.setMatchScore(0.0);
            result.calculateMatchLevel();
        }
        
        return result;
    }
    
    @Override
    public boolean validateBasicConditions(RchxJzgjbxx teacher) {
        // 简化的基本条件检查
        return teacher.getXm() != null && !teacher.getXm().isEmpty() &&
               teacher.getZgh() != null && !teacher.getZgh().isEmpty();
    }
}
