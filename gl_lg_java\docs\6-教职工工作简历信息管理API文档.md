# 教职工工作简历信息管理API文档

## 基础信息
- **模块名称**: 教职工工作简历信息管理
- **基础路径**: `/api/jzggzjlxx`
- **权限要求**: 需要JWT Token认证
- **数据格式**: JSON

## 统一响应格式

```json
{
    "code": 200,           // 状态码：200-成功，500-失败，401-未授权，403-权限不足
    "message": "操作成功",  // 响应消息
    "data": {}            // 响应数据（可能为null）
}
```

## 接口列表

### 1. 根据职工号查询工作简历

**接口地址**: `GET /api/jzggzjlxx/{zgh}`

**权限要求**: 教师+

**路径参数**:
- `zgh` (string, 必填): 职工号

**请求示例**:
```
GET /api/jzggzjlxx/T001
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": [
        {
            "zgh": "T001",
            "gzdw": "北京大学计算机学院",
            "gznr": "从事人工智能算法研究",
            "crzw": "副教授",
            "cszy": "计算机科学与技术",
            "gzzmr": "李教授",
            "gzszd": "中国北京",
            "gzqsrq": "2020-09-01",
            "gzjsrq": "2025-06-30",
            "cjsj": "2025-07-18 10:00:00",
            "gxsj": "2025-07-18 10:00:00"
        },
        {
            "zgh": "T001",
            "gzdw": "清华大学软件学院",
            "gznr": "从事软件工程教学",
            "crzw": "讲师",
            "cszy": "软件工程",
            "gzzmr": "王教授",
            "gzszd": "中国北京",
            "gzqsrq": "2018-03-01",
            "gzjsrq": "2020-08-31",
            "cjsj": "2025-07-18 10:00:00",
            "gxsj": "2025-07-18 10:00:00"
        }
    ]
}
```

### 2. 分页查询工作简历信息

**接口地址**: `POST /api/jzggzjlxx/page`

**权限要求**: 教师+

**请求体**:
```json
{
    "zgh": "T001",                  // 职工号（可选）
    "gzdw": "北京大学",             // 工作单位（可选）
    "gznr": "人工智能",             // 工作内容（可选）
    "crzw": "教授",                 // 曾任职务（可选）
    "cszy": "计算机",               // 从事专业（可选）
    "gzzmr": "李教授",              // 工作证明人（可选）
    "gzszd": "北京",                // 工作所在地（可选）
    "startDate": "2020-01-01",      // 工作起始日期开始（可选）
    "endDate": "2025-12-31",        // 工作起始日期结束（可选）
    "pageNum": 1,                   // 页码（默认1）
    "pageSize": 10,                 // 页大小（默认10）
    "orderBy": "gzqsrq",            // 排序字段（默认gzqsrq）
    "orderDirection": "desc"        // 排序方向（默认desc）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "records": [
            {
                "zgh": "T001",
                "gzdw": "北京大学计算机学院",
                "gznr": "从事人工智能算法研究",
                "crzw": "副教授",
                "cszy": "计算机科学与技术",
                "gzzmr": "李教授",
                "gzszd": "中国北京",
                "gzqsrq": "2020-09-01",
                "gzjsrq": "2025-06-30",
                "cjsj": "2025-07-18 10:00:00",
                "gxsj": "2025-07-18 10:00:00"
            }
        ],
        "total": 15,
        "size": 10,
        "current": 1,
        "pages": 2
    }
}
```

### 3. 多条件查询工作简历信息

**接口地址**: `POST /api/jzggzjlxx/list`

**权限要求**: 教师+

**请求体**: 同分页查询，但不返回分页信息

### 4. 根据工作单位模糊查询

**接口地址**: `GET /api/jzggzjlxx/company/{gzdw}`

**权限要求**: 学院管理员+

**路径参数**:
- `gzdw` (string, 必填): 工作单位关键字

### 5. 根据工作内容模糊查询

**接口地址**: `GET /api/jzggzjlxx/content/{gznr}`

**权限要求**: 学院管理员+

**路径参数**:
- `gznr` (string, 必填): 工作内容关键字

### 6. 根据曾任职务模糊查询

**接口地址**: `GET /api/jzggzjlxx/position/{crzw}`

**权限要求**: 学院管理员+

**路径参数**:
- `crzw` (string, 必填): 曾任职务关键字

### 7. 根据从事专业模糊查询

**接口地址**: `GET /api/jzggzjlxx/major/{cszy}`

**权限要求**: 学院管理员+

**路径参数**:
- `cszy` (string, 必填): 从事专业关键字

### 8. 根据工作所在地查询

**接口地址**: `GET /api/jzggzjlxx/location/{gzszd}`

**权限要求**: 学院管理员+

**路径参数**:
- `gzszd` (string, 必填): 工作所在地

### 9. 新增工作简历信息

**接口地址**: `POST /api/jzggzjlxx`

**权限要求**: 教师+

**请求体**:
```json
{
    "zgh": "T002",                          // 职工号（必填）
    "gzdw": "中科院计算技术研究所",         // 工作单位（必填）
    "gznr": "从事大数据分析研究",           // 工作内容（必填）
    "crzw": "研究员",                       // 曾任职务（可选）
    "cszy": "数据科学",                     // 从事专业（可选）
    "gzzmr": "张研究员",                    // 工作证明人（可选）
    "gzszd": "中国北京",                    // 工作所在地（可选）
    "gzqsrq": "2021-01-01",                 // 工作起始日期（必填）
    "gzjsrq": "2024-12-31",                 // 工作结束日期（可选）
    "gzms": "负责大数据平台的设计与开发"    // 工作描述（可选）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "新增成功",
    "data": "新增成功"
}
```

### 10. 更新工作简历信息

**接口地址**: `PUT /api/jzggzjlxx`

**权限要求**: 教师+（只能修改自己的简历）

**请求体**: 同新增，需要包含完整的记录信息

### 11. 删除工作简历信息

**接口地址**: `DELETE /api/jzggzjlxx/{zgh}`

**权限要求**: 系统管理员

**路径参数**:
- `zgh` (string, 必填): 职工号

### 12. 批量新增工作简历信息

**接口地址**: `POST /api/jzggzjlxx/batch`

**权限要求**: 学院管理员+

**请求体**:
```json
[
    {
        "zgh": "T003",
        "gzdw": "华为技术有限公司",
        "gznr": "从事云计算技术研发",
        "crzw": "高级工程师",
        "cszy": "云计算",
        "gzzmr": "刘总监",
        "gzszd": "中国深圳",
        "gzqsrq": "2019-07-01",
        "gzjsrq": "2021-06-30"
    }
]
```

### 13. 批量更新工作简历信息

**接口地址**: `PUT /api/jzggzjlxx/batch`

**权限要求**: 学院管理员+

### 14. 批量删除工作简历信息

**接口地址**: `DELETE /api/jzggzjlxx/batch`

**权限要求**: 系统管理员

**请求体**:
```json
["T001", "T002", "T003"]
```

## 工作性质分类

### 教育行业
- **高等教育**: 大学、学院等高等教育机构
- **基础教育**: 中小学等基础教育机构
- **职业教育**: 职业技术学校、培训机构

### 科研机构
- **科研院所**: 中科院、社科院等科研机构
- **企业研发**: 企业内部研发部门
- **国际组织**: 国际科研合作机构

### 企业单位
- **国有企业**: 国有控股企业
- **民营企业**: 民营控股企业
- **外资企业**: 外商投资企业
- **合资企业**: 中外合资企业

### 政府机关
- **中央机关**: 国家部委等中央政府机关
- **地方政府**: 省市县各级政府机关
- **事业单位**: 政府下属事业单位

## 职务级别说明

### 教学职务
- **教授**: 正高级职称
- **副教授**: 副高级职称
- **讲师**: 中级职称
- **助教**: 初级职称

### 研究职务
- **研究员**: 正高级研究职称
- **副研究员**: 副高级研究职称
- **助理研究员**: 中级研究职称
- **研究实习员**: 初级研究职称

### 工程职务
- **正高级工程师**: 正高级工程职称
- **高级工程师**: 副高级工程职称
- **工程师**: 中级工程职称
- **助理工程师**: 初级工程职称

### 管理职务
- **正处级**: 正处级领导职务
- **副处级**: 副处级领导职务
- **正科级**: 正科级领导职务
- **副科级**: 副科级领导职务

## 专业领域分类

### 理工科
- **计算机科学与技术**
- **软件工程**
- **人工智能**
- **数据科学与大数据技术**
- **网络空间安全**

### 人文社科
- **教育学**
- **心理学**
- **管理学**
- **经济学**
- **法学**

## 权限说明

### 数据权限
- **教师**: 只能查看和修改自己的工作简历
- **学院管理员**: 可以查看本学院教职工的工作简历
- **系统管理员**: 可以管理所有工作简历

### 操作权限
- **查询**: 教师+（只能查看自己的）
- **新增**: 教师+（只能添加自己的）
- **修改**: 教师+（只能修改自己的）
- **删除**: 系统管理员

## 错误码说明

- **200**: 操作成功
- **401**: 未授权（Token无效或过期）
- **403**: 权限不足
- **500**: 服务器内部错误

## 注意事项

1. 所有接口都需要在请求头中携带JWT Token
2. 教师权限的用户只能操作自己的工作简历
3. 工作简历按时间倒序排列，最新的工作经历在前
4. 批量操作支持事务，失败时会回滚
5. 日期格式统一使用 yyyy-MM-dd
6. 工作简历可以有多条记录，记录不同时期的工作经历
7. 工作证明人信息用于验证工作经历的真实性
8. 支持国内外工作经历的记录
