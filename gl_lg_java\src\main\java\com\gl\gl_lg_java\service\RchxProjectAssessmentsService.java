package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectAssessments;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 项目考核管理Service接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface RchxProjectAssessmentsService extends IService<RchxProjectAssessments> {

    /**
     * 分页查询项目考核信息
     * 
     * @param page 分页参数
     * @param projectCode 项目编号
     * @param projectName 项目名称
     * @param assessmentType 考核类型
     * @param assessmentStatus 考核状态
     * @param projectLeaderZgh 项目负责人职工号
     * @param assessorZgh 考核人职工号
     * @param assessmentResult 考核结果
     * @return 分页结果
     */
    IPage<RchxProjectAssessments> pageQuery(Page<RchxProjectAssessments> page,
                                            String projectCode,
                                            String projectName,
                                            String assessmentType,
                                            String assessmentStatus,
                                            String projectLeaderZgh,
                                            String assessorZgh,
                                            String assessmentResult);

    /**
     * 根据项目ID查询考核列表
     * 
     * @param projectId 项目ID
     * @return 考核列表
     */
    List<RchxProjectAssessments> getByProjectId(Long projectId);

    /**
     * 根据项目编号查询考核列表
     * 
     * @param projectCode 项目编号
     * @return 考核列表
     */
    List<RchxProjectAssessments> getByProjectCode(String projectCode);

    /**
     * 根据考核类型查询考核列表
     * 
     * @param assessmentType 考核类型
     * @return 考核列表
     */
    List<RchxProjectAssessments> getByAssessmentType(String assessmentType);

    /**
     * 根据考核状态查询考核列表
     * 
     * @param assessmentStatus 考核状态
     * @return 考核列表
     */
    List<RchxProjectAssessments> getByAssessmentStatus(String assessmentStatus);

    /**
     * 根据项目负责人查询考核列表
     * 
     * @param projectLeaderZgh 项目负责人职工号
     * @return 考核列表
     */
    List<RchxProjectAssessments> getByProjectLeader(String projectLeaderZgh);

    /**
     * 更新考核得分和结果
     * 
     * @param id 考核ID
     * @param assessmentScore 考核得分
     * @param assessmentResult 考核结果
     * @param assessmentComments 考核意见
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean updateAssessmentScore(Long id, BigDecimal assessmentScore, String assessmentResult, 
                                 String assessmentComments, String updateBy);

    /**
     * 更新考核状态
     * 
     * @param id 考核ID
     * @param assessmentStatus 考核状态
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean updateAssessmentStatus(Long id, String assessmentStatus, String updateBy);

    /**
     * 发送考核提醒
     * 
     * @param id 考核ID
     * @return 是否成功
     */
    boolean sendAssessmentReminder(Long id);

    /**
     * 获取考核统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getAssessmentStatistics();

    /**
     * 获取考核类型统计
     * 
     * @return 考核类型统计
     */
    List<Map<String, Object>> getAssessmentTypeStatistics();

    /**
     * 获取项目负责人考核统计
     * 
     * @return 项目负责人考核统计
     */
    List<Map<String, Object>> getProjectLeaderAssessmentStatistics();

    /**
     * 获取即将到期的考核任务
     * 
     * @param days 天数
     * @return 即将到期的考核任务列表
     */
    List<RchxProjectAssessments> getAssessmentsNearDeadline(Integer days);

    /**
     * 获取逾期的考核任务
     * 
     * @return 逾期的考核任务列表
     */
    List<RchxProjectAssessments> getOverdueAssessments();

    /**
     * 创建考核任务
     * 
     * @param assessment 考核信息
     * @return 是否成功
     */
    boolean createAssessment(RchxProjectAssessments assessment);

    /**
     * 更新考核任务
     * 
     * @param assessment 考核信息
     * @return 是否成功
     */
    boolean updateAssessment(RchxProjectAssessments assessment);

    /**
     * 删除考核任务
     * 
     * @param id 考核ID
     * @return 是否成功
     */
    boolean deleteAssessment(Long id);

    /**
     * 批量删除考核任务
     * 
     * @param ids 考核ID列表
     * @return 是否成功
     */
    boolean batchDeleteAssessments(List<Long> ids);

    /**
     * 根据考核编号查询
     * 
     * @param assessmentCode 考核编号
     * @return 考核信息
     */
    RchxProjectAssessments getByAssessmentCode(String assessmentCode);

    /**
     * 检查考核编号是否存在
     * 
     * @param assessmentCode 考核编号
     * @return 是否存在
     */
    boolean existsByAssessmentCode(String assessmentCode);

    /**
     * 生成考核编号
     * 
     * @param assessmentType 考核类型
     * @return 考核编号
     */
    String generateAssessmentCode(String assessmentType);

    /**
     * 获取项目的最新考核信息
     * 
     * @param projectId 项目ID
     * @return 最新考核信息
     */
    RchxProjectAssessments getLatestAssessmentByProjectId(Long projectId);

    /**
     * 开始考核
     * 
     * @param id 考核ID
     * @param assessorZgh 考核人职工号
     * @param assessorName 考核人姓名
     * @return 是否成功
     */
    boolean startAssessment(Long id, String assessorZgh, String assessorName);

    /**
     * 完成考核
     * 
     * @param id 考核ID
     * @param assessmentScore 考核得分
     * @param assessmentComments 考核意见
     * @param improvementSuggestions 改进建议
     * @param followUpActions 后续行动
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean completeAssessment(Long id, BigDecimal assessmentScore, String assessmentComments,
                              String improvementSuggestions, String followUpActions, String updateBy);

    /**
     * 取消考核
     * 
     * @param id 考核ID
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean cancelAssessment(Long id, String updateBy);

    /**
     * 导出考核列表
     * 
     * @param assessmentType 考核类型
     * @param assessmentStatus 考核状态
     * @param projectLeaderZgh 项目负责人职工号
     * @return 考核列表
     */
    List<RchxProjectAssessments> exportAssessmentList(String assessmentType, String assessmentStatus, String projectLeaderZgh);
}
