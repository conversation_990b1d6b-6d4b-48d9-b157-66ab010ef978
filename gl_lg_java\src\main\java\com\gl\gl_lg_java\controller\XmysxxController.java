package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gl.gl_lg_java.annotation.RequirePermission;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxXmysxx;
import com.gl.gl_lg_java.dto.XmysxxQueryDTO;
import com.gl.gl_lg_java.service.RchxXmysxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 项目预算信息控制器
 */
@RestController
@RequestMapping("/api/xmysxx")
@Slf4j
public class XmysxxController {
    
    @Autowired
    private RchxXmysxxService xmysxxService;
    
    /**
     * 根据项目编号查询预算信息
     */
    @GetMapping("/{xmbh}")
    @RequirePermission("教师")
    public Result<List<RchxXmysxx>> getByXmbh(@PathVariable String xmbh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            List<RchxXmysxx> result = xmysxxService.listByXmbh(xmbh);
            
            // 权限控制：教师只能查看自己创建的预算信息
            if ("教师".equals(currentUserQx)) {
                result = result.stream()
                    .filter(item -> currentUserZgh.equals(item.getCjrgh()))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询项目预算信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询项目预算信息
     */
    @PostMapping("/page")
    @RequirePermission("教师")
    public Result<IPage<RchxXmysxx>> getPage(@RequestBody XmysxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己创建的预算信息
            if ("教师".equals(currentUserQx)) {
                queryDTO.setCjrgh(currentUserZgh);
            }
            
            IPage<RchxXmysxx> result = xmysxxService.pageByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目预算信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 多条件查询项目预算信息
     */
    @PostMapping("/list")
    @RequirePermission("教师")
    public Result<List<RchxXmysxx>> getList(@RequestBody XmysxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己创建的预算信息
            if ("教师".equals(currentUserQx)) {
                queryDTO.setCjrgh(currentUserZgh);
            }
            
            List<RchxXmysxx> result = xmysxxService.listByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询项目预算信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据项目名称模糊查询
     */
    @GetMapping("/name/{xmmc}")
    @RequirePermission("学院管理员")
    public Result<List<RchxXmysxx>> getByXmmcLike(@PathVariable String xmmc) {
        try {
            List<RchxXmysxx> result = xmysxxService.listByXmmcLike(xmmc);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据项目名称查询预算信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据凭单号查询
     */
    @GetMapping("/voucher/{pdh}")
    @RequirePermission("学院管理员")
    public Result<List<RchxXmysxx>> getByPdh(@PathVariable String pdh) {
        try {
            List<RchxXmysxx> result = xmysxxService.listByPdh(pdh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据凭单号查询预算信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据经办人模糊查询
     */
    @GetMapping("/handler/{jbr}")
    @RequirePermission("学院管理员")
    public Result<List<RchxXmysxx>> getByJbrLike(@PathVariable String jbr) {
        try {
            List<RchxXmysxx> result = xmysxxService.listByJbrLike(jbr);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据经办人查询预算信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据报销人模糊查询
     */
    @GetMapping("/reimbursement/{bxr}")
    @RequirePermission("学院管理员")
    public Result<List<RchxXmysxx>> getByBxrLike(@PathVariable String bxr) {
        try {
            List<RchxXmysxx> result = xmysxxService.listByBxrLike(bxr);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据报销人查询预算信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据创建人工号查询
     */
    @GetMapping("/creator/{cjrgh}")
    @RequirePermission("教师")
    public Result<List<RchxXmysxx>> getByCjrgh(@PathVariable String cjrgh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己创建的预算信息
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(cjrgh)) {
                return Result.error(403, "权限不足，只能查看自己创建的预算信息");
            }
            
            List<RchxXmysxx> result = xmysxxService.listByCjrgh(cjrgh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据创建人查询预算信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据审核状态查询
     */
    @GetMapping("/audit/{shzt}")
    @RequirePermission("评审")
    public Result<List<RchxXmysxx>> getByShzt(@PathVariable String shzt) {
        try {
            List<RchxXmysxx> result = xmysxxService.listByShzt(shzt);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据审核状态查询预算信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增项目预算信息
     */
    @PostMapping
    @RequirePermission("学院管理员")
    public Result<String> create(@RequestBody RchxXmysxx xmysxx, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能创建自己的预算信息
            if ("教师".equals(currentUserQx)) {
                xmysxx.setCjrgh(currentUserZgh);
            }
            
            boolean success = xmysxxService.saveXmysxx(xmysxx);
            if (success) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增项目预算信息失败: {}", e.getMessage());
            return Result.error("新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新项目预算信息
     */
    @PutMapping
    @RequirePermission("学院管理员")
    public Result<String> update(@RequestBody RchxXmysxx xmysxx, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能修改自己创建的预算信息
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(xmysxx.getCjrgh())) {
                return Result.error(403, "权限不足，只能修改自己创建的预算信息");
            }
            
            boolean success = xmysxxService.updateXmysxx(xmysxx);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新项目预算信息失败: {}", e.getMessage());
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除项目预算信息
     */
    @DeleteMapping("/{xmbh}")
    @RequirePermission("系统管理员")
    public Result<String> delete(@PathVariable String xmbh) {
        try {
            boolean success = xmysxxService.removeByXmbh(xmbh);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除项目预算信息失败: {}", e.getMessage());
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除项目预算信息
     */
    @DeleteMapping("/batch")
    @RequirePermission("系统管理员")
    public Result<String> deleteBatch(@RequestBody List<String> xmbhs) {
        try {
            boolean success = xmysxxService.removeBatchByXmbhs(xmbhs);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除项目预算信息失败: {}", e.getMessage());
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量新增项目预算信息
     */
    @PostMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> createBatch(@RequestBody List<RchxXmysxx> xmysxxList) {
        try {
            boolean success = xmysxxService.saveBatchXmysxx(xmysxxList);
            if (success) {
                return Result.success("批量新增成功");
            } else {
                return Result.error("批量新增失败");
            }
        } catch (Exception e) {
            log.error("批量新增项目预算信息失败: {}", e.getMessage());
            return Result.error("批量新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新项目预算信息
     */
    @PutMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> updateBatch(@RequestBody List<RchxXmysxx> xmysxxList) {
        try {
            boolean success = xmysxxService.updateBatchXmysxx(xmysxxList);
            if (success) {
                return Result.success("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新项目预算信息失败: {}", e.getMessage());
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }
}
