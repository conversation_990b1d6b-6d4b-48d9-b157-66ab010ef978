# 权限管理系统文档

## 权限设计

### 权限类型
系统共有四种权限，按权限级别从低到高：

| 权限名称 | 权限描述 | 数据范围 |
|---------|---------|---------|
| 教师 | 普通教师，只能查看和修改自己的信息 | 个人数据 |
| 评审 | 评审人员，只能进行评审相关操作 | 评审数据 |
| 学院管理员 | 学院管理员，只能管理本学院的教职工信息 | 学院数据 |
| 系统管理员 | 系统管理员，拥有所有权限 | 全部数据 |

### 权限独立性
- 每种权限只能访问自己对应的功能
- 只有系统管理员拥有所有权限
- 例如：评审人员不能做学院管理的事情，学院管理员不能做评审的事情

## 使用方法

### 1. 在Controller中使用权限注解

```java
@RestController
@RequestMapping("/api/teacher")
public class TeacherController {
    
    // 只有学院管理员及以上权限才能访问
    @RequirePermission("学院管理员")
    @PostMapping
    public Result<String> createTeacher(@RequestBody RchxJzgjbxx teacher) {
        // 创建教职工逻辑
    }
    
    // 只有系统管理员才能访问（精确匹配）
    @RequirePermission(value = "系统管理员", exact = true)
    @DeleteMapping("/{zgh}")
    public Result<String> deleteTeacher(@PathVariable String zgh) {
        // 删除教职工逻辑
    }
    
    // 评审及以上权限可以访问
    @RequirePermission("评审")
    @GetMapping("/review")
    public Result<List<RchxJzgjbxx>> getReviewList() {
        // 获取评审列表
    }
    
    // 所有登录用户都可以访问（不需要权限注解）
    @GetMapping("/me")
    public Result<RchxJzgjbxx> getMyInfo() {
        // 获取个人信息
    }
}
```

### 2. 在Service中使用权限工具类

```java
@Service
public class TeacherService {
    
    @Autowired
    private PermissionUtil permissionUtil;
    
    public List<RchxJzgjbxx> getTeacherList(String currentUserQx, String currentUserZgh) {
        if (permissionUtil.isSystemAdmin(currentUserQx)) {
            // 系统管理员可以查看所有数据
            return getAllTeachers();
        } else if (permissionUtil.isCollegeAdmin(currentUserQx)) {
            // 学院管理员只能查看本学院数据
            return getTeachersByCollege(getCurrentUserCollege(currentUserZgh));
        } else if (permissionUtil.isReviewer(currentUserQx)) {
            // 评审可以查看评审相关数据
            return getReviewTeachers();
        } else {
            // 普通教师只能查看自己的数据
            return getTeacherByZgh(currentUserZgh);
        }
    }
}
```

### 3. 权限检查方法

```java
// 检查是否有指定权限
boolean hasPermission = permissionUtil.hasPermission("教师", "学院管理员"); // false
boolean hasPermission = permissionUtil.hasPermission("学院管理员", "教师"); // false
boolean hasPermission = permissionUtil.hasPermission("系统管理员", "教师"); // true（系统管理员拥有所有权限）

// 检查是否是指定权限
boolean isTeacher = permissionUtil.isTeacher("教师"); // true
boolean isReviewer = permissionUtil.isReviewer("评审"); // true

// 检查是否是管理员
boolean isAdmin = permissionUtil.isAdmin("学院管理员"); // true
boolean isAdmin = permissionUtil.isAdmin("教师"); // false

// 检查是否可以管理用户
boolean canManage = permissionUtil.canManageUsers("学院管理员"); // true

// 检查是否可以评审
boolean canReview = permissionUtil.canReview("评审"); // true
```

## 前端权限控制

### 1. Vue组件中的权限判断

```javascript
// 在Vue组件中
export default {
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo
    },
    
    // 是否是教师
    isTeacher() {
      return this.userInfo.qx === '教师'
    },
    
    // 是否是评审
    isReviewer() {
      return this.userInfo.qx === '评审'
    },
    
    // 是否是学院管理员
    isCollegeAdmin() {
      return this.userInfo.qx === '学院管理员'
    },
    
    // 是否是系统管理员
    isSystemAdmin() {
      return this.userInfo.qx === '系统管理员'
    },
    
    // 是否是管理员（学院管理员或系统管理员）
    isAdmin() {
      return ['学院管理员', '系统管理员'].includes(this.userInfo.qx)
    },
    
    // 是否可以管理用户
    canManageUsers() {
      return ['学院管理员', '系统管理员'].includes(this.userInfo.qx)
    },
    
    // 是否可以评审
    canReview() {
      return ['评审', '学院管理员', '系统管理员'].includes(this.userInfo.qx)
    }
  }
}
```

### 2. 模板中的权限控制

```vue
<template>
  <div>
    <!-- 只有管理员可以看到 -->
    <el-button v-if="isAdmin" type="primary">管理用户</el-button>
    
    <!-- 只有系统管理员可以看到 -->
    <el-button v-if="isSystemAdmin" type="danger">删除用户</el-button>
    
    <!-- 评审及以上权限可以看到 -->
    <el-button v-if="canReview" type="success">开始评审</el-button>
    
    <!-- 所有用户都可以看到 -->
    <el-button>查看个人信息</el-button>
  </div>
</template>
```

### 3. 路由权限控制

```javascript
// router/index.js
const routes = [
  {
    path: '/teacher',
    component: TeacherManagement,
    meta: { 
      requiresAuth: true,
      requiredPermission: '教师' // 最低权限要求
    }
  },
  {
    path: '/review',
    component: ReviewManagement,
    meta: { 
      requiresAuth: true,
      requiredPermission: '评审'
    }
  },
  {
    path: '/admin',
    component: AdminPanel,
    meta: { 
      requiresAuth: true,
      requiredPermission: '学院管理员'
    }
  },
  {
    path: '/system',
    component: SystemManagement,
    meta: { 
      requiresAuth: true,
      requiredPermission: '系统管理员',
      exact: true // 精确匹配权限
    }
  }
]

// 路由守卫
router.beforeEach((to, from, next) => {
  const userInfo = store.state.user.userInfo
  
  if (to.meta.requiresAuth) {
    if (!userInfo.qx) {
      next('/login')
      return
    }
    
    if (to.meta.requiredPermission) {
      const hasPermission = checkPermission(userInfo.qx, to.meta.requiredPermission, to.meta.exact)
      if (!hasPermission) {
        Message.error('权限不足')
        next('/403')
        return
      }
    }
  }
  
  next()
})

function checkPermission(userPermission, requiredPermission, exact = false) {
  const permissions = ['教师', '评审', '学院管理员', '系统管理员']
  const userLevel = permissions.indexOf(userPermission) + 1
  const requiredLevel = permissions.indexOf(requiredPermission) + 1
  
  if (exact) {
    return userLevel === requiredLevel
  } else {
    return userLevel >= requiredLevel
  }
}
```

## 权限配置

### 数据库权限设置

```sql
-- 设置用户权限
UPDATE t_rchx_jzgjbxx SET qx = '教师' WHERE zgh = 'T001';
UPDATE t_rchx_jzgjbxx SET qx = '评审' WHERE zgh = 'T002';
UPDATE t_rchx_jzgjbxx SET qx = '学院管理员' WHERE zgh = 'T003';
UPDATE t_rchx_jzgjbxx SET qx = '系统管理员' WHERE zgh = 'admin';
```

### 权限验证流程

1. **JWT拦截器**：验证Token有效性，提取用户信息
2. **权限拦截器**：根据接口权限注解验证用户权限
3. **业务层权限**：在Service中根据权限控制数据范围
4. **前端权限**：根据用户权限显示/隐藏功能按钮

## 注意事项

1. **权限名称必须准确**：使用枚举中定义的权限名称
2. **权限级别**：数字越大权限越高
3. **精确匹配**：使用 `exact = true` 时只匹配指定权限
4. **数据安全**：后端必须验证权限，前端权限控制只是用户体验优化
