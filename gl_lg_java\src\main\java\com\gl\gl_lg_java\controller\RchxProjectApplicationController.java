package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectApplication;
import com.gl.gl_lg_java.service.RchxProjectApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目申报表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@RestController
@RequestMapping("/api/project-application")
public class RchxProjectApplicationController {

    @Autowired
    private RchxProjectApplicationService projectApplicationService;

    /**
     * 分页查询项目申报
     */
    @GetMapping("/page")
    public Result<IPage<RchxProjectApplication>> page(@RequestParam(defaultValue = "1") Integer current,
                                                     @RequestParam(defaultValue = "10") Integer size,
                                                     @RequestParam(required = false) Integer categoryId,
                                                     @RequestParam(required = false) Integer typeId,
                                                     @RequestParam(required = false) Integer deptId,
                                                     @RequestParam(required = false) String status,
                                                     @RequestParam(required = false) String projectName,
                                                     @RequestParam(required = false) String applicantZgh) {
        try {
            Page<RchxProjectApplication> page = new Page<>(current, size);
            IPage<RchxProjectApplication> result = projectApplicationService.pageQuery(page, categoryId, typeId, deptId, status, projectName, applicantZgh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目申报失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目申报
     */
    @GetMapping("/{id}")
    public Result<RchxProjectApplication> getById(@PathVariable Long id) {
        try {
            RchxProjectApplication application = projectApplicationService.getById(id);
            if (application == null) {
                return Result.error("项目申报不存在");
            }
            return Result.success(application);
        } catch (Exception e) {
            log.error("查询项目申报失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目编号查询申报信息
     */
    @GetMapping("/code/{projectCode}")
    public Result<RchxProjectApplication> getByProjectCode(@PathVariable String projectCode) {
        try {
            RchxProjectApplication application = projectApplicationService.getByProjectCode(projectCode);
            if (application == null) {
                return Result.error("项目申报不存在");
            }
            return Result.success(application);
        } catch (Exception e) {
            log.error("根据项目编号查询申报失败: projectCode={}", projectCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据申报人获取申报列表
     */
    @GetMapping("/applicant/{applicantZgh}")
    public Result<List<RchxProjectApplication>> getByApplicant(@PathVariable String applicantZgh) {
        try {
            List<RchxProjectApplication> applications = projectApplicationService.getByApplicant(applicantZgh);
            return Result.success(applications);
        } catch (Exception e) {
            log.error("根据申报人查询申报失败: applicantZgh={}", applicantZgh, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目申报
     */
    @PostMapping
    public Result<String> save(@RequestBody RchxProjectApplication application) {
        try {
            // 检查项目编号是否重复
            if (projectApplicationService.existsByProjectCode(application.getProjectCode(), null)) {
                return Result.error("项目编号已存在");
            }
            
            boolean success = projectApplicationService.save(application);
            return success ? Result.success("新增成功") : Result.error("新增失败");
        } catch (Exception e) {
            log.error("新增项目申报失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目申报
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Long id, @RequestBody RchxProjectApplication application) {
        try {
            // 检查项目编号是否重复
            if (projectApplicationService.existsByProjectCode(application.getProjectCode(), id)) {
                return Result.error("项目编号已存在");
            }
            
            application.setId(id);
            boolean success = projectApplicationService.updateById(application);
            return success ? Result.success("更新成功") : Result.error("更新失败");
        } catch (Exception e) {
            log.error("更新项目申报失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目申报
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        try {
            boolean success = projectApplicationService.removeById(id);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除项目申报失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 提交申报
     */
    @PutMapping("/{id}/submit")
    public Result<String> submitApplication(@PathVariable Long id, @RequestParam String submitBy) {
        try {
            boolean success = projectApplicationService.submitApplication(id, submitBy);
            return success ? Result.success("提交成功") : Result.error("提交失败");
        } catch (Exception e) {
            log.error("提交申报失败: id={}, submitBy={}", id, submitBy, e);
            return Result.error("提交失败: " + e.getMessage());
        }
    }

    /**
     * 审核申报
     */
    @PutMapping("/{id}/review")
    public Result<String> reviewApplication(@PathVariable Long id, 
                                           @RequestParam boolean approved,
                                           @RequestParam String reviewerZgh,
                                           @RequestParam(required = false) String reviewComments) {
        try {
            boolean success = projectApplicationService.reviewApplication(id, approved, reviewerZgh, reviewComments);
            return success ? Result.success("审核成功") : Result.error("审核失败");
        } catch (Exception e) {
            log.error("审核申报失败: id={}, approved={}, reviewerZgh={}", id, approved, reviewerZgh, e);
            return Result.error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 获取申报统计信息
     */
    @GetMapping("/statistics")
    public Result<Object> getApplicationStatistics(@RequestParam(required = false) Integer deptId,
                                                   @RequestParam(required = false) Integer categoryId) {
        try {
            Object statistics = projectApplicationService.getApplicationStatistics(deptId, categoryId);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取申报统计信息失败: deptId={}, categoryId={}", deptId, categoryId, e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }
}
