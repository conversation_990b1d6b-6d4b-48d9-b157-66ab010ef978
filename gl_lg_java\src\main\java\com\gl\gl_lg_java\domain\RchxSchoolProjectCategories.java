package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 校级项目类别实体类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_rchx_school_project_categories")
public class RchxSchoolProjectCategories implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类别ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类别编码
     */
    @TableField("category_code")
    private String categoryCode;

    /**
     * 类别名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 资助金额下限(万元)
     */
    @TableField("funding_range_min")
    private BigDecimal fundingRangeMin;

    /**
     * 资助金额上限(万元)
     */
    @TableField("funding_range_max")
    private BigDecimal fundingRangeMax;

    /**
     * 申报要求说明
     */
    @TableField("application_requirements")
    private String applicationRequirements;

    /**
     * 评审标准
     */
    @TableField("evaluation_criteria")
    private String evaluationCriteria;

    /**
     * 项目周期(月)
     */
    @TableField("project_duration_months")
    private Integer projectDurationMonths;

    /**
     * 类别描述
     */
    @TableField("description")
    private String description;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否启用(0禁用,1启用)
     */
    @TableField("is_enabled")
    private Boolean isEnabled;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
