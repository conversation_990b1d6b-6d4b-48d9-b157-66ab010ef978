package com.gl.gl_lg_java.controller;

import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.AnalysisRequest;
import com.gl.gl_lg_java.dto.AnalysisResult;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.service.SchoolProjectMatchingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 校级项目人才匹配分析控制器
 */
@RestController
@RequestMapping("/api/school-project-matching")
@Api(tags = "校级项目人才匹配分析")
@Slf4j
public class SchoolProjectMatchingController {
    
    @Autowired
    private SchoolProjectMatchingService matchingService;
    
    /**
     * 获取所有支持的校级项目
     */
    @GetMapping("/projects")
    @ApiOperation("获取所有支持的校级项目")
    public Result<List<Map<String, Object>>> getAllSchoolProjects() {
        try {
            List<Map<String, Object>> projects = matchingService.getAllSchoolProjects();
            return Result.success(projects);
        } catch (Exception e) {
            log.error("获取校级项目列表失败", e);
            return Result.error("获取项目列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行校级项目匹配分析
     */
    @PostMapping("/analyze")
    @ApiOperation("执行校级项目匹配分析")
    public Result<AnalysisResult> analyzeProjectMatching(
            @RequestBody @ApiParam("分析请求") AnalysisRequest request) {
        try {
            log.info("收到校级项目匹配分析请求: {}", request);
            
            // 参数验证
            if (request.getProjectCode() == null || request.getProjectCode().trim().isEmpty()) {
                return Result.error("项目代码不能为空");
            }
            
            // 设置默认参数
            AnalysisParams params = request.getAnalysisParams();
            if (params == null) {
                params = AnalysisParams.getDefault();
            }
            
            // 执行分析
            AnalysisResult result = matchingService.analyzeProjectMatching(
                request.getProjectCode(), params);
            
            return Result.success(result);
            
        } catch (IllegalArgumentException e) {
            log.warn("校级项目匹配分析参数错误: {}", e.getMessage());
            return Result.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("校级项目匹配分析失败", e);
            return Result.error("分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取分析结果
     */
    @GetMapping("/results/{analysisId}")
    @ApiOperation("获取分析结果")
    public Result<AnalysisResult> getAnalysisResults(
            @PathVariable @ApiParam("分析ID") String analysisId) {
        try {
            AnalysisResult result = matchingService.getAnalysisResults(analysisId);
            if (result == null) {
                return Result.error("分析结果不存在或已过期");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取分析结果失败: analysisId={}", analysisId, e);
            return Result.error("获取分析结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析单个教师的匹配情况
     */
    @PostMapping("/analyze/teacher/{teacherZgh}")
    @ApiOperation("分析单个教师的匹配情况")
    public Result<MatchResult> analyzeSingleTeacher(
            @PathVariable @ApiParam("教师职工号") String teacherZgh,
            @RequestParam @ApiParam("项目代码") String projectCode,
            @RequestBody(required = false) @ApiParam("分析参数") AnalysisParams params) {
        try {
            log.info("收到单个教师匹配分析请求: teacherZgh={}, projectCode={}", 
                    teacherZgh, projectCode);
            
            // 设置默认参数
            if (params == null) {
                params = AnalysisParams.getDefault();
            }
            
            // 执行分析
            MatchResult result = matchingService.analyzeSingleTeacher(
                teacherZgh, projectCode, params);
            
            return Result.success(result);
            
        } catch (IllegalArgumentException e) {
            log.warn("单个教师匹配分析参数错误: {}", e.getMessage());
            return Result.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("单个教师匹配分析失败: teacherZgh={}, projectCode={}", 
                    teacherZgh, projectCode, e);
            return Result.error("分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取教师对所有项目的匹配情况
     */
    @PostMapping("/analyze/teacher/{teacherZgh}/all-projects")
    @ApiOperation("获取教师对所有项目的匹配情况")
    public Result<List<MatchResult>> analyzeTeacherForAllProjects(
            @PathVariable @ApiParam("教师职工号") String teacherZgh,
            @RequestBody(required = false) @ApiParam("分析参数") AnalysisParams params) {
        try {
            log.info("收到教师全项目匹配分析请求: teacherZgh={}", teacherZgh);
            
            // 设置默认参数
            if (params == null) {
                params = AnalysisParams.getDefault();
            }
            
            // 执行分析
            List<MatchResult> results = matchingService.analyzeTeacherForAllProjects(
                teacherZgh, params);
            
            return Result.success(results);
            
        } catch (IllegalArgumentException e) {
            log.warn("教师全项目匹配分析参数错误: {}", e.getMessage());
            return Result.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("教师全项目匹配分析失败: teacherZgh={}", teacherZgh, e);
            return Result.error("分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 快速匹配分析（简化版）
     */
    @GetMapping("/quick-analyze")
    @ApiOperation("快速匹配分析")
    public Result<AnalysisResult> quickAnalyze(
            @RequestParam @ApiParam("项目代码") String projectCode,
            @RequestParam(defaultValue = "60") @ApiParam("最低匹配分数") Double minScore,
            @RequestParam(defaultValue = "50") @ApiParam("最大结果数量") Integer maxResults) {
        try {
            log.info("收到快速匹配分析请求: projectCode={}, minScore={}, maxResults={}", 
                    projectCode, minScore, maxResults);
            
            // 构建分析参数
            AnalysisParams params = AnalysisParams.builder()
                .timeRange("5")
                .minMatchScore(minScore)
                .maxResults(maxResults)
                .includeDetails(false)
                .build();
            
            // 执行分析
            AnalysisResult result = matchingService.analyzeProjectMatching(projectCode, params);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("快速匹配分析失败: projectCode={}", projectCode, e);
            return Result.error("快速分析失败: " + e.getMessage());
        }
    }
}
