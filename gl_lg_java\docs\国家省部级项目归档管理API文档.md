# 国家/省部级项目归档管理系统API文档

## 📋 概述

本文档描述了国家/省部级项目归档管理系统的所有API接口，包括项目级别管理、项目归档管理和文件管理等功能。

### 🔗 基础信息
- **API前缀**: `/api`
- **数据格式**: JSON
- **字符编码**: UTF-8

### 📝 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

**响应状态码说明：**
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

---

## 🏢 1. 项目级别管理 (`/api/national-project-levels`)

### 1.1 分页查询项目级别

```http
POST /api/national-project-levels/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| levelType | String | 否 | - | 级别类型(NATIONAL/PROVINCIAL/MUNICIPAL) |
| levelName | String | 否 | - | 级别名称（模糊查询） |
| isEnabled | Boolean | 否 | - | 是否启用 |

### 1.2 获取启用的项目级别

```http
GET /api/national-project-levels/enabled
```

### 1.3 根据级别类型查询

```http
GET /api/national-project-levels/type/{levelType}
```

### 1.4 根据资助金额查询匹配的级别

```http
GET /api/national-project-levels/funding/{amount}
```

### 1.5 新增项目级别

```http
POST /api/national-project-levels
```

**请求体示例：**
```json
{
  "levelCode": "NATIONAL_KEY",
  "levelName": "国家重点研发计划",
  "levelType": "NATIONAL",
  "levelWeight": 100,
  "fundingRangeMin": 100.00,
  "fundingRangeMax": 1000.00,
  "archiveRequirement": "需提交完整的研究报告、成果材料、财务报告等",
  "description": "国家级重点研发项目",
  "sortOrder": 1,
  "isEnabled": true
}
```

### 1.6 更新项目级别

```http
PUT /api/national-project-levels/{id}
```

### 1.7 删除项目级别

```http
DELETE /api/national-project-levels/{id}
```

### 1.8 启用/禁用项目级别

```http
PUT /api/national-project-levels/{id}/enabled?enabled=true
```

---

## 📁 2. 项目归档管理 (`/api/national-project-archive`)

### 2.1 分页查询项目归档

```http
POST /api/national-project-archive/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| projectName | String | 否 | - | 项目名称（模糊查询） |
| projectCode | String | 否 | - | 项目编号（模糊查询） |
| leaderName | String | 否 | - | 负责人姓名（模糊查询） |
| archiveStatus | String | 否 | - | 归档状态 |
| levelId | Integer | 否 | - | 项目级别ID |
| deptId | Integer | 否 | - | 部门ID |
| startDate | Date | 否 | - | 项目开始日期 |
| endDate | Date | 否 | - | 项目结束日期 |

### 2.2 根据归档编号查询

```http
GET /api/national-project-archive/code/{archiveCode}
```

### 2.3 根据项目编号查询

```http
GET /api/national-project-archive/project/{projectCode}
```

### 2.4 根据归档状态查询

```http
GET /api/national-project-archive/status/{archiveStatus}
```

**归档状态说明：**
- `PENDING`: 待归档
- `ARCHIVED`: 已归档
- `REVIEWING`: 审核中
- `APPROVED`: 审核通过
- `REJECTED`: 审核拒绝
- `INVALID`: 失效

### 2.5 新增项目归档

```http
POST /api/national-project-archive
```

**请求体示例：**
```json
{
  "projectCode": "PROJ2025001",
  "projectName": "基于人工智能的智能制造关键技术研究",
  "levelId": 1,
  "deptId": 1,
  "projectLeaderZgh": "202401001",
  "projectLeaderName": "张三",
  "projectLeaderPhone": "13800138001",
  "projectLeaderEmail": "<EMAIL>",
  "projectLeaderTitle": "教授",
  "projectStartDate": "2024-01-01",
  "projectEndDate": "2026-12-31",
  "projectDurationMonths": 36,
  "projectBudget": 500.00,
  "actualFunding": 500.00,
  "fundingSource": "国家重点研发计划",
  "projectDescription": "本项目旨在研究基于人工智能的智能制造关键技术",
  "keywords": "人工智能,智能制造,机器学习",
  "researchField": "智能制造",
  "cooperationUnits": "清华大学,华为技术有限公司",
  "priorityLevel": "HIGH",
  "confidentialityLevel": "INTERNAL",
  "createBy": "202401002"
}
```

### 2.6 提交归档

```http
PUT /api/national-project-archive/{id}/submit?archiveBy=202401001&archiveByName=张三
```

### 2.7 审核归档

```http
PUT /api/national-project-archive/{id}/review
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| reviewerZgh | String | 是 | 审核人职工号 |
| reviewerName | String | 是 | 审核人姓名 |
| reviewComments | String | 是 | 审核意见 |
| reviewScore | BigDecimal | 是 | 审核评分 |
| archiveStatus | String | 是 | 审核结果状态 |

### 2.8 设置归档截止时间

```http
PUT /api/national-project-archive/{id}/deadline?deadline=2025-12-31 23:59:59
```

---

## 📄 3. 归档文件管理 (`/api/national-archive-files`)

### 3.1 分页查询归档文件

```http
POST /api/national-archive-files/page
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| archiveId | Long | 否 | - | 归档ID |
| projectCode | String | 否 | - | 项目编号 |
| fileCategory | String | 否 | - | 文件类别 |
| fileName | String | 否 | - | 文件名称（模糊查询） |
| uploadBy | String | 否 | - | 上传人 |
| isRequired | Boolean | 否 | - | 是否必需文件 |
| isConfidential | Boolean | 否 | - | 是否机密文件 |

### 3.2 根据归档ID查询文件

```http
GET /api/national-archive-files/archive/{archiveId}
```

### 3.3 根据项目编号查询文件

```http
GET /api/national-archive-files/project/{projectCode}
```

### 3.4 根据文件类别查询

```http
GET /api/national-archive-files/category/{fileCategory}
```

**文件类别说明：**
- `APPLICATION`: 申报书
- `CONTRACT`: 合同书
- `REPORT`: 研究报告
- `ACHIEVEMENT`: 成果材料
- `FINANCIAL`: 财务材料
- `OTHER`: 其他

### 3.5 上传文件

```http
POST /api/national-archive-files/upload
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| archiveId | Long | 是 | 归档ID |
| projectCode | String | 是 | 项目编号 |
| fileCategory | String | 是 | 文件类别 |
| file | MultipartFile | 是 | 上传的文件 |
| uploadBy | String | 是 | 上传人职工号 |
| uploadByName | String | 是 | 上传人姓名 |
| description | String | 否 | 文件描述 |
| isRequired | Boolean | 否 | 是否必需文件 |
| isConfidential | Boolean | 否 | 是否机密文件 |

### 3.6 批量上传文件

```http
POST /api/national-archive-files/upload/batch
```

### 3.7 下载文件

```http
GET /api/national-archive-files/{id}/download
```

### 3.8 获取文件下载URL

```http
GET /api/national-archive-files/{id}/download-url
```

### 3.9 删除文件（逻辑删除）

```http
DELETE /api/national-archive-files/{id}?deleteBy=202401001
```

---

## 📊 4. 统计接口

### 4.1 项目级别统计

```http
GET /api/national-project-levels/statistics/type
```

### 4.2 归档状态统计

```http
GET /api/national-project-archive/statistics/status
```

### 4.3 归档级别统计

```http
GET /api/national-project-archive/statistics/level
```

### 4.4 归档部门统计

```http
GET /api/national-project-archive/statistics/department
```

### 4.5 归档进度统计

```http
GET /api/national-project-archive/statistics/progress
```

### 4.6 文件类别统计

```http
GET /api/national-archive-files/statistics/category
```

### 4.7 文件存储统计

```http
GET /api/national-archive-files/statistics/storage
```

---

## 🔧 5. 系统特性

### 5.1 文件存储
- 使用MinIO对象存储
- 支持文件分类管理
- 提供文件MD5校验
- 支持文件下载统计

### 5.2 权限控制
- 基于职工号的权限验证
- 支持项目负责人权限
- 支持文件访问权限控制

### 5.3 归档流程
- 待归档 → 审核中 → 审核通过/拒绝
- 支持归档截止时间管理
- 支持归档提醒功能

### 5.4 数据验证
- 归档编号唯一性验证
- 项目编号唯一性验证
- 文件类型和大小限制
- 重复文件检测

---

## 📝 6. 使用示例

### 6.1 完整的项目归档流程

1. **创建项目级别**
2. **新增项目归档记录**
3. **上传归档文件**
4. **提交归档**
5. **审核归档**
6. **查看归档统计**

### 6.2 文件管理流程

1. **上传文件到指定归档项目**
2. **查看文件列表**
3. **下载文件**
4. **更新文件信息**
5. **删除不需要的文件**

本API文档涵盖了国家/省部级项目归档管理系统的所有核心功能，为前端开发和系统集成提供了完整的接口规范。
