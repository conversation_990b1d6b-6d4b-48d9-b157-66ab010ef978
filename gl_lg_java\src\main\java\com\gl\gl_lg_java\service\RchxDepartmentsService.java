package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxDepartments;

import java.util.List;

/**
 * 管理部门表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface RchxDepartmentsService extends IService<RchxDepartments> {

    /**
     * 获取所有启用的管理部门（按排序号排序）
     * 
     * @return 管理部门列表
     */
    List<RchxDepartments> getEnabledDepartments();

    /**
     * 根据部门编码获取部门信息
     * 
     * @param deptCode 部门编码
     * @return 管理部门
     */
    RchxDepartments getByDeptCode(String deptCode);

    /**
     * 启用/禁用管理部门
     * 
     * @param id 部门ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean updateEnabled(Integer id, Boolean enabled);

    /**
     * 检查部门编码是否存在
     * 
     * @param deptCode 部门编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByDeptCode(String deptCode, Integer excludeId);
}
