package com.gl.gl_lg_java.service;

import com.gl.gl_lg_java.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.gl.gl_lg_java.service.RchxNotificationTypesService;
import com.gl.gl_lg_java.service.NotificationRedisService;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 通知系统Redis服务
 * 用于管理教师通知的缓存操作
 */
@Service
@Slf4j
public class NotificationRedisService {

    @Autowired
    private RedisUtil redisUtil;

    @Value("${teacher-notification.redis.connection-expire:3600}")
    private long connectionExpire;

    @Value("${teacher-notification.redis.unread-count-expire:1800}")
    private long unreadCountExpire;

    // Redis Key前缀
    private static final String TEACHER_ONLINE_PREFIX = "teacher:online:";
    private static final String TEACHER_UNREAD_PREFIX = "teacher:unread:";
    private static final String NOTIFICATION_HOT_PREFIX = "notification:hot:";

    /**
     * 设置教师在线状态
     * @param zgh 职工号
     * @param sessionId WebSocket会话ID
     */
    public void setTeacherOnline(String zgh, String sessionId) {
        try {
            String key = TEACHER_ONLINE_PREFIX + zgh;
            Map<String, Object> onlineInfo = new HashMap<>();
            onlineInfo.put("sessionId", sessionId);
            onlineInfo.put("connectTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            onlineInfo.put("lastHeartbeat", System.currentTimeMillis());
            
            redisUtil.set(key, onlineInfo, connectionExpire);
            log.info("设置教师在线状态: zgh={}, sessionId={}", zgh, sessionId);
        } catch (Exception e) {
            log.error("设置教师在线状态失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }

    /**
     * 更新教师心跳时间
     * @param zgh 职工号
     */
    public void updateTeacherHeartbeat(String zgh) {
        try {
            String key = TEACHER_ONLINE_PREFIX + zgh;
            if (redisUtil.hasKey(key)) {
                @SuppressWarnings("unchecked")
                Map<String, Object> onlineInfo = (Map<String, Object>) redisUtil.get(key);
                if (onlineInfo != null) {
                    onlineInfo.put("lastHeartbeat", System.currentTimeMillis());
                    redisUtil.set(key, onlineInfo, connectionExpire);
                    log.debug("更新教师心跳: zgh={}", zgh);
                }
            }
        } catch (Exception e) {
            log.error("更新教师心跳失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }

    /**
     * 检查教师是否在线
     * @param zgh 职工号
     * @return 是否在线
     */
    public boolean isTeacherOnline(String zgh) {
        try {
            String key = TEACHER_ONLINE_PREFIX + zgh;
            boolean online = redisUtil.hasKey(key);
            log.debug("检查教师在线状态: zgh={}, online={}", zgh, online);
            return online;
        } catch (Exception e) {
            log.error("检查教师在线状态失败: zgh={}, error={}", zgh, e.getMessage());
            return false;
        }
    }

    /**
     * 移除教师在线状态
     * @param zgh 职工号
     */
    public void removeTeacherOnline(String zgh) {
        try {
            String key = TEACHER_ONLINE_PREFIX + zgh;
            redisUtil.delete(key);
            log.info("移除教师在线状态: zgh={}", zgh);
        } catch (Exception e) {
            log.error("移除教师在线状态失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }

    /**
     * 设置教师未读通知数量
     * @param zgh 职工号
     * @param count 未读数量
     */
    public void setTeacherUnreadCount(String zgh, long count) {
        try {
            String key = TEACHER_UNREAD_PREFIX + zgh;
            redisUtil.set(key, count, unreadCountExpire);
            log.debug("设置教师未读通知数量: zgh={}, count={}", zgh, count);
        } catch (Exception e) {
            log.error("设置教师未读通知数量失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }

    /**
     * 获取教师未读通知数量
     * @param zgh 职工号
     * @return 未读数量，-1表示缓存中没有
     */
    public long getTeacherUnreadCount(String zgh) {
        try {
            String key = TEACHER_UNREAD_PREFIX + zgh;
            Object count = redisUtil.get(key);
            if (count != null) {
                long result = Long.parseLong(count.toString());
                log.debug("获取教师未读通知数量: zgh={}, count={}", zgh, result);
                return result;
            }
            return -1;
        } catch (Exception e) {
            log.error("获取教师未读通知数量失败: zgh={}, error={}", zgh, e.getMessage());
            return -1;
        }
    }

    /**
     * 增加教师未读通知数量
     * @param zgh 职工号
     * @param delta 增加数量
     * @return 增加后的数量
     */
    public long incrementTeacherUnreadCount(String zgh, long delta) {
        try {
            String key = TEACHER_UNREAD_PREFIX + zgh;
            long result = redisUtil.increment(key, delta);
            redisUtil.expire(key, unreadCountExpire);
            log.debug("增加教师未读通知数量: zgh={}, delta={}, result={}", zgh, delta, result);
            return result;
        } catch (Exception e) {
            log.error("增加教师未读通知数量失败: zgh={}, error={}", zgh, e.getMessage());
            return 0;
        }
    }

    /**
     * 减少教师未读通知数量
     * @param zgh 职工号
     * @param delta 减少数量
     * @return 减少后的数量
     */
    public long decrementTeacherUnreadCount(String zgh, long delta) {
        try {
            String key = TEACHER_UNREAD_PREFIX + zgh;
            long result = redisUtil.decrement(key, delta);
            if (result < 0) {
                redisUtil.set(key, 0, unreadCountExpire);
                result = 0;
            }
            log.debug("减少教师未读通知数量: zgh={}, delta={}, result={}", zgh, delta, result);
            return result;
        } catch (Exception e) {
            log.error("减少教师未读通知数量失败: zgh={}, error={}", zgh, e.getMessage());
            return 0;
        }
    }

    /**
     * 缓存热点通知
     * @param notificationId 通知ID
     * @param notificationData 通知数据
     */
    public void cacheHotNotification(Long notificationId, Object notificationData) {
        try {
            String key = NOTIFICATION_HOT_PREFIX + notificationId;
            redisUtil.set(key, notificationData, 3600); // 缓存1小时
            log.debug("缓存热点通知: id={}", notificationId);
        } catch (Exception e) {
            log.error("缓存热点通知失败: id={}, error={}", notificationId, e.getMessage());
        }
    }

    /**
     * 获取热点通知缓存
     * @param notificationId 通知ID
     * @return 通知数据
     */
    public Object getHotNotification(Long notificationId) {
        try {
            String key = NOTIFICATION_HOT_PREFIX + notificationId;
            Object data = redisUtil.get(key);
            log.debug("获取热点通知缓存: id={}, found={}", notificationId, data != null);
            return data;
        } catch (Exception e) {
            log.error("获取热点通知缓存失败: id={}, error={}", notificationId, e.getMessage());
            return null;
        }
    }

    /**
     * 测试Redis连接
     * @return 是否连接成功
     */
    public boolean testConnection() {
        return redisUtil.testConnection();
    }
}
