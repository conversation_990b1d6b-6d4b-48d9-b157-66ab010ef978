package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectCollection;

import java.util.List;

/**
 * 项目选题征集表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface RchxProjectCollectionService extends IService<RchxProjectCollection> {

    /**
     * 分页查询项目征集
     * 
     * @param page 分页参数
     * @param categoryId 项目大类ID
     * @param typeId 项目类别ID
     * @param deptId 管理部门ID
     * @param status 状态
     * @param collectionName 征集名称（模糊查询）
     * @return 分页结果
     */
    IPage<RchxProjectCollection> pageQuery(Page<RchxProjectCollection> page, 
                                          Integer categoryId, 
                                          Integer typeId, 
                                          Integer deptId, 
                                          String status, 
                                          String collectionName);

    /**
     * 根据征集编号获取征集信息
     * 
     * @param collectionCode 征集编号
     * @return 项目征集
     */
    RchxProjectCollection getByCollectionCode(String collectionCode);

    /**
     * 获取当前有效的征集（状态为PUBLISHED且在受理时间内）
     * 
     * @return 有效征集列表
     */
    List<RchxProjectCollection> getActiveCollections();

    /**
     * 提交征集
     * 
     * @param id 征集ID
     * @param submitBy 提交人
     * @return 是否成功
     */
    boolean submitCollection(Long id, String submitBy);

    /**
     * 审核征集
     * 
     * @param id 征集ID
     * @param approved 是否通过
     * @param reviewerZgh 审核人职工号
     * @param reviewComments 审核意见
     * @return 是否成功
     */
    boolean reviewCollection(Long id, boolean approved, String reviewerZgh, String reviewComments);

    /**
     * 发布征集
     * 
     * @param id 征集ID
     * @return 是否成功
     */
    boolean publishCollection(Long id);

    /**
     * 关闭征集
     * 
     * @param id 征集ID
     * @return 是否成功
     */
    boolean closeCollection(Long id);

    /**
     * 检查征集编号是否存在
     * 
     * @param collectionCode 征集编号
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByCollectionCode(String collectionCode, Long excludeId);
}
