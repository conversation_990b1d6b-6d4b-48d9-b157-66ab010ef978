<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3cb47040-bc64-4dfd-8ef7-b8f4f9fd41e4" name="更改" comment="通信服务对接，邮箱系统对接，以及通知服务接口。" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="true" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="安全分析" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zrT50UBIYHZXUmEfjwNb7Im3jI" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_MARK_IGNORED_FILES_AS_EXCLUDED&quot;: &quot;true&quot;,
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Maven.gl_lg_java [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.gl_lg_java [package].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;找到数据库连接形参&quot;,
    &quot;Notification.DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Spring Boot.GlLgJavaApplication.executor&quot;: &quot;Run&quot;,
    &quot;checkBoxType&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/GL_ligong/gl_lg_java/src/main/resources&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.augment.tokenmanager.plugin.AugmentTokenManagerConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\GL_ligong\gl_lg_java\src\main\resources" />
      <recent name="C:\Users\<USER>\GL_ligong\gl_lg_java\doc" />
      <recent name="C:\Users\<USER>\GL_ligong\gl_lg_java" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\GL_ligong\gl_lg_java\src\main\java\com\gl\gl_lg_java\util" />
      <recent name="C:\Users\<USER>\GL_ligong\gl_lg_java\docs" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="GlLgJavaApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="gl_lg_java" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.gl.gl_lg_java.GlLgJavaApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3cb47040-bc64-4dfd-8ef7-b8f4f9fd41e4" name="更改" comment="" />
      <created>1752488004448</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752488004448</updated>
      <workItem from="1752488005802" duration="11104000" />
      <workItem from="1752805638289" duration="17340000" />
      <workItem from="1752831202600" duration="30000" />
      <workItem from="1752831261372" duration="60000" />
      <workItem from="1752831923452" duration="15000" />
      <workItem from="1752831951924" duration="39000" />
      <workItem from="1753235439180" duration="295000" />
      <workItem from="1753235745604" duration="1534000" />
      <workItem from="1753237310292" duration="18014000" />
      <workItem from="1753319210912" duration="33246000" />
      <workItem from="1753414214829" duration="9314000" />
      <workItem from="1753773820323" duration="3966000" />
      <workItem from="1753926059811" duration="308000" />
      <workItem from="1754384242395" duration="11000" />
      <workItem from="1754444219320" duration="27000" />
      <workItem from="1754444336403" duration="2549000" />
      <workItem from="1754462362766" duration="9186000" />
      <workItem from="1754528822105" duration="18986000" />
      <workItem from="1754616498255" duration="5147000" />
      <workItem from="1754635972669" duration="4793000" />
    </task>
    <task id="LOCAL-00001" summary="初始化">
      <option name="closed" value="true" />
      <created>1752509442071</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752509442071</updated>
    </task>
    <task id="LOCAL-00002" summary="所有表结构">
      <option name="closed" value="true" />
      <created>1752809360942</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752809360942</updated>
    </task>
    <task id="LOCAL-00003" summary="统计以及数据返回修改">
      <option name="closed" value="true" />
      <created>1753238195388</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753238195388</updated>
    </task>
    <task id="LOCAL-00004" summary="个人接口 以及年龄部门">
      <option name="closed" value="true" />
      <created>1753253597257</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753253597257</updated>
    </task>
    <task id="LOCAL-00005" summary="邮件接口密码处理 以及统计年龄职称接口 晚上">
      <option name="closed" value="true" />
      <created>1753264443046</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753264443046</updated>
    </task>
    <task id="LOCAL-00006" summary="通信服务对接，邮箱系统对接，以及通知服务接口。">
      <option name="closed" value="true" />
      <created>1753345316100</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753345316100</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始化" />
    <MESSAGE value="所有表结构" />
    <MESSAGE value="统计以及数据返回修改" />
    <MESSAGE value="个人接口 以及年龄部门" />
    <MESSAGE value="邮件接口密码处理 以及统计年龄职称接口 晚上" />
    <MESSAGE value="通信服务对接，邮箱系统对接，以及通知服务接口。" />
    <option name="LAST_COMMIT_MESSAGE" value="通信服务对接，邮箱系统对接，以及通知服务接口。" />
  </component>
  <component name="VgoProject">
    <integration-enabled>false</integration-enabled>
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="java.lang.NullPointerException" package="java.lang" />
          <option name="timeStamp" value="1" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>