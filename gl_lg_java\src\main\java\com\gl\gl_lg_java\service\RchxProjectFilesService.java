package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectFiles;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 项目文件表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface RchxProjectFilesService extends IService<RchxProjectFiles> {

    /**
     * 分页查询项目文件
     * 
     * @param page 分页参数
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param projectCode 项目编号
     * @param fileCategory 文件分类
     * @param uploadBy 上传人
     * @return 分页结果
     */
    IPage<RchxProjectFiles> pageQuery(Page<RchxProjectFiles> page, 
                                     String businessType, 
                                     Long businessId, 
                                     String projectCode, 
                                     String fileCategory,
                                     String uploadBy);

    /**
     * 根据业务信息获取文件列表
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 文件列表
     */
    List<RchxProjectFiles> getByBusiness(String businessType, Long businessId);

    /**
     * 根据项目编号获取文件列表
     * 
     * @param projectCode 项目编号
     * @return 文件列表
     */
    List<RchxProjectFiles> getByProjectCode(String projectCode);

    /**
     * 根据文件分类获取文件列表
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param fileCategory 文件分类
     * @return 文件列表
     */
    List<RchxProjectFiles> getByCategory(String businessType, Long businessId, String fileCategory);

    /**
     * 上传文件
     * 
     * @param file 文件
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param projectCode 项目编号
     * @param fileCategory 文件分类
     * @param description 文件描述
     * @param uploadBy 上传人
     * @return 文件信息
     */
    RchxProjectFiles uploadFile(MultipartFile file, 
                               String businessType, 
                               Long businessId, 
                               String projectCode, 
                               String fileCategory, 
                               String description, 
                               String uploadBy);

    /**
     * 删除文件（逻辑删除）
     * 
     * @param id 文件ID
     * @param deleteBy 删除人
     * @return 是否成功
     */
    boolean deleteFile(Long id, String deleteBy);

    /**
     * 批量删除文件（逻辑删除）
     * 
     * @param ids 文件ID列表
     * @param deleteBy 删除人
     * @return 是否成功
     */
    boolean batchDeleteFiles(List<Long> ids, String deleteBy);

    /**
     * 获取文件下载URL
     * 
     * @param id 文件ID
     * @return 下载URL
     */
    String getDownloadUrl(Long id);

    /**
     * 获取文件统计信息
     * 
     * @param businessType 业务类型（可选）
     * @param projectCode 项目编号（可选）
     * @return 统计信息
     */
    Object getFileStatistics(String businessType, String projectCode);
}
