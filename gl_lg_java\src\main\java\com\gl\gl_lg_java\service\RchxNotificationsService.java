package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxNotifications;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_notifications(教师通知表)】的数据库操作Service
 * @createDate 2025-07-24 13:00:00
 */
public interface RchxNotificationsService extends IService<RchxNotifications> {

    /**
     * 分页查询教师通知列表
     * @param zgh 职工号
     * @param current 当前页
     * @param size 每页大小
     * @param isRead 是否已读（null表示查询所有）
     * @param type 通知类型（null表示查询所有）
     * @return 分页结果
     */
    IPage<RchxNotifications> getNotificationPage(String zgh, long current, long size, 
                                                  Integer isRead, String type);

    /**
     * 获取教师未读通知数量
     * @param zgh 职工号
     * @return 未读通知数量
     */
    long getUnreadCount(String zgh);

    /**
     * 获取教师最新通知
     * @param zgh 职工号
     * @param limit 数量限制
     * @return 通知列表
     */
    List<RchxNotifications> getLatestNotifications(String zgh, int limit);

    /**
     * 标记通知为已读
     * @param zgh 职工号
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean markAsRead(String zgh, Long notificationId);

    /**
     * 批量标记通知为已读
     * @param zgh 职工号
     * @param ids 通知ID列表
     * @return 更新数量
     */
    int batchMarkAsRead(String zgh, List<Long> ids);

    /**
     * 标记所有通知为已读
     * @param zgh 职工号
     * @return 更新数量
     */
    int markAllAsRead(String zgh);

    /**
     * 创建通知
     * @param zgh 接收者职工号
     * @param title 标题
     * @param content 内容
     * @param type 类型
     * @param priority 优先级
     * @param senderZgh 发送者职工号
     * @param senderName 发送者姓名
     * @return 通知ID
     */
    Long createNotification(String zgh, String title, String content, String type, 
                           Integer priority, String senderZgh, String senderName);

    /**
     * 获取系统统计信息
     * @return 统计信息
     */
    Map<String, Object> getSystemStats();
}
