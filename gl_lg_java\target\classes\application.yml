server:
  port: 8080
  address: 0.0.0.0

spring:
  # DevTools热更新配置
  devtools:
    restart:
      enabled: true                    # 启用热重启
      additional-paths: src/main/java  # 监控的路径
      exclude: static/**,public/**     # 排除静态资源
    livereload:
      enabled: true                    # 启用LiveReload
      port: 35729                      # LiveReload端口

  datasource:
    url: jdbc:mysql://**************:3306/glrchx?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8&autoReconnect=true&failOverReadOnly=false&maxReconnects=10
    username: glrchx
    password: glrchx
    driver-class-name: com.mysql.cj.jdbc.Driver
    # HikariCP连接池配置
    hikari:
      # 连接池名称
      pool-name: HikariPool-GL
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接池大小
      maximum-pool-size: 20
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 空闲连接超时时间（毫秒）
      idle-timeout: 600000
      # 连接最大生命周期（毫秒）- 设置为4小时，小于MySQL的8小时超时
      max-lifetime: 14400000
      # 连接测试查询
      connection-test-query: SELECT 1
      # 是否自动提交
      auto-commit: true
      # 连接泄漏检测阈值（毫秒）
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # 邮件服务配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: hnyttbhmpixjchjg
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            trust: smtp.qq.com

  # Redis配置（使用服务器Redis）
  redis:
    host: ***************
    port: 6379
    password: bb47f906b8a6b984
    database: 2  # 使用数据库2，避免与其他应用冲突
    timeout: 10000ms  # 增加连接超时时间
    connect-timeout: 10000ms  # 连接超时
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 10000ms  # 等待时间
      shutdown-timeout: 100ms
    # 集群配置（如果需要）
    # cluster:
    #   nodes: 127.0.0.1:6379
    #   max-redirects: 3

  # WebSocket配置
  websocket:
    # 消息缓冲区大小
    message:
      size-limit: 8192
    # 会话超时时间 (8小时 = 28800000ms)
    session:
      timeout: 28800000

# MinIO对象存储配置
minio:
  # MinIO服务器地址
  endpoint: http://**************:9000
  # 访问密钥
  access-key: minio
  # 秘密密钥
  secret-key: rchxminio
  # 默认存储桶名称
  bucket-name: rchx
  # 文件访问URL前缀（用于生成文件访问链接）
  url-prefix: http://**************:9000
  # 连接超时时间（秒）
  connect-timeout: 10
  # 写入超时时间（秒）
  write-timeout: 60
  # 读取超时时间（秒）
  read-timeout: 10

# 宝塔邮局配置
bt:
  mail:
#    host: rchx.159email.shop:8888  # 宝塔面板端口，根据demo.py示例
    host: rchx.159email.shop     #rchx.159email.shop:8888  宝塔面板端口，根据demo.py示例
    from: <EMAIL>
    password: rchxRCHX123

# 邮件服务类型选择 (spring: Spring邮件服务, bt: 宝塔邮局服务)
mail:
  service:
    type: bt

# MyBatis配置 - 纯注解模式，不使用XML
mybatis-plus:
  # 完全不扫描XML文件，使用纯注解模式
  type-aliases-package: com.gl.gl_lg_java.domain
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-statement-timeout: 30

logging:
  level:
    com.gl.gl_lg_java: debug

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 通知系统配置（包含WebSocket自定义配置）
teacher-notification:
  # WebSocket自定义配置（长连接优化）
  websocket:
    endpoint: /ws/teacher-notifications
    allowed-origins: "*"
    heartbeat-interval: 30000      # 30秒心跳间隔
    max-connections: 1000          # 增加最大连接数
    connection-timeout: 300000     # 5分钟无响应超时
    enable-long-connection: true   # 启用长连接模式
  # 批量推送大小
  batch-size: 50
  # 重试次数
  retry-times: 3
  # 过期时间(天)
  expire-days: 30
  # 是否启用桌面通知
  desktop-notification: true
  # 通知保留数量（每个用户）
  max-notifications-per-user: 100
  # Redis缓存配置
  redis:
    # 连接状态缓存时间(秒)
    connection-expire: 3600
    # 未读数量缓存时间(秒)
    unread-count-expire: 1800
