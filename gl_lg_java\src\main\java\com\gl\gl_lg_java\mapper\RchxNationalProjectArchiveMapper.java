package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gl.gl_lg_java.domain.RchxNationalProjectArchive;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_national_project_archive(国家/省部级项目归档主表)】的数据库操作Mapper
 * @createDate 2025-01-04 10:00:00
 * @Entity com.gl.gl_lg_java.domain.RchxNationalProjectArchive
 */
@Mapper
public interface RchxNationalProjectArchiveMapper extends BaseMapper<RchxNationalProjectArchive> {

        /**
         * 核心字段列表 - 用于列表查询优化
         */
        String CORE_FIELDS = "id, archive_code, project_code, project_name, level_id, dept_id, " +
                        "project_leader_zgh, project_leader_name, project_start_date, project_end_date, " +
                        "project_budget, actual_funding, archive_status, archive_time, create_time";

        /**
         * 根据归档编号查询
         */
        @Select("SELECT * FROM t_rchx_national_project_archive WHERE archive_code = #{archiveCode}")
        RchxNationalProjectArchive findByArchiveCode(@Param("archiveCode") String archiveCode);

        /**
         * 根据项目编号查询
         */
        @Select("SELECT * FROM t_rchx_national_project_archive WHERE project_code = #{projectCode}")
        RchxNationalProjectArchive findByProjectCode(@Param("projectCode") String projectCode);

        /**
         * 根据项目名称模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_archive WHERE project_name LIKE CONCAT('%', #{projectName}, '%') ORDER BY create_time DESC")
        List<RchxNationalProjectArchive> findByProjectNameLike(@Param("projectName") String projectName);

        /**
         * 根据项目负责人职工号查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_archive WHERE project_leader_zgh = #{leaderZgh} ORDER BY create_time DESC")
        List<RchxNationalProjectArchive> findByProjectLeaderZgh(@Param("leaderZgh") String leaderZgh);

        /**
         * 根据项目负责人姓名模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_archive WHERE project_leader_name LIKE CONCAT('%', #{leaderName}, '%') ORDER BY create_time DESC")
        List<RchxNationalProjectArchive> findByProjectLeaderNameLike(@Param("leaderName") String leaderName);

        /**
         * 根据归档状态查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_archive WHERE archive_status = #{archiveStatus} ORDER BY create_time DESC")
        List<RchxNationalProjectArchive> findByArchiveStatus(@Param("archiveStatus") String archiveStatus);

        /**
         * 根据项目级别ID查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_archive WHERE level_id = #{levelId} ORDER BY create_time DESC")
        List<RchxNationalProjectArchive> findByLevelId(@Param("levelId") Integer levelId);

        /**
         * 根据部门ID查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_archive WHERE dept_id = #{deptId} ORDER BY create_time DESC")
        List<RchxNationalProjectArchive> findByDeptId(@Param("deptId") Integer deptId);

        /**
         * 根据项目时间范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_national_project_archive WHERE " +
                        "project_start_date >= #{startDate} AND project_end_date <= #{endDate} " +
                        "ORDER BY project_start_date DESC")
        List<RchxNationalProjectArchive> findByProjectDateRange(@Param("startDate") LocalDate startDate,
                        @Param("endDate") LocalDate endDate);

        /**
         * 根据归档时间范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_national_project_archive WHERE " +
                        "archive_time >= #{startTime} AND archive_time <= #{endTime} " +
                        "ORDER BY archive_time DESC")
        List<RchxNationalProjectArchive> findByArchiveTimeRange(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询即将到期的项目（归档截止时间）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_national_project_archive WHERE " +
                        "archive_status = 'PENDING' AND archive_deadline IS NOT NULL AND " +
                        "archive_deadline <= #{deadline} ORDER BY archive_deadline ASC")
        List<RchxNationalProjectArchive> findPendingByDeadline(@Param("deadline") LocalDateTime deadline);

        // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错

        /**
         * 统计各状态的项目数量
         */
        @Select("SELECT archive_status, COUNT(*) as count FROM t_rchx_national_project_archive GROUP BY archive_status")
        List<Map<String, Object>> countByArchiveStatus();

        /**
         * 统计各级别的项目数量
         */
        @Select("SELECT npl.level_name, COUNT(*) as count FROM t_rchx_national_project_archive npa " +
                        "LEFT JOIN t_rchx_national_project_levels npl ON npa.level_id = npl.id " +
                        "GROUP BY npa.level_id, npl.level_name")
        List<Map<String, Object>> countByLevel();

        /**
         * 统计各部门的项目数量
         */
        @Select("SELECT d.dept_name, COUNT(*) as count FROM t_rchx_national_project_archive npa " +
                        "LEFT JOIN t_rchx_departments d ON npa.dept_id = d.id " +
                        "GROUP BY npa.dept_id, d.dept_name")
        List<Map<String, Object>> countByDepartment();

        /**
         * 获取项目归档概览（关联查询）
         */
        @Select("SELECT npa.*, npl.level_name, npl.level_type, d.dept_name " +
                        "FROM t_rchx_national_project_archive npa " +
                        "LEFT JOIN t_rchx_national_project_levels npl ON npa.level_id = npl.id " +
                        "LEFT JOIN t_rchx_departments d ON npa.dept_id = d.id " +
                        "WHERE npa.id = #{id}")
        Map<String, Object> getArchiveOverview(@Param("id") Long id);
}
