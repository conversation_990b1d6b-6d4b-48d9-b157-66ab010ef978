package com.gl.gl_lg_java.matcher.impl;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.matcher.ProjectMatcher;
import com.gl.gl_lg_java.util.CommonConditionValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;

/**
 * 领军人才引育计划项目匹配算法（简化版）
 */
@Component
@Slf4j
public class LeaderTalentMatcher implements ProjectMatcher {
    
    @Autowired
    private CommonConditionValidator validator;
    
    @Override
    public String getProjectCode() {
        return "LJRC";
    }
    
    @Override
    public String getProjectName() {
        return "领军人才引育计划项目";
    }
    
    @Override
    public MatchResult matchTeacher(RchxJzgjbxx teacher, AnalysisParams params) {
        log.info("开始分析教师{}的领军人才匹配度", teacher.getZgh());
        
        MatchResult result = MatchResult.builder()
            .teacherZgh(teacher.getZgh())
            .teacherName(teacher.getXm())
            .department(teacher.getBm())
            .title(teacher.getZc())
            .age(validator.calculateAge(teacher))
            .createdTime(LocalDateTime.now())
            .strengths(new ArrayList<>())
            .weaknesses(new ArrayList<>())
            .recommendations(new ArrayList<>())
            .build();
        
        try {
            // 年龄检查
            Integer age = validator.calculateAge(teacher);
            if (age != null && age > 55) {
                result.setIsQualified(false);
                result.setReason("年龄超过55周岁限制");
                result.setMatchScore(0.0);
                result.calculateMatchLevel();
                return result;
            }
            
            // 简化的匹配逻辑
            double score = 60.0; // 基础分
            
            // 基于职称给分（领军人才要求较高）
            if (teacher.getZc() != null) {
                if (teacher.getZc().contains("教授")) {
                    score += 25.0;
                    result.getStrengths().add("具有教授职称");
                } else if (teacher.getZc().contains("副教授")) {
                    score += 15.0;
                    result.getStrengths().add("具有副教授职称");
                } else {
                    result.getWeaknesses().add("职称水平有待提升");
                }
            }
            
            // 年龄优势
            if (age != null && age <= 55) {
                score += 10.0;
                result.getStrengths().add(String.format("年龄符合要求（%d岁≤55岁）", age));
            }
            
            // 基于部门推断学科影响力
            if (teacher.getBm() != null && !teacher.getBm().isEmpty()) {
                score += 5.0;
                result.getStrengths().add("有明确的学科归属");
            }
            
            result.setMatchScore(score);
            result.setIsQualified(score >= params.getMinMatchScore());
            result.calculateMatchLevel();
            
            // 生成建议
            if (result.getIsQualified()) {
                result.getRecommendations().add("建议准备人才层次认定材料");
                result.getRecommendations().add("完善重大成果证明文件");
            } else {
                result.getRecommendations().add("建议提升学术影响力和成果水平");
                result.getRecommendations().add("争取获得自治区C类及以上人才认定");
            }
            
            log.info("教师{}领军人才匹配分析完成，得分: {}", teacher.getZgh(), score);
            
        } catch (Exception e) {
            log.error("教师{}领军人才匹配分析失败", teacher.getZgh(), e);
            result.setIsQualified(false);
            result.setReason("分析过程中发生错误: " + e.getMessage());
            result.setMatchScore(0.0);
            result.calculateMatchLevel();
        }
        
        return result;
    }
    
    @Override
    public boolean validateBasicConditions(RchxJzgjbxx teacher) {
        return teacher.getXm() != null && !teacher.getXm().isEmpty() &&
               teacher.getZgh() != null && !teacher.getZgh().isEmpty();
    }
}
