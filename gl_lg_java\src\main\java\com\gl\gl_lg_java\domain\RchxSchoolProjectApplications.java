package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 校级项目在线申报实体类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_rchx_school_project_applications")
public class RchxSchoolProjectApplications implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申报ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申报编号
     */
    @TableField("application_code")
    private String applicationCode;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目类别ID
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 申报人所属部门ID
     */
    @TableField("dept_id")
    private Integer deptId;

    /**
     * 申报人职工号
     */
    @TableField("applicant_zgh")
    private String applicantZgh;

    /**
     * 申报人姓名
     */
    @TableField("applicant_name")
    private String applicantName;

    /**
     * 申报人电话
     */
    @TableField("applicant_phone")
    private String applicantPhone;

    /**
     * 申报人邮箱
     */
    @TableField("applicant_email")
    private String applicantEmail;

    /**
     * 申报人职称
     */
    @TableField("applicant_title")
    private String applicantTitle;

    /**
     * 申请资助资金(万元)
     */
    @TableField("funding_amount")
    private BigDecimal fundingAmount;

    /**
     * 项目描述
     */
    @TableField("project_description")
    private String projectDescription;

    /**
     * 申报理由
     */
    @TableField("application_reason")
    private String applicationReason;

    /**
     * 预期成果
     */
    @TableField("expected_outcomes")
    private String expectedOutcomes;

    /**
     * 实施方案
     */
    @TableField("implementation_plan")
    private String implementationPlan;

    /**
     * 经费预算
     */
    @TableField("budget_plan")
    private String budgetPlan;

    /**
     * 团队成员信息
     */
    @TableField("team_members")
    private String teamMembers;

    /**
     * 申报截止日期
     */
    @TableField("application_deadline")
    private LocalDateTime applicationDeadline;

    /**
     * 项目开始日期
     */
    @TableField("project_start_date")
    private LocalDate projectStartDate;

    /**
     * 项目结束日期
     */
    @TableField("project_end_date")
    private LocalDate projectEndDate;

    /**
     * 状态(DRAFT草稿,SUBMITTED已提交,REVIEWING审核中,APPROVED通过,REJECTED拒绝,WITHDRAWN撤回)
     */
    @TableField("status")
    private String status;

    /**
     * 申报状态(PREPARING准备中,SUBMITTED已提交,UNDER_REVIEW初审中,EXPERT_REVIEW专家评审,APPROVED已通过,REJECTED已拒绝)
     */
    @TableField("application_status")
    private String applicationStatus;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;

    /**
     * 审核开始时间
     */
    @TableField("review_start_time")
    private LocalDateTime reviewStartTime;

    /**
     * 审核结束时间
     */
    @TableField("review_end_time")
    private LocalDateTime reviewEndTime;

    /**
     * 审核人职工号
     */
    @TableField("reviewer_zgh")
    private String reviewerZgh;

    /**
     * 审核人姓名
     */
    @TableField("reviewer_name")
    private String reviewerName;

    /**
     * 审核意见
     */
    @TableField("review_comments")
    private String reviewComments;

    /**
     * 评审得分
     */
    @TableField("review_score")
    private BigDecimal reviewScore;

    /**
     * 批准资助金额(万元)
     */
    @TableField("approval_amount")
    private BigDecimal approvalAmount;

    /**
     * 优先级(HIGH高,NORMAL普通,LOW低)
     */
    @TableField("priority_level")
    private String priorityLevel;

    /**
     * 是否在线申报(0否,1是)
     */
    @TableField("is_online_application")
    private Boolean isOnlineApplication;

    /**
     * 申报批次
     */
    @TableField("application_round")
    private String applicationRound;

    /**
     * 关键词(逗号分隔)
     */
    @TableField("keywords")
    private String keywords;

    /**
     * 研究领域
     */
    @TableField("research_field")
    private String researchField;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人职工号
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人职工号
     */
    @TableField("update_by")
    private String updateBy;
}
