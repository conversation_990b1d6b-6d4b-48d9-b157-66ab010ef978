package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;
/**
* <AUTHOR>
* @description 针对表【t_rchx_jzgjbxx(教职工基本信息)】的数据库操作Mapper
* @createDate 2025-07-18 11:11:41
* @Entity com.gl.gl_lg_java.domain.RchxJzgjbxx
*/
@Mapper
public interface RchxJzgjbxxMapper extends BaseMapper<RchxJzgjbxx> {

    /**
     * 核心字段列表 - 用于列表查询优化
     */
    String CORE_FIELDS = "zgh, xm, bm, xb, zc, zgxl, zgxw, dqzt, dh, dzxx, qx";

    /**
     * 根据职工号查询教职工信息（详情查询，返回所有字段）
     */
    @Select("SELECT * FROM t_rchx_jzgjbxx WHERE zgh = #{zgh}")
    RchxJzgjbxx findByZgh(String zgh);

    /**
     * 根据手机号查询教职工信息（详情查询，返回所有字段）
     */
    @Select("SELECT * FROM t_rchx_jzgjbxx WHERE dh = #{dh}")
    RchxJzgjbxx findByDh(String dh);

    /**
     * 根据身份证号查询教职工信息（详情查询，返回所有字段）
     */
    @Select("SELECT * FROM t_rchx_jzgjbxx WHERE sfzjh = #{sfzjh}")
    RchxJzgjbxx findBySfzjh(String sfzjh);

    /**
     * 根据邮箱查询教职工信息（详情查询，返回所有字段）
     */
    @Select("SELECT * FROM t_rchx_jzgjbxx WHERE dzxx = #{dzxx}")
    RchxJzgjbxx findByDzxx(String dzxx);

    /**
     * 根据账号查询教职工信息（详情查询，返回所有字段）
     * 支持职工号、手机号、身份证号、邮箱
     */
    @Select("SELECT * FROM t_rchx_jzgjbxx WHERE zgh = #{account} OR dh = #{account} OR sfzjh = #{account} OR dzxx = #{account}")
    RchxJzgjbxx findByAccount(String account);

    /**
     * 根据姓名模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzgjbxx WHERE xm LIKE CONCAT('%', #{xm}, '%')")
    List<RchxJzgjbxx> findByXmLike(@Param("xm") String xm);

    /**
     * 根据部门查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzgjbxx WHERE bm = #{bm}")
    List<RchxJzgjbxx> findByBm(@Param("bm") String bm);

    /**
     * 根据部门模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzgjbxx WHERE bm LIKE CONCAT('%', #{bm}, '%')")
    List<RchxJzgjbxx> findByBmLike(@Param("bm") String bm);

    /**
     * 根据职称查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzgjbxx WHERE zc = #{zc}")
    List<RchxJzgjbxx> findByZc(@Param("zc") String zc);

    /**
     * 根据权限查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzgjbxx WHERE qx = #{qx}")
    List<RchxJzgjbxx> findByQx(@Param("qx") String qx);

    /**
     * 根据性别查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzgjbxx WHERE xb = #{xb}")
    List<RchxJzgjbxx> findByXb(@Param("xb") String xb);

    /**
     * 根据学历查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzgjbxx WHERE zgxl = #{zgxl}")
    List<RchxJzgjbxx> findByZgxl(@Param("zgxl") String zgxl);

    /**
     * 根据学位查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzgjbxx WHERE zgxw = #{zgxw}")
    List<RchxJzgjbxx> findByZgxw(@Param("zgxw") String zgxw);

    /**
     * 快速获取所有部门选项（不统计数量）
     */
    @Select("SELECT DISTINCT bm FROM t_rchx_jzgjbxx WHERE bm IS NOT NULL AND bm != '' ORDER BY bm")
    List<String> findDistinctDepartments();

    /**
     * 快速获取所有当前状态选项（不统计数量）
     */
    @Select("SELECT DISTINCT dqzt FROM t_rchx_jzgjbxx WHERE dqzt IS NOT NULL AND dqzt != '' ORDER BY dqzt")
    List<String> findDistinctDqzt();

    /**
     * 按职称统计
     */
    @Select("SELECT COALESCE(zc, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_jzgjbxx GROUP BY zc")
    List<Map<String, Object>> getStatsByZc();

    /**
     * 快速获取所有职称选项（不统计数量）
     */
    @Select("SELECT DISTINCT zc FROM t_rchx_jzgjbxx WHERE zc IS NOT NULL AND zc != '' ORDER BY zc")
    List<String> findDistinctZc();

    /**
     * 按职称等级统计
     */
    @Select("SELECT COALESCE(zcdj, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_jzgjbxx GROUP BY zcdj")
    List<Map<String, Object>> getStatsByZcdj();

    /**
     * 按最高学历层次统计
     */
    @Select("SELECT COALESCE(zgxlcc, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_jzgjbxx GROUP BY zgxlcc")
    List<Map<String, Object>> getStatsByZgxlcc();

    /**
     * 快速获取所有职称等级选项（不统计数量）
     */
    @Select("SELECT DISTINCT zcdj FROM t_rchx_jzgjbxx WHERE zcdj IS NOT NULL AND zcdj != '' ORDER BY zcdj")
    List<String> findDistinctZcdj();

    /**
     * 快速获取所有最高学历层次选项（不统计数量）
     */
    @Select("SELECT DISTINCT zgxlcc FROM t_rchx_jzgjbxx WHERE zgxlcc IS NOT NULL AND zgxlcc != '' ORDER BY zgxlcc")
    List<String> findDistinctZgxlcc();

    /**
     * 按年龄段统计教师人数
     * 使用CASE WHEN计算年龄段：35岁以下、35-45岁、45岁以上
     */
    @Select("SELECT " +
            "CASE " +
            "  WHEN TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) < 35 THEN '35岁以下' " +
            "  WHEN TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) BETWEEN 35 AND 45 THEN '35-45岁' " +
            "  WHEN TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) > 45 THEN '45岁以上' " +
            "  ELSE '年龄未知' " +
            "END as fieldValue, " +
            "COUNT(*) as count " +
            "FROM t_rchx_jzgjbxx " +
            "WHERE csrq IS NOT NULL AND csrq != '' " +
            "GROUP BY fieldValue")
    List<Map<String, Object>> getStatsByAgeRange();

    /**
     * 按年龄段分页查询教师信息
     * @param page 分页对象
     * @param ageRange 年龄段：35岁以下、35-45岁、45岁以上，为空则查询所有
     */
    @Select("<script>" +
            "SELECT * FROM t_rchx_jzgjbxx " +
            "WHERE csrq IS NOT NULL AND csrq != '' " +
            "<if test='ageRange != null and ageRange != \"\"'>" +
            "AND CASE " +
            "  WHEN TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) &lt; 35 THEN '35岁以下' " +
            "  WHEN TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) BETWEEN 35 AND 45 THEN '35-45岁' " +
            "  WHEN TIMESTAMPDIFF(YEAR, STR_TO_DATE(csrq, '%Y-%m-%d'), CURDATE()) &gt; 45 THEN '45岁以上' " +
            "  ELSE '年龄未知' " +
            "END = #{ageRange} " +
            "</if>" +
            "ORDER BY csrq DESC" +
            "</script>")
    IPage<RchxJzgjbxx> findPageByAgeRange(Page<RchxJzgjbxx> page, @Param("ageRange") String ageRange);





    // 多条件查询已移至Service层使用QueryWrapper实现，更灵活且不易出错

    // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错
}




