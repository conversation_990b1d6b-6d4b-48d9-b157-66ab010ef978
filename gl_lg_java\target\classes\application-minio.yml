# MinIO配置示例
# 请根据实际环境修改配置参数

minio:
  # MinIO服务器地址
  endpoint: http://localhost:9000
  
  # 访问密钥
  access-key: minioadmin
  
  # 秘密密钥
  secret-key: minioadmin
  
  # 默认存储桶名称
  bucket-name: rchx-default
  
  # 校级项目文件存储桶名称
  school-project-bucket-name: rchx-school-project
  
  # 文件访问URL前缀
  url-prefix: http://localhost:9000
  
  # 连接超时时间（秒）
  connect-timeout: 30
  
  # 写入超时时间（秒）
  write-timeout: 60
  
  # 读取超时时间（秒）
  read-timeout: 60
  
  # 预签名URL过期时间（秒）
  presigned-url-expiry: 3600
  
  # 最大文件大小（字节）100MB
  max-file-size: 104857600
  
  # 允许的文件类型
  allowed-file-types:
    - application/pdf
    - application/msword
    - application/vnd.openxmlformats-officedocument.wordprocessingml.document
    - application/vnd.ms-excel
    - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    - application/vnd.ms-powerpoint
    - application/vnd.openxmlformats-officedocument.presentationml.presentation
    - image/jpeg
    - image/png
    - image/gif
    - text/plain
    - application/zip
    - application/x-rar-compressed

# 文件上传配置
spring:
  servlet:
    multipart:
      # 单个文件最大大小
      max-file-size: 100MB
      # 总上传大小限制
      max-request-size: 500MB
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 临时文件位置
      location: /tmp

# 日志配置
logging:
  level:
    com.gl.gl_lg_java.service.impl.MinioFileServiceImpl: DEBUG
    io.minio: DEBUG
