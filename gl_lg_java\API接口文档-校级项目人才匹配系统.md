# 校级项目人才匹配系统 API 接口文档

## 📋 接口概览

| 接口名称 | 请求方式 | 接口地址 | 功能描述 |
|---------|---------|---------|---------|
| 获取所有支持的校级项目 | GET | `/api/school-project-matching/projects` | 获取系统支持的所有校级项目列表 |
| 执行校级项目匹配分析 | POST | `/api/school-project-matching/analyze` | 对指定项目执行全员匹配分析 |
| 获取分析结果 | GET | `/api/school-project-matching/results/{analysisId}` | 根据分析ID获取分析结果 |
| 分析单个教师匹配情况 | POST | `/api/school-project-matching/analyze/teacher/{teacherZgh}` | 分析单个教师对指定项目的匹配情况 |
| 教师全项目匹配分析 | POST | `/api/school-project-matching/analyze/teacher/{teacherZgh}/all-projects` | 分析教师对所有项目的匹配情况 |
| 快速匹配分析 | GET | `/api/school-project-matching/quick-analyze` | 简化的快速分析接口 |

## 🔧 通用响应格式

所有接口都使用统一的响应格式：

```json
{
  "code": 200,           // 响应码：200-成功，其他-失败
  "message": "操作成功",  // 响应消息
  "data": {},           // 响应数据，具体结构见各接口说明
  "timestamp": "2025-01-08T10:30:00"  // 响应时间
}
```

## 📊 数据模型定义

### AnalysisParams（分析参数）
```json
{
  "timeRange": "5",           // 时间范围（年），可选值："5", "10", "all"
  "minMatchScore": 60.0,      // 最低匹配分数，范围：0-100
  "maxResults": 100,          // 最大结果数量，范围：1-1000
  "includeDetails": true,     // 是否包含详细信息
  "createdBy": "admin"        // 分析创建人（可选）
}
```

### MatchResult（匹配结果）
```json
{
  "teacherZgh": "202301001",        // 教师职工号
  "teacherName": "张三",            // 教师姓名
  "department": "计算机学院",        // 所在部门
  "title": "副教授",               // 职称
  "age": 35,                      // 年龄
  "matchScore": 85.5,             // 匹配分数（0-100）
  "matchLevel": "HIGH",           // 匹配等级：HIGH/MEDIUM/LOW/VERY_LOW
  "isQualified": true,            // 是否符合申报条件
  "reason": null,                 // 不符合原因（isQualified为false时）
  "strengths": [                  // 优势条件
    "具有副教授职称",
    "年龄符合要求（35岁≤36岁）"
  ],
  "weaknesses": [                 // 不足之处
    "高质量论文数量不足"
  ],
  "recommendations": [            // 改进建议
    "建议加强高质量论文发表",
    "积极申报国家级或省部级科研项目"
  ],
  "matchDetails": {},             // 详细匹配信息（可选）
  "createdTime": "2025-01-08T10:30:00"  // 创建时间
}
```

### AnalysisSummary（分析摘要）
```json
{
  "totalTeachers": 100,      // 总教师数
  "analyzedTeachers": 95,    // 已分析教师数
  "qualifiedTeachers": 25,   // 符合条件教师数
  "averageScore": 72.5,      // 平均匹配分数
  "highMatchCount": 8,       // 高匹配数量
  "mediumMatchCount": 12,    // 中匹配数量
  "lowMatchCount": 5         // 低匹配数量
}
```

---

## 🔍 接口详细说明

### 1. 获取所有支持的校级项目

**接口地址**: `GET /api/school-project-matching/projects`

**功能描述**: 获取系统支持的所有校级项目列表

**请求参数**: 无

**请求示例**:
```bash
curl -X GET "http://localhost:8080/api/school-project-matching/projects"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "projectCode": "CXTD",
      "projectName": "创新团队支持计划项目",
      "isEnabled": true,
      "category": "团队类"
    },
    {
      "projectCode": "FWGL",
      "projectName": "服务管理人才支持计划项目",
      "isEnabled": true,
      "category": "管理类"
    },
    {
      "projectCode": "LJRC",
      "projectName": "领军人才引育计划项目",
      "isEnabled": true,
      "category": "领军类"
    },
    {
      "projectCode": "PFXZ1",
      "projectName": "屏风学者引育计划项目-Ⅰ类",
      "isEnabled": true,
      "category": "屏风学者"
    },
    {
      "projectCode": "PFXZ2",
      "projectName": "屏风学者引育计划项目-Ⅱ类",
      "isEnabled": true,
      "category": "屏风学者"
    },
    {
      "projectCode": "PFXZ3",
      "projectName": "屏风学者引育计划项目-Ⅲ类",
      "isEnabled": true,
      "category": "屏风学者"
    },
    {
      "projectCode": "QNRC",
      "projectName": "青年人才未来工程项目",
      "isEnabled": true,
      "category": "青年类"
    },
    {
      "projectCode": "SHFW",
      "projectName": "社会服务人才培育计划项目",
      "isEnabled": true,
      "category": "服务类"
    }
  ],
  "timestamp": "2025-01-08T10:30:00"
}
```

**项目代码说明**:
- `CXTD`: 创新团队支持计划项目
- `FWGL`: 服务管理人才支持计划项目
- `LJRC`: 领军人才引育计划项目
- `PFXZ1`: 屏风学者引育计划项目-Ⅰ类
- `PFXZ2`: 屏风学者引育计划项目-Ⅱ类
- `PFXZ3`: 屏风学者引育计划项目-Ⅲ类
- `QNRC`: 青年人才未来工程项目
- `SHFW`: 社会服务人才培育计划项目

---

### 2. 执行校级项目匹配分析

**接口地址**: `POST /api/school-project-matching/analyze`

**功能描述**: 对指定项目执行全员匹配分析

**请求参数**:
```json
{
  "projectCode": "QNRC",        // 必填，项目代码
  "analysisParams": {           // 可选，分析参数
    "timeRange": "5",           // 可选，默认"5"
    "minMatchScore": 60.0,      // 可选，默认60.0
    "maxResults": 100,          // 可选，默认100
    "includeDetails": true,     // 可选，默认true
    "createdBy": "admin"        // 可选
  }
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/school-project-matching/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "projectCode": "QNRC",
    "analysisParams": {
      "timeRange": "5",
      "minMatchScore": 60,
      "maxResults": 50,
      "includeDetails": true
    }
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "analysisId": "ANALYSIS_1704700200000_123",
    "projectCode": "QNRC",
    "projectName": "青年人才未来工程项目",
    "status": "COMPLETED",
    "summary": {
      "totalTeachers": 100,
      "analyzedTeachers": 100,
      "qualifiedTeachers": 25,
      "averageScore": 72.5,
      "highMatchCount": 8,
      "mediumMatchCount": 12,
      "lowMatchCount": 5
    },
    "results": [
      {
        "teacherZgh": "202301001",
        "teacherName": "张三",
        "department": "计算机学院",
        "title": "副教授",
        "age": 35,
        "matchScore": 85.5,
        "matchLevel": "HIGH",
        "isQualified": true,
        "strengths": [
          "具有副教授职称",
          "年龄符合要求（35岁≤36岁）"
        ],
        "weaknesses": [],
        "recommendations": [
          "建议尽快准备申报材料",
          "完善成果证明文件"
        ],
        "createdTime": "2025-01-08T10:30:00"
      }
    ],
    "analysisTime": "2025-01-08T10:30:00",
    "completedTime": "2025-01-08T10:31:00"
  },
  "timestamp": "2025-01-08T10:31:00"
}
```

---

### 3. 获取分析结果

**接口地址**: `GET /api/school-project-matching/results/{analysisId}`

**功能描述**: 根据分析ID获取分析结果

**路径参数**:
- `analysisId`: 分析ID（必填）

**请求示例**:
```bash
curl -X GET "http://localhost:8080/api/school-project-matching/results/ANALYSIS_1704700200000_123"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "analysisId": "ANALYSIS_1704700200000_123",
    "projectCode": "QNRC",
    "projectName": "青年人才未来工程项目",
    "status": "COMPLETED",
    "summary": {
      "totalTeachers": 100,
      "analyzedTeachers": 100,
      "qualifiedTeachers": 25,
      "averageScore": 72.5,
      "highMatchCount": 8,
      "mediumMatchCount": 12,
      "lowMatchCount": 5
    },
    "results": [...],
    "analysisTime": "2025-01-08T10:30:00",
    "completedTime": "2025-01-08T10:31:00"
  },
  "timestamp": "2025-01-08T10:32:00"
}
```

**错误响应示例**:
```json
{
  "code": 404,
  "message": "分析结果不存在或已过期",
  "data": null,
  "timestamp": "2025-01-08T10:32:00"
}
```

---

### 4. 分析单个教师匹配情况

**接口地址**: `POST /api/school-project-matching/analyze/teacher/{teacherZgh}`

**功能描述**: 分析单个教师对指定项目的匹配情况

**路径参数**:
- `teacherZgh`: 教师职工号（必填）

**查询参数**:
- `projectCode`: 项目代码（必填）

**请求体**（可选）:
```json
{
  "timeRange": "5",
  "minMatchScore": 60.0,
  "maxResults": 100,
  "includeDetails": true
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/school-project-matching/analyze/teacher/202301001?projectCode=QNRC" \
  -H "Content-Type: application/json" \
  -d '{
    "timeRange": "5",
    "minMatchScore": 60,
    "includeDetails": true
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "teacherZgh": "202301001",
    "teacherName": "张三",
    "department": "计算机学院",
    "title": "副教授",
    "age": 35,
    "matchScore": 85.5,
    "matchLevel": "HIGH",
    "isQualified": true,
    "reason": null,
    "strengths": [
      "具有副教授职称",
      "年龄符合要求（35岁≤36岁）",
      "获得省部级三等奖及以上"
    ],
    "weaknesses": [],
    "recommendations": [
      "建议尽快准备申报材料",
      "完善成果证明文件"
    ],
    "createdTime": "2025-01-08T10:30:00"
  },
  "timestamp": "2025-01-08T10:30:00"
}
```

**错误响应示例**:
```json
{
  "code": 400,
  "message": "参数错误: 教师不存在: 202301001",
  "data": null,
  "timestamp": "2025-01-08T10:30:00"
}
```

---

### 5. 教师全项目匹配分析

**接口地址**: `POST /api/school-project-matching/analyze/teacher/{teacherZgh}/all-projects`

**功能描述**: 分析教师对所有项目的匹配情况

**路径参数**:
- `teacherZgh`: 教师职工号（必填）

**请求体**（可选）:
```json
{
  "timeRange": "5",
  "minMatchScore": 0.0,
  "maxResults": 100,
  "includeDetails": true
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/school-project-matching/analyze/teacher/202301001/all-projects" \
  -H "Content-Type: application/json" \
  -d '{
    "timeRange": "5",
    "includeDetails": true
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "teacherZgh": "202301001",
      "teacherName": "张三",
      "department": "计算机学院",
      "title": "副教授",
      "age": 35,
      "matchScore": 85.5,
      "matchLevel": "HIGH",
      "isQualified": true,
      "projectCode": "QNRC",
      "projectName": "青年人才未来工程项目",
      "strengths": ["具有副教授职称", "年龄符合要求"],
      "weaknesses": [],
      "recommendations": ["建议尽快准备申报材料"],
      "createdTime": "2025-01-08T10:30:00"
    },
    {
      "teacherZgh": "202301001",
      "teacherName": "张三",
      "department": "计算机学院",
      "title": "副教授",
      "age": 35,
      "matchScore": 75.0,
      "matchLevel": "MEDIUM",
      "isQualified": true,
      "projectCode": "LJRC",
      "projectName": "领军人才引育计划项目",
      "strengths": ["年龄符合要求", "具有副教授职称"],
      "weaknesses": ["需要提升到教授级别"],
      "recommendations": ["建议提升学术影响力"],
      "createdTime": "2025-01-08T10:30:00"
    }
  ],
  "timestamp": "2025-01-08T10:30:00"
}
```

---

### 6. 快速匹配分析

**接口地址**: `GET /api/school-project-matching/quick-analyze`

**功能描述**: 简化的快速分析接口，适合快速筛选和预览

**查询参数**:
- `projectCode`: 项目代码（必填）
- `minScore`: 最低匹配分数（可选，默认60）
- `maxResults`: 最大结果数量（可选，默认50）

**请求示例**:
```bash
curl -X GET "http://localhost:8080/api/school-project-matching/quick-analyze?projectCode=QNRC&minScore=70&maxResults=20"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "analysisId": "ANALYSIS_1704700200000_456",
    "projectCode": "QNRC",
    "projectName": "青年人才未来工程项目",
    "status": "COMPLETED",
    "summary": {
      "totalTeachers": 100,
      "analyzedTeachers": 100,
      "qualifiedTeachers": 15,
      "averageScore": 75.2,
      "highMatchCount": 5,
      "mediumMatchCount": 8,
      "lowMatchCount": 2
    },
    "results": [
      {
        "teacherZgh": "202301001",
        "teacherName": "张三",
        "department": "计算机学院",
        "title": "副教授",
        "age": 35,
        "matchScore": 85.5,
        "matchLevel": "HIGH",
        "isQualified": true,
        "createdTime": "2025-01-08T10:30:00"
      }
    ],
    "analysisTime": "2025-01-08T10:30:00",
    "completedTime": "2025-01-08T10:30:15"
  },
  "timestamp": "2025-01-08T10:30:15"
}
```

---

## 📋 错误码说明

| 错误码 | 错误信息 | 说明 |
|-------|---------|------|
| 200 | 操作成功 | 请求处理成功 |
| 400 | 参数错误 | 请求参数不正确或缺失必填参数 |
| 404 | 资源不存在 | 请求的资源不存在（如教师不存在、分析结果不存在） |
| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |

### 常见错误示例

#### 1. 项目代码无效
```json
{
  "code": 400,
  "message": "参数错误: 不支持的项目类型: INVALID",
  "data": null,
  "timestamp": "2025-01-08T10:30:00"
}
```

#### 2. 教师不存在
```json
{
  "code": 400,
  "message": "参数错误: 教师不存在: 999999999",
  "data": null,
  "timestamp": "2025-01-08T10:30:00"
}
```

#### 3. 分析结果过期
```json
{
  "code": 404,
  "message": "分析结果不存在或已过期",
  "data": null,
  "timestamp": "2025-01-08T10:30:00"
}
```

---

## 🎯 匹配等级说明

| 等级 | 英文标识 | 分数范围 | 说明 |
|-----|---------|---------|------|
| 高匹配 | HIGH | 85-100 | 非常符合项目要求，强烈推荐申报 |
| 中匹配 | MEDIUM | 70-84 | 基本符合项目要求，建议申报 |
| 低匹配 | LOW | 60-69 | 勉强符合项目要求，可考虑申报 |
| 极低匹配 | VERY_LOW | 0-59 | 不符合项目要求，不建议申报 |

---

## 🔧 前端集成示例

### JavaScript/Axios 示例

#### 1. 获取项目列表
```javascript
// 获取所有支持的项目
async function getProjects() {
  try {
    const response = await axios.get('/api/school-project-matching/projects');
    if (response.data.code === 200) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('获取项目列表失败:', error);
    throw error;
  }
}
```

#### 2. 执行匹配分析
```javascript
// 执行项目匹配分析
async function analyzeProject(projectCode, params = {}) {
  try {
    const requestData = {
      projectCode: projectCode,
      analysisParams: {
        timeRange: params.timeRange || "5",
        minMatchScore: params.minMatchScore || 60,
        maxResults: params.maxResults || 100,
        includeDetails: params.includeDetails !== false
      }
    };

    const response = await axios.post('/api/school-project-matching/analyze', requestData);
    if (response.data.code === 200) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('分析失败:', error);
    throw error;
  }
}
```

#### 3. 分析单个教师
```javascript
// 分析单个教师
async function analyzeSingleTeacher(teacherZgh, projectCode, params = {}) {
  try {
    const url = `/api/school-project-matching/analyze/teacher/${teacherZgh}?projectCode=${projectCode}`;
    const response = await axios.post(url, params);
    if (response.data.code === 200) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('教师分析失败:', error);
    throw error;
  }
}
```

#### 4. 快速分析
```javascript
// 快速分析
async function quickAnalyze(projectCode, minScore = 60, maxResults = 50) {
  try {
    const url = `/api/school-project-matching/quick-analyze?projectCode=${projectCode}&minScore=${minScore}&maxResults=${maxResults}`;
    const response = await axios.get(url);
    if (response.data.code === 200) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('快速分析失败:', error);
    throw error;
  }
}
```

### Vue.js 组件示例

```vue
<template>
  <div class="project-matching">
    <!-- 项目选择 -->
    <el-select v-model="selectedProject" placeholder="请选择项目">
      <el-option
        v-for="project in projects"
        :key="project.projectCode"
        :label="project.projectName"
        :value="project.projectCode">
      </el-option>
    </el-select>

    <!-- 分析参数 -->
    <el-form :model="analysisParams" inline>
      <el-form-item label="最低分数">
        <el-input-number v-model="analysisParams.minMatchScore" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="最大结果数">
        <el-input-number v-model="analysisParams.maxResults" :min="1" :max="1000"></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="startAnalysis" :loading="analyzing">开始分析</el-button>
      </el-form-item>
    </el-form>

    <!-- 分析结果 -->
    <div v-if="analysisResult">
      <h3>分析摘要</h3>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总教师数" :value="analysisResult.summary.totalTeachers"></el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="符合条件" :value="analysisResult.summary.qualifiedTeachers"></el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="平均分数" :value="analysisResult.summary.averageScore" :precision="1"></el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="高匹配" :value="analysisResult.summary.highMatchCount"></el-statistic>
        </el-col>
      </el-row>

      <h3>匹配结果</h3>
      <el-table :data="analysisResult.results" style="width: 100%">
        <el-table-column prop="teacherName" label="姓名"></el-table-column>
        <el-table-column prop="department" label="部门"></el-table-column>
        <el-table-column prop="title" label="职称"></el-table-column>
        <el-table-column prop="age" label="年龄"></el-table-column>
        <el-table-column prop="matchScore" label="匹配分数">
          <template #default="scope">
            <el-tag :type="getScoreType(scope.row.matchLevel)">
              {{ scope.row.matchScore.toFixed(1) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isQualified" label="是否符合">
          <template #default="scope">
            <el-tag :type="scope.row.isQualified ? 'success' : 'danger'">
              {{ scope.row.isQualified ? '符合' : '不符合' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProjectMatching',
  data() {
    return {
      projects: [],
      selectedProject: '',
      analysisParams: {
        timeRange: '5',
        minMatchScore: 60,
        maxResults: 100,
        includeDetails: true
      },
      analyzing: false,
      analysisResult: null
    }
  },
  async mounted() {
    await this.loadProjects();
  },
  methods: {
    async loadProjects() {
      try {
        this.projects = await getProjects();
      } catch (error) {
        this.$message.error('加载项目列表失败');
      }
    },

    async startAnalysis() {
      if (!this.selectedProject) {
        this.$message.warning('请先选择项目');
        return;
      }

      this.analyzing = true;
      try {
        this.analysisResult = await analyzeProject(this.selectedProject, this.analysisParams);
        this.$message.success('分析完成');
      } catch (error) {
        this.$message.error('分析失败: ' + error.message);
      } finally {
        this.analyzing = false;
      }
    },

    getScoreType(level) {
      switch (level) {
        case 'HIGH': return 'success';
        case 'MEDIUM': return 'warning';
        case 'LOW': return 'info';
        default: return 'danger';
      }
    }
  }
}
</script>
```

---

## 📞 技术支持

- **接口基础URL**: `http://localhost:8080` (开发环境)
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`
- **超时时间**: 建议设置60秒（分析接口可能需要较长时间）

### 注意事项

1. **分析时间**: 全员分析可能需要较长时间，建议前端显示加载状态
2. **结果缓存**: 分析结果有24小时缓存，相同参数的重复请求会返回缓存结果
3. **并发限制**: 系统限制最多5个并发分析任务
4. **数据更新**: 教师基础数据每日凌晨更新，建议在数据更新后重新分析

如有问题请联系开发团队。
```
