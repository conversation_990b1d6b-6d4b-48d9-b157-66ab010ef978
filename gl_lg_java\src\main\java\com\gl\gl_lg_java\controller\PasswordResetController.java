package com.gl.gl_lg_java.controller;

import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.dto.ForgotPasswordDTO;
import com.gl.gl_lg_java.dto.ResetPasswordDTO;
import com.gl.gl_lg_java.service.PasswordResetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 密码重置控制器
 */
@RestController
@RequestMapping("/api/auth")
@Slf4j
public class PasswordResetController {

    @Autowired
    private PasswordResetService passwordResetService;

    /**
     * 发送密码重置验证码
     * @param forgotPasswordDTO 忘记密码请求
     */
    @PostMapping("/forgot-password")
    public Result<String> forgotPassword(@RequestBody ForgotPasswordDTO forgotPasswordDTO) {
        try {
            log.info("收到密码重置请求: {}", forgotPasswordDTO.getAccount());

            if (forgotPasswordDTO.getAccount() == null || forgotPasswordDTO.getAccount().trim().isEmpty()) {
                return Result.error("账号不能为空");
            }

            boolean success = passwordResetService.sendResetCode(forgotPasswordDTO.getAccount().trim());

            if (success) {
                return Result.success("密码重置验证码已发送到您的邮箱，请查收");
            } else {
                return Result.error("发送失败，请检查账号是否存在或邮箱是否正确");
            }

        } catch (Exception e) {
            log.error("密码重置请求处理失败: {}", e.getMessage());
            return Result.error("系统错误，请稍后重试");
        }
    }

    /**
     * 验证验证码并重置密码
     * @param resetPasswordDTO 重置密码请求
     */
    @PostMapping("/reset-password")
    public Result<String> resetPassword(@RequestBody ResetPasswordDTO resetPasswordDTO) {
        try {
            log.info("收到密码重置确认请求: {}", resetPasswordDTO.getAccount());

            // 参数验证
            if (resetPasswordDTO.getAccount() == null || resetPasswordDTO.getAccount().trim().isEmpty()) {
                return Result.error("账号不能为空");
            }
            if (resetPasswordDTO.getCode() == null || resetPasswordDTO.getCode().trim().isEmpty()) {
                return Result.error("验证码不能为空");
            }
            if (resetPasswordDTO.getNewPassword() == null || resetPasswordDTO.getNewPassword().trim().isEmpty()) {
                return Result.error("新密码不能为空");
            }

            // 密码长度验证
            if (resetPasswordDTO.getNewPassword().length() < 6) {
                return Result.error("密码长度不能少于6位");
            }

            boolean success = passwordResetService.resetPassword(
                resetPasswordDTO.getAccount().trim(),
                resetPasswordDTO.getCode().trim(),
                resetPasswordDTO.getNewPassword()
            );

            if (success) {
                return Result.success("密码重置成功，请使用新密码登录");
            } else {
                return Result.error("密码重置失败，请检查验证码是否正确或已过期");
            }

        } catch (Exception e) {
            log.error("密码重置确认处理失败: {}", e.getMessage());
            return Result.error("系统错误，请稍后重试");
        }
    }
}
