package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxDepartments;
import com.gl.gl_lg_java.mapper.RchxDepartmentsMapper;
import com.gl.gl_lg_java.service.RchxDepartmentsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 管理部门表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@Service
public class RchxDepartmentsServiceImpl extends ServiceImpl<RchxDepartmentsMapper, RchxDepartments> 
        implements RchxDepartmentsService {

    @Override
    public List<RchxDepartments> getEnabledDepartments() {
        QueryWrapper<RchxDepartments> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_enabled", true)
                   .orderByAsc("sort_order", "id");
        return list(queryWrapper);
    }

    @Override
    public RchxDepartments getByDeptCode(String deptCode) {
        QueryWrapper<RchxDepartments> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dept_code", deptCode);
        return getOne(queryWrapper);
    }

    @Override
    public boolean updateEnabled(Integer id, Boolean enabled) {
        try {
            RchxDepartments department = new RchxDepartments();
            department.setId(id);
            department.setIsEnabled(enabled);
            return updateById(department);
        } catch (Exception e) {
            log.error("更新管理部门启用状态失败: id={}, enabled={}", id, enabled, e);
            return false;
        }
    }

    @Override
    public boolean existsByDeptCode(String deptCode, Integer excludeId) {
        QueryWrapper<RchxDepartments> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dept_code", deptCode);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) > 0;
    }
}
