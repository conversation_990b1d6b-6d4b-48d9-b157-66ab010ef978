# 获奖成果基本信息统计接口文档

## 接口概述

本文档描述了获奖成果基本信息的统计接口，包括总数统计和各维度的分类统计。

## 1. 获奖成果总数统计

**接口地址**: `GET /api/hjcgjbxx/total-count`

**权限要求**: 教师+

**功能**: 获取获奖成果总数及各维度统计汇总

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "totalCount": 150,
        "timestamp": 1721376000000,
        "shztStats": {
            "已审核": 120,
            "待审核": 25,
            "已驳回": 5
        },
        "hjjbStats": {
            "国家级": 30,
            "省部级": 80,
            "市厅级": 40
        },
        "jldjStats": {
            "一等奖": 20,
            "二等奖": 50,
            "三等奖": 80
        },
        "cgxsStats": {
            "科技进步奖": 60,
            "自然科学奖": 40,
            "技术发明奖": 50
        }
    }
}
```

## 2. 按审核状态统计

**接口地址**: `GET /api/hjcgjbxx/stats/shzt`

**权限要求**: 教师+

**功能**: 按审核状态统计获奖成果数量

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "已审核": 120,
        "待审核": 25,
        "已驳回": 5
    }
}
```

## 3. 按教研室名称统计

**接口地址**: `GET /api/hjcgjbxx/stats/jysmc`

**权限要求**: 教师+

**功能**: 按教研室名称统计获奖成果数量

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "计算机教研室": 45,
        "软件工程教研室": 38,
        "网络工程教研室": 32,
        "信息安全教研室": 35
    }
}
```

## 4. 按颁奖单位统计

**接口地址**: `GET /api/hjcgjbxx/stats/bjdw`

**权限要求**: 教师+

**功能**: 按颁奖单位统计获奖成果数量

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "国家科学技术部": 15,
        "教育部": 25,
        "省科技厅": 40,
        "省教育厅": 35,
        "市科技局": 35
    }
}
```

## 5. 按成果形式统计

**接口地址**: `GET /api/hjcgjbxx/stats/cgxs`

**权限要求**: 教师+

**功能**: 按成果形式统计获奖成果数量

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "科技进步奖": 60,
        "自然科学奖": 40,
        "技术发明奖": 50
    }
}
```

## 6. 按获奖级别统计

**接口地址**: `GET /api/hjcgjbxx/stats/hjjb`

**权限要求**: 教师+

**功能**: 按获奖级别统计获奖成果数量

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "国家级": 30,
        "省部级": 80,
        "市厅级": 40
    }
}
```

## 7. 按奖励等级统计

**接口地址**: `GET /api/hjcgjbxx/stats/jldj`

**权限要求**: 教师+

**功能**: 按奖励等级统计获奖成果数量

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "一等奖": 20,
        "二等奖": 50,
        "三等奖": 80
    }
}
```

## 8. 按单位名称统计

**接口地址**: `GET /api/hjcgjbxx/stats/dwmc`

**权限要求**: 教师+

**功能**: 按单位名称统计获奖成果数量

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "计算机学院": 120,
        "软件学院": 30
    }
}
```

## 前端调用示例

```javascript
// 获取总数统计
const getTotalStats = async () => {
    try {
        const response = await fetch('/api/hjcgjbxx/total-count', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (result.code === 200) {
            console.log('总数统计:', result.data);
        }
    } catch (error) {
        console.error('获取统计失败:', error);
    }
};

// 获取审核状态统计
const getShztStats = async () => {
    try {
        const response = await fetch('/api/hjcgjbxx/stats/shzt', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (result.code === 200) {
            console.log('审核状态统计:', result.data);
        }
    } catch (error) {
        console.error('获取统计失败:', error);
    }
};
```

## 注意事项

1. 所有统计接口都需要至少"教师"权限
2. 统计结果中，空值或null值会显示为"NULL或空"
3. 统计数据实时计算，反映当前数据库状态
4. 建议在前端进行适当的缓存以提高性能
5. 大数据量情况下可能需要考虑分页或异步处理

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |
