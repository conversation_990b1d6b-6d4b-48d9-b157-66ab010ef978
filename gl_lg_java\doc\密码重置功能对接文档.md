# 密码重置功能对接文档

## 概述
本文档描述了科研管理系统密码重置功能的前后端对接接口，包括发送重置验证码和验证码重置密码两个核心功能。

**重要说明：** 密码重置接口为公开接口，无需JWT Token认证，因为用户忘记密码时无法提供有效的Token。

## 功能流程
1. 用户输入账号（支持职工号、手机号、身份证号、邮箱）
2. 系统发送6位数字验证码到用户邮箱
3. 用户输入验证码和新密码
4. 系统验证并重置密码

## 接口列表

### 1. 发送密码重置验证码

**接口地址：** `POST /api/auth/forgot-password`

**权限要求：** 无需认证（公开接口）

**请求头：**
```
Content-Type: application/json
```

**请求参数：**
```json
{
  "account": "admin"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account | String | 是 | 用户账号，支持职工号、手机号、身份证号、邮箱 |

**响应示例：**

成功响应：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "密码重置验证码已发送到您的邮箱，请查收"
}
```

失败响应：
```json
{
  "code": 500,
  "message": "发送失败，请检查账号是否存在或邮箱是否正确",
  "data": null
}
```

**错误码说明：**
- `200`: 发送成功
- `500`: 发送失败（账号不存在、邮箱未设置、发送频繁等）

---

### 2. 验证码重置密码

**接口地址：** `POST /api/auth/reset-password`

**权限要求：** 无需认证（公开接口）

**请求头：**
```
Content-Type: application/json
```

**请求参数：**
```json
{
  "account": "admin",
  "code": "123456",
  "newPassword": "newpassword123"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account | String | 是 | 用户账号，与发送验证码时的账号一致 |
| code | String | 是 | 6位数字验证码 |
| newPassword | String | 是 | 新密码，长度不少于6位 |

**响应示例：**

成功响应：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "密码重置成功，请使用新密码登录"
}
```

失败响应：
```json
{
  "code": 500,
  "message": "密码重置失败，请检查验证码是否正确或已过期",
  "data": null
}
```

**错误码说明：**
- `200`: 重置成功
- `500`: 重置失败（验证码错误、过期、账号不存在等）

---

## 接口测试

### 1. 发送密码重置验证码
```bash
# 使用PowerShell（无需Token）
powershell -Command "Invoke-WebRequest -Uri 'http://localhost:8080/api/auth/forgot-password' -Method POST -Headers @{'Content-Type'='application/json'} -Body '{\"account\":\"admin\"}' | Select-Object -ExpandProperty Content"
```

### 2. 重置密码
```bash
# 使用PowerShell（无需Token）
powershell -Command "Invoke-WebRequest -Uri 'http://localhost:8080/api/auth/reset-password' -Method POST -Headers @{'Content-Type'='application/json'} -Body '{\"account\":\"admin\",\"code\":\"123456\",\"newPassword\":\"newpassword\"}' | Select-Object -ExpandProperty Content"
```

---

## 前端实现建议

### 1. 忘记密码页面结构
```html
<form>
  <!-- 步骤1：输入账号 -->
  <div v-if="step === 1">
    <input v-model="account" placeholder="请输入职工号/手机号/身份证号/邮箱" />
    <button @click="sendCode">发送验证码</button>
  </div>
  
  <!-- 步骤2：输入验证码和新密码 -->
  <div v-if="step === 2">
    <input v-model="code" placeholder="请输入6位验证码" maxlength="6" />
    <input v-model="newPassword" type="password" placeholder="请输入新密码" />
    <button @click="resetPassword">重置密码</button>
  </div>
</form>
```

### 2. Vue.js 示例代码
```javascript
export default {
  data() {
    return {
      step: 1,
      account: '',
      code: '',
      newPassword: '',
      countdown: 0
    }
  },
  methods: {
    // 发送验证码
    async sendCode() {
      if (!this.account.trim()) {
        this.$message.error('请输入账号');
        return;
      }
      
      try {
        const response = await this.$http.post('/api/auth/forgot-password', {
          account: this.account
        });
        
        if (response.data.code === 200) {
          this.$message.success(response.data.data);
          this.step = 2;
          this.startCountdown();
        } else {
          this.$message.error(response.data.message);
        }
      } catch (error) {
        this.$message.error('网络错误，请稍后重试');
      }
    },
    
    // 重置密码
    async resetPassword() {
      if (!this.code.trim()) {
        this.$message.error('请输入验证码');
        return;
      }
      if (!this.newPassword.trim()) {
        this.$message.error('请输入新密码');
        return;
      }
      if (this.newPassword.length < 6) {
        this.$message.error('密码长度不能少于6位');
        return;
      }
      
      try {
        const response = await this.$http.post('/api/auth/reset-password', {
          account: this.account,
          code: this.code,
          newPassword: this.newPassword
        });
        
        if (response.data.code === 200) {
          this.$message.success(response.data.data);
          // 跳转到登录页面
          this.$router.push('/login');
        } else {
          this.$message.error(response.data.message);
        }
      } catch (error) {
        this.$message.error('网络错误，请稍后重试');
      }
    },
    
    // 倒计时
    startCountdown() {
      this.countdown = 60;
      const timer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    }
  }
}
```

---

## 注意事项

### 1. 安全限制
- 验证码有效期：**15分钟**
- 发送频率限制：同一邮箱**1分钟内只能发送一次**
- 验证码使用次数：**仅可使用一次**

### 2. 用户体验优化
- 发送验证码后显示倒计时，防止频繁点击
- 验证码输入框限制6位数字
- 密码强度提示（建议至少6位）
- 错误信息友好提示

### 3. 错误处理
- 网络超时处理
- 服务器错误提示
- 参数验证错误提示
- 验证码过期提醒

### 4. 邮件内容
用户将收到如下格式的邮件：
```
主题：科研管理系统 - 密码重置验证码

尊敬的 张三：

您好！您正在申请重置科研管理系统的登录密码。

您的密码重置验证码是：123456

验证码有效期为15分钟，请及时使用。

安全提示：
• 请勿将验证码告诉他人
• 如果这不是您本人的操作，请忽略此邮件
• 验证码仅可使用一次

如有疑问，请联系系统管理员。

科研管理系统
2025年07月23日 16:30
```

---

## 测试用例

### 1. 正常流程测试
1. 输入有效账号 → 发送成功
2. 输入正确验证码和新密码 → 重置成功
3. 使用新密码登录 → 登录成功

### 2. 异常情况测试
1. 输入不存在的账号 → 提示账号不存在
2. 输入错误验证码 → 提示验证码错误
3. 验证码过期后使用 → 提示验证码过期
4. 密码长度不足 → 提示密码长度错误

---

## 联系方式
如有技术问题，请联系后端开发团队。
