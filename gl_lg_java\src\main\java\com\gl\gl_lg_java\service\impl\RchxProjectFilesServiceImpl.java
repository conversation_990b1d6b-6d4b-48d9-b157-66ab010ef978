package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.config.MinioProperties;
import com.gl.gl_lg_java.domain.RchxProjectFiles;
import com.gl.gl_lg_java.mapper.RchxProjectFilesMapper;
import com.gl.gl_lg_java.service.RchxProjectFilesService;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 项目文件表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@Service
public class RchxProjectFilesServiceImpl extends ServiceImpl<RchxProjectFilesMapper, RchxProjectFiles>
        implements RchxProjectFilesService {

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioProperties minioProperties;

    @Override
    public IPage<RchxProjectFiles> pageQuery(Page<RchxProjectFiles> page,
            String businessType,
            Long businessId,
            String projectCode,
            String fileCategory,
            String uploadBy) {
        QueryWrapper<RchxProjectFiles> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasText(businessType)) {
            queryWrapper.eq("business_type", businessType);
        }
        if (businessId != null) {
            queryWrapper.eq("business_id", businessId);
        }
        if (StringUtils.hasText(projectCode)) {
            queryWrapper.like("project_code", projectCode);
        }
        if (StringUtils.hasText(fileCategory)) {
            queryWrapper.eq("file_category", fileCategory);
        }
        if (StringUtils.hasText(uploadBy)) {
            queryWrapper.eq("upload_by", uploadBy);
        }

        queryWrapper.eq("is_deleted", false)
                .orderByDesc("upload_time");
        return page(page, queryWrapper);
    }

    @Override
    public List<RchxProjectFiles> getByBusiness(String businessType, Long businessId) {
        QueryWrapper<RchxProjectFiles> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("business_type", businessType)
                .eq("business_id", businessId)
                .eq("is_deleted", false)
                .orderByDesc("upload_time");
        return list(queryWrapper);
    }

    @Override
    public List<RchxProjectFiles> getByProjectCode(String projectCode) {
        QueryWrapper<RchxProjectFiles> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_code", projectCode)
                .eq("is_deleted", false)
                .orderByDesc("upload_time");
        return list(queryWrapper);
    }

    @Override
    public List<RchxProjectFiles> getByCategory(String businessType, Long businessId, String fileCategory) {
        QueryWrapper<RchxProjectFiles> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("business_type", businessType)
                .eq("business_id", businessId)
                .eq("file_category", fileCategory)
                .eq("is_deleted", false)
                .orderByDesc("upload_time");
        return list(queryWrapper);
    }

    @Override
    public RchxProjectFiles uploadFile(MultipartFile file,
            String businessType,
            Long businessId,
            String projectCode,
            String fileCategory,
            String description,
            String uploadBy) {
        try {
            // 生成文件名和路径
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }

            String datePrefix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String fileName = UUID.randomUUID().toString() + extension;
            String objectKey = "project-files/" + businessType.toLowerCase() + "/" + datePrefix + "/" + fileName;

            // 上传到MinIO，设置公共读取权限
            Map<String, String> headers = new HashMap<>();
            headers.put("x-amz-acl", "public-read");

            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(objectKey)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .headers(headers)
                    .build());

            // 保存文件信息到数据库
            RchxProjectFiles projectFile = new RchxProjectFiles();
            projectFile.setBusinessType(businessType);
            projectFile.setBusinessId(businessId);
            projectFile.setProjectCode(projectCode);
            projectFile.setFileName(fileName);
            projectFile.setFileOriginalName(originalFilename);
            projectFile.setFilePath(objectKey);
            projectFile.setFileSize(file.getSize());
            // 简化文件类型，避免超长MIME类型
            String fileType = simplifyFileType(file.getContentType(), extension);
            projectFile.setFileType(fileType);
            projectFile.setFileExtension(extension);
            projectFile.setBucketName(minioProperties.getBucketName());
            projectFile.setObjectKey(objectKey);
            projectFile.setContentType(file.getContentType());
            projectFile.setFileCategory(fileCategory);
            projectFile.setDescription(description);
            projectFile.setUploadBy(uploadBy);

            save(projectFile);

            log.info("文件上传成功: {}", objectKey);
            return projectFile;

        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(Long id, String deleteBy) {
        try {
            RchxProjectFiles file = new RchxProjectFiles();
            file.setId(id);
            file.setIsDeleted(true);
            file.setDeleteTime(LocalDateTime.now());
            file.setDeleteBy(deleteBy);
            return updateById(file);
        } catch (Exception e) {
            log.error("删除文件失败: id={}, deleteBy={}", id, deleteBy, e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteFiles(List<Long> ids, String deleteBy) {
        try {
            for (Long id : ids) {
                deleteFile(id, deleteBy);
            }
            return true;
        } catch (Exception e) {
            log.error("批量删除文件失败: ids={}, deleteBy={}", ids, deleteBy, e);
            return false;
        }
    }

    @Override
    public String getDownloadUrl(Long id) {
        try {
            RchxProjectFiles file = getById(id);
            if (file == null || file.getIsDeleted()) {
                return null;
            }

            // 生成预签名下载URL，有效期7天
            String presignedUrl = minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(file.getBucketName())
                            .object(file.getObjectKey())
                            .expiry(7, TimeUnit.DAYS)
                            .build());

            return presignedUrl;
        } catch (Exception e) {
            log.error("获取文件下载URL失败: id={}", id, e);
            return null;
        }
    }

    @Override
    public Object getFileStatistics(String businessType, String projectCode) {
        QueryWrapper<RchxProjectFiles> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", false);

        if (StringUtils.hasText(businessType)) {
            queryWrapper.eq("business_type", businessType);
        }
        if (StringUtils.hasText(projectCode)) {
            queryWrapper.eq("project_code", projectCode);
        }

        // 总数统计
        long totalCount = count(queryWrapper);

        // 简化的业务类型统计
        Map<String, Long> businessTypeCount = new HashMap<>();
        businessTypeCount.put("COLLECTION", 0L);
        businessTypeCount.put("APPLICATION", 0L);
        businessTypeCount.put("INSPECTION", 0L);
        businessTypeCount.put("COMPLETION", 0L);

        // 简化的文件分类统计
        Map<String, Long> categoryCount = new HashMap<>();
        categoryCount.put("GUIDE", 0L);
        categoryCount.put("MATERIAL", 0L);
        categoryCount.put("REPORT", 0L);
        categoryCount.put("OTHER", 0L);

        // 获取所有记录并统计
        List<RchxProjectFiles> allRecords = this.list(queryWrapper);
        for (RchxProjectFiles record : allRecords) {
            String recordBusinessType = record.getBusinessType();
            if (recordBusinessType != null && businessTypeCount.containsKey(recordBusinessType)) {
                businessTypeCount.put(recordBusinessType, businessTypeCount.get(recordBusinessType) + 1);
            }

            String fileCategory = record.getFileCategory();
            if (fileCategory != null && categoryCount.containsKey(fileCategory)) {
                categoryCount.put(fileCategory, categoryCount.get(fileCategory) + 1);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", totalCount);
        result.put("businessTypeCount", businessTypeCount);
        result.put("categoryCount", categoryCount);

        return result;
    }

    /**
     * 简化文件类型，避免超长MIME类型导致数据库字段长度超限
     */
    private String simplifyFileType(String contentType, String extension) {
        if (contentType == null || contentType.isEmpty()) {
            return getFileTypeByExtension(extension);
        }

        // 如果MIME类型太长，截断或简化
        if (contentType.length() > 50) {
            // 根据扩展名返回简化的类型
            return getFileTypeByExtension(extension);
        }

        return contentType;
    }

    /**
     * 根据文件扩展名获取简化的文件类型
     */
    private String getFileTypeByExtension(String extension) {
        if (extension == null) {
            return "application/octet-stream";
        }

        switch (extension.toLowerCase()) {
            case ".pdf":
                return "application/pdf";
            case ".doc":
            case ".docx":
                return "application/msword";
            case ".xls":
            case ".xlsx":
                return "application/excel";
            case ".ppt":
            case ".pptx":
                return "application/powerpoint";
            case ".txt":
                return "text/plain";
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".zip":
                return "application/zip";
            case ".rar":
                return "application/rar";
            default:
                return "application/octet-stream";
        }
    }
}
