package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 教职工基本信息
 * @TableName t_rchx_jzgjbxx
 */
@TableName(value ="t_rchx_jzgjbxx")
@Data
public class RchxJzgjbxx implements Serializable {
    /**
     * 职工号
     */
    @TableId(value = "zgh", type = IdType.INPUT)
    private String zgh;

    /**
     * 部门
     */
    @TableField(value = "bm")
    private String bm;

    /**
     * 部门代码
     */
    @TableField(value = "szdwdm")
    private String szdwdm;

    /**
     * 姓名
     */
    @TableField(value = "xm")
    private String xm;

    /**
     * 英文姓名
     */
    @TableField(value = "ywxm")
    private String ywxm;

    /**
     * 姓名拼音
     */
    @TableField(value = "xmpy")
    private String xmpy;

    /**
     * 曾用名
     */
    @TableField(value = "cym")
    private String cym;

    /**
     * 性别
     */
    @TableField(value = "xb")
    private String xb;

    /**
     * 性别码
     */
    @TableField(value = "xbm")
    private String xbm;

    /**
     * 出生日期
     */
    @TableField(value = "csrq")
    private String csrq;

    /**
     * 民族
     */
    @TableField(value = "mz")
    private String mz;

    /**
     * 民族码
     */
    @TableField(value = "mzm")
    private String mzm;

    /**
     * 籍贯
     */
    @TableField(value = "jg")
    private String jg;

    /**
     * 籍贯码
     */
    @TableField(value = "jgm")
    private String jgm;

    /**
     * 国籍/地区
     */
    @TableField(value = "gjdq")
    private String gjdq;

    /**
     * 国家地区码
     */
    @TableField(value = "gjdqm")
    private String gjdqm;

    /**
     * 身份证件号
     */
    @TableField(value = "sfzjh")
    private String sfzjh;

    /**
     * 政治面貌
     */
    @TableField(value = "zzmm")
    private String zzmm;

    /**
     * 政治面貌码
     */
    @TableField(value = "zzmmm")
    private String zzmmm;

    /**
     * 移动电话
     */
    @TableField(value = "yddh")
    private String yddh;

    /**
     * 电子信箱
     */
    @TableField(value = "dzxx")
    private String dzxx;

    /**
     * 最高学历
     */
    @TableField(value = "zgxl")
    private String zgxl;

    /**
     * 最高学历码
     */
    @TableField(value = "zgxlm")
    private String zgxlm;

    /**
     * 参加工作年月
     */
    @TableField(value = "cjgzny")
    private String cjgzny;

    /**
     * 来校日期
     */
    @TableField(value = "lxrq")
    private String lxrq;

    /**
     * 编制类别
     */
    @TableField(value = "bzlb")
    private String bzlb;

    /**
     * 编制类别码
     */
    @TableField(value = "bzlbm")
    private String bzlbm;

    /**
     * 当前状态
     */
    @TableField(value = "dqzt")
    private String dqzt;

    /**
     * 当前状态码
     */
    @TableField(value = "dqztm")
    private String dqztm;

    /**
     * 现从事专业号
     */
    @TableField(value = "xcszyh")
    private String xcszyh;

    /**
     * 现从事专业名称
     */
    @TableField(value = "xcszmc")
    private String xcszmc;

    /**
     * 薪酬方式
     */
    @TableField(value = "xcfs")
    private String xcfs;

    /**
     * 薪酬方式码
     */
    @TableField(value = "xcfsm")
    private String xcfsm;

    /**
     * 到校原因
     */
    @TableField(value = "dxyy")
    private String dxyy;

    /**
     * 岗位类型
     */
    @TableField(value = "gwlx")
    private String gwlx;

    /**
     * 岗位类型码
     */
    @TableField(value = "gwlxm")
    private String gwlxm;

    /**
     * 财政岗位执行等级
     */
    @TableField(value = "czgwzxdj")
    private String czgwzxdj;

    /**
     * 财政岗位执行等级码
     */
    @TableField(value = "czgwzxdjm")
    private String czgwzxdjm;

    /**
     * 最高学位
     */
    @TableField(value = "zgxw")
    private String zgxw;

    /**
     * 最高学位码
     */
    @TableField(value = "zgxwm")
    private String zgxwm;

    /**
     * 职务级别
     */
    @TableField(value = "zwjb")
    private String zwjb;

    /**
     * 职务级别码
     */
    @TableField(value = "zwjbm")
    private String zwjbm;

    /**
     * 职称
     */
    @TableField(value = "zc")
    private String zc;

    /**
     * 职称码
     */
    @TableField(value = "zcm")
    private String zcm;

    /**
     * 职称获得时间
     */
    @TableField(value = "zchdsj")
    private String zchdsj;

    /**
     * 现党政职务
     */
    @TableField(value = "xdzzw")
    private String xdzzw;

    /**
     * 财政岗位类别等级
     */
    @TableField(value = "czgwlbdj")
    private String czgwlbdj;

    /**
     * 最高学历层次
     */
    @TableField(value = "zgxlcc")
    private String zgxlcc;

    /**
     * 最高学历层次码
     */
    @TableField(value = "zgxlccm")
    private String zgxlccm;

    /**
     * 技术职务码
     */
    @TableField(value = "jszwm")
    private String jszwm;

    /**
     * 技术职务
     */
    @TableField(value = "jszw")
    private String jszw;

    /**
     * 毕业学校
     */
    @TableField(value = "byxx")
    private String byxx;

    /**
     * 职称等级
     */
    @TableField(value = "zcdj")
    private String zcdj;

    /**
     * 工龄
     */
    @TableField(value = "gl")
    private String gl;

    /**
     * 户口住址
     */
    @TableField(value = "hkzz")
    private String hkzz;

    /**
     * 家庭住址
     */
    @TableField(value = "jtzz")
    private String jtzz;

    /**
     * 现居住址
     */
    @TableField(value = "xjzz")
    private String xjzz;

    /**
     * 来校前单位
     */
    @TableField(value = "lxqdw")
    private String lxqdw;

    /**
     * 来源地区
     */
    @TableField(value = "lydq")
    private String lydq;

    /**
     * 博士后工作站
     */
    @TableField(value = "bshgzz")
    private String bshgzz;

    /**
     * 博士后学科
     */
    @TableField(value = "bshxk")
    private String bshxk;

    /**
     * 博士后出站时间
     */
    @TableField(value = "bshczsj")
    private String bshczsj;

    /**
     * 婚姻状况
     */
    @TableField(value = "hyzk")
    private String hyzk;

    /**
     * 信仰宗教
     */
    @TableField(value = "xyzj")
    private String xyzj;

    /**
     * 出生地
     */
    @TableField(value = "csd")
    private String csd;

    /**
     * 起薪日期
     */
    @TableField(value = "qxrq")
    private String qxrq;

    /**
     * 从教年月
     */
    @TableField(value = "cjny")
    private String cjny;

    /**
     * 教职工类别码
     */
    @TableField(value = "jzglbm")
    private String jzglbm;

    /**
     * 任课状态码
     */
    @TableField(value = "rkzkm")
    private String rkzkm;

    /**
     * 档案编号
     */
    @TableField(value = "dabh")
    private String dabh;

    /**
     * 档案文本
     */
    @TableField(value = "dawb")
    private String dawb;

    /**
     * 特长
     */
    @TableField(value = "tc")
    private String tc;

    /**
     * 学科类别码
     */
    @TableField(value = "xklbm")
    private String xklbm;

    /**
     * 一级学科码
     */
    @TableField(value = "yjxkm")
    private String yjxkm;

    /**
     * 二级学科码
     */
    @TableField(value = "ejxkm")
    private String ejxkm;

    /**
     * 研究方向
     */
    @TableField(value = "yjfx")
    private String yjfx;

    /**
     * 身份证件类型码
     */
    @TableField(value = "sfzjlxm")
    private String sfzjlxm;

    /**
     * 身份证件类型
     */
    @TableField(value = "sfzjlx")
    private String sfzjlx;

    /**
     * 港澳台侨外码
     */
    @TableField(value = "gatqwm")
    private String gatqwm;

    /**
     * 健康状况码
     */
    @TableField(value = "jkzkm")
    private String jkzkm;

    /**
     * 血型码
     */
    @TableField(value = "xxm")
    private String xxm;

    /**
     * 身份证件有效期
     */
    @TableField(value = "sfzjyxq")
    private String sfzjyxq;

    /**
     * 校区号
     */
    @TableField(value = "xqh")
    private String xqh;

    /**
     * 通信地址
     */
    @TableField(value = "txdz")
    private String txdz;

    /**
     * 电话
     */
    @TableField(value = "dh")
    private String dh;

    /**
     * 网络地址
     */
    @TableField(value = "wldz")
    private String wldz;

    /**
     * 即时通讯号
     */
    @TableField(value = "jstxh")
    private String jstxh;

    /**
     * 传真电话
     */
    @TableField(value = "czdh")
    private String czdh;

    /**
     * 教职工类型
     */
    @TableField(value = "jzglx")
    private String jzglx;

    /**
     * 时间戳
     */
    @TableField(value = "tstamp")
    private String tstamp;

    /**
     * 密码
     */
    @TableField(value = "pass")
    private String pass;

    /**
     * 权限
     */
    @TableField(value = "qx")
    private String qx;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RchxJzgjbxx other = (RchxJzgjbxx) that;
        return (this.getZgh() == null ? other.getZgh() == null : this.getZgh().equals(other.getZgh()))
            && (this.getBm() == null ? other.getBm() == null : this.getBm().equals(other.getBm()))
            && (this.getSzdwdm() == null ? other.getSzdwdm() == null : this.getSzdwdm().equals(other.getSzdwdm()))
            && (this.getXm() == null ? other.getXm() == null : this.getXm().equals(other.getXm()))
            && (this.getYwxm() == null ? other.getYwxm() == null : this.getYwxm().equals(other.getYwxm()))
            && (this.getXmpy() == null ? other.getXmpy() == null : this.getXmpy().equals(other.getXmpy()))
            && (this.getCym() == null ? other.getCym() == null : this.getCym().equals(other.getCym()))
            && (this.getXb() == null ? other.getXb() == null : this.getXb().equals(other.getXb()))
            && (this.getXbm() == null ? other.getXbm() == null : this.getXbm().equals(other.getXbm()))
            && (this.getCsrq() == null ? other.getCsrq() == null : this.getCsrq().equals(other.getCsrq()))
            && (this.getMz() == null ? other.getMz() == null : this.getMz().equals(other.getMz()))
            && (this.getMzm() == null ? other.getMzm() == null : this.getMzm().equals(other.getMzm()))
            && (this.getJg() == null ? other.getJg() == null : this.getJg().equals(other.getJg()))
            && (this.getJgm() == null ? other.getJgm() == null : this.getJgm().equals(other.getJgm()))
            && (this.getGjdq() == null ? other.getGjdq() == null : this.getGjdq().equals(other.getGjdq()))
            && (this.getGjdqm() == null ? other.getGjdqm() == null : this.getGjdqm().equals(other.getGjdqm()))
            && (this.getSfzjh() == null ? other.getSfzjh() == null : this.getSfzjh().equals(other.getSfzjh()))
            && (this.getZzmm() == null ? other.getZzmm() == null : this.getZzmm().equals(other.getZzmm()))
            && (this.getZzmmm() == null ? other.getZzmmm() == null : this.getZzmmm().equals(other.getZzmmm()))
            && (this.getYddh() == null ? other.getYddh() == null : this.getYddh().equals(other.getYddh()))
            && (this.getDzxx() == null ? other.getDzxx() == null : this.getDzxx().equals(other.getDzxx()))
            && (this.getZgxl() == null ? other.getZgxl() == null : this.getZgxl().equals(other.getZgxl()))
            && (this.getZgxlm() == null ? other.getZgxlm() == null : this.getZgxlm().equals(other.getZgxlm()))
            && (this.getCjgzny() == null ? other.getCjgzny() == null : this.getCjgzny().equals(other.getCjgzny()))
            && (this.getLxrq() == null ? other.getLxrq() == null : this.getLxrq().equals(other.getLxrq()))
            && (this.getBzlb() == null ? other.getBzlb() == null : this.getBzlb().equals(other.getBzlb()))
            && (this.getBzlbm() == null ? other.getBzlbm() == null : this.getBzlbm().equals(other.getBzlbm()))
            && (this.getDqzt() == null ? other.getDqzt() == null : this.getDqzt().equals(other.getDqzt()))
            && (this.getDqztm() == null ? other.getDqztm() == null : this.getDqztm().equals(other.getDqztm()))
            && (this.getXcszyh() == null ? other.getXcszyh() == null : this.getXcszyh().equals(other.getXcszyh()))
            && (this.getXcszmc() == null ? other.getXcszmc() == null : this.getXcszmc().equals(other.getXcszmc()))
            && (this.getXcfs() == null ? other.getXcfs() == null : this.getXcfs().equals(other.getXcfs()))
            && (this.getXcfsm() == null ? other.getXcfsm() == null : this.getXcfsm().equals(other.getXcfsm()))
            && (this.getDxyy() == null ? other.getDxyy() == null : this.getDxyy().equals(other.getDxyy()))
            && (this.getGwlx() == null ? other.getGwlx() == null : this.getGwlx().equals(other.getGwlx()))
            && (this.getGwlxm() == null ? other.getGwlxm() == null : this.getGwlxm().equals(other.getGwlxm()))
            && (this.getCzgwzxdj() == null ? other.getCzgwzxdj() == null : this.getCzgwzxdj().equals(other.getCzgwzxdj()))
            && (this.getCzgwzxdjm() == null ? other.getCzgwzxdjm() == null : this.getCzgwzxdjm().equals(other.getCzgwzxdjm()))
            && (this.getZgxw() == null ? other.getZgxw() == null : this.getZgxw().equals(other.getZgxw()))
            && (this.getZgxwm() == null ? other.getZgxwm() == null : this.getZgxwm().equals(other.getZgxwm()))
            && (this.getZwjb() == null ? other.getZwjb() == null : this.getZwjb().equals(other.getZwjb()))
            && (this.getZwjbm() == null ? other.getZwjbm() == null : this.getZwjbm().equals(other.getZwjbm()))
            && (this.getZc() == null ? other.getZc() == null : this.getZc().equals(other.getZc()))
            && (this.getZcm() == null ? other.getZcm() == null : this.getZcm().equals(other.getZcm()))
            && (this.getZchdsj() == null ? other.getZchdsj() == null : this.getZchdsj().equals(other.getZchdsj()))
            && (this.getXdzzw() == null ? other.getXdzzw() == null : this.getXdzzw().equals(other.getXdzzw()))
            && (this.getCzgwlbdj() == null ? other.getCzgwlbdj() == null : this.getCzgwlbdj().equals(other.getCzgwlbdj()))
            && (this.getZgxlcc() == null ? other.getZgxlcc() == null : this.getZgxlcc().equals(other.getZgxlcc()))
            && (this.getZgxlccm() == null ? other.getZgxlccm() == null : this.getZgxlccm().equals(other.getZgxlccm()))
            && (this.getJszwm() == null ? other.getJszwm() == null : this.getJszwm().equals(other.getJszwm()))
            && (this.getJszw() == null ? other.getJszw() == null : this.getJszw().equals(other.getJszw()))
            && (this.getByxx() == null ? other.getByxx() == null : this.getByxx().equals(other.getByxx()))
            && (this.getZcdj() == null ? other.getZcdj() == null : this.getZcdj().equals(other.getZcdj()))
            && (this.getGl() == null ? other.getGl() == null : this.getGl().equals(other.getGl()))
            && (this.getHkzz() == null ? other.getHkzz() == null : this.getHkzz().equals(other.getHkzz()))
            && (this.getJtzz() == null ? other.getJtzz() == null : this.getJtzz().equals(other.getJtzz()))
            && (this.getXjzz() == null ? other.getXjzz() == null : this.getXjzz().equals(other.getXjzz()))
            && (this.getLxqdw() == null ? other.getLxqdw() == null : this.getLxqdw().equals(other.getLxqdw()))
            && (this.getLydq() == null ? other.getLydq() == null : this.getLydq().equals(other.getLydq()))
            && (this.getBshgzz() == null ? other.getBshgzz() == null : this.getBshgzz().equals(other.getBshgzz()))
            && (this.getBshxk() == null ? other.getBshxk() == null : this.getBshxk().equals(other.getBshxk()))
            && (this.getBshczsj() == null ? other.getBshczsj() == null : this.getBshczsj().equals(other.getBshczsj()))
            && (this.getHyzk() == null ? other.getHyzk() == null : this.getHyzk().equals(other.getHyzk()))
            && (this.getXyzj() == null ? other.getXyzj() == null : this.getXyzj().equals(other.getXyzj()))
            && (this.getCsd() == null ? other.getCsd() == null : this.getCsd().equals(other.getCsd()))
            && (this.getQxrq() == null ? other.getQxrq() == null : this.getQxrq().equals(other.getQxrq()))
            && (this.getCjny() == null ? other.getCjny() == null : this.getCjny().equals(other.getCjny()))
            && (this.getJzglbm() == null ? other.getJzglbm() == null : this.getJzglbm().equals(other.getJzglbm()))
            && (this.getRkzkm() == null ? other.getRkzkm() == null : this.getRkzkm().equals(other.getRkzkm()))
            && (this.getDabh() == null ? other.getDabh() == null : this.getDabh().equals(other.getDabh()))
            && (this.getDawb() == null ? other.getDawb() == null : this.getDawb().equals(other.getDawb()))
            && (this.getTc() == null ? other.getTc() == null : this.getTc().equals(other.getTc()))
            && (this.getXklbm() == null ? other.getXklbm() == null : this.getXklbm().equals(other.getXklbm()))
            && (this.getYjxkm() == null ? other.getYjxkm() == null : this.getYjxkm().equals(other.getYjxkm()))
            && (this.getEjxkm() == null ? other.getEjxkm() == null : this.getEjxkm().equals(other.getEjxkm()))
            && (this.getYjfx() == null ? other.getYjfx() == null : this.getYjfx().equals(other.getYjfx()))
            && (this.getSfzjlxm() == null ? other.getSfzjlxm() == null : this.getSfzjlxm().equals(other.getSfzjlxm()))
            && (this.getSfzjlx() == null ? other.getSfzjlx() == null : this.getSfzjlx().equals(other.getSfzjlx()))
            && (this.getGatqwm() == null ? other.getGatqwm() == null : this.getGatqwm().equals(other.getGatqwm()))
            && (this.getJkzkm() == null ? other.getJkzkm() == null : this.getJkzkm().equals(other.getJkzkm()))
            && (this.getXxm() == null ? other.getXxm() == null : this.getXxm().equals(other.getXxm()))
            && (this.getSfzjyxq() == null ? other.getSfzjyxq() == null : this.getSfzjyxq().equals(other.getSfzjyxq()))
            && (this.getXqh() == null ? other.getXqh() == null : this.getXqh().equals(other.getXqh()))
            && (this.getTxdz() == null ? other.getTxdz() == null : this.getTxdz().equals(other.getTxdz()))
            && (this.getDh() == null ? other.getDh() == null : this.getDh().equals(other.getDh()))
            && (this.getWldz() == null ? other.getWldz() == null : this.getWldz().equals(other.getWldz()))
            && (this.getJstxh() == null ? other.getJstxh() == null : this.getJstxh().equals(other.getJstxh()))
            && (this.getCzdh() == null ? other.getCzdh() == null : this.getCzdh().equals(other.getCzdh()))
            && (this.getJzglx() == null ? other.getJzglx() == null : this.getJzglx().equals(other.getJzglx()))
            && (this.getTstamp() == null ? other.getTstamp() == null : this.getTstamp().equals(other.getTstamp()))
            && (this.getPass() == null ? other.getPass() == null : this.getPass().equals(other.getPass()))
            && (this.getQx() == null ? other.getQx() == null : this.getQx().equals(other.getQx()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getZgh() == null) ? 0 : getZgh().hashCode());
        result = prime * result + ((getBm() == null) ? 0 : getBm().hashCode());
        result = prime * result + ((getSzdwdm() == null) ? 0 : getSzdwdm().hashCode());
        result = prime * result + ((getXm() == null) ? 0 : getXm().hashCode());
        result = prime * result + ((getYwxm() == null) ? 0 : getYwxm().hashCode());
        result = prime * result + ((getXmpy() == null) ? 0 : getXmpy().hashCode());
        result = prime * result + ((getCym() == null) ? 0 : getCym().hashCode());
        result = prime * result + ((getXb() == null) ? 0 : getXb().hashCode());
        result = prime * result + ((getXbm() == null) ? 0 : getXbm().hashCode());
        result = prime * result + ((getCsrq() == null) ? 0 : getCsrq().hashCode());
        result = prime * result + ((getMz() == null) ? 0 : getMz().hashCode());
        result = prime * result + ((getMzm() == null) ? 0 : getMzm().hashCode());
        result = prime * result + ((getJg() == null) ? 0 : getJg().hashCode());
        result = prime * result + ((getJgm() == null) ? 0 : getJgm().hashCode());
        result = prime * result + ((getGjdq() == null) ? 0 : getGjdq().hashCode());
        result = prime * result + ((getGjdqm() == null) ? 0 : getGjdqm().hashCode());
        result = prime * result + ((getSfzjh() == null) ? 0 : getSfzjh().hashCode());
        result = prime * result + ((getZzmm() == null) ? 0 : getZzmm().hashCode());
        result = prime * result + ((getZzmmm() == null) ? 0 : getZzmmm().hashCode());
        result = prime * result + ((getYddh() == null) ? 0 : getYddh().hashCode());
        result = prime * result + ((getDzxx() == null) ? 0 : getDzxx().hashCode());
        result = prime * result + ((getZgxl() == null) ? 0 : getZgxl().hashCode());
        result = prime * result + ((getZgxlm() == null) ? 0 : getZgxlm().hashCode());
        result = prime * result + ((getCjgzny() == null) ? 0 : getCjgzny().hashCode());
        result = prime * result + ((getLxrq() == null) ? 0 : getLxrq().hashCode());
        result = prime * result + ((getBzlb() == null) ? 0 : getBzlb().hashCode());
        result = prime * result + ((getBzlbm() == null) ? 0 : getBzlbm().hashCode());
        result = prime * result + ((getDqzt() == null) ? 0 : getDqzt().hashCode());
        result = prime * result + ((getDqztm() == null) ? 0 : getDqztm().hashCode());
        result = prime * result + ((getXcszyh() == null) ? 0 : getXcszyh().hashCode());
        result = prime * result + ((getXcszmc() == null) ? 0 : getXcszmc().hashCode());
        result = prime * result + ((getXcfs() == null) ? 0 : getXcfs().hashCode());
        result = prime * result + ((getXcfsm() == null) ? 0 : getXcfsm().hashCode());
        result = prime * result + ((getDxyy() == null) ? 0 : getDxyy().hashCode());
        result = prime * result + ((getGwlx() == null) ? 0 : getGwlx().hashCode());
        result = prime * result + ((getGwlxm() == null) ? 0 : getGwlxm().hashCode());
        result = prime * result + ((getCzgwzxdj() == null) ? 0 : getCzgwzxdj().hashCode());
        result = prime * result + ((getCzgwzxdjm() == null) ? 0 : getCzgwzxdjm().hashCode());
        result = prime * result + ((getZgxw() == null) ? 0 : getZgxw().hashCode());
        result = prime * result + ((getZgxwm() == null) ? 0 : getZgxwm().hashCode());
        result = prime * result + ((getZwjb() == null) ? 0 : getZwjb().hashCode());
        result = prime * result + ((getZwjbm() == null) ? 0 : getZwjbm().hashCode());
        result = prime * result + ((getZc() == null) ? 0 : getZc().hashCode());
        result = prime * result + ((getZcm() == null) ? 0 : getZcm().hashCode());
        result = prime * result + ((getZchdsj() == null) ? 0 : getZchdsj().hashCode());
        result = prime * result + ((getXdzzw() == null) ? 0 : getXdzzw().hashCode());
        result = prime * result + ((getCzgwlbdj() == null) ? 0 : getCzgwlbdj().hashCode());
        result = prime * result + ((getZgxlcc() == null) ? 0 : getZgxlcc().hashCode());
        result = prime * result + ((getZgxlccm() == null) ? 0 : getZgxlccm().hashCode());
        result = prime * result + ((getJszwm() == null) ? 0 : getJszwm().hashCode());
        result = prime * result + ((getJszw() == null) ? 0 : getJszw().hashCode());
        result = prime * result + ((getByxx() == null) ? 0 : getByxx().hashCode());
        result = prime * result + ((getZcdj() == null) ? 0 : getZcdj().hashCode());
        result = prime * result + ((getGl() == null) ? 0 : getGl().hashCode());
        result = prime * result + ((getHkzz() == null) ? 0 : getHkzz().hashCode());
        result = prime * result + ((getJtzz() == null) ? 0 : getJtzz().hashCode());
        result = prime * result + ((getXjzz() == null) ? 0 : getXjzz().hashCode());
        result = prime * result + ((getLxqdw() == null) ? 0 : getLxqdw().hashCode());
        result = prime * result + ((getLydq() == null) ? 0 : getLydq().hashCode());
        result = prime * result + ((getBshgzz() == null) ? 0 : getBshgzz().hashCode());
        result = prime * result + ((getBshxk() == null) ? 0 : getBshxk().hashCode());
        result = prime * result + ((getBshczsj() == null) ? 0 : getBshczsj().hashCode());
        result = prime * result + ((getHyzk() == null) ? 0 : getHyzk().hashCode());
        result = prime * result + ((getXyzj() == null) ? 0 : getXyzj().hashCode());
        result = prime * result + ((getCsd() == null) ? 0 : getCsd().hashCode());
        result = prime * result + ((getQxrq() == null) ? 0 : getQxrq().hashCode());
        result = prime * result + ((getCjny() == null) ? 0 : getCjny().hashCode());
        result = prime * result + ((getJzglbm() == null) ? 0 : getJzglbm().hashCode());
        result = prime * result + ((getRkzkm() == null) ? 0 : getRkzkm().hashCode());
        result = prime * result + ((getDabh() == null) ? 0 : getDabh().hashCode());
        result = prime * result + ((getDawb() == null) ? 0 : getDawb().hashCode());
        result = prime * result + ((getTc() == null) ? 0 : getTc().hashCode());
        result = prime * result + ((getXklbm() == null) ? 0 : getXklbm().hashCode());
        result = prime * result + ((getYjxkm() == null) ? 0 : getYjxkm().hashCode());
        result = prime * result + ((getEjxkm() == null) ? 0 : getEjxkm().hashCode());
        result = prime * result + ((getYjfx() == null) ? 0 : getYjfx().hashCode());
        result = prime * result + ((getSfzjlxm() == null) ? 0 : getSfzjlxm().hashCode());
        result = prime * result + ((getSfzjlx() == null) ? 0 : getSfzjlx().hashCode());
        result = prime * result + ((getGatqwm() == null) ? 0 : getGatqwm().hashCode());
        result = prime * result + ((getJkzkm() == null) ? 0 : getJkzkm().hashCode());
        result = prime * result + ((getXxm() == null) ? 0 : getXxm().hashCode());
        result = prime * result + ((getSfzjyxq() == null) ? 0 : getSfzjyxq().hashCode());
        result = prime * result + ((getXqh() == null) ? 0 : getXqh().hashCode());
        result = prime * result + ((getTxdz() == null) ? 0 : getTxdz().hashCode());
        result = prime * result + ((getDh() == null) ? 0 : getDh().hashCode());
        result = prime * result + ((getWldz() == null) ? 0 : getWldz().hashCode());
        result = prime * result + ((getJstxh() == null) ? 0 : getJstxh().hashCode());
        result = prime * result + ((getCzdh() == null) ? 0 : getCzdh().hashCode());
        result = prime * result + ((getJzglx() == null) ? 0 : getJzglx().hashCode());
        result = prime * result + ((getTstamp() == null) ? 0 : getTstamp().hashCode());
        result = prime * result + ((getPass() == null) ? 0 : getPass().hashCode());
        result = prime * result + ((getQx() == null) ? 0 : getQx().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", zgh=").append(zgh);
        sb.append(", bm=").append(bm);
        sb.append(", szdwdm=").append(szdwdm);
        sb.append(", xm=").append(xm);
        sb.append(", ywxm=").append(ywxm);
        sb.append(", xmpy=").append(xmpy);
        sb.append(", cym=").append(cym);
        sb.append(", xb=").append(xb);
        sb.append(", xbm=").append(xbm);
        sb.append(", csrq=").append(csrq);
        sb.append(", mz=").append(mz);
        sb.append(", mzm=").append(mzm);
        sb.append(", jg=").append(jg);
        sb.append(", jgm=").append(jgm);
        sb.append(", gjdq=").append(gjdq);
        sb.append(", gjdqm=").append(gjdqm);
        sb.append(", sfzjh=").append(sfzjh);
        sb.append(", zzmm=").append(zzmm);
        sb.append(", zzmmm=").append(zzmmm);
        sb.append(", yddh=").append(yddh);
        sb.append(", dzxx=").append(dzxx);
        sb.append(", zgxl=").append(zgxl);
        sb.append(", zgxlm=").append(zgxlm);
        sb.append(", cjgzny=").append(cjgzny);
        sb.append(", lxrq=").append(lxrq);
        sb.append(", bzlb=").append(bzlb);
        sb.append(", bzlbm=").append(bzlbm);
        sb.append(", dqzt=").append(dqzt);
        sb.append(", dqztm=").append(dqztm);
        sb.append(", xcszyh=").append(xcszyh);
        sb.append(", xcszmc=").append(xcszmc);
        sb.append(", xcfs=").append(xcfs);
        sb.append(", xcfsm=").append(xcfsm);
        sb.append(", dxyy=").append(dxyy);
        sb.append(", gwlx=").append(gwlx);
        sb.append(", gwlxm=").append(gwlxm);
        sb.append(", czgwzxdj=").append(czgwzxdj);
        sb.append(", czgwzxdjm=").append(czgwzxdjm);
        sb.append(", zgxw=").append(zgxw);
        sb.append(", zgxwm=").append(zgxwm);
        sb.append(", zwjb=").append(zwjb);
        sb.append(", zwjbm=").append(zwjbm);
        sb.append(", zc=").append(zc);
        sb.append(", zcm=").append(zcm);
        sb.append(", zchdsj=").append(zchdsj);
        sb.append(", xdzzw=").append(xdzzw);
        sb.append(", czgwlbdj=").append(czgwlbdj);
        sb.append(", zgxlcc=").append(zgxlcc);
        sb.append(", zgxlccm=").append(zgxlccm);
        sb.append(", jszwm=").append(jszwm);
        sb.append(", jszw=").append(jszw);
        sb.append(", byxx=").append(byxx);
        sb.append(", zcdj=").append(zcdj);
        sb.append(", gl=").append(gl);
        sb.append(", hkzz=").append(hkzz);
        sb.append(", jtzz=").append(jtzz);
        sb.append(", xjzz=").append(xjzz);
        sb.append(", lxqdw=").append(lxqdw);
        sb.append(", lydq=").append(lydq);
        sb.append(", bshgzz=").append(bshgzz);
        sb.append(", bshxk=").append(bshxk);
        sb.append(", bshczsj=").append(bshczsj);
        sb.append(", hyzk=").append(hyzk);
        sb.append(", xyzj=").append(xyzj);
        sb.append(", csd=").append(csd);
        sb.append(", qxrq=").append(qxrq);
        sb.append(", cjny=").append(cjny);
        sb.append(", jzglbm=").append(jzglbm);
        sb.append(", rkzkm=").append(rkzkm);
        sb.append(", dabh=").append(dabh);
        sb.append(", dawb=").append(dawb);
        sb.append(", tc=").append(tc);
        sb.append(", xklbm=").append(xklbm);
        sb.append(", yjxkm=").append(yjxkm);
        sb.append(", ejxkm=").append(ejxkm);
        sb.append(", yjfx=").append(yjfx);
        sb.append(", sfzjlxm=").append(sfzjlxm);
        sb.append(", sfzjlx=").append(sfzjlx);
        sb.append(", gatqwm=").append(gatqwm);
        sb.append(", jkzkm=").append(jkzkm);
        sb.append(", xxm=").append(xxm);
        sb.append(", sfzjyxq=").append(sfzjyxq);
        sb.append(", xqh=").append(xqh);
        sb.append(", txdz=").append(txdz);
        sb.append(", dh=").append(dh);
        sb.append(", wldz=").append(wldz);
        sb.append(", jstxh=").append(jstxh);
        sb.append(", czdh=").append(czdh);
        sb.append(", jzglx=").append(jzglx);
        sb.append(", tstamp=").append(tstamp);
        sb.append(", pass=").append(pass);
        sb.append(", qx=").append(qx);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}