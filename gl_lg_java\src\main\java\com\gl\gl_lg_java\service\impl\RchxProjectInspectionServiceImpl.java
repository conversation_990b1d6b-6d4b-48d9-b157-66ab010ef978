package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxProjectInspection;
import com.gl.gl_lg_java.mapper.RchxProjectInspectionMapper;
import com.gl.gl_lg_java.service.RchxProjectInspectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目中检表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@Service
public class RchxProjectInspectionServiceImpl extends ServiceImpl<RchxProjectInspectionMapper, RchxProjectInspection>
        implements RchxProjectInspectionService {

    @Override
    public IPage<RchxProjectInspection> pageQuery(Page<RchxProjectInspection> page,
            Integer categoryId,
            Integer typeId,
            Integer deptId,
            String status,
            String projectCode,
            String projectLeaderZgh) {
        QueryWrapper<RchxProjectInspection> queryWrapper = new QueryWrapper<>();

        if (categoryId != null) {
            queryWrapper.eq("category_id", categoryId);
        }
        if (typeId != null) {
            queryWrapper.eq("type_id", typeId);
        }
        if (deptId != null) {
            queryWrapper.eq("dept_id", deptId);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        if (StringUtils.hasText(projectCode)) {
            queryWrapper.like("project_code", projectCode);
        }
        if (StringUtils.hasText(projectLeaderZgh)) {
            queryWrapper.eq("project_leader_zgh", projectLeaderZgh);
        }

        queryWrapper.orderByDesc("create_time");
        return page(page, queryWrapper);
    }

    @Override
    public List<RchxProjectInspection> getByProjectCode(String projectCode) {
        QueryWrapper<RchxProjectInspection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_code", projectCode)
                .orderByDesc("create_time");
        return list(queryWrapper);
    }

    @Override
    public List<RchxProjectInspection> getByProjectLeader(String projectLeaderZgh) {
        QueryWrapper<RchxProjectInspection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_leader_zgh", projectLeaderZgh)
                .orderByDesc("create_time");
        return list(queryWrapper);
    }

    @Override
    public boolean submitInspection(Long id, String submitBy) {
        try {
            RchxProjectInspection inspection = new RchxProjectInspection();
            inspection.setId(id);
            inspection.setStatus("SUBMITTED");
            inspection.setSubmitTime(LocalDateTime.now());
            inspection.setUpdateBy(submitBy);
            return updateById(inspection);
        } catch (Exception e) {
            log.error("提交中检失败: id={}, submitBy={}", id, submitBy, e);
            return false;
        }
    }

    @Override
    public boolean reviewInspection(Long id, boolean approved, String reviewerZgh, String reviewComments) {
        try {
            RchxProjectInspection inspection = new RchxProjectInspection();
            inspection.setId(id);
            inspection.setStatus(approved ? "APPROVED" : "REJECTED");
            inspection.setReviewTime(LocalDateTime.now());
            inspection.setReviewerZgh(reviewerZgh);
            inspection.setReviewComments(reviewComments);
            inspection.setUpdateBy(reviewerZgh);
            return updateById(inspection);
        } catch (Exception e) {
            log.error("审核中检失败: id={}, approved={}, reviewerZgh={}", id, approved, reviewerZgh, e);
            return false;
        }
    }

    @Override
    public Object getInspectionStatistics(Integer deptId, Integer categoryId) {
        QueryWrapper<RchxProjectInspection> queryWrapper = new QueryWrapper<>();

        if (deptId != null) {
            queryWrapper.eq("dept_id", deptId);
        }
        if (categoryId != null) {
            queryWrapper.eq("category_id", categoryId);
        }

        // 总数统计
        long totalCount = this.count(queryWrapper);

        // 简化的状态统计
        Map<String, Long> statusCount = new HashMap<>();
        statusCount.put("DRAFT", 0L);
        statusCount.put("SUBMITTED", 0L);
        statusCount.put("REVIEWING", 0L);
        statusCount.put("APPROVED", 0L);
        statusCount.put("REJECTED", 0L);

        // 获取所有记录并统计
        List<RchxProjectInspection> allRecords = this.list(queryWrapper);
        for (RchxProjectInspection record : allRecords) {
            String status = record.getStatus();
            if (status != null && statusCount.containsKey(status)) {
                statusCount.put(status, statusCount.get(status) + 1);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", totalCount);
        result.put("statusCount", statusCount);

        return result;
    }
}
