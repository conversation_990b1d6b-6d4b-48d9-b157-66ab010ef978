package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gl.gl_lg_java.annotation.RequirePermission;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxHjcgjbxx;
import com.gl.gl_lg_java.dto.HjcgjbxxQueryDTO;
import com.gl.gl_lg_java.service.RchxHjcgjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 获奖成果基本信息控制器
 */
@RestController
@RequestMapping("/api/hjcgjbxx")
@Slf4j
public class HjcgjbxxController {
    
    @Autowired
    private RchxHjcgjbxxService hjcgjbxxService;
    
    /**
     * 根据获奖成果编号查询
     */
    @GetMapping("/{hjcgbh}")
    @RequirePermission("教师")
    public Result<RchxHjcgjbxx> getByHjcgbh(@PathVariable String hjcgbh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            RchxHjcgjbxx result = hjcgjbxxService.getByHjcgbh(hjcgbh);
            
            // 权限控制：教师只能查看自己的获奖成果
            if ("教师".equals(currentUserQx) && result != null && !currentUserZgh.equals(result.getDywcrzgh())) {
                return Result.error(403, "权限不足，只能查看自己的获奖成果");
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询获奖成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询获奖成果信息
     */
    @PostMapping("/page")
    @RequirePermission("教师")
    public Result<IPage<RchxHjcgjbxx>> getPage(@RequestBody HjcgjbxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的获奖成果
            if ("教师".equals(currentUserQx)) {
                queryDTO.setDywcrzgh(currentUserZgh);
            }
            
            IPage<RchxHjcgjbxx> result = hjcgjbxxService.pageByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询获奖成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 多条件查询获奖成果信息
     */
    @PostMapping("/list")
    @RequirePermission("教师")
    public Result<List<RchxHjcgjbxx>> getList(@RequestBody HjcgjbxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的获奖成果
            if ("教师".equals(currentUserQx)) {
                queryDTO.setDywcrzgh(currentUserZgh);
            }
            
            List<RchxHjcgjbxx> result = hjcgjbxxService.listByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询获奖成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据获奖成果名称模糊查询
     */
    @GetMapping("/name/{hjcgmc}")
    @RequirePermission("学院管理员")
    public Result<List<RchxHjcgjbxx>> getByHjcgmcLike(@PathVariable String hjcgmc) {
        try {
            List<RchxHjcgjbxx> result = hjcgjbxxService.listByHjcgmcLike(hjcgmc);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据获奖成果名称查询失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据第一完成人职工号查询
     */
    @GetMapping("/winner/{dywcrzgh}")
    @RequirePermission("教师")
    public Result<List<RchxHjcgjbxx>> getByDywcrzgh(@PathVariable String dywcrzgh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的获奖成果
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(dywcrzgh)) {
                return Result.error(403, "权限不足，只能查看自己的获奖成果");
            }
            
            List<RchxHjcgjbxx> result = hjcgjbxxService.listByDywcrzgh(dywcrzgh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据第一完成人查询获奖成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据奖励名称模糊查询
     */
    @GetMapping("/award/{jlmc}")
    @RequirePermission("学院管理员")
    public Result<List<RchxHjcgjbxx>> getByJlmcLike(@PathVariable String jlmc) {
        try {
            List<RchxHjcgjbxx> result = hjcgjbxxService.listByJlmcLike(jlmc);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据奖励名称查询获奖成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据获奖级别查询
     */
    @GetMapping("/level/{hjjb}")
    @RequirePermission("学院管理员")
    public Result<List<RchxHjcgjbxx>> getByHjjb(@PathVariable String hjjb) {
        try {
            List<RchxHjcgjbxx> result = hjcgjbxxService.listByHjjb(hjjb);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据获奖级别查询获奖成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据奖励等级查询
     */
    @GetMapping("/grade/{jldj}")
    @RequirePermission("学院管理员")
    public Result<List<RchxHjcgjbxx>> getByJldj(@PathVariable String jldj) {
        try {
            List<RchxHjcgjbxx> result = hjcgjbxxService.listByJldj(jldj);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据奖励等级查询获奖成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据审核状态查询
     */
    @GetMapping("/audit/{shzt}")
    @RequirePermission("评审")
    public Result<List<RchxHjcgjbxx>> getByShzt(@PathVariable String shzt) {
        try {
            List<RchxHjcgjbxx> result = hjcgjbxxService.listByShzt(shzt);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据审核状态查询获奖成果信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增获奖成果信息
     */
    @PostMapping
    @RequirePermission("学院管理员")
    public Result<String> create(@RequestBody RchxHjcgjbxx hjcgjbxx) {
        try {
            boolean success = hjcgjbxxService.saveHjcgjbxx(hjcgjbxx);
            if (success) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增获奖成果信息失败: {}", e.getMessage());
            return Result.error("新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新获奖成果信息
     */
    @PutMapping
    @RequirePermission("学院管理员")
    public Result<String> update(@RequestBody RchxHjcgjbxx hjcgjbxx, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能修改自己的获奖成果
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(hjcgjbxx.getDywcrzgh())) {
                return Result.error(403, "权限不足，只能修改自己的获奖成果");
            }
            
            boolean success = hjcgjbxxService.updateByHjcgbh(hjcgjbxx);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新获奖成果信息失败: {}", e.getMessage());
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除获奖成果信息
     */
    @DeleteMapping("/{hjcgbh}")
    @RequirePermission("系统管理员")
    public Result<String> delete(@PathVariable String hjcgbh) {
        try {
            boolean success = hjcgjbxxService.removeByHjcgbh(hjcgbh);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除获奖成果信息失败: {}", e.getMessage());
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除获奖成果信息
     */
    @DeleteMapping("/batch")
    @RequirePermission("系统管理员")
    public Result<String> deleteBatch(@RequestBody List<String> hjcgbhs) {
        try {
            boolean success = hjcgjbxxService.removeBatchByHjcgbhs(hjcgbhs);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除获奖成果信息失败: {}", e.getMessage());
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量新增获奖成果信息
     */
    @PostMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> createBatch(@RequestBody List<RchxHjcgjbxx> hjcgjbxxList) {
        try {
            boolean success = hjcgjbxxService.saveBatchHjcgjbxx(hjcgjbxxList);
            if (success) {
                return Result.success("批量新增成功");
            } else {
                return Result.error("批量新增失败");
            }
        } catch (Exception e) {
            log.error("批量新增获奖成果信息失败: {}", e.getMessage());
            return Result.error("批量新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新获奖成果信息
     */
    @PutMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> updateBatch(@RequestBody List<RchxHjcgjbxx> hjcgjbxxList) {
        try {
            boolean success = hjcgjbxxService.updateBatchHjcgjbxx(hjcgjbxxList);
            if (success) {
                return Result.success("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新获奖成果信息失败: {}", e.getMessage());
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取获奖成果总数统计
     */
    @GetMapping("/total-count")
    @RequirePermission("教师")
    public Result<Map<String, Object>> getTotalCount() {
        try {
            log.info("获取获奖成果总数统计");
            Map<String, Object> stats = hjcgjbxxService.getTotalCount();
            log.info("获奖成果总数统计获取成功，总数: {}", stats.get("totalCount"));
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取获奖成果总数统计失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 按审核状态统计
     */
    @GetMapping("/stats/shzt")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByShzt() {
        try {
            log.info("查询审核状态统计");
            Map<String, Integer> stats = hjcgjbxxService.getStatsByShzt();
            log.info("审核状态统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询审核状态统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按教研室名称统计
     */
    @GetMapping("/stats/jysmc")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByJysmc() {
        try {
            log.info("查询教研室名称统计");
            Map<String, Integer> stats = hjcgjbxxService.getStatsByJysmc();
            log.info("教研室名称统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询教研室名称统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按颁奖单位统计
     */
    @GetMapping("/stats/bjdw")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByBjdw() {
        try {
            log.info("查询颁奖单位统计");
            Map<String, Integer> stats = hjcgjbxxService.getStatsByBjdw();
            log.info("颁奖单位统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询颁奖单位统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按成果形式统计
     */
    @GetMapping("/stats/cgxs")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByCgxs() {
        try {
            log.info("查询成果形式统计");
            Map<String, Integer> stats = hjcgjbxxService.getStatsByCgxs();
            log.info("成果形式统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询成果形式统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按获奖级别统计
     */
    @GetMapping("/stats/hjjb")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByHjjb() {
        try {
            log.info("查询获奖级别统计");
            Map<String, Integer> stats = hjcgjbxxService.getStatsByHjjb();
            log.info("获奖级别统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询获奖级别统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按奖励等级统计
     */
    @GetMapping("/stats/jldj")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByJldj() {
        try {
            log.info("查询奖励等级统计");
            Map<String, Integer> stats = hjcgjbxxService.getStatsByJldj();
            log.info("奖励等级统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询奖励等级统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按单位名称统计
     */
    @GetMapping("/stats/dwmc")
    @RequirePermission("教师")
    public Result<Map<String, Integer>> getStatsByDwmc() {
        try {
            log.info("查询单位名称统计");
            Map<String, Integer> stats = hjcgjbxxService.getStatsByDwmc();
            log.info("单位名称统计结果: {}", stats);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询单位名称统计失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取奖励等级选项列表 - 用于前端筛选
     */
    @GetMapping("/jldj-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getJldjOptions() {
        try {
            List<Map<String, Object>> options = hjcgjbxxService.getJldjOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取奖励等级选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取获奖级别选项列表 - 用于前端筛选
     */
    @GetMapping("/hjjb-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getHjjbOptions() {
        try {
            List<Map<String, Object>> options = hjcgjbxxService.getHjjbOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取获奖级别选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核状态选项列表 - 用于前端筛选
     */
    @GetMapping("/shzt-options")
    @RequirePermission("教师")
    public Result<List<Map<String, Object>>> getShztOptions() {
        try {
            List<Map<String, Object>> options = hjcgjbxxService.getShztOptions();
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取审核状态选项列表失败: {}", e.getMessage());
            return Result.error("获取失败: " + e.getMessage());
        }
    }
}
