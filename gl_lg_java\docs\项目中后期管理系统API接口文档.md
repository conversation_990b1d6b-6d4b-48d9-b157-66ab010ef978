# 项目中后期管理系统 API 接口文档

## 概述

项目中后期管理系统提供完整的项目管理、考核管理和文件管理功能，支持项目全生命周期管理。
**响应格式**: 统一使用 `Result<T>` 包装响应数据
```json
{
  "code": 200,
  "message": "success",
  "data": {...}
}
```

---

## 1. 项目中后期管理接口

**基础路径**: `/project-midlate-management`

### 1.1 分页查询项目列表

**接口**: `GET /page`

**描述**: 分页查询项目中后期管理信息，支持多条件筛选

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 页码 |
| size | Integer | 否 | 10 | 每页大小 |
| projectCode | String | 否 | - | 项目编号 |
| projectName | String | 否 | - | 项目名称 |
| projectStatus | String | 否 | - | 项目状态 |
| categoryId | Integer | 否 | - | 项目大类ID |
| typeId | Integer | 否 | - | 项目类别ID |
| deptId | Integer | 否 | - | 部门ID |
| projectLeaderZgh | String | 否 | - | 项目负责人职工号 |
| riskLevel | String | 否 | - | 风险等级 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "projectCode": "PROJ2025001",
        "projectName": "智能制造系统开发",
        "projectStatus": "ONGOING",
        "progressPercentage": 65.5,
        "riskLevel": "MEDIUM",
        "categoryName": "技术研发",
        "typeName": "软件开发",
        "deptName": "研发部"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 1.2 查询项目详情

**接口**: `GET /{id}`

**描述**: 根据ID查询项目详细信息

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 项目ID |

### 1.3 创建项目

**接口**: `POST /`

**描述**: 创建新的项目中后期管理记录

**请求体**:
```json
{
  "projectCode": "PROJ2025002",
  "projectName": "AI智能分析平台",
  "categoryId": 1,
  "typeId": 2,
  "deptId": 3,
  "projectLeaderZgh": "EMP001",
  "projectLeaderName": "张三",
  "projectLeaderPhone": "13800138000",
  "projectLeaderEmail": "<EMAIL>",
  "projectStartDate": "2025-01-01",
  "projectEndDate": "2025-12-31",
  "projectBudget": 500.00,
  "projectDescription": "基于AI技术的智能数据分析平台开发",
  "createBy": "admin"
}
```

### 1.4 更新项目

**接口**: `PUT /`

**描述**: 更新项目信息

### 1.5 删除项目

**接口**: `DELETE /{id}`

**描述**: 逻辑删除项目（设置为非激活状态）

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| updateBy | String | 是 | 更新人 |

### 1.6 批量删除项目

**接口**: `DELETE /batch`

**描述**: 批量逻辑删除项目

**请求体**: `[1, 2, 3]` (项目ID数组)

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| updateBy | String | 是 | 更新人 |

### 1.7 更新项目进度

**接口**: `PUT /{id}/progress`

**描述**: 更新项目进度百分比和说明

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| progressPercentage | BigDecimal | 是 | 进度百分比(0-100) |
| progressDescription | String | 否 | 进度说明 |
| updateBy | String | 是 | 更新人 |

### 1.8 更新项目状态

**接口**: `PUT /{id}/status`

**描述**: 更新项目状态

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectStatus | String | 是 | 项目状态 |
| updateBy | String | 是 | 更新人 |

**项目状态枚举**:
- `ONGOING`: 进行中
- `MIDTERM_REVIEW`: 中期检查
- `FINAL_REVIEW`: 结项评审
- `COMPLETED`: 已完成
- `SUSPENDED`: 暂停
- `TERMINATED`: 终止

### 1.9 更新风险等级

**接口**: `PUT /{id}/risk`

**描述**: 更新项目风险等级和描述

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| riskLevel | String | 是 | 风险等级 |
| riskDescription | String | 否 | 风险描述 |
| updateBy | String | 是 | 更新人 |

**风险等级枚举**:
- `LOW`: 低风险
- `MEDIUM`: 中等风险
- `HIGH`: 高风险
- `CRITICAL`: 严重风险

### 1.10 查询统计信息

**接口**: `GET /statistics`

**描述**: 获取项目统计信息

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalProjects": 150,
    "ongoingProjects": 85,
    "completedProjects": 45,
    "midtermReviewProjects": 12,
    "finalReviewProjects": 8,
    "avgProgress": 67.5,
    "totalBudget": 15000000.00,
    "totalLeaders": 25
  }
}
```

### 1.11 部门项目统计

**接口**: `GET /statistics/department`

**描述**: 获取各部门项目统计信息

### 1.12 高风险项目列表

**接口**: `GET /high-risk`

**描述**: 获取高风险和严重风险项目列表

### 1.13 即将到期项目

**接口**: `GET /near-deadline`

**描述**: 获取即将到期的项目列表

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | Integer | 否 | 30 | 天数范围 |

### 1.14 根据项目负责人查询

**接口**: `GET /by-leader/{projectLeaderZgh}`

**描述**: 根据项目负责人职工号查询项目列表

### 1.15 根据部门查询

**接口**: `GET /by-department/{deptId}`

**描述**: 根据部门ID查询项目列表

### 1.16 根据状态查询

**接口**: `GET /by-status/{projectStatus}`

**描述**: 根据项目状态查询项目列表

### 1.17 根据项目编号查询

**接口**: `GET /by-code/{projectCode}`

**描述**: 根据项目编号查询项目信息

### 1.18 检查项目编号是否存在

**接口**: `GET /exists/{projectCode}`

**描述**: 检查项目编号是否已存在

### 1.19 激活/停用项目

**接口**: `PUT /{id}/toggle-status`

**描述**: 激活或停用项目

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isActive | Boolean | 是 | 是否激活 |
| updateBy | String | 是 | 更新人 |

### 1.20 项目概览信息

**接口**: `GET /{id}/overview`

**描述**: 获取项目概览信息（包含考核和文件统计）

### 1.21 导出项目列表

**接口**: `GET /export`

**描述**: 导出项目列表数据

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectStatus | String | 否 | 项目状态 |
| deptId | Integer | 否 | 部门ID |
| projectLeaderZgh | String | 否 | 项目负责人职工号 |

---

## 2. 项目考核管理接口

**基础路径**: `/project-assessments`

### 2.1 分页查询考核列表

**接口**: `GET /page`

**描述**: 分页查询项目考核信息

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 页码 |
| size | Integer | 否 | 10 | 每页大小 |
| projectCode | String | 否 | - | 项目编号 |
| projectName | String | 否 | - | 项目名称 |
| assessmentType | String | 否 | - | 考核类型 |
| assessmentStatus | String | 否 | - | 考核状态 |
| projectLeaderZgh | String | 否 | - | 项目负责人职工号 |
| assessorZgh | String | 否 | - | 考核人职工号 |
| assessmentResult | String | 否 | - | 考核结果 |

### 2.2 查询考核详情

**接口**: `GET /{id}`

**描述**: 根据ID查询考核详细信息

### 2.3 创建考核任务

**接口**: `POST /`

**描述**: 创建新的考核任务

**请求体**:
```json
{
  "projectId": 1,
  "projectCode": "PROJ2025001",
  "projectName": "智能制造系统开发",
  "projectLeaderZgh": "EMP001",
  "projectLeaderName": "张三",
  "assessmentType": "MIDTERM",
  "assessmentName": "中期考核",
  "assessmentDescription": "项目中期进展考核",
  "assessmentDate": "2025-06-30T10:00:00",
  "assessmentDeadline": "2025-07-15T18:00:00",
  "assessmentLocation": "会议室A",
  "assessmentMethod": "OFFLINE",
  "maxScore": 100.00,
  "passScore": 60.00,
  "createBy": "admin"
}
```

**考核类型枚举**:
- `MIDTERM`: 中期考核
- `FINAL`: 结项考核
- `SPECIAL`: 专项考核
- `ANNUAL`: 年度考核
- `QUARTERLY`: 季度考核

**考核状态枚举**:
- `PENDING`: 待考核
- `ONGOING`: 考核中
- `COMPLETED`: 已完成
- `CANCELLED`: 已取消
- `OVERDUE`: 已逾期

### 2.4 开始考核

**接口**: `PUT /{id}/start`

**描述**: 开始考核任务

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| assessorZgh | String | 是 | 考核人职工号 |
| assessorName | String | 是 | 考核人姓名 |

### 2.5 完成考核

**接口**: `PUT /{id}/complete`

**描述**: 完成考核并提交结果

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| assessmentScore | BigDecimal | 是 | 考核得分 |
| assessmentComments | String | 否 | 考核意见 |
| improvementSuggestions | String | 否 | 改进建议 |
| followUpActions | String | 否 | 后续行动 |
| updateBy | String | 是 | 更新人 |

**考核结果自动判定**:
- 90分及以上: `EXCELLENT` (优秀)
- 80-89分: `GOOD` (良好)
- 60-79分: `PASS` (合格)
- 60分以下: `FAIL` (不合格)

### 2.6 取消考核

**接口**: `PUT /{id}/cancel`

**描述**: 取消考核任务

### 2.7 发送考核提醒

**接口**: `POST /{id}/reminder`

**描述**: 发送考核提醒通知

### 2.8 考核统计信息

**接口**: `GET /statistics`

**描述**: 获取考核统计信息

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalAssessments": 200,
    "pendingAssessments": 25,
    "ongoingAssessments": 15,
    "completedAssessments": 150,
    "overdueAssessments": 10,
    "avgScore": 82.5,
    "excellentCount": 45,
    "goodCount": 65,
    "passCount": 35,
    "failCount": 5
  }
}
```

### 2.9 考核类型统计

**接口**: `GET /statistics/type`

**描述**: 获取各考核类型统计信息

### 2.10 项目负责人考核统计

**接口**: `GET /statistics/leader`

**描述**: 获取项目负责人考核统计信息

### 2.11 即将到期考核

**接口**: `GET /near-deadline`

**描述**: 获取即将到期的考核任务

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | Integer | 否 | 7 | 天数范围 |

### 2.12 逾期考核任务

**接口**: `GET /overdue`

**描述**: 获取已逾期的考核任务

### 2.13 生成考核编号

**接口**: `GET /generate-code/{assessmentType}`

**描述**: 根据考核类型生成唯一考核编号

**编号格式**: `{类型前缀}{年份}{3位序号}`
- 中期考核: `MID2025001`
- 结项考核: `FINAL2025001`
- 专项考核: `SPEC2025001`

### 2.14 根据考核编号查询

**接口**: `GET /by-code/{assessmentCode}`

**描述**: 根据考核编号查询考核信息

### 2.15 导出考核列表

**接口**: `GET /export`

**描述**: 导出考核列表数据

---

## 3. 项目中后期文件管理接口

**基础路径**: `/project-midlate-files`

### 3.1 分页查询文件列表

**接口**: `GET /page`

**描述**: 分页查询文件信息

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 页码 |
| size | Integer | 否 | 10 | 每页大小 |
| businessType | String | 否 | - | 业务类型 |
| businessId | Long | 否 | - | 业务ID |
| projectCode | String | 否 | - | 项目编号 |
| fileCategory | String | 否 | - | 文件类别 |
| uploadBy | String | 否 | - | 上传人 |
| isRequired | Boolean | 否 | - | 是否必需 |
| isConfidential | Boolean | 否 | - | 是否机密 |

### 3.2 上传文件

**接口**: `POST /upload`

**描述**: 上传文件到MinIO存储

**请求参数** (multipart/form-data):
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| file | MultipartFile | 是 | - | 文件 |
| businessType | String | 是 | - | 业务类型 |
| businessId | Long | 是 | - | 业务ID |
| projectCode | String | 是 | - | 项目编号 |
| fileCategory | String | 是 | - | 文件类别 |
| fileDescription | String | 否 | - | 文件描述 |
| isRequired | Boolean | 否 | false | 是否必需 |
| isConfidential | Boolean | 否 | false | 是否机密 |
| versionNumber | String | 否 | 1.0 | 版本号 |
| uploadBy | String | 是 | - | 上传人 |
| uploadByName | String | 是 | - | 上传人姓名 |

**业务类型枚举**:
- `PROJECT_MANAGEMENT`: 项目管理
- `ASSESSMENT`: 考核管理

**文件类别枚举**:
- `PROGRESS_REPORT`: 进度报告
- `ASSESSMENT_MATERIAL`: 考核材料
- `MEETING_MINUTES`: 会议纪要
- `MILESTONE_DOCUMENT`: 里程碑文档
- `RISK_ANALYSIS`: 风险分析
- `OTHER`: 其他

**支持的文件类型**:
- 文档: `.pdf`, `.doc`, `.docx`, `.xls`, `.xlsx`, `.ppt`, `.pptx`, `.txt`
- 图片: `.jpg`, `.jpeg`, `.png`, `.gif`
- 压缩包: `.zip`, `.rar`

**文件大小限制**: 最大100MB

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "fileName": "项目进度报告_abc123.pdf",
    "fileOriginalName": "项目进度报告.pdf",
    "filePath": "midlate-files/project_management/2025/01/04/项目进度报告_abc123.pdf",
    "fileSize": 2048576,
    "fileType": "application/pdf",
    "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
    "uploadTime": "2025-01-04T10:30:00"
  }
}
```

### 3.3 下载文件

**接口**: `GET /{id}/download`

**描述**: 直接下载文件

**响应**: 文件二进制流

### 3.4 获取文件下载URL

**接口**: `GET /{id}/download-url`

**描述**: 获取MinIO预签名下载URL（有效期7天）

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": "https://minio.example.com/bucket/object?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
}
```

### 3.5 删除文件

**接口**: `DELETE /{id}`

**描述**: 逻辑删除文件

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deleteBy | String | 是 | 删除人 |

### 3.6 批量删除文件

**接口**: `DELETE /batch`

**描述**: 批量逻辑删除文件

**请求体**: `[1, 2, 3]` (文件ID数组)

### 3.7 更新文件信息

**接口**: `PUT /{id}`

**描述**: 更新文件描述、属性等信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| fileDescription | String | 否 | 文件描述 |
| isRequired | Boolean | 否 | 是否必需 |
| isConfidential | Boolean | 否 | 是否机密 |
| versionNumber | String | 否 | 版本号 |

### 3.8 替换文件

**接口**: `POST /{id}/replace`

**描述**: 上传新版本文件替换原文件

**请求参数** (multipart/form-data):
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | MultipartFile | 是 | 新文件 |
| versionNumber | String | 是 | 版本号 |
| uploadBy | String | 是 | 上传人 |
| uploadByName | String | 是 | 上传人姓名 |

### 3.9 文件统计信息

**接口**: `GET /statistics`

**描述**: 获取文件统计信息

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalFiles": 1500,
    "totalSize": 10737418240,
    "requiredFiles": 300,
    "confidentialFiles": 150,
    "totalDownloads": 5000,
    "totalUploaders": 50
  }
}
```

### 3.10 文件类别统计

**接口**: `GET /statistics/category`

**描述**: 获取各文件类别统计信息

### 3.11 最近上传文件

**接口**: `GET /recent`

**描述**: 获取最近上传的文件列表

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | Integer | 否 | 10 | 限制数量 |

### 3.12 热门下载文件

**接口**: `GET /popular`

**描述**: 获取下载次数最多的文件列表

### 3.13 检查文件重复

**接口**: `POST /check-duplicate`

**描述**: 检查上传文件是否与已有文件重复（基于MD5）

**请求参数** (multipart/form-data):
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | MultipartFile | 是 | 文件 |

### 3.14 文件版本历史

**接口**: `GET /version-history`

**描述**: 获取文件的版本历史记录

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| businessType | String | 是 | 业务类型 |
| businessId | Long | 是 | 业务ID |
| originalFileName | String | 是 | 原始文件名 |

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. **文件上传限制**:
   - 单个文件最大100MB
   - 支持的文件类型有限制
   - 文件名会自动添加UUID避免重复

2. **MinIO存储**:
   - 文件存储在MinIO对象存储中
   - 预签名URL有效期为7天
   - 文件路径按日期分层存储

3. **权限控制**:
   - 机密文件需要特殊权限访问
   - 文件删除为逻辑删除，不会真正删除文件

4. **数据一致性**:
   - 项目删除时不会影响已关联的考核和文件
   - 考核完成后状态不可逆转
   - 文件版本管理支持历史追溯

---

**文档版本**: v1.0  
**更新时间**: 2025-01-04  
**维护人员**: GL开发团队
