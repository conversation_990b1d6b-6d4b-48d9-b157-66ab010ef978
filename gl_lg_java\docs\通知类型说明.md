# 教师管理系统通知类型说明

## 概述

本文档详细说明了教师管理系统中所有通知类型的定义、使用场景和触发条件。

## 通知类型分类

### 1. 基础系统通知 (1-10)

| 类型编码 | 类型名称 | 触发场景 | 接收对象 | 优先级 |
|---------|---------|---------|---------|--------|
| `teacher_info_updated` | 教师信息更新 | 管理员修改教师基本信息后 | 被修改的教师 | 普通 |
| `permission_changed` | 权限变更 | 管理员调整教师权限后 | 被调整权限的教师 | 重要 |
| `system_maintenance` | 系统维护 | 系统计划维护前24小时 | 所有在线用户 | 重要 |
| `system_announcement` | 系统公告 | 管理员发布重要公告 | 根据公告范围 | 紧急 |
| `password_reset_success` | 密码重置成功 | 教师密码重置成功后 | 重置密码的教师 | 普通 |
| `account_locked` | 账户锁定 | 账户异常被锁定时 | 被锁定的教师 | 紧急 |

### 2. 项目相关通知 (11-20)

| 类型编码 | 类型名称 | 触发场景 | 接收对象 | 优先级 |
|---------|---------|---------|---------|--------|
| `project_submitted` | 项目申报提交 | 教师提交项目申报材料 | 项目申报人 | 普通 |
| `project_approved` | 项目审核通过 | 项目申报审核通过 | 项目申报人 | 重要 |
| `project_rejected` | 项目审核驳回 | 项目申报审核驳回 | 项目申报人 | 重要 |
| `project_deadline_warning` | 项目截止提醒 | 项目截止前7天/3天/1天 | 项目参与人 | 重要 |
| `project_progress_update` | 项目进度更新 | 项目进度状态变更 | 项目组成员 | 普通 |
| `project_completion` | 项目结项通知 | 项目完成结项 | 项目组成员 | 重要 |
| `project_funding_approved` | 项目经费批准 | 项目经费申请批准 | 项目负责人 | 重要 |
| `project_report_due` | 项目报告到期 | 项目报告提交截止前提醒 | 项目负责人 | 重要 |

### 3. 合同相关通知 (21-30)

| 类型编码 | 类型名称 | 触发场景 | 接收对象 | 优先级 |
|---------|---------|---------|---------|--------|
| `contract_signed` | 合同签署完成 | 劳动合同或项目合同签署 | 合同当事人 | 重要 |
| `contract_expiring` | 合同即将到期 | 合同到期前30天提醒 | 合同当事人 | 重要 |
| `contract_renewed` | 合同续签成功 | 合同续签手续完成 | 合同当事人 | 重要 |
| `contract_terminated` | 合同终止通知 | 合同提前终止 | 合同当事人 | 紧急 |
| `contract_amendment` | 合同变更通知 | 合同条款发生变更 | 合同当事人 | 重要 |

### 4. 学术相关通知 (31-40)

| 类型编码 | 类型名称 | 触发场景 | 接收对象 | 优先级 |
|---------|---------|---------|---------|--------|
| `paper_published` | 论文发表通知 | 学术论文发表成功 | 论文作者 | 普通 |
| `conference_invitation` | 会议邀请通知 | 收到学术会议邀请 | 被邀请教师 | 普通 |
| `academic_achievement` | 学术成果认定 | 学术成果认定结果 | 成果申报人 | 重要 |
| `peer_review_request` | 同行评议请求 | 分配同行评议任务 | 评议专家 | 普通 |

### 5. 教学相关通知 (41-50)

| 类型编码 | 类型名称 | 触发场景 | 接收对象 | 优先级 |
|---------|---------|---------|---------|--------|
| `course_assigned` | 课程分配通知 | 新学期课程分配 | 授课教师 | 重要 |
| `teaching_evaluation` | 教学评估通知 | 教学质量评估开始 | 被评估教师 | 普通 |
| `exam_schedule` | 考试安排通知 | 考试时间地点确定 | 监考教师 | 重要 |
| `grade_submission_due` | 成绩提交截止 | 成绩提交截止前提醒 | 任课教师 | 重要 |

### 6. 人事相关通知 (51-60)

| 类型编码 | 类型名称 | 触发场景 | 接收对象 | 优先级 |
|---------|---------|---------|---------|--------|
| `promotion_result` | 职称评定结果 | 职称评定审核完成 | 申报教师 | 重要 |
| `training_notification` | 培训通知 | 教师培训活动安排 | 参训教师 | 普通 |
| `leave_approved` | 请假审批通过 | 请假申请审批通过 | 申请人 | 普通 |
| `leave_rejected` | 请假审批驳回 | 请假申请审批驳回 | 申请人 | 普通 |
| `salary_adjustment` | 薪资调整通知 | 薪资待遇调整 | 相关教师 | 重要 |

### 7. 财务相关通知 (61-70)

| 类型编码 | 类型名称 | 触发场景 | 接收对象 | 优先级 |
|---------|---------|---------|---------|--------|
| `reimbursement_approved` | 报销审批通过 | 费用报销审批通过 | 申请人 | 普通 |
| `reimbursement_rejected` | 报销审批驳回 | 费用报销审批驳回 | 申请人 | 普通 |
| `budget_allocated` | 预算分配通知 | 项目或部门预算分配 | 预算负责人 | 重要 |
| `invoice_reminder` | 发票提交提醒 | 发票提交截止前提醒 | 相关教师 | 普通 |

### 8. 设备资源通知 (71-80)

| 类型编码 | 类型名称 | 触发场景 | 接收对象 | 优先级 |
|---------|---------|---------|---------|--------|
| `equipment_allocated` | 设备分配通知 | 实验设备分配使用 | 设备使用者 | 普通 |
| `lab_booking_confirmed` | 实验室预约确认 | 实验室使用预约确认 | 预约人 | 普通 |
| `resource_maintenance` | 资源维护通知 | 设备或系统维护 | 相关用户 | 重要 |

### 9. 数据操作通知 (81-90)

| 类型编码 | 类型名称 | 触发场景 | 接收对象 | 优先级 |
|---------|---------|---------|---------|--------|
| `data_import_success` | 数据导入成功 | 批量数据导入成功 | 操作人员 | 普通 |
| `data_import_failed` | 数据导入失败 | 批量数据导入失败 | 操作人员 | 重要 |
| `data_export_ready` | 数据导出完成 | 数据导出任务完成 | 操作人员 | 普通 |
| `batch_operation_completed` | 批量操作完成 | 批量操作任务完成 | 操作人员 | 普通 |

## 通知优先级说明

### 优先级定义
- **普通 (1)**: 一般信息通知，不紧急
- **重要 (2)**: 需要及时关注的通知
- **紧急 (3)**: 需要立即处理的通知

### 优先级显示规则
- 紧急通知：红色标识，置顶显示
- 重要通知：橙色标识，优先显示
- 普通通知：蓝色标识，正常显示

## 通知权限控制

### 发送权限
- **系统管理员**: 可发送所有类型通知
- **学院管理员**: 可发送本学院相关通知
- **系统自动**: 系统触发的自动通知

### 接收权限
- **教师**: 接收与自己相关的通知
- **评审**: 接收评审相关通知
- **学院管理员**: 接收学院管理相关通知
- **系统管理员**: 接收所有通知

## 通知模板变量

### 常用变量
- `{teacherName}`: 教师姓名
- `{adminName}`: 管理员姓名
- `{projectName}`: 项目名称
- `{contractType}`: 合同类型
- `{deadline}`: 截止时间
- `{amount}`: 金额
- `{reason}`: 原因说明

### 使用示例
```
标题模板: "您的{projectName}项目审核结果"
内容模板: "尊敬的{teacherName}老师，您申报的{projectName}项目已审核{result}。"
```

## 通知发送策略

### 实时通知
- 系统公告
- 紧急通知
- 权限变更

### 定时通知
- 截止日期提醒
- 合同到期提醒
- 定期报告提醒

### 批量通知
- 系统维护通知
- 培训通知
- 政策更新通知

## 扩展建议

### 可扩展的通知类型
- 科研奖励通知
- 国际交流通知
- 专利申请通知
- 图书借阅通知
- 会议室预约通知

### 自定义通知
- 支持管理员自定义通知类型
- 支持通知模板自定义
- 支持通知规则配置

---

**文档版本**: v1.0  
**创建时间**: 2025-07-24  
**维护人员**: 教师系统开发团队
