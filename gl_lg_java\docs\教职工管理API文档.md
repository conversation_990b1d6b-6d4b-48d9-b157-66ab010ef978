# 教职工管理API接口文档

## 基础信息

- **基础URL**: `http://localhost:8080/api/jzgjbxx`
- **认证方式**: JWT Token
- **权限说明**:
  - **教师**: 只能查看和修改自己的信息
  - **评审**: 可以查看教职工信息
  - **学院管理员**: 可以增删改查教职工信息
  - **系统管理员**: 拥有所有权限

---

## 1. 查询接口

### 1.1 根据职工号查询

**接口信息**
- **URL**: `GET /api/jzgjbxx/{zgh}`
- **权限**: 教师及以上
- **权限控制**: 教师只能查看自己的信息

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| zgh | String | Path | 职工号 | T001 |

**响应示例**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "zgh": "T001",
        "xm": "张三",
        "sfzjh": "110101199001011234",
        "xb": "男",
        "bm": "计算机学院",
        "zc": "教授",
        "qx": "教师",
        "dh": "13800138000",
        "dzxx": "<EMAIL>",
        "zgxl": "博士研究生",
        "zgxw": "博士",
        "zwjb": "正高级",
        "zzmm": "中共党员",
        "mz": "汉族",
        "jg": "北京市",
        "gjdq": "中国"
    }
}
```

### 1.2 分页查询教职工信息

**接口信息**
- **URL**: `POST /api/jzgjbxx/page`
- **权限**: 学院管理员及以上
- **权限控制**: 教师只能查看自己的信息

**请求参数**
```json
{
    "zgh": "T001",
    "xm": "张三",
    "sfzjh": "110101",
    "xb": "男",
    "bm": "计算机",
    "zc": "教授",
    "qx": "教师",
    "dh": "138",
    "dzxx": "example.com",
    "zgxl": "博士研究生",
    "zgxw": "博士",
    "zwjb": "正高级",
    "zzmm": "中共党员",
    "mz": "汉族",
    "jg": "北京",
    "gjdq": "中国",
    "pageNum": 1,
    "pageSize": 10,
    "orderBy": "zgh",
    "orderDirection": "asc"
}
```

**响应示例**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "zgh": "T001",
                "xm": "张三",
                "bm": "计算机学院",
                "zc": "教授",
                "qx": "教师",
                "dh": "13800138000",
                "dzxx": "<EMAIL>"
            }
        ],
        "total": 100,
        "size": 10,
        "current": 1,
        "pages": 10
    }
}
```

### 1.3 多条件查询（不分页）

**接口信息**
- **URL**: `POST /api/jzgjbxx/list`
- **权限**: 学院管理员及以上

### 1.4 根据姓名模糊查询

**接口信息**
- **URL**: `GET /api/jzgjbxx/name/{xm}`
- **权限**: 学院管理员及以上

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| xm | String | Path | 姓名（支持模糊查询） | 张 |

### 1.5 根据部门查询

**接口信息**
- **URL**: `GET /api/jzgjbxx/dept/{bm}`
- **权限**: 学院管理员及以上

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| bm | String | Path | 部门名称 | 计算机学院 |

### 1.6 根据职称查询

**接口信息**
- **URL**: `GET /api/jzgjbxx/title/{zc}`
- **权限**: 学院管理员及以上

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| zc | String | Path | 职称 | 教授 |

### 1.7 根据权限查询

**接口信息**
- **URL**: `GET /api/jzgjbxx/permission/{qx}`
- **权限**: 系统管理员

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| qx | String | Path | 权限 | 教师 |

---

## 2. 新增接口

### 2.1 新增教职工信息

**接口信息**
- **URL**: `POST /api/jzgjbxx`
- **权限**: 学院管理员及以上

**请求参数**
```json
{
    "zgh": "T001",
    "xm": "张三",
    "sfzjh": "110101199001011234",
    "xb": "男",
    "bm": "计算机学院",
    "zc": "教授",
    "qx": "教师",
    "dh": "13800138000",
    "dzxx": "<EMAIL>",
    "pass": "123456",
    "zgxl": "博士研究生",
    "zgxw": "博士"
}
```

### 2.2 批量新增教职工信息

**接口信息**
- **URL**: `POST /api/jzgjbxx/batch`
- **权限**: 学院管理员及以上

**请求参数**
```json
[
    {
        "zgh": "T001",
        "xm": "张三",
        "bm": "计算机学院",
        "zc": "教授",
        "qx": "教师"
    },
    {
        "zgh": "T002",
        "xm": "李四",
        "bm": "计算机学院",
        "zc": "副教授",
        "qx": "教师"
    }
]
```

---

## 3. 更新接口

### 3.1 更新教职工信息

**接口信息**
- **URL**: `PUT /api/jzgjbxx`
- **权限**: 学院管理员及以上
- **权限控制**: 教师只能修改自己的信息

**请求参数**
```json
{
    "zgh": "T001",
    "xm": "张三",
    "dh": "13900139000",
    "dzxx": "<EMAIL>",
    "zc": "副教授"
}
```

### 3.2 批量更新教职工信息

**接口信息**
- **URL**: `PUT /api/jzgjbxx/batch`
- **权限**: 学院管理员及以上

---

## 4. 删除接口

### 4.1 删除教职工信息

**接口信息**
- **URL**: `DELETE /api/jzgjbxx/{zgh}`
- **权限**: 系统管理员

**请求参数**
| 参数名 | 类型 | 位置 | 说明 | 示例 |
|--------|------|------|------|------|
| zgh | String | Path | 职工号 | T001 |

### 4.2 批量删除教职工信息

**接口信息**
- **URL**: `DELETE /api/jzgjbxx/batch`
- **权限**: 系统管理员

**请求参数**
```json
["T001", "T002", "T003"]
```

---

## 5. 查询参数说明

### JzgjbxxQueryDTO 参数说明

| 参数名 | 类型 | 说明 | 是否必填 | 示例 |
|--------|------|------|----------|------|
| zgh | String | 职工号（模糊查询） | 否 | T001 |
| xm | String | 姓名（模糊查询） | 否 | 张三 |
| sfzjh | String | 身份证件号（模糊查询） | 否 | 110101 |
| xb | String | 性别 | 否 | 男 |
| bm | String | 部门（模糊查询） | 否 | 计算机 |
| zc | String | 职称（模糊查询） | 否 | 教授 |
| qx | String | 权限 | 否 | 教师 |
| dh | String | 电话（模糊查询） | 否 | 138 |
| dzxx | String | 电子信箱（模糊查询） | 否 | example.com |
| zgxl | String | 最高学历 | 否 | 博士研究生 |
| zgxw | String | 最高学位 | 否 | 博士 |
| zwjb | String | 职务级别 | 否 | 正高级 |
| zzmm | String | 政治面貌 | 否 | 中共党员 |
| mz | String | 民族 | 否 | 汉族 |
| jg | String | 籍贯（模糊查询） | 否 | 北京 |
| gjdq | String | 国家地区 | 否 | 中国 |
| pageNum | Integer | 页码 | 否 | 1 |
| pageSize | Integer | 页大小 | 否 | 10 |
| orderBy | String | 排序字段 | 否 | zgh |
| orderDirection | String | 排序方向 | 否 | asc |

---

## 6. 权限控制说明

### 6.1 数据权限
- **教师**: 只能查看和修改自己的信息
- **评审**: 可以查看所有教职工信息
- **学院管理员**: 可以管理本学院的教职工信息
- **系统管理员**: 可以管理所有教职工信息

### 6.2 功能权限
- **查询**: 教师及以上
- **新增**: 学院管理员及以上
- **修改**: 学院管理员及以上（教师可修改自己的信息）
- **删除**: 系统管理员

---

## 7. 错误响应示例

### 权限不足
```json
{
    "code": 403,
    "message": "权限不足，只能查看自己的信息",
    "data": null
}
```

### 数据不存在
```json
{
    "code": 500,
    "message": "教职工信息不存在",
    "data": null
}
```

---

## 8. 使用示例

### 前端Vue.js调用示例

```javascript
// 查询教职工列表
async getTeacherList() {
    const queryData = {
        xm: '张',
        bm: '计算机',
        pageNum: 1,
        pageSize: 10
    }
    
    const response = await this.$http.post('/api/jzgjbxx/page', queryData)
    if (response.data.code === 200) {
        this.teacherList = response.data.data.records
        this.total = response.data.data.total
    }
}

// 新增教职工
async createTeacher() {
    const teacherData = {
        zgh: 'T001',
        xm: '张三',
        bm: '计算机学院',
        zc: '教授',
        qx: '教师',
        dh: '13800138000',
        dzxx: '<EMAIL>'
    }
    
    const response = await this.$http.post('/api/jzgjbxx', teacherData)
    if (response.data.code === 200) {
        this.$message.success('新增成功')
        this.getTeacherList()
    }
}
```
