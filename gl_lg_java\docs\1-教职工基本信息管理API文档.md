# 教职工基本信息管理API文档

## 基础信息
- **模块名称**: 教职工基本信息管理
- **基础路径**: `/api/jzgjbxx`
- **权限要求**: 需要JWT Token认证
- **数据格式**: JSON

## 统一响应格式

```json
{
    "code": 200,           // 状态码：200-成功，500-失败，401-未授权，403-权限不足
    "message": "操作成功",  // 响应消息
    "data": {}            // 响应数据（可能为null）
}
```

## 接口列表

### 1. 根据职工号查询教职工信息

**接口地址**: `GET /api/jzgjbxx/{zgh}`

**权限要求**: 教师+

**路径参数**:
- `zgh` (string, 必填): 职工号

**请求示例**:
```
GET /api/jzgjbxx/admin
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "zgh": "admin",
        "xm": "管理员",
        "sfzjh": "110101199001011234",
        "xb": "男",
        "bm": "系统管理部",
        "zc": "系统管理员",
        "qx": "系统管理员",
        "mm": "admin",
        "sjh": "13800138000",
        "yx": "<EMAIL>",
        "cjsj": "2025-07-18 10:00:00",
        "gxsj": "2025-07-18 10:00:00"
    }
}
```

### 2. 分页查询教职工信息

**接口地址**: `POST /api/jzgjbxx/page`

**权限要求**: 学院管理员+

**请求体**:
```json
{
    "zgh": "T001",              // 职工号（可选）
    "xm": "张三",               // 姓名（可选）
    "sfzjh": "110101",          // 身份证号（可选）
    "xb": "男",                 // 性别（可选）
    "bm": "计算机学院",         // 部门（可选）
    "zc": "教授",               // 职称（可选）
    "qx": "教师",               // 权限（可选）
    "dqzt": "在岗",             // 当前状态（可选）：在岗、退休、辞职、调出、博士后出站、死亡、返聘、不续聘、解除合同、终止合同、开除
    "pageNum": 1,               // 页码（默认1）
    "pageSize": 10,             // 页大小（默认10）
    "orderBy": "cjsj",          // 排序字段（默认cjsj）
    "orderDirection": "desc"    // 排序方向（默认desc）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "records": [
            {
                "zgh": "T001",
                "xm": "张三",
                "sfzjh": "110101199001011234",
                "xb": "男",
                "bm": "计算机学院",
                "zc": "教授",
                "qx": "教师",
                "mm": "123456",
                "sjh": "13800138001",
                "yx": "<EMAIL>",
                "cjsj": "2025-07-18 10:00:00",
                "gxsj": "2025-07-18 10:00:00"
            }
        ],
        "total": 100,
        "size": 10,
        "current": 1,
        "pages": 10
    }
}
```

### 3. 多条件查询教职工信息

**接口地址**: `POST /api/jzgjbxx/list`

**权限要求**: 学院管理员+

**请求体**: 同分页查询，但不返回分页信息

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": [
        {
            "zgh": "T001",
            "xm": "张三",
            "sfzjh": "110101199001011234",
            "xb": "男",
            "bm": "计算机学院",
            "zc": "教授",
            "qx": "教师",
            "mm": "123456",
            "sjh": "13800138001",
            "yx": "<EMAIL>",
            "cjsj": "2025-07-18 10:00:00",
            "gxsj": "2025-07-18 10:00:00"
        }
    ]
}
```

### 4. 根据姓名模糊查询

**接口地址**: `GET /api/jzgjbxx/name/{xm}`

**权限要求**: 学院管理员+

**路径参数**:
- `xm` (string, 必填): 姓名关键字

**响应示例**: 同多条件查询

### 5. 根据部门查询

**接口地址**: `GET /api/jzgjbxx/dept/{bm}`

**权限要求**: 学院管理员+

**路径参数**:
- `bm` (string, 必填): 部门名称

### 6. 根据职称查询

**接口地址**: `GET /api/jzgjbxx/title/{zc}`

**权限要求**: 学院管理员+

**路径参数**:
- `zc` (string, 必填): 职称

### 7. 根据权限查询

**接口地址**: `GET /api/jzgjbxx/permission/{qx}`

**权限要求**: 系统管理员

**路径参数**:
- `qx` (string, 必填): 权限类型

### 8. 新增教职工信息

**接口地址**: `POST /api/jzgjbxx`

**权限要求**: 学院管理员+

**请求体**:
```json
{
    "zgh": "T002",                      // 职工号（必填）
    "xm": "李四",                       // 姓名（必填）
    "sfzjh": "110101199002022345",      // 身份证号（必填）
    "xb": "女",                         // 性别（必填）
    "bm": "计算机学院",                 // 部门（必填）
    "zc": "副教授",                     // 职称（必填）
    "qx": "教师",                       // 权限（必填）
    "mm": "123456",                     // 密码（必填）
    "sjh": "13800138002",               // 手机号（可选）
    "yx": "<EMAIL>"            // 邮箱（可选）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "新增成功",
    "data": "新增成功"
}
```

### 9. 更新教职工信息

**接口地址**: `PUT /api/jzgjbxx`

**权限要求**: 学院管理员+（教师可修改自己的信息）

**请求体**: 同新增，但zgh不可修改

### 10. 删除教职工信息

**接口地址**: `DELETE /api/jzgjbxx/{zgh}`

**权限要求**: 系统管理员

**路径参数**:
- `zgh` (string, 必填): 职工号

### 11. 批量新增教职工信息

**接口地址**: `POST /api/jzgjbxx/batch`

**权限要求**: 学院管理员+

**请求体**:
```json
[
    {
        "zgh": "T003",
        "xm": "王五",
        "sfzjh": "110101199003033456",
        "xb": "男",
        "bm": "计算机学院",
        "zc": "讲师",
        "qx": "教师",
        "mm": "123456",
        "sjh": "13800138003",
        "yx": "<EMAIL>"
    }
]
```

### 12. 批量更新教职工信息

**接口地址**: `PUT /api/jzgjbxx/batch`

**权限要求**: 学院管理员+

### 13. 批量删除教职工信息

**接口地址**: `DELETE /api/jzgjbxx/batch`

**权限要求**: 系统管理员

**请求体**:
```json
["T001", "T002", "T003"]
```

## 权限说明

### 数据权限
- **教师**: 只能查看和修改自己的基本信息
- **评审**: 可以查看教职工信息，但不能修改
- **学院管理员**: 可以管理本学院的教职工信息
- **系统管理员**: 可以管理所有教职工信息

### 操作权限
- **查询**: 教师+
- **新增**: 学院管理员+
- **修改**: 学院管理员+（教师可修改自己的）
- **删除**: 系统管理员

## 错误码说明

- **200**: 操作成功
- **401**: 未授权（Token无效或过期）
- **403**: 权限不足
- **500**: 服务器内部错误

## 注意事项

1. 所有接口都需要在请求头中携带JWT Token
2. 教师权限的用户只能操作自己的数据
3. 密码字段在返回结果中会包含，管理员可以查看
4. 批量操作支持事务，失败时会回滚
5. 分页查询默认按创建时间倒序排列
