-- =====================================================
-- 项目管理系统数据库表结构
-- 创建时间: 2025-01-04
-- 说明: 包含项目选题征集、申报、中检、结项的完整业务流程
-- =====================================================

USE glrchx;

-- =====================================================
-- 1. 基础字典表
-- =====================================================

-- 1.1 项目大类表
DROP TABLE IF EXISTS `t_rchx_project_categories`;
CREATE TABLE `t_rchx_project_categories` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '大类ID',
  `category_code` varchar(50) NOT NULL COMMENT '大类编码',
  `category_name` varchar(100) NOT NULL COMMENT '大类名称',
  `description` text COMMENT '大类描述',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序号',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0禁用,1启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`category_code`),
  KEY `idx_sort_enabled` (`sort_order`, `is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目大类表';

-- 1.2 项目类别表
DROP TABLE IF EXISTS `t_rchx_project_types`;
CREATE TABLE `t_rchx_project_types` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '类别ID',
  `type_code` varchar(50) NOT NULL COMMENT '类别编码',
  `type_name` varchar(100) NOT NULL COMMENT '类别名称',
  `category_id` int NOT NULL COMMENT '所属大类ID',
  `description` text COMMENT '类别描述',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序号',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0禁用,1启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_code` (`type_code`),
  KEY `idx_category_sort` (`category_id`, `sort_order`),
  KEY `idx_enabled` (`is_enabled`),
  CONSTRAINT `fk_type_category` FOREIGN KEY (`category_id`) REFERENCES `t_rchx_project_categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目类别表';

-- 1.3 管理部门表
DROP TABLE IF EXISTS `t_rchx_departments`;
CREATE TABLE `t_rchx_departments` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `dept_code` varchar(50) NOT NULL COMMENT '部门编码',
  `dept_name` varchar(100) NOT NULL COMMENT '部门名称',
  `contact_person` varchar(50) COMMENT '联系人',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `description` text COMMENT '部门描述',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序号',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0禁用,1启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dept_code` (`dept_code`),
  KEY `idx_sort_enabled` (`sort_order`, `is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理部门表';

-- =====================================================
-- 2. 业务流程表
-- =====================================================

-- 2.1 项目选题征集表
DROP TABLE IF EXISTS `t_rchx_project_collection`;
CREATE TABLE `t_rchx_project_collection` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '征集ID',
  `collection_code` varchar(50) NOT NULL COMMENT '征集编号',
  `collection_name` varchar(200) NOT NULL COMMENT '征集名称',
  `category_id` int NOT NULL COMMENT '项目大类ID',
  `type_id` int NOT NULL COMMENT '项目类别ID',
  `dept_id` int NOT NULL COMMENT '管理部门ID',
  `guide_content` longtext COMMENT '填报指南内容',
  `accept_start_time` datetime NOT NULL COMMENT '受理开始时间',
  `accept_end_time` datetime NOT NULL COMMENT '受理结束时间',
  `status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态(DRAFT草稿,PUBLISHED发布,CLOSED关闭)',
  `submit_time` datetime COMMENT '提交时间',
  `review_time` datetime COMMENT '审核时间',
  `reviewer_zgh` varchar(50) COMMENT '审核人职工号',
  `review_comments` text COMMENT '审核意见',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人职工号',
  `update_by` varchar(50) COMMENT '更新人职工号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_collection_code` (`collection_code`),
  KEY `idx_category_type` (`category_id`, `type_id`),
  KEY `idx_dept_status` (`dept_id`, `status`),
  KEY `idx_accept_time` (`accept_start_time`, `accept_end_time`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_collection_category` FOREIGN KEY (`category_id`) REFERENCES `t_rchx_project_categories` (`id`),
  CONSTRAINT `fk_collection_type` FOREIGN KEY (`type_id`) REFERENCES `t_rchx_project_types` (`id`),
  CONSTRAINT `fk_collection_dept` FOREIGN KEY (`dept_id`) REFERENCES `t_rchx_departments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目选题征集表';

-- 2.2 项目申报表
DROP TABLE IF EXISTS `t_rchx_project_application`;
CREATE TABLE `t_rchx_project_application` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '申报ID',
  `project_code` varchar(50) NOT NULL COMMENT '项目编号',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `category_id` int NOT NULL COMMENT '项目大类ID',
  `type_id` int NOT NULL COMMENT '项目类别ID',
  `dept_id` int NOT NULL COMMENT '管理部门ID',
  `applicant_zgh` varchar(50) NOT NULL COMMENT '申报人职工号',
  `applicant_name` varchar(50) NOT NULL COMMENT '申报人姓名',
  `guide_content` longtext COMMENT '填报指南内容',
  `accept_start_time` datetime NOT NULL COMMENT '受理开始时间',
  `accept_end_time` datetime NOT NULL COMMENT '受理结束时间',
  `status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态(DRAFT草稿,SUBMITTED已提交,REVIEWING审核中,APPROVED通过,REJECTED拒绝)',
  `submit_time` datetime COMMENT '提交时间',
  `review_time` datetime COMMENT '审核时间',
  `reviewer_zgh` varchar(50) COMMENT '审核人职工号',
  `review_comments` text COMMENT '审核意见',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人职工号',
  `update_by` varchar(50) COMMENT '更新人职工号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_category_type` (`category_id`, `type_id`),
  KEY `idx_dept_status` (`dept_id`, `status`),
  KEY `idx_applicant` (`applicant_zgh`),
  KEY `idx_accept_time` (`accept_start_time`, `accept_end_time`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_application_category` FOREIGN KEY (`category_id`) REFERENCES `t_rchx_project_categories` (`id`),
  CONSTRAINT `fk_application_type` FOREIGN KEY (`type_id`) REFERENCES `t_rchx_project_types` (`id`),
  CONSTRAINT `fk_application_dept` FOREIGN KEY (`dept_id`) REFERENCES `t_rchx_departments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目申报表';

-- 2.3 项目中检表
DROP TABLE IF EXISTS `t_rchx_project_inspection`;
CREATE TABLE `t_rchx_project_inspection` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '中检ID',
  `project_code` varchar(50) NOT NULL COMMENT '项目编号',
  `inspection_name` varchar(200) NOT NULL COMMENT '中检名称',
  `category_id` int NOT NULL COMMENT '项目大类ID',
  `type_id` int NOT NULL COMMENT '项目类别ID',
  `dept_id` int NOT NULL COMMENT '管理部门ID',
  `project_leader_zgh` varchar(50) NOT NULL COMMENT '项目负责人职工号',
  `project_leader_name` varchar(50) NOT NULL COMMENT '项目负责人姓名',
  `guide_content` longtext COMMENT '填报指南内容',
  `accept_start_time` datetime NOT NULL COMMENT '受理开始时间',
  `accept_end_time` datetime NOT NULL COMMENT '受理结束时间',
  `inspection_progress` decimal(5,2) COMMENT '项目进度百分比',
  `progress_description` text COMMENT '进度说明',
  `status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态(DRAFT草稿,SUBMITTED已提交,REVIEWING审核中,APPROVED通过,REJECTED拒绝)',
  `submit_time` datetime COMMENT '提交时间',
  `review_time` datetime COMMENT '审核时间',
  `reviewer_zgh` varchar(50) COMMENT '审核人职工号',
  `review_comments` text COMMENT '审核意见',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人职工号',
  `update_by` varchar(50) COMMENT '更新人职工号',
  PRIMARY KEY (`id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_category_type` (`category_id`, `type_id`),
  KEY `idx_dept_status` (`dept_id`, `status`),
  KEY `idx_leader` (`project_leader_zgh`),
  KEY `idx_accept_time` (`accept_start_time`, `accept_end_time`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_inspection_category` FOREIGN KEY (`category_id`) REFERENCES `t_rchx_project_categories` (`id`),
  CONSTRAINT `fk_inspection_type` FOREIGN KEY (`type_id`) REFERENCES `t_rchx_project_types` (`id`),
  CONSTRAINT `fk_inspection_dept` FOREIGN KEY (`dept_id`) REFERENCES `t_rchx_departments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目中检表';

-- 2.4 项目结项表
DROP TABLE IF EXISTS `t_rchx_project_completion`;
CREATE TABLE `t_rchx_project_completion` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '结项ID',
  `project_code` varchar(50) NOT NULL COMMENT '项目编号',
  `completion_name` varchar(200) NOT NULL COMMENT '结项名称',
  `category_id` int NOT NULL COMMENT '项目大类ID',
  `type_id` int NOT NULL COMMENT '项目类别ID',
  `dept_id` int NOT NULL COMMENT '管理部门ID',
  `project_leader_zgh` varchar(50) NOT NULL COMMENT '项目负责人职工号',
  `project_leader_name` varchar(50) NOT NULL COMMENT '项目负责人姓名',
  `guide_content` longtext COMMENT '填报指南内容',
  `accept_start_time` datetime NOT NULL COMMENT '受理开始时间',
  `accept_end_time` datetime NOT NULL COMMENT '受理结束时间',
  `completion_result` text COMMENT '完成成果描述',
  `achievement_summary` text COMMENT '成果总结',
  `status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态(DRAFT草稿,SUBMITTED已提交,REVIEWING审核中,APPROVED通过,REJECTED拒绝)',
  `submit_time` datetime COMMENT '提交时间',
  `review_time` datetime COMMENT '审核时间',
  `reviewer_zgh` varchar(50) COMMENT '审核人职工号',
  `review_comments` text COMMENT '审核意见',
  `completion_score` decimal(5,2) COMMENT '结项评分',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人职工号',
  `update_by` varchar(50) COMMENT '更新人职工号',
  PRIMARY KEY (`id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_category_type` (`category_id`, `type_id`),
  KEY `idx_dept_status` (`dept_id`, `status`),
  KEY `idx_leader` (`project_leader_zgh`),
  KEY `idx_accept_time` (`accept_start_time`, `accept_end_time`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_completion_category` FOREIGN KEY (`category_id`) REFERENCES `t_rchx_project_categories` (`id`),
  CONSTRAINT `fk_completion_type` FOREIGN KEY (`type_id`) REFERENCES `t_rchx_project_types` (`id`),
  CONSTRAINT `fk_completion_dept` FOREIGN KEY (`dept_id`) REFERENCES `t_rchx_departments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目结项表';

-- =====================================================
-- 3. 文件管理表
-- =====================================================

-- 3.1 项目文件表（统一管理所有阶段的文件）
DROP TABLE IF EXISTS `t_rchx_project_files`;
CREATE TABLE `t_rchx_project_files` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型(COLLECTION征集,APPLICATION申报,INSPECTION中检,COMPLETION结项)',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `project_code` varchar(50) COMMENT '项目编号',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `file_extension` varchar(10) NOT NULL COMMENT '文件扩展名',
  `bucket_name` varchar(100) NOT NULL DEFAULT 'rchx' COMMENT 'MinIO存储桶名称',
  `object_key` varchar(500) NOT NULL COMMENT 'MinIO对象键',
  `content_type` varchar(100) COMMENT '内容类型',
  `file_category` varchar(50) COMMENT '文件分类(GUIDE指南,MATERIAL材料,REPORT报告,OTHER其他)',
  `description` text COMMENT '文件描述',
  `upload_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `upload_by` varchar(50) NOT NULL COMMENT '上传人职工号',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0未删除,1已删除)',
  `delete_time` datetime COMMENT '删除时间',
  `delete_by` varchar(50) COMMENT '删除人职工号',
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_file_category` (`file_category`),
  KEY `idx_upload_by` (`upload_by`),
  KEY `idx_bucket_object` (`bucket_name`, `object_key`),
  KEY `idx_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目文件表';

-- =====================================================
-- 5. 多表联查示例SQL
-- =====================================================

-- 5.1 项目全生命周期查询
-- 查询某个项目从征集到结项的完整信息
SELECT
    pa.project_code AS '项目编号',
    pa.project_name AS '项目名称',
    pc.category_name AS '项目大类',
    pt.type_name AS '项目类别',
    pd.dept_name AS '管理部门',
    pa.applicant_name AS '申报人',
    pa.status AS '申报状态',
    pi.inspection_progress AS '中检进度',
    pi.status AS '中检状态',
    pcom.completion_score AS '结项评分',
    pcom.status AS '结项状态',
    pa.create_time AS '申报时间',
    pi.create_time AS '中检时间',
    pcom.create_time AS '结项时间'
FROM t_rchx_project_application pa
LEFT JOIN t_rchx_project_inspection pi ON pa.project_code = pi.project_code
LEFT JOIN t_rchx_project_completion pcom ON pa.project_code = pcom.project_code
LEFT JOIN t_rchx_project_categories pc ON pa.category_id = pc.id
LEFT JOIN t_rchx_project_types pt ON pa.type_id = pt.id
LEFT JOIN t_rchx_departments pd ON pa.dept_id = pd.id
WHERE pa.project_code = 'PROJECT_CODE_HERE'
ORDER BY pa.create_time DESC;

-- 5.2 按部门统计项目情况
-- 统计各部门管理的项目数量和状态分布
SELECT
    pd.dept_name AS '管理部门',
    COUNT(DISTINCT pa.id) AS '申报项目数',
    COUNT(DISTINCT CASE WHEN pa.status = 'APPROVED' THEN pa.id END) AS '申报通过数',
    COUNT(DISTINCT pi.id) AS '中检项目数',
    COUNT(DISTINCT CASE WHEN pi.status = 'APPROVED' THEN pi.id END) AS '中检通过数',
    COUNT(DISTINCT pcom.id) AS '结项项目数',
    COUNT(DISTINCT CASE WHEN pcom.status = 'APPROVED' THEN pcom.id END) AS '结项通过数',
    ROUND(AVG(pcom.completion_score), 2) AS '平均结项评分'
FROM t_rchx_departments pd
LEFT JOIN t_rchx_project_application pa ON pd.id = pa.dept_id
LEFT JOIN t_rchx_project_inspection pi ON pd.id = pi.dept_id
LEFT JOIN t_rchx_project_completion pcom ON pd.id = pcom.dept_id
WHERE pd.is_enabled = 1
GROUP BY pd.id, pd.dept_name
ORDER BY pd.sort_order;

-- 5.3 按项目类别分析申报情况
-- 分析不同项目大类和类别的申报情况
SELECT
    pc.category_name AS '项目大类',
    pt.type_name AS '项目类别',
    COUNT(pa.id) AS '申报总数',
    COUNT(CASE WHEN pa.status = 'APPROVED' THEN 1 END) AS '通过数量',
    COUNT(CASE WHEN pa.status = 'REJECTED' THEN 1 END) AS '拒绝数量',
    COUNT(CASE WHEN pa.status IN ('DRAFT', 'SUBMITTED', 'REVIEWING') THEN 1 END) AS '待处理数量',
    ROUND(COUNT(CASE WHEN pa.status = 'APPROVED' THEN 1 END) * 100.0 / COUNT(pa.id), 2) AS '通过率(%)'
FROM t_rchx_project_categories pc
LEFT JOIN t_rchx_project_types pt ON pc.id = pt.category_id
LEFT JOIN t_rchx_project_application pa ON pt.id = pa.type_id
WHERE pc.is_enabled = 1 AND pt.is_enabled = 1
GROUP BY pc.id, pc.category_name, pt.id, pt.type_name
HAVING COUNT(pa.id) > 0
ORDER BY pc.sort_order, pt.sort_order;

-- 5.4 项目文件关联查询
-- 查询某个项目各阶段的所有文件
SELECT
    pf.business_type AS '业务阶段',
    CASE pf.business_type
        WHEN 'COLLECTION' THEN '选题征集'
        WHEN 'APPLICATION' THEN '项目申报'
        WHEN 'INSPECTION' THEN '项目中检'
        WHEN 'COMPLETION' THEN '项目结项'
        ELSE '其他'
    END AS '阶段名称',
    pf.file_category AS '文件分类',
    pf.file_original_name AS '文件名',
    pf.file_size AS '文件大小(字节)',
    pf.upload_time AS '上传时间',
    pf.upload_by AS '上传人',
    pf.object_key AS 'MinIO对象键'
FROM t_rchx_project_files pf
WHERE pf.project_code = 'PROJECT_CODE_HERE'
  AND pf.is_deleted = 0
ORDER BY pf.upload_time DESC;

-- 5.5 时间维度统计查询
-- 按月统计各阶段项目数量
SELECT
    DATE_FORMAT(pa.create_time, '%Y-%m') AS '月份',
    COUNT(pa.id) AS '申报项目数',
    COUNT(pi.id) AS '中检项目数',
    COUNT(pcom.id) AS '结项项目数'
FROM t_rchx_project_application pa
LEFT JOIN t_rchx_project_inspection pi ON DATE_FORMAT(pa.create_time, '%Y-%m') = DATE_FORMAT(pi.create_time, '%Y-%m')
LEFT JOIN t_rchx_project_completion pcom ON DATE_FORMAT(pa.create_time, '%Y-%m') = DATE_FORMAT(pcom.create_time, '%Y-%m')
WHERE pa.create_time >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
GROUP BY DATE_FORMAT(pa.create_time, '%Y-%m')
ORDER BY DATE_FORMAT(pa.create_time, '%Y-%m') DESC;

-- =====================================================
-- 6. 性能优化索引
-- =====================================================

-- 6.1 复合索引优化
-- 项目申报表的复合查询索引
CREATE INDEX idx_application_complex ON t_rchx_project_application
(dept_id, status, category_id, create_time DESC);

-- 项目中检表的复合查询索引
CREATE INDEX idx_inspection_complex ON t_rchx_project_inspection
(dept_id, status, project_code, create_time DESC);

-- 项目结项表的复合查询索引
CREATE INDEX idx_completion_complex ON t_rchx_project_completion
(dept_id, status, project_code, create_time DESC);

-- 文件表的复合查询索引
CREATE INDEX idx_files_complex ON t_rchx_project_files
(project_code, business_type, is_deleted, upload_time DESC);

-- 6.2 全文搜索索引（如果需要）
-- ALTER TABLE t_rchx_project_application ADD FULLTEXT(project_name, guide_content);
-- ALTER TABLE t_rchx_project_inspection ADD FULLTEXT(inspection_name, progress_description);
-- ALTER TABLE t_rchx_project_completion ADD FULLTEXT(completion_name, completion_result);

-- =====================================================
-- 7. 视图定义（便于查询）
-- =====================================================

-- 7.1 项目概览视图
CREATE OR REPLACE VIEW v_project_overview AS
SELECT
    pa.id,
    pa.project_code,
    pa.project_name,
    pc.category_name,
    pt.type_name,
    pd.dept_name,
    pa.applicant_name,
    pa.status AS application_status,
    pi.status AS inspection_status,
    pcom.status AS completion_status,
    pi.inspection_progress,
    pcom.completion_score,
    pa.create_time AS application_time,
    pi.create_time AS inspection_time,
    pcom.create_time AS completion_time
FROM t_rchx_project_application pa
LEFT JOIN t_rchx_project_inspection pi ON pa.project_code = pi.project_code
LEFT JOIN t_rchx_project_completion pcom ON pa.project_code = pcom.project_code
LEFT JOIN t_rchx_project_categories pc ON pa.category_id = pc.id
LEFT JOIN t_rchx_project_types pt ON pa.type_id = pt.id
LEFT JOIN t_rchx_departments pd ON pa.dept_id = pd.id;

-- 7.2 部门统计视图
CREATE OR REPLACE VIEW v_department_statistics AS
SELECT
    pd.id AS dept_id,
    pd.dept_name,
    COUNT(DISTINCT pa.id) AS total_applications,
    COUNT(DISTINCT CASE WHEN pa.status = 'APPROVED' THEN pa.id END) AS approved_applications,
    COUNT(DISTINCT pi.id) AS total_inspections,
    COUNT(DISTINCT CASE WHEN pi.status = 'APPROVED' THEN pi.id END) AS approved_inspections,
    COUNT(DISTINCT pcom.id) AS total_completions,
    COUNT(DISTINCT CASE WHEN pcom.status = 'APPROVED' THEN pcom.id END) AS approved_completions,
    ROUND(AVG(pcom.completion_score), 2) AS avg_completion_score
FROM t_rchx_departments pd
LEFT JOIN t_rchx_project_application pa ON pd.id = pa.dept_id
LEFT JOIN t_rchx_project_inspection pi ON pd.id = pi.dept_id
LEFT JOIN t_rchx_project_completion pcom ON pd.id = pcom.dept_id
WHERE pd.is_enabled = 1
GROUP BY pd.id, pd.dept_name;

-- =====================================================
-- 8. 执行完成提示
-- =====================================================

SELECT '项目管理系统数据库表结构创建完成！' as message;

-- =====================================================
-- 9. 表结构说明
-- =====================================================

/*
数据库表结构说明：

1. 基础字典表（3个）：
   - t_rchx_project_categories: 项目大类表
   - t_rchx_project_types: 项目类别表
   - t_rchx_departments: 管理部门表

2. 业务流程表（4个）：
   - t_rchx_project_collection: 项目选题征集表
   - t_rchx_project_application: 项目申报表
   - t_rchx_project_inspection: 项目中检表
   - t_rchx_project_completion: 项目结项表

3. 文件管理表（1个）：
   - t_rchx_project_files: 项目文件表（支持MinIO存储）

4. 视图（2个）：
   - v_project_overview: 项目概览视图
   - v_department_statistics: 部门统计视图

特性：
- 支持项目全生命周期管理
- 集成MinIO文件存储
- 完善的索引设计
- 丰富的多表联查示例
- 灵活的权限控制
- 详细的审核流程
*/
