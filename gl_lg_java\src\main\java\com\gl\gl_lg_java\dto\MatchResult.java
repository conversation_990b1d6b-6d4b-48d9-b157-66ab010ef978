package com.gl.gl_lg_java.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 匹配结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchResult {
    
    /**
     * 教师职工号
     */
    private String teacherZgh;
    
    /**
     * 教师姓名
     */
    private String teacherName;
    
    /**
     * 所在部门
     */
    private String department;
    
    /**
     * 职称
     */
    private String title;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 匹配分数
     */
    private Double matchScore;
    
    /**
     * 匹配等级
     */
    private String matchLevel;
    
    /**
     * 是否符合申报条件
     */
    private Boolean isQualified;
    
    /**
     * 不符合原因
     */
    private String reason;
    
    /**
     * 优势条件
     */
    private List<String> strengths;
    
    /**
     * 不足之处
     */
    private List<String> weaknesses;
    
    /**
     * 改进建议
     */
    private List<String> recommendations;
    
    /**
     * 详细匹配信息
     */
    private MatchDetails matchDetails;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 根据分数计算匹配等级
     */
    public void calculateMatchLevel() {
        if (matchScore == null) {
            this.matchLevel = "UNKNOWN";
            return;
        }
        
        if (matchScore >= 85) {
            this.matchLevel = "HIGH";
        } else if (matchScore >= 70) {
            this.matchLevel = "MEDIUM";
        } else if (matchScore >= 60) {
            this.matchLevel = "LOW";
        } else {
            this.matchLevel = "VERY_LOW";
        }
    }
    
    /**
     * 获取匹配等级中文描述
     */
    public String getMatchLevelText() {
        switch (matchLevel) {
            case "HIGH": return "高匹配";
            case "MEDIUM": return "中匹配";
            case "LOW": return "低匹配";
            case "VERY_LOW": return "极低匹配";
            default: return "未知";
        }
    }
}
