package com.gl.gl_lg_java.controller;

import com.gl.gl_lg_java.annotation.RequirePermission;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.dto.ProjectRequirementDTO;
import com.gl.gl_lg_java.dto.TalentMatchingResultDTO;
import com.gl.gl_lg_java.dto.TeacherCapabilityDTO;
import com.gl.gl_lg_java.service.TalentMatchingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 人才项目适配度分析控制器
 */
@RestController
@RequestMapping("/api/talent-matching")
@Slf4j
public class TalentMatchingController {
    
    @Autowired
    private TalentMatchingService talentMatchingService;
    
    /**
     * 执行人才项目适配度分析
     */
    @PostMapping("/analyze")
    @RequirePermission("学院管理员")
    public Result<List<TalentMatchingResultDTO>> analyzeTalentMatching(
            @RequestBody ProjectRequirementDTO requirement) {
        try {
            log.info("开始执行人才适配度分析，项目：{}", requirement.getProjectName());
            
            // 验证权重配置
            if (!requirement.isWeightValid()) {
                return Result.error("权重配置无效，总和必须为100%");
            }
            
            List<TalentMatchingResultDTO> results = talentMatchingService.analyzeTalentMatching(requirement);
            
            log.info("人才适配度分析完成，找到{}个符合条件的候选人", results.size());
            return Result.success(results);
            
        } catch (Exception e) {
            log.error("人才适配度分析失败：{}", e.getMessage(), e);
            return Result.error("分析失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取教师详细能力分析
     */
    @GetMapping("/teacher-analysis/{teacherId}")
    @RequirePermission("教师")
    public Result<TeacherCapabilityDTO> getTeacherCapabilityAnalysis(
            @PathVariable String teacherId) {
        try {
            log.info("获取教师能力分析，教师ID：{}", teacherId);
            
            TeacherCapabilityDTO analysis = talentMatchingService.getTeacherCapabilityAnalysis(teacherId);
            
            log.info("教师能力分析完成，教师：{}，综合得分：{}", 
                analysis.getTeacherName(), analysis.getOverallScore());
            return Result.success(analysis);
            
        } catch (Exception e) {
            log.error("获取教师能力分析失败：{}", e.getMessage(), e);
            return Result.error("分析失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取预设项目模板
     */
    @GetMapping("/templates")
    @RequirePermission("教师")
    public Result<List<ProjectRequirementDTO>> getPresetTemplates() {
        try {
            log.info("获取预设项目模板");
            
            List<ProjectRequirementDTO> templates = talentMatchingService.getPresetTemplates();
            
            log.info("获取预设模板成功，共{}个模板", templates.size());
            return Result.success(templates);
            
        } catch (Exception e) {
            log.error("获取预设模板失败：{}", e.getMessage(), e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }
    
    /**
     * 快速匹配分析（使用预设模板）
     */
    @PostMapping("/quick-analyze")
    @RequirePermission("学院管理员")
    public Result<List<TalentMatchingResultDTO>> quickAnalyze(
            @RequestParam String templateType,
            @RequestParam String projectName,
            @RequestParam(required = false) String projectDescription) {
        try {
            log.info("执行快速匹配分析，模板类型：{}，项目：{}", templateType, projectName);
            
            // 获取预设模板
            List<ProjectRequirementDTO> templates = talentMatchingService.getPresetTemplates();
            ProjectRequirementDTO template = templates.stream()
                .filter(t -> t.getProjectType().equals(templateType))
                .findFirst()
                .orElse(templates.get(0)); // 默认使用第一个模板
            
            // 设置项目信息
            template.setProjectName(projectName);
            if (projectDescription != null) {
                template.setProjectDescription(projectDescription);
            }
            
            List<TalentMatchingResultDTO> results = talentMatchingService.analyzeTalentMatching(template);
            
            log.info("快速匹配分析完成，找到{}个符合条件的候选人", results.size());
            return Result.success(results);
            
        } catch (Exception e) {
            log.error("快速匹配分析失败：{}", e.getMessage(), e);
            return Result.error("分析失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量教师能力评估
     */
    @PostMapping("/batch-evaluate")
    @RequirePermission("学院管理员")
    public Result<List<TeacherCapabilityDTO>> batchEvaluateTeachers(
            @RequestBody List<String> teacherIds) {
        try {
            log.info("开始批量教师能力评估，教师数量：{}", teacherIds.size());
            
            List<TeacherCapabilityDTO> results = teacherIds.stream()
                .map(teacherId -> {
                    try {
                        return talentMatchingService.getTeacherCapabilityAnalysis(teacherId);
                    } catch (Exception e) {
                        log.warn("教师{}能力分析失败：{}", teacherId, e.getMessage());
                        return null;
                    }
                })
                .filter(result -> result != null)
                .collect(java.util.stream.Collectors.toList());
            
            log.info("批量教师能力评估完成，成功分析{}个教师", results.size());
            return Result.success(results);
            
        } catch (Exception e) {
            log.error("批量教师能力评估失败：{}", e.getMessage(), e);
            return Result.error("评估失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取分析统计信息
     */
    @GetMapping("/statistics")
    @RequirePermission("学院管理员")
    public Result<Object> getAnalysisStatistics() {
        try {
            log.info("获取分析统计信息");
            
            // 这里可以添加统计信息的计算逻辑
            // 比如：总教师数、各职称分布、各维度平均分等
            
            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("message", "统计功能开发中");
            statistics.put("timestamp", java.time.LocalDateTime.now());
            
            return Result.success(statistics);
            
        } catch (Exception e) {
            log.error("获取统计信息失败：{}", e.getMessage(), e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }
}
