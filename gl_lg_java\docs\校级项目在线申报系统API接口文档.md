# 校级项目在线申报系统API接口文档

## 基础信息

- **Content-Type**: `application/json` (除文件上传外)
- **响应格式**: 统一使用Result包装

## 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

**状态码说明：**
- `200` - 成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `500` - 服务器内部错误

## 1. 校级项目类别管理API

### 1.1 分页查询项目类别

**接口地址：** `GET /api/school-project/categories/page`

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | int | 否 | 1 | 页码 |
| size | int | 否 | 10 | 页大小 |
| categoryName | string | 否 | - | 类别名称（模糊查询） |
| isEnabled | boolean | 否 | - | 是否启用 |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "categoryCode": "CAT2025001",
        "categoryName": "教学改革项目",
        "fundingRangeMin": 1.00,
        "fundingRangeMax": 5.00,
        "applicationRequirements": "需提交教学改革方案",
        "evaluationCriteria": "教学效果、创新性",
        "projectDurationMonths": 12,
        "description": "支持教学改革创新",
        "sortOrder": 1,
        "isEnabled": true,
        "applicationCount": 10,
        "approvedCount": 8,
        "totalFundingRequested": 50.00,
        "totalFundingApproved": 35.00,
        "createTime": "2025-01-04T10:00:00",
        "updateTime": "2025-01-04T10:00:00"
      }
    ],
    "total": 50,
    "current": 1,
    "size": 10,
    "pages": 5
  }
}
```

### 1.2 查询启用的项目类别

**接口地址：** `GET /api/school-project/categories/enabled`

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "categoryCode": "CAT2025001",
      "categoryName": "教学改革项目",
      "fundingRangeMin": 1.00,
      "fundingRangeMax": 5.00,
      "sortOrder": 1
    }
  ]
}
```

### 1.3 根据ID查询项目类别

**接口地址：** `GET /api/school-project/categories/{id}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 类别ID |

### 1.4 根据类别编码查询

**接口地址：** `GET /api/school-project/categories/code/{categoryCode}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryCode | string | 是 | 类别编码 |

### 1.5 创建项目类别

**接口地址：** `POST /api/school-project/categories`

**请求体：**
```json
{
  "categoryCode": "CAT2025002",
  "categoryName": "科研启动项目",
  "fundingRangeMin": 2.00,
  "fundingRangeMax": 10.00,
  "applicationRequirements": "需提交研究方案、文献综述、预期成果等",
  "evaluationCriteria": "学术价值、创新性、可行性",
  "projectDurationMonths": 24,
  "description": "支持青年教师开展科学研究工作",
  "sortOrder": 2,
  "isEnabled": true
}
```

**字段说明：**
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryCode | string | 是 | 类别编码，唯一 |
| categoryName | string | 是 | 类别名称 |
| fundingRangeMin | decimal | 否 | 资助金额下限(万元) |
| fundingRangeMax | decimal | 否 | 资助金额上限(万元) |
| applicationRequirements | string | 否 | 申报要求说明 |
| evaluationCriteria | string | 否 | 评审标准 |
| projectDurationMonths | int | 否 | 项目周期(月) |
| description | string | 否 | 类别描述 |
| sortOrder | int | 否 | 排序号 |
| isEnabled | boolean | 否 | 是否启用，默认true |

### 1.6 更新项目类别

**接口地址：** `PUT /api/school-project/categories/{id}`

**请求体：** 同创建接口

### 1.7 删除项目类别

**接口地址：** `DELETE /api/school-project/categories/{id}`

### 1.8 批量删除项目类别

**接口地址：** `DELETE /api/school-project/categories/batch`

**请求体：**
```json
[1, 2, 3]
```

### 1.9 启用/禁用类别

**接口地址：** `PUT /api/school-project/categories/{id}/status`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isEnabled | boolean | 是 | 是否启用 |

### 1.10 调整类别排序

**接口地址：** `PUT /api/school-project/categories/{id}/sort`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sortOrder | int | 是 | 排序号 |

### 1.11 获取类别统计信息

**接口地址：** `GET /api/school-project/categories/statistics`

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "categoryName": "教学改革项目",
      "categoryCode": "CAT2025001",
      "fundingRangeMin": 1.00,
      "fundingRangeMax": 5.00,
      "applicationCount": 10,
      "approvedCount": 8,
      "submittedCount": 9,
      "draftCount": 1,
      "totalFundingRequested": 50.00,
      "totalFundingApproved": 35.00,
      "approvalRate": 80.00
    }
  ]
}
```

### 1.12 根据资助金额范围查询类别

**接口地址：** `GET /api/school-project/categories/funding-range`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| fundingAmount | decimal | 是 | 资助金额 |

### 1.13 生成类别编码

**接口地址：** `GET /api/school-project/categories/generate-code`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryName | string | 是 | 类别名称 |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": "CAT2025003"
}
```

### 1.14 导出类别列表

**接口地址：** `GET /api/school-project/categories/export`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isEnabled | boolean | 否 | 是否启用 |

## 2. 文件管理API

### 2.1 上传文件

**接口地址：** `POST /api/school-project/files/upload`

**Content-Type：** `multipart/form-data`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| applicationId | long | 是 | 申报ID |
| applicationCode | string | 是 | 申报编号 |
| fileCategory | string | 是 | 文件类别 |
| fileDescription | string | 否 | 文件描述 |
| isRequired | boolean | 否 | 是否必需文件，默认false |
| uploadBy | string | 是 | 上传人职工号 |
| uploadByName | string | 是 | 上传人姓名 |
| file | file | 是 | 上传的文件 |

**文件类别枚举：**
- `APPLICATION` - 申报书
- `BUDGET` - 预算书
- `RESUME` - 个人简历
- `ACHIEVEMENT` - 成果证明
- `PLAN` - 实施方案
- `OTHER` - 其他

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "applicationId": 1,
    "applicationCode": "SCHOOL2024001",
    "fileCategory": "APPLICATION",
    "fileName": "项目申报书_20250104_abc123.pdf",
    "fileOriginalName": "项目申报书.pdf",
    "filePath": "SCHOOL2024001/APPLICATION/20250104/abc123_项目申报书.pdf",
    "fileSize": 1024000,
    "fileType": "application/pdf",
    "fileExtension": ".pdf",
    "bucketName": "rchx-school-project",
    "objectName": "SCHOOL2024001/APPLICATION/20250104/abc123_项目申报书.pdf",
    "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
    "fileDescription": "项目申报书",
    "isRequired": true,
    "uploadBy": "202401001",
    "uploadByName": "张三",
    "downloadCount": 0,
    "uploadTime": "2025-01-04T10:30:00",
    "createTime": "2025-01-04T10:30:00"
  }
}
```

### 2.2 获取文件下载URL

**接口地址：** `GET /api/school-project/files/download/{fileId}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| fileId | long | 是 | 文件ID |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": "http://localhost:9000/rchx-school-project/SCHOOL2024001/APPLICATION/20250104/abc123_项目申报书.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
}
```

### 2.3 直接下载文件流

**接口地址：** `GET /api/school-project/files/stream/{fileId}`

**说明：** 直接返回文件流，浏览器会自动下载文件

**响应：** 文件二进制流

### 2.4 删除文件

**接口地址：** `DELETE /api/school-project/files/{fileId}`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deleteBy | string | 是 | 删除人职工号 |

### 2.5 批量删除文件

**接口地址：** `DELETE /api/school-project/files/batch`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deleteBy | string | 是 | 删除人职工号 |

**请求体：**
```json
[1, 2, 3]
```

### 2.6 分页查询文件信息

**接口地址：** `GET /api/school-project/files/page`

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | int | 否 | 1 | 页码 |
| size | int | 否 | 10 | 页大小 |
| applicationId | long | 否 | - | 申报ID |
| applicationCode | string | 否 | - | 申报编号 |
| fileCategory | string | 否 | - | 文件类别 |
| uploadBy | string | 否 | - | 上传人职工号 |
| isRequired | boolean | 否 | - | 是否必需文件 |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "applicationId": 1,
        "applicationCode": "SCHOOL2024001",
        "fileCategory": "APPLICATION",
        "fileName": "项目申报书_20250104_abc123.pdf",
        "fileOriginalName": "项目申报书.pdf",
        "fileSize": 1024000,
        "fileType": "application/pdf",
        "uploadBy": "202401001",
        "uploadByName": "张三",
        "downloadCount": 5,
        "uploadTime": "2025-01-04T10:30:00",
        "projectName": "智能教学系统研发",
        "applicantName": "李四"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10,
    "pages": 10
  }
}
```

### 2.7 根据申报ID查询文件列表

**接口地址：** `GET /api/school-project/files/application/{applicationId}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| applicationId | long | 是 | 申报ID |

### 2.8 根据申报编号查询文件列表

**接口地址：** `GET /api/school-project/files/application-code/{applicationCode}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| applicationCode | string | 是 | 申报编号 |

### 2.9 获取文件统计信息

**接口地址：** `GET /api/school-project/files/statistics`

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalFiles": 150,
    "totalSize": 1073741824,
    "requiredFiles": 80,
    "totalDownloads": 500,
    "totalUploaders": 25,
    "totalApplications": 30
  }
}
```

### 2.10 检查申报的必需文件是否完整

**接口地址：** `GET /api/school-project/files/check-required/{applicationId}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| applicationId | long | 是 | 申报ID |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "hasApplication": 1,
    "hasBudget": 1,
    "hasResume": 0,
    "totalFiles": 5
  }
}
```

## 3. 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权 | 检查登录状态和权限 |
| 403 | 禁止访问 | 检查用户权限 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 500 | 服务器内部错误 | 联系系统管理员 |

## 4. 文件上传限制

### 4.1 文件大小限制
- 单个文件最大：100MB
- 总上传大小限制：500MB

### 4.2 支持的文件类型
- **文档类型**：PDF、Word(.doc/.docx)、Excel(.xls/.xlsx)、PowerPoint(.ppt/.pptx)
- **图片类型**：JPEG、PNG、GIF
- **其他类型**：TXT、ZIP、RAR

### 4.3 文件存储路径格式
```
申报编号/文件类别/日期/UUID_原始文件名
例如：SCHOOL2024001/APPLICATION/20250104/abc123_项目申报书.pdf
```

## 5. 使用示例

### 5.1 JavaScript文件上传示例

```javascript
async function uploadFile(file, applicationId, applicationCode) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('applicationId', applicationId);
  formData.append('applicationCode', applicationCode);
  formData.append('fileCategory', 'APPLICATION');
  formData.append('uploadBy', '202401001');
  formData.append('uploadByName', '张三');

  try {
    const response = await fetch('/api/school-project/files/upload', {
      method: 'POST',
      body: formData
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('上传成功:', result.data);
      return result.data;
    } else {
      console.error('上传失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('上传异常:', error);
    throw error;
  }
}
```

### 5.2 文件下载示例

```javascript
async function downloadFile(fileId) {
  try {
    const response = await fetch(`/api/school-project/files/download/${fileId}`);
    const result = await response.json();

    if (result.code === 200) {
      // 使用预签名URL下载
      window.open(result.data);
    } else {
      console.error('获取下载链接失败:', result.message);
    }
  } catch (error) {
    console.error('下载异常:', error);
  }
}
```

### 5.3 创建项目类别示例

```javascript
async function createCategory(categoryData) {
  try {
    const response = await fetch('/api/school-project/categories', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(categoryData)
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('创建成功');
      return result.data;
    } else {
      console.error('创建失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建异常:', error);
    throw error;
  }
}
```

## 6. 注意事项

1. **文件上传**：
   - 确保文件类型在允许范围内
   - 注意文件大小限制
   - 上传后会自动生成唯一的文件名

2. **文件下载**：
   - 预签名URL有7天有效期
   - 每次下载会更新下载统计

3. **文件删除**：
   - 采用逻辑删除，不会立即从MinIO中删除物理文件
   - 删除操作不可逆，请谨慎操作

4. **权限控制**：
   - 建议根据用户角色控制API访问权限
   - 文件上传下载需要验证用户身份

5. **性能优化**：
   - 大文件上传建议使用分片上传
   - 批量操作时注意请求超时设置
```
