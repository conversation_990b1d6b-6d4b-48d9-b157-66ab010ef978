package com.gl.gl_lg_java.dto;

import lombok.Data;
import java.util.List;

/**
 * 科技论文统计数据传输对象
 */
@Data
public class KjlwStatisticsDTO {

    /**
     * 单位统计信息
     */
    @Data
    public static class UnitStatistics {
        private String dwmc;        // 单位名称
        private Long paperCount;    // 论文数量
    }

    /**
     * 发布范围统计信息
     */
    @Data
    public static class PublishScopeStatistics {
        private String fbfw;        // 发布范围
        private Long paperCount;    // 论文数量
    }

    /**
     * 学科类别统计信息
     */
    @Data
    public static class SubjectCategoryStatistics {
        private String xklb;        // 学科类别
        private Long paperCount;    // 论文数量
    }

    /**
     * 综合统计结果
     */
    @Data
    public static class ComprehensiveStatistics {
        // 单位统计 - 所有单位及其论文数量
        private List<UnitStatistics> unitStatistics;
        
        // 发布范围统计 - 所有发布范围及其论文数量
        private List<PublishScopeStatistics> publishScopeStatistics;
        
        // 学科类别统计 - 所有学科类别及其论文数量
        private List<SubjectCategoryStatistics> subjectCategoryStatistics;
        
        // 总体统计
        private Long totalPapers;           // 论文总数
        private Long totalUnits;            // 单位总数
        private Long totalPublishScopes;    // 发布范围总数
        private Long totalSubjectCategories; // 学科类别总数
    }
}
