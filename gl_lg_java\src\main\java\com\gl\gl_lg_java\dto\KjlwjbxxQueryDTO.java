package com.gl.gl_lg_java.dto;

import lombok.Data;

/**
 * 科技论文基本信息查询DTO
 */
@Data
public class KjlwjbxxQueryDTO {
    
    /**
     * 论文编号
     */
    private String lwbh;
    
    /**
     * 论文名称
     */
    private String lwmc;
    
    /**
     * 第一作者编号
     */
    private String dyzzbh;
    
    /**
     * 第一作者姓名
     */
    private String dyzzxm;
    
    /**
     * 通讯作者编号
     */
    private String txzzbh;
    
    /**
     * 通讯作者姓名
     */
    private String txzzxm;
    
    /**
     * 刊物名称
     */
    private String kwmc;
    
    /**
     * 刊物类型
     */
    private String kwlx;
    
    /**
     * 发表范围
     */
    private String fbfw;
    
    /**
     * 审核状态
     */
    private String shzt;
    
    /**
     * 单位名称
     */
    private String dwmc;
    
    /**
     * 发表日期开始
     */
    private String startDate;
    
    /**
     * 发表日期结束
     */
    private String endDate;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String orderBy = "fbrq";
    
    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "desc";
}
