package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.gl.gl_lg_java.domain.RchxJzggzjlxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_jzggzjlxx(教职工工作简历信息)】的数据库操作Mapper
 * @createDate 2025-07-18 11:11:41
 * @Entity com.gl.gl_lg_java.domain.RchxJzggzjlxx
 */
@Mapper
public interface RchxJzggzjlxxMapper extends BaseMapper<RchxJzggzjlxx> {

    /**
     * 核心字段列表 - 用于列表查询优化
     */
    String CORE_FIELDS = "zgh, gzqsrq, gzzzrq, gzdw, gznr, crzw, cszy, gzzmr, gzszd";

    /**
     * 根据职工号查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzggzjlxx WHERE zgh = #{zgh} ORDER BY gzqsrq DESC")
    List<RchxJzggzjlxx> findByZgh(@Param("zgh") String zgh);

    /**
     * 根据工作单位模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS
            + " FROM t_rchx_jzggzjlxx WHERE gzdw LIKE CONCAT('%', #{gzdw}, '%') ORDER BY gzqsrq DESC")
    List<RchxJzggzjlxx> findByGzdwLike(@Param("gzdw") String gzdw);

    /**
     * 根据工作内容模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS
            + " FROM t_rchx_jzggzjlxx WHERE gznr LIKE CONCAT('%', #{gznr}, '%') ORDER BY gzqsrq DESC")
    List<RchxJzggzjlxx> findByGznrLike(@Param("gznr") String gznr);

    /**
     * 根据曾任职务模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS
            + " FROM t_rchx_jzggzjlxx WHERE crzw LIKE CONCAT('%', #{crzw}, '%') ORDER BY gzqsrq DESC")
    List<RchxJzggzjlxx> findByCrzwLike(@Param("crzw") String crzw);

    /**
     * 根据从事专业模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS
            + " FROM t_rchx_jzggzjlxx WHERE cszy LIKE CONCAT('%', #{cszy}, '%') ORDER BY gzqsrq DESC")
    List<RchxJzggzjlxx> findByCszyLike(@Param("cszy") String cszy);

    /**
     * 根据工作证明人模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS
            + " FROM t_rchx_jzggzjlxx WHERE gzzmr LIKE CONCAT('%', #{gzzmr}, '%') ORDER BY gzqsrq DESC")
    List<RchxJzggzjlxx> findByGzzmrLike(@Param("gzzmr") String gzzmr);

    /**
     * 根据工作所在地查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_jzggzjlxx WHERE gzszd = #{gzszd} ORDER BY gzqsrq DESC")
    List<RchxJzggzjlxx> findByGzszd(@Param("gzszd") String gzszd);

    /**
     * 根据工作起始日期范围查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS
            + " FROM t_rchx_jzggzjlxx WHERE gzqsrq &gt;= #{startDate} AND gzqsrq &lt;= #{endDate} ORDER BY gzqsrq DESC")
    List<RchxJzggzjlxx> findByGzqsrqBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

    // 多条件查询已移至Service层使用QueryWrapper实现，更灵活且不易出错

    // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错
}
