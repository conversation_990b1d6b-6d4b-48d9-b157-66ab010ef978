-- =====================================================
-- 项目评审台数据库表结构
-- 创建时间: 2025-01-04
-- 说明: 用于项目评审管理，支持MinIO文件存储
-- =====================================================

USE glrchx;

-- =====================================================
-- 1. 项目评审台主表
-- =====================================================

DROP TABLE IF EXISTS `t_rchx_project_review_platform`;
CREATE TABLE `t_rchx_project_review_platform` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评审ID',
  `review_code` varchar(50) NOT NULL COMMENT '评审编号',
  `project_id` bigint COMMENT '关联项目申报表ID',
  `project_code` varchar(50) COMMENT '项目编号',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `project_type_id` int NOT NULL COMMENT '项目类型ID',
  `category_id` int NOT NULL COMMENT '项目大类ID',
  `dept_id` int NOT NULL COMMENT '申报人部门ID',
  `applicant_zgh` varchar(50) NOT NULL COMMENT '申报人职工号',
  `applicant_name` varchar(50) NOT NULL COMMENT '申报人姓名',
  `applicant_phone` varchar(20) COMMENT '申报人电话',
  `applicant_email` varchar(100) COMMENT '申报人邮箱',
  `submit_time` datetime NOT NULL COMMENT '提交时间',
  `review_deadline` datetime NOT NULL COMMENT '评审截止时间',
  `review_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '评审状态(PENDING待评审,REVIEWING评审中,COMPLETED已完成,REJECTED已拒绝,WITHDRAWN撤回)',
  `review_start_time` datetime COMMENT '评审开始时间',
  `review_end_time` datetime COMMENT '评审结束时间',
  `reviewer_zgh` varchar(50) COMMENT '评审人职工号',
  `reviewer_name` varchar(50) COMMENT '评审人姓名',
  `review_comments` text COMMENT '评审意见',
  `review_score` decimal(5,2) COMMENT '评审分数',
  `priority` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优先级(1普通,2重要,3紧急)',
  `funding_amount` decimal(10,2) COMMENT '申请资助资金(万元)',
  `project_description` longtext COMMENT '项目描述',
  `expected_outcomes` text COMMENT '预期成果',
  `implementation_plan` text COMMENT '实施方案',
  `budget_plan` text COMMENT '经费预算',
  `team_members` text COMMENT '团队成员信息',
  `project_start_date` date COMMENT '项目开始日期',
  `project_end_date` date COMMENT '项目结束日期',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开(0否,1是)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人职工号',
  `update_by` varchar(50) COMMENT '更新人职工号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_review_code` (`review_code`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_review_status` (`review_status`),
  KEY `idx_project_type` (`project_type_id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_dept` (`dept_id`),
  KEY `idx_applicant` (`applicant_zgh`),
  KEY `idx_reviewer` (`reviewer_zgh`),
  KEY `idx_deadline` (`review_deadline`),
  KEY `idx_submit_time` (`submit_time`),
  KEY `idx_priority_status` (`priority`, `review_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目评审台主表';

-- =====================================================
-- 2. 项目评审文件表
-- =====================================================

DROP TABLE IF EXISTS `t_rchx_project_review_files`;
CREATE TABLE `t_rchx_project_review_files` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `review_id` bigint NOT NULL COMMENT '评审ID',
  `review_code` varchar(50) NOT NULL COMMENT '评审编号',
  `file_category` varchar(50) NOT NULL COMMENT '文件类别(PROJECT_MATERIAL项目材料,REVIEW_GUIDE评审指南,EXPERT_OPINION专家意见,REVIEW_RESULT评审结果,APPLICATION申报书,BUDGET预算书,RESUME个人简历,ACHIEVEMENT成果证明,PLAN实施方案,OTHER其他)',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT 'MinIO文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL COMMENT '文件MIME类型',
  `file_extension` varchar(10) NOT NULL COMMENT '文件扩展名',
  `bucket_name` varchar(100) NOT NULL DEFAULT 'rchx-review' COMMENT 'MinIO存储桶名称',
  `object_name` varchar(500) NOT NULL COMMENT 'MinIO对象名称',
  `file_md5` varchar(32) COMMENT '文件MD5值',
  `file_description` text COMMENT '文件描述',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必需文件(0否,1是)',
  `is_confidential` tinyint(1) DEFAULT '0' COMMENT '是否机密文件(0否,1是)',
  `access_level` varchar(20) DEFAULT 'PUBLIC' COMMENT '访问级别(PUBLIC公开,EXPERT专家,ADMIN管理员)',
  `version_number` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `upload_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `upload_by` varchar(50) NOT NULL COMMENT '上传人职工号',
  `upload_by_name` varchar(50) NOT NULL COMMENT '上传人姓名',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `last_download_time` datetime COMMENT '最后下载时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0否,1是)',
  `delete_time` datetime COMMENT '删除时间',
  `delete_by` varchar(50) COMMENT '删除人职工号',
  PRIMARY KEY (`id`),
  KEY `idx_review_id` (`review_id`),
  KEY `idx_review_code` (`review_code`),
  KEY `idx_file_category` (`file_category`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_upload_by` (`upload_by`),
  KEY `idx_bucket_object` (`bucket_name`, `object_name`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_access_level` (`access_level`),
  CONSTRAINT `fk_review_files_review_id` FOREIGN KEY (`review_id`) REFERENCES `t_rchx_project_review_platform` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目评审文件表';

-- =====================================================
-- 3. 评审状态字典数据
-- =====================================================

-- 插入评审状态说明数据（如果需要单独的字典表）
-- 这里使用注释形式说明各状态含义：
/*
评审状态说明：
- PENDING: 待评审 - 项目已提交，等待分配评审人员
- REVIEWING: 评审中 - 已分配评审人员，正在进行评审
- COMPLETED: 已完成 - 评审已完成，给出最终结果
- REJECTED: 已拒绝 - 评审不通过，项目被拒绝
- WITHDRAWN: 撤回 - 申报人主动撤回项目申请

文件类别说明：
- PROJECT_MATERIAL: 项目材料 - 项目相关的基础材料
- REVIEW_GUIDE: 评审指南 - 评审标准和指导文件
- EXPERT_OPINION: 专家意见 - 专家评审意见文件
- REVIEW_RESULT: 评审结果 - 最终评审结果文件
- APPLICATION: 申报书 - 项目申报书
- BUDGET: 预算书 - 项目预算计划书
- RESUME: 个人简历 - 申报人简历
- ACHIEVEMENT: 成果证明 - 相关成果证明材料
- PLAN: 实施方案 - 项目实施计划
- OTHER: 其他 - 其他相关文件
*/

-- =====================================================
-- 4. 创建评审台查询视图
-- =====================================================

DROP VIEW IF EXISTS `v_project_review_summary`;
CREATE VIEW `v_project_review_summary` AS
SELECT
    prp.id,
    prp.review_code,
    prp.project_name,
    prp.applicant_name,
    prp.submit_time,
    prp.review_deadline,
    prp.review_status,
    prp.review_score,
    prp.priority,
    prp.funding_amount,
    pc.category_name AS project_category,
    pt.type_name AS project_type,
    pd.dept_name AS applicant_dept,
    prp.reviewer_name,
    prp.review_start_time,
    prp.review_end_time,
    CASE
        WHEN prp.review_deadline < NOW() AND prp.review_status IN ('PENDING', 'REVIEWING') THEN '已超期'
        WHEN DATEDIFF(prp.review_deadline, NOW()) <= 3 AND prp.review_status IN ('PENDING', 'REVIEWING') THEN '即将到期'
        ELSE '正常'
    END AS deadline_status,
    (SELECT COUNT(*) FROM t_rchx_project_review_files prf WHERE prf.review_id = prp.id AND prf.is_deleted = 0) AS file_count
FROM t_rchx_project_review_platform prp
LEFT JOIN t_rchx_project_categories pc ON prp.category_id = pc.id
LEFT JOIN t_rchx_project_types pt ON prp.project_type_id = pt.id
LEFT JOIN t_rchx_departments pd ON prp.dept_id = pd.id;

-- =====================================================
-- 5. 创建索引优化
-- =====================================================

-- 复合索引优化常用查询
CREATE INDEX `idx_status_deadline` ON `t_rchx_project_review_platform` (`review_status`, `review_deadline`);
CREATE INDEX `idx_type_category_status` ON `t_rchx_project_review_platform` (`project_type_id`, `category_id`, `review_status`);
CREATE INDEX `idx_applicant_submit_time` ON `t_rchx_project_review_platform` (`applicant_zgh`, `submit_time`);

-- =====================================================
-- 6. 示例数据插入（可选）
-- =====================================================

-- 插入示例评审数据
INSERT INTO `t_rchx_project_review_platform` (
    `review_code`, `project_name`, `project_type_id`, `category_id`, `dept_id`,
    `applicant_zgh`, `applicant_name`, `applicant_phone`, `applicant_email`,
    `submit_time`, `review_deadline`, `review_status`, `priority`,
    `funding_amount`, `project_description`, `expected_outcomes`,
    `implementation_plan`, `budget_plan`, `team_members`,
    `project_start_date`, `project_end_date`, `create_by`
) VALUES
(
    'REV2025010001', '基于AI的智能教学系统研究', 1, 1, 1,
    'T001', '张教授', '13800138001', '<EMAIL>',
    '2025-01-01 10:00:00', '2025-01-15 18:00:00', 'PENDING', 2,
    50.00, '本项目旨在开发基于人工智能技术的智能教学系统，提升教学效果和学习体验。',
    '预期开发出一套完整的智能教学平台，发表高质量学术论文2-3篇。',
    '分三个阶段实施：需求分析、系统设计、开发测试。',
    '设备费30万，人员费15万，其他费用5万。',
    '项目负责人：张教授；核心成员：李副教授、王讲师；研究生：3名',
    '2025-02-01', '2025-12-31', 'T001'
),
(
    'REV2025010002', '新材料在建筑工程中的应用研究', 2, 2, 2,
    'T002', '李工程师', '13800138002', '<EMAIL>',
    '2025-01-02 14:30:00', '2025-01-20 18:00:00', 'REVIEWING', 1,
    30.00, '研究新型复合材料在现代建筑工程中的应用前景和技术可行性。',
    '形成新材料应用技术规范，申请专利1-2项。',
    '实验室测试、工程试点、数据分析、成果总结。',
    '材料费20万，设备费8万，差旅费2万。',
    '项目负责人：李工程师；合作单位：建筑设计院',
    '2025-02-15', '2025-11-30', 'T002'
);

-- =====================================================
-- 7. 执行完成提示
-- =====================================================

SELECT '项目评审台数据库表结构创建完成！' as message;

-- =====================================================
-- 8. 表结构说明
-- =====================================================

/*
数据库表结构说明：

1. 核心表结构：
   - t_rchx_project_review_platform: 项目评审台主表
   - t_rchx_project_review_files: 项目评审文件表

2. 主要功能：
   - 支持完整的项目评审流程管理
   - 集成MinIO文件存储系统
   - 支持文件分类和权限管理
   - 提供丰富的查询视图和统计功能

3. 核心字段覆盖：
   - 评审状态：review_status (待评审、评审中、已完成、已拒绝、撤回)
   - 项目类型：project_type_id (关联项目类型表)
   - 项目名称：project_name
   - 申报人：applicant_name, applicant_zgh
   - 提交时间：submit_time
   - 截止时间：review_deadline

4. 文件存储：
   - 使用MinIO对象存储
   - 支持多种文件类别管理
   - 提供文件MD5校验
   - 支持访问权限控制

5. 索引优化：
   - 针对常用查询场景设计复合索引
   - 支持高效的分页查询
   - 优化评审状态和截止时间查询

6. 扩展功能：
   - 自动生成评审编号
   - 评审状态流程管理
   - 截止时间预警机制
   - 统计报表支持
*/
