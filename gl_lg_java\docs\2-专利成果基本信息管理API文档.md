# 专利成果基本信息管理API文档

## 基础信息
- **模块名称**: 专利成果基本信息管理
- **基础路径**: `/api/zlcgjbxx`
- **权限要求**: 需要JWT Token认证
- **数据格式**: JSON

## 统一响应格式

```json
{
    "code": 200,           // 状态码：200-成功，500-失败，401-未授权，403-权限不足
    "message": "操作成功",  // 响应消息
    "data": {}            // 响应数据（可能为null）
}
```

## 接口列表

### 1. 根据专利成果编号查询

**接口地址**: `GET /api/zlcgjbxx/{zlcgbh}`

**权限要求**: 教师+

**路径参数**:
- `zlcgbh` (string, 必填): 专利成果编号

**请求示例**:
```
GET /api/zlcgjbxx/ZL2025001
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "zlcgbh": "ZL2025001",
        "zlcgmc": "一种新型人工智能算法",
        "dyfmrzgh": "T001",
        "dyfmrxm": "张三",
        "zllx": "发明专利",
        "zlzt": "已授权",
        "shzt": "已审核",
        "sqrq": "2025-01-15",
        "sqh": "202510001234",
        "dwmc": "计算机学院",
        "cjsj": "2025-07-18 10:00:00",
        "czsj": "2025-07-18 10:00:00"
    }
}
```

### 2. 分页查询专利成果信息

**接口地址**: `POST /api/zlcgjbxx/page`

**权限要求**: 教师+

**请求体**:
```json
{
    "zlcgbh": "ZL2025",             // 专利成果编号（可选）
    "zlcgmc": "人工智能",           // 专利成果名称（可选）
    "dyfmrzgh": "T001",             // 第一发明人职工号（可选）
    "dyfmrxm": "张三",              // 第一发明人姓名（可选）
    "zllx": "发明专利",             // 专利类型（可选）
    "zlzt": "已授权",               // 专利状态（可选）
    "shzt": "已审核",               // 审核状态（可选）
    "dwmc": "计算机学院",           // 单位名称（可选）
    "startDate": "2025-01-01",      // 申请日期开始（可选）
    "endDate": "2025-12-31",        // 申请日期结束（可选）
    "pageNum": 1,                   // 页码（默认1）
    "pageSize": 10,                 // 页大小（默认10）
    "orderBy": "sqrq",              // 排序字段（默认sqrq）
    "orderDirection": "desc"        // 排序方向（默认desc）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "records": [
            {
                "zlcgbh": "ZL2025001",
                "zlcgmc": "一种新型人工智能算法",
                "dyfmrzgh": "T001",
                "dyfmrxm": "张三",
                "zllx": "发明专利",
                "zlzt": "已授权",
                "shzt": "已审核",
                "sqrq": "2025-01-15",
                "sqh": "202510001234",
                "dwmc": "计算机学院",
                "cjsj": "2025-07-18 10:00:00",
                "czsj": "2025-07-18 10:00:00"
            }
        ],
        "total": 50,
        "size": 10,
        "current": 1,
        "pages": 5
    }
}
```

### 3. 多条件查询专利成果信息

**接口地址**: `POST /api/zlcgjbxx/list`

**权限要求**: 教师+

**请求体**: 同分页查询，但不返回分页信息

### 4. 根据专利名称模糊查询

**接口地址**: `GET /api/zlcgjbxx/name/{zlcgmc}`

**权限要求**: 学院管理员+

**路径参数**:
- `zlcgmc` (string, 必填): 专利名称关键字

### 5. 根据发明人职工号查询

**接口地址**: `GET /api/zlcgjbxx/inventor/{dyfmrzgh}`

**权限要求**: 教师+

**路径参数**:
- `dyfmrzgh` (string, 必填): 第一发明人职工号

### 6. 根据专利类型查询

**接口地址**: `GET /api/zlcgjbxx/type/{zllx}`

**权限要求**: 学院管理员+

**路径参数**:
- `zllx` (string, 必填): 专利类型

### 7. 根据专利状态查询

**接口地址**: `GET /api/zlcgjbxx/status/{zlzt}`

**权限要求**: 学院管理员+

**路径参数**:
- `zlzt` (string, 必填): 专利状态

### 8. 根据审核状态查询

**接口地址**: `GET /api/zlcgjbxx/audit/{shzt}`

**权限要求**: 评审+

**路径参数**:
- `shzt` (string, 必填): 审核状态

### 9. 新增专利成果信息

**接口地址**: `POST /api/zlcgjbxx`

**权限要求**: 学院管理员+

**请求体**:
```json
{
    "zlcgbh": "ZL2025002",                  // 专利成果编号（必填）
    "zlcgmc": "一种新型数据处理方法",       // 专利成果名称（必填）
    "dyfmrzgh": "T002",                     // 第一发明人职工号（必填）
    "dyfmrxm": "李四",                      // 第一发明人姓名（必填）
    "zllx": "实用新型",                     // 专利类型（必填）
    "zlzt": "申请中",                       // 专利状态（必填）
    "shzt": "待审核",                       // 审核状态（必填）
    "sqrq": "2025-02-15",                   // 申请日期（必填）
    "sqh": "202510002345",                  // 申请号（可选）
    "dwmc": "计算机学院",                   // 单位名称（可选）
    "zlms": "本发明涉及数据处理技术领域"    // 专利描述（可选）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "新增成功",
    "data": "新增成功"
}
```

### 10. 更新专利成果信息

**接口地址**: `PUT /api/zlcgjbxx`

**权限要求**: 学院管理员+（教师可修改自己的专利）

**请求体**: 同新增，但zlcgbh不可修改

### 11. 删除专利成果信息

**接口地址**: `DELETE /api/zlcgjbxx/{zlcgbh}`

**权限要求**: 系统管理员

**路径参数**:
- `zlcgbh` (string, 必填): 专利成果编号

### 12. 批量新增专利成果信息

**接口地址**: `POST /api/zlcgjbxx/batch`

**权限要求**: 学院管理员+

**请求体**:
```json
[
    {
        "zlcgbh": "ZL2025003",
        "zlcgmc": "一种新型机器学习模型",
        "dyfmrzgh": "T003",
        "dyfmrxm": "王五",
        "zllx": "发明专利",
        "zlzt": "申请中",
        "shzt": "待审核",
        "sqrq": "2025-03-15",
        "sqh": "202510003456",
        "dwmc": "计算机学院"
    }
]
```

### 13. 批量更新专利成果信息

**接口地址**: `PUT /api/zlcgjbxx/batch`

**权限要求**: 学院管理员+

### 14. 批量删除专利成果信息

**接口地址**: `DELETE /api/zlcgjbxx/batch`

**权限要求**: 系统管理员

**请求体**:
```json
["ZL2025001", "ZL2025002", "ZL2025003"]
```

## 专利类型说明

- **发明专利**: 对产品、方法或者其改进所提出的新的技术方案
- **实用新型**: 对产品的形状、构造或者其结合所提出的适于实用的新的技术方案
- **外观设计**: 对产品的形状、图案或者其结合以及色彩与形状、图案的结合所作出的富有美感并适于工业应用的新设计

## 专利状态说明

- **申请中**: 专利申请已提交，正在审查过程中
- **已授权**: 专利申请已通过审查，获得专利权
- **已驳回**: 专利申请被驳回
- **已撤回**: 申请人主动撤回专利申请
- **已失效**: 专利权已失效

## 审核状态说明

- **待审核**: 等待内部审核
- **审核中**: 正在进行内部审核
- **已审核**: 内部审核通过
- **审核不通过**: 内部审核未通过

## 权限说明

### 数据权限
- **教师**: 只能查看和修改自己的专利成果
- **评审**: 可以查看专利成果信息，进行专利评审
- **学院管理员**: 可以管理本学院的专利成果
- **系统管理员**: 可以管理所有专利成果

### 操作权限
- **查询**: 教师+
- **新增**: 学院管理员+
- **修改**: 学院管理员+（教师可修改自己的）
- **删除**: 系统管理员

## 错误码说明

- **200**: 操作成功
- **401**: 未授权（Token无效或过期）
- **403**: 权限不足
- **500**: 服务器内部错误

## 注意事项

1. 所有接口都需要在请求头中携带JWT Token
2. 教师权限的用户只能操作自己的专利成果
3. 专利成果编号必须唯一
4. 批量操作支持事务，失败时会回滚
5. 分页查询默认按申请日期倒序排列
6. 日期格式统一使用 yyyy-MM-dd
7. 专利状态和审核状态有固定的枚举值
