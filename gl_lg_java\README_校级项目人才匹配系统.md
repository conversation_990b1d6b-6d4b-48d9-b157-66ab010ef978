# 校级项目人才匹配系统

## 📋 系统概述

校级项目人才匹配系统是基于Spring Boot开发的智能人才匹配分析系统，支持六大校级项目的人才匹配分析：

1. **创新团队支持计划项目** (CXTD)
2. **服务管理人才支持计划项目** (FWGL)  
3. **领军人才引育计划项目** (LJRC)
4. **屏风学者引育计划项目-Ⅰ类** (PFXZ1)
5. **屏风学者引育计划项目-Ⅱ类** (PFXZ2)
6. **屏风学者引育计划项目-Ⅲ类** (PFXZ3)
7. **青年人才未来工程项目** (QNRC)
8. **社会服务人才培育计划项目** (SHFW)

## 🚀 快速开始

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 访问API文档
```
http://localhost:8080/swagger-ui.html
```

### 3. 基本API调用示例

#### 获取所有支持的项目
```bash
curl -X GET "http://localhost:8080/api/school-project-matching/projects"
```

#### 执行青年人才项目匹配分析
```bash
curl -X POST "http://localhost:8080/api/school-project-matching/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "projectCode": "QNRC",
    "analysisParams": {
      "timeRange": "5",
      "minMatchScore": 60,
      "maxResults": 50,
      "includeDetails": true
    }
  }'
```

#### 快速分析
```bash
curl -X GET "http://localhost:8080/api/school-project-matching/quick-analyze?projectCode=QNRC&minScore=60&maxResults=20"
```

## 📊 API接口说明

### 1. 获取项目列表
- **URL**: `GET /api/school-project-matching/projects`
- **描述**: 获取所有支持的校级项目列表
- **返回**: 项目代码、名称、分类等信息

### 2. 执行匹配分析
- **URL**: `POST /api/school-project-matching/analyze`
- **描述**: 对指定项目执行全员匹配分析
- **参数**:
  - `projectCode`: 项目代码
  - `analysisParams`: 分析参数
    - `timeRange`: 时间范围（年）
    - `minMatchScore`: 最低匹配分数
    - `maxResults`: 最大结果数量
    - `includeDetails`: 是否包含详细信息

### 3. 单个教师分析
- **URL**: `POST /api/school-project-matching/analyze/teacher/{teacherZgh}`
- **描述**: 分析单个教师对指定项目的匹配情况
- **参数**: 
  - `teacherZgh`: 教师职工号
  - `projectCode`: 项目代码（查询参数）

### 4. 教师全项目分析
- **URL**: `POST /api/school-project-matching/analyze/teacher/{teacherZgh}/all-projects`
- **描述**: 分析教师对所有项目的匹配情况

### 5. 快速分析
- **URL**: `GET /api/school-project-matching/quick-analyze`
- **描述**: 简化的快速分析接口
- **参数**: 
  - `projectCode`: 项目代码
  - `minScore`: 最低分数（默认60）
  - `maxResults`: 最大结果数（默认50）

## 🔧 匹配算法说明

### 青年人才未来工程项目 (QNRC)
- **年龄限制**: 自然科学≤36岁，社会科学≤38岁
- **成果要求**: 满足情形1或情形2
- **评分权重**: 发展潜力60% + 创新能力40%

### 领军人才引育计划项目 (LJRC)  
- **年龄限制**: ≤55岁
- **人才层次**: 自治区C类及以上
- **评分权重**: 学术影响60% + 创新能力40%

### 屏风学者项目
- **Ⅰ类**: 广西D层次标准
- **Ⅱ类**: 广西E层次标准  
- **Ⅲ类**: 资格类或成果类条件（7项中任选3项）

### 社会服务人才培育计划 (SHFW)
- **年龄限制**: 原则上≤50岁，特殊情况≤55岁
- **经费要求**: 近五年技术开发项目累计≥100万元
- **评分权重**: 学术25% + 服务35% + 影响20% + 经费20%

## 📈 返回结果说明

### AnalysisResult（分析结果）
```json
{
  "analysisId": "分析ID",
  "projectCode": "项目代码", 
  "projectName": "项目名称",
  "status": "COMPLETED",
  "summary": {
    "totalTeachers": 100,
    "analyzedTeachers": 100,
    "qualifiedTeachers": 25,
    "averageScore": 72.5,
    "highMatchCount": 8,
    "mediumMatchCount": 12,
    "lowMatchCount": 5
  },
  "results": [...]
}
```

### MatchResult（匹配结果）
```json
{
  "teacherZgh": "教师职工号",
  "teacherName": "教师姓名",
  "department": "所在部门",
  "title": "职称",
  "age": 35,
  "matchScore": 85.5,
  "matchLevel": "HIGH",
  "isQualified": true,
  "strengths": ["优势条件列表"],
  "weaknesses": ["不足之处列表"], 
  "recommendations": ["改进建议列表"]
}
```

## 🛠️ 开发说明

### 核心组件
- **ProjectMatcher**: 项目匹配器接口
- **ProjectMatcherFactory**: 匹配器工厂
- **SchoolProjectMatchingService**: 核心业务服务
- **CommonConditionValidator**: 通用条件验证工具

### 扩展新项目
1. 实现`ProjectMatcher`接口
2. 在`ProjectMatcherFactory`中注册
3. 添加相应的业务逻辑

### 数据依赖
- 教师基本信息表 (`t_rchx_jzgjbxx`)
- 获奖成果表 (`t_rchx_hjcgjbxx`)
- 科技论文表 (`t_rchx_kjlwjbxx`)
- 科技项目表 (`t_rchx_kjxmjbxx`)
- 专利成果表 (`t_rchx_zlcgjbxx`)

## 📝 注意事项

1. **数据完整性**: 确保基础数据表中有足够的测试数据
2. **性能优化**: 大量数据分析时建议使用异步处理
3. **参数调优**: 根据实际需求调整匹配分数阈值
4. **扩展性**: 系统支持新增项目类型和匹配规则

## 🔍 故障排除

### 常见问题
1. **教师不存在**: 检查职工号是否正确
2. **项目代码无效**: 确认项目代码在支持列表中
3. **分析结果为空**: 检查分数阈值设置是否过高

### 日志查看
```bash
tail -f logs/application.log | grep "SchoolProjectMatching"
```

## 📞 技术支持

如有问题请联系开发团队或查看详细的技术方案文档。
