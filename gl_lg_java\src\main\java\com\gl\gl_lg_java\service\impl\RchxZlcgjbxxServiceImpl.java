package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxZlcgjbxx;
import com.gl.gl_lg_java.dto.ZlcgjbxxQueryDTO;
import com.gl.gl_lg_java.dto.ZlcgStatisticsDTO;
import com.gl.gl_lg_java.mapper.RchxZlcgjbxxMapper;
import com.gl.gl_lg_java.service.RchxZlcgjbxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_zlcgjbxx(专利成果基本信息)】的数据库操作Service实现
 * @createDate 2025-07-18 11:11:41
 */
@Service
@Slf4j
public class RchxZlcgjbxxServiceImpl extends ServiceImpl<RchxZlcgjbxxMapper, RchxZlcgjbxx>
        implements RchxZlcgjbxxService {

    @Autowired
    private RchxZlcgjbxxMapper zlcgjbxxMapper;

    @Override
    public RchxZlcgjbxx getByZlcgbh(String zlcgbh) {
        return zlcgjbxxMapper.findByZlcgbh(zlcgbh);
    }

    @Override
    public List<RchxZlcgjbxx> listByZlcgmcLike(String zlcgmc) {
        return zlcgjbxxMapper.findByZlcgmcLike(zlcgmc);
    }

    @Override
    public List<RchxZlcgjbxx> listByDwh(String dwh) {
        return zlcgjbxxMapper.findByDwh(dwh);
    }

    @Override
    public List<RchxZlcgjbxx> listByDwmcLike(String dwmc) {
        return zlcgjbxxMapper.findByDwmcLike(dwmc);
    }

    @Override
    public List<RchxZlcgjbxx> listByZllx(String zllx) {
        return zlcgjbxxMapper.findByZllx(zllx);
    }

    @Override
    public List<RchxZlcgjbxx> listByZlzt(String zlzt) {
        return zlcgjbxxMapper.findByZlzt(zlzt);
    }

    @Override
    public List<RchxZlcgjbxx> listByShzt(String shzt) {
        return zlcgjbxxMapper.findByShzt(shzt);
    }

    @Override
    public List<RchxZlcgjbxx> listByDyfmrzgh(String dyfmrzgh) {
        return zlcgjbxxMapper.findByDyfmrzgh(dyfmrzgh);
    }

    @Override
    public List<RchxZlcgjbxx> listByDyfmrLike(String dyfmr) {
        return zlcgjbxxMapper.findByDyfmrLike(dyfmr);
    }

    @Override
    public List<RchxZlcgjbxx> listByPzrqBetween(String startDate, String endDate) {
        return zlcgjbxxMapper.findByPzrqBetween(startDate, endDate);
    }

    @Override
    public List<RchxZlcgjbxx> listBySqzlrqBetween(String startDate, String endDate) {
        return zlcgjbxxMapper.findBySqzlrqBetween(startDate, endDate);
    }

    @Override
    public List<RchxZlcgjbxx> listByMultiConditions(ZlcgjbxxQueryDTO queryDTO) {
        // 使用QueryWrapper替代复杂动态SQL
        QueryWrapper<RchxZlcgjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getZlcgbh())) {
            queryWrapper.like("zlcgbh", queryDTO.getZlcgbh());
        }
        if (StringUtils.hasText(queryDTO.getZlcgmc())) {
            queryWrapper.like("zlcgmc", queryDTO.getZlcgmc());
        }
        if (StringUtils.hasText(queryDTO.getDwmc())) {
            queryWrapper.like("dwmc", queryDTO.getDwmc());
        }
        if (StringUtils.hasText(queryDTO.getZllx())) {
            queryWrapper.eq("zllx", queryDTO.getZllx());
        }
        if (StringUtils.hasText(queryDTO.getZlzt())) {
            queryWrapper.eq("zlzt", queryDTO.getZlzt());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getDyfmr())) {
            queryWrapper.like("dyfmr", queryDTO.getDyfmr());
        }
        if (StringUtils.hasText(queryDTO.getDyfmrzgh())) {
            queryWrapper.eq("dyfmrzgh", queryDTO.getDyfmrzgh());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("pzrq", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("pzrq", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("cjsj");

        return zlcgjbxxMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<RchxZlcgjbxx> pageByMultiConditions(ZlcgjbxxQueryDTO queryDTO) {
        // 使用MyBatis-Plus的分页功能替代复杂动态SQL
        Page<RchxZlcgjbxx> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<RchxZlcgjbxx> queryWrapper = new QueryWrapper<>();

        // 动态添加查询条件
        if (StringUtils.hasText(queryDTO.getZlcgbh())) {
            queryWrapper.like("zlcgbh", queryDTO.getZlcgbh());
        }
        if (StringUtils.hasText(queryDTO.getZlcgmc())) {
            queryWrapper.like("zlcgmc", queryDTO.getZlcgmc());
        }
        if (StringUtils.hasText(queryDTO.getDwmc())) {
            queryWrapper.like("dwmc", queryDTO.getDwmc());
        }
        if (StringUtils.hasText(queryDTO.getZllx())) {
            queryWrapper.eq("zllx", queryDTO.getZllx());
        }
        if (StringUtils.hasText(queryDTO.getZlzt())) {
            queryWrapper.eq("zlzt", queryDTO.getZlzt());
        }
        if (StringUtils.hasText(queryDTO.getShzt())) {
            queryWrapper.eq("shzt", queryDTO.getShzt());
        }
        if (StringUtils.hasText(queryDTO.getDyfmr())) {
            queryWrapper.like("dyfmr", queryDTO.getDyfmr());
        }
        if (StringUtils.hasText(queryDTO.getDyfmrzgh())) {
            queryWrapper.eq("dyfmrzgh", queryDTO.getDyfmrzgh());
        }
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            queryWrapper.ge("pzrq", queryDTO.getStartDate());
        }
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            queryWrapper.le("pzrq", queryDTO.getEndDate());
        }

        // 排序
        queryWrapper.orderByDesc("cjsj");

        return zlcgjbxxMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveZlcgjbxx(RchxZlcgjbxx zlcgjbxx) {
        try {
            // 设置创建时间
            if (zlcgjbxx.getCjsj() == null) {
                zlcgjbxx.setCjsj(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            return save(zlcgjbxx);
        } catch (Exception e) {
            log.error("新增专利成果信息失败: {}", e.getMessage());
            throw new RuntimeException("新增专利成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByZlcgbh(RchxZlcgjbxx zlcgjbxx) {
        try {
            // 设置操作时间
            zlcgjbxx.setCzsj(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            return updateById(zlcgjbxx);
        } catch (Exception e) {
            log.error("更新专利成果信息失败: {}", e.getMessage());
            throw new RuntimeException("更新专利成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByZlcgbh(String zlcgbh) {
        try {
            return removeById(zlcgbh);
        } catch (Exception e) {
            log.error("删除专利成果信息失败: {}", e.getMessage());
            throw new RuntimeException("删除专利成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByZlcgbhs(List<String> zlcgbhs) {
        try {
            return removeByIds(zlcgbhs);
        } catch (Exception e) {
            log.error("批量删除专利成果信息失败: {}", e.getMessage());
            throw new RuntimeException("批量删除专利成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchZlcgjbxx(List<RchxZlcgjbxx> zlcgjbxxList) {
        try {
            // 设置创建时间
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            zlcgjbxxList.forEach(item -> {
                if (item.getCjsj() == null) {
                    item.setCjsj(currentTime);
                }
            });
            return saveBatch(zlcgjbxxList);
        } catch (Exception e) {
            log.error("批量新增专利成果信息失败: {}", e.getMessage());
            throw new RuntimeException("批量新增专利成果信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchZlcgjbxx(List<RchxZlcgjbxx> zlcgjbxxList) {
        try {
            // 设置操作时间
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            zlcgjbxxList.forEach(item -> item.setCzsj(currentTime));
            return updateBatchById(zlcgjbxxList);
        } catch (Exception e) {
            log.error("批量更新专利成果信息失败: {}", e.getMessage());
            throw new RuntimeException("批量更新专利成果信息失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByZllx() {
        try {
            log.info("查询专利类型统计（使用数据库聚合查询）");
            Map<String, Integer> zllxCounts = convertToIntegerMap(zlcgjbxxMapper.getStatsByZllx());
            log.info("专利类型统计结果: {}", zllxCounts);
            return zllxCounts;
        } catch (Exception e) {
            log.error("查询专利类型统计失败: {}", e.getMessage());
            throw new RuntimeException("查询专利类型统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByZlzt() {
        try {
            log.info("查询专利状态统计（使用数据库聚合查询）");
            Map<String, Integer> zlztCounts = convertToIntegerMap(zlcgjbxxMapper.getStatsByZlzt());
            log.info("专利状态统计结果: {}", zlztCounts);
            return zlztCounts;
        } catch (Exception e) {
            log.error("查询专利状态统计失败: {}", e.getMessage());
            throw new RuntimeException("查询专利状态统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getStatsByShzt() {
        try {
            log.info("查询审核状态统计（使用数据库聚合查询）");
            Map<String, Integer> shztCounts = convertToIntegerMap(zlcgjbxxMapper.getStatsByShzt());
            log.info("审核状态统计结果: {}", shztCounts);
            return shztCounts;
        } catch (Exception e) {
            log.error("查询审核状态统计失败: {}", e.getMessage());
            throw new RuntimeException("查询审核状态统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getZllxOptions() {
        try {
            log.info("快速获取专利类型选项列表（不统计数量）");
            List<String> zllxList = zlcgjbxxMapper.findDistinctZllx();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String zllx : zllxList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", zllx);
                option.put("label", zllx);
                options.add(option);
            }

            log.info("专利类型选项列表快速获取成功，共{}个类型", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取专利类型选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取专利类型选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getZlztOptions() {
        try {
            log.info("快速获取专利状态选项列表（不统计数量）");
            List<String> zlztList = zlcgjbxxMapper.findDistinctZlzt();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String zlzt : zlztList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", zlzt);
                option.put("label", zlzt);
                options.add(option);
            }

            log.info("专利状态选项列表快速获取成功，共{}个状态", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取专利状态选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取专利状态选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getShztOptions() {
        try {
            log.info("快速获取审核状态选项列表（不统计数量）");
            List<String> shztList = zlcgjbxxMapper.findDistinctShzt();

            // 转换为前端需要的格式
            List<Map<String, Object>> options = new ArrayList<>();
            for (String shzt : shztList) {
                Map<String, Object> option = new HashMap<>();
                option.put("value", shzt);
                option.put("label", shzt);
                options.add(option);
            }

            log.info("审核状态选项列表快速获取成功，共{}个状态", options.size());
            return options;
        } catch (Exception e) {
            log.error("快速获取审核状态选项列表失败: {}", e.getMessage());
            throw new RuntimeException("快速获取审核状态选项列表失败: " + e.getMessage());
        }
    }

    @Override
    public Long getPersonalCount(String zgh) {
        try {
            log.info("查询教师个人专利成果数量，职工号: {}", zgh);
            Long count = zlcgjbxxMapper.getPersonalCountByZgh(zgh);
            log.info("教师个人专利成果数量查询结果: {}", count);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("查询教师个人专利成果数量失败: {}", e.getMessage());
            throw new RuntimeException("查询教师个人专利成果数量失败: " + e.getMessage());
        }
    }

    /**
     * 转换统计结果为Integer类型的Map
     */
    private Map<String, Integer> convertToIntegerMap(List<Map<String, Object>> statsList) {
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> stats : statsList) {
            String fieldValue = (String) stats.get("fieldValue");
            Object countObj = stats.get("count");
            Integer count = 0;
            if (countObj instanceof Number) {
                count = ((Number) countObj).intValue();
            }
            result.put(fieldValue, count);
        }
        return result;
    }

    @Override
    public ZlcgStatisticsDTO.ComprehensiveStatistics getComprehensiveStatistics() {
        try {
            log.info("开始获取专利成果综合统计数据");

            ZlcgStatisticsDTO.ComprehensiveStatistics statistics = new ZlcgStatisticsDTO.ComprehensiveStatistics();

            // 获取单位统计
            List<ZlcgStatisticsDTO.UnitStatistics> unitStats = zlcgjbxxMapper.getUnitStatistics();
            statistics.setUnitStatistics(unitStats);
            log.debug("单位统计数据获取完成，共{}个单位", unitStats.size());

            // 获取专利类型统计
            List<ZlcgStatisticsDTO.PatentTypeStatistics> patentTypeStats = zlcgjbxxMapper.getPatentTypeStatistics();
            statistics.setPatentTypeStatistics(patentTypeStats);
            log.debug("专利类型统计数据获取完成，共{}个类型", patentTypeStats.size());

            // 获取第一发明人TOP10
            List<ZlcgStatisticsDTO.FirstInventorStatistics> topFirstInventors = zlcgjbxxMapper.getTopFirstInventors();
            statistics.setTopFirstInventors(topFirstInventors);
            log.debug("第一发明人TOP10数据获取完成，共{}个发明人", topFirstInventors.size());

            // 获取总体统计
            Map<String, Object> overallStats = zlcgjbxxMapper.getOverallStatistics();
            if (overallStats != null) {
                statistics.setTotalPatents(((Number) overallStats.get("totalPatents")).longValue());
                statistics.setTotalUnits(((Number) overallStats.get("totalUnits")).longValue());
                statistics.setTotalPatentTypes(((Number) overallStats.get("totalPatentTypes")).longValue());
            }

            log.info("专利成果综合统计数据获取完成: 专利总数={}, 单位总数={}, 专利类型总数={}",
                    statistics.getTotalPatents(), statistics.getTotalUnits(), statistics.getTotalPatentTypes());

            return statistics;

        } catch (Exception e) {
            log.error("获取专利成果综合统计数据失败", e);
            throw new RuntimeException("获取统计数据失败: " + e.getMessage());
        }
    }

    // ========== 人才匹配算法专用方法实现 ==========

    @Override
    public boolean hasInventionPatents(String teacherZgh, int minCount) {
        try {
            QueryWrapper<RchxZlcgjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dyfmrzgh", teacherZgh)
                    .like("zllx", "发明专利");

            long count = count(queryWrapper);
            return count >= minCount;
        } catch (Exception e) {
            log.error("检查教师{}发明专利失败: {}", teacherZgh, e.getMessage());
            return false;
        }
    }

    @Override
    public int countByTeacher(String teacherZgh) {
        try {
            QueryWrapper<RchxZlcgjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dyfmrzgh", teacherZgh);

            return Math.toIntExact(count(queryWrapper));
        } catch (Exception e) {
            log.error("统计教师{}专利总数失败: {}", teacherZgh, e.getMessage());
            return 0;
        }
    }

    @Override
    public int countInventionPatentsByTeacher(String teacherZgh) {
        try {
            QueryWrapper<RchxZlcgjbxx> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dyfmrzgh", teacherZgh)
                    .like("zllx", "发明专利");

            return Math.toIntExact(count(queryWrapper));
        } catch (Exception e) {
            log.error("统计教师{}发明专利数量失败: {}", teacherZgh, e.getMessage());
            return 0;
        }
    }
}
