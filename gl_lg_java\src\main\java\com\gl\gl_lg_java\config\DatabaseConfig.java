package com.gl.gl_lg_java.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 数据库健康检查配置
 */
@Component
@Slf4j
public class DatabaseConfig implements HealthIndicator {
    
    @Autowired
    private DataSource dataSource;
    
    @Override
    public Health health() {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(1)) {
                return Health.up()
                        .withDetail("database", "MySQL")
                        .withDetail("status", "连接正常")
                        .build();
            } else {
                return Health.down()
                        .withDetail("database", "MySQL")
                        .withDetail("status", "连接无效")
                        .build();
            }
        } catch (SQLException e) {
            log.error("数据库连接检查失败: {}", e.getMessage());
            return Health.down()
                    .withDetail("database", "MySQL")
                    .withDetail("status", "连接失败")
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
}
