package com.gl.gl_lg_java.dto;

import lombok.Data;

/**
 * 科技项目基本信息查询DTO
 */
@Data
public class KjxmjbxxQueryDTO {
    
    /**
     * 项目主键
     */
    private String xmid;
    
    /**
     * 项目编号
     */
    private String xmbh;
    
    /**
     * 项目名称
     */
    private String xmmc;
    
    /**
     * 校内编号
     */
    private String xnbh;
    
    /**
     * 单位号
     */
    private String dwh;
    
    /**
     * 单位名称
     */
    private String dwmc;
    
    /**
     * 项目负责人号
     */
    private String xmfzrh;
    
    /**
     * 负责人姓名
     */
    private String fzrxm;
    
    /**
     * 项目来源
     */
    private String xmly;
    
    /**
     * 项目性质
     */
    private String xmxz;
    
    /**
     * 项目类别
     */
    private String xmlb;
    
    /**
     * 项目级别
     */
    private String xmjb;
    
    /**
     * 项目执行状态
     */
    private String xmzxzt;
    
    /**
     * 审核状态
     */
    private String shzt;
    
    /**
     * 立项开始日期
     */
    private String lxStartDate;
    
    /**
     * 立项结束日期
     */
    private String lxEndDate;
    
    /**
     * 开始日期范围-开始
     */
    private String ksStartDate;
    
    /**
     * 开始日期范围-结束
     */
    private String ksEndDate;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String orderBy = "lxrq";
    
    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "desc";
}
