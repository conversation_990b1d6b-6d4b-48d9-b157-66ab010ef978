package com.gl.gl_lg_java.util;

import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;

/**
 * 通用基本条件验证工具类
 */
@Component
@Slf4j
public class CommonConditionValidator {

    /**
     * 验证政治素养
     * 
     * @param teacher 教师信息
     * @return 是否符合政治素养要求
     */
    public boolean checkPoliticalQuality(RchxJzgjbxx teacher) {
        // 基于现有数据进行基本判断
        // 实际应用中可能需要更详细的政治素养评估数据

        // 检查是否有违法违纪记录（这里简化处理）
        if (hasViolationRecord(teacher)) {
            log.warn("教师{}存在违法违纪记录", teacher.getZgh());
            return false;
        }

        // 检查基本信息完整性
        if (!StringUtils.hasText(teacher.getXm()) || !StringUtils.hasText(teacher.getZgh())) {
            log.warn("教师{}基本信息不完整", teacher.getZgh());
            return false;
        }

        // 默认通过政治素养检查（实际应用中需要人工确认）
        return true;
    }

    /**
     * 验证学术道德和职业道德
     * 
     * @param teacher 教师信息
     * @return 是否符合学术道德要求
     */
    public boolean checkAcademicAndProfessionalEthics(RchxJzgjbxx teacher) {
        // 检查是否有学术不端记录
        if (hasAcademicMisconductRecord(teacher)) {
            log.warn("教师{}存在学术不端记录", teacher.getZgh());
            return false;
        }

        // 检查是否有师德失范记录
        if (hasProfessionalMisconductRecord(teacher)) {
            log.warn("教师{}存在师德失范记录", teacher.getZgh());
            return false;
        }

        return true;
    }

    /**
     * 计算教师当前年龄
     * 
     * @param teacher 教师信息
     * @return 年龄，如果出生日期无效则返回null
     */
    public Integer calculateAge(RchxJzgjbxx teacher) {
        if (!StringUtils.hasText(teacher.getCsrq())) {
            return null;
        }

        try {
            LocalDate birthDate = LocalDate.parse(teacher.getCsrq(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return Period.between(birthDate, LocalDate.now()).getYears();
        } catch (Exception e) {
            log.warn("教师{}出生日期格式错误: {}", teacher.getZgh(), teacher.getCsrq());
            return null;
        }
    }

    /**
     * 计算距离法定退休年龄的年数
     * 
     * @param teacher 教师信息
     * @return 距离退休年数，如果无法计算则返回0
     */
    public int calculateYearsToRetirement(RchxJzgjbxx teacher) {
        Integer currentAge = calculateAge(teacher);
        if (currentAge == null) {
            return 0;
        }

        // 根据性别确定退休年龄（简化处理）
        int retirementAge = "女".equals(teacher.getXb()) ? 55 : 60;

        return Math.max(0, retirementAge - currentAge);
    }

    /**
     * 检查年龄是否符合要求
     * 
     * @param teacher 教师信息
     * @param maxAge  最大年龄限制
     * @return 是否符合年龄要求
     */
    public boolean checkAgeRequirement(RchxJzgjbxx teacher, int maxAge) {
        Integer age = calculateAge(teacher);
        if (age == null) {
            log.warn("教师{}年龄信息缺失", teacher.getZgh());
            return false;
        }

        return age <= maxAge;
    }

    /**
     * 检查是否为全职在岗人员
     * 
     * @param teacher 教师信息
     * @return 是否为全职在岗
     */
    public boolean isFullTimeEmployee(RchxJzgjbxx teacher) {
        // 基于现有字段判断（可能需要根据实际情况调整）
        return StringUtils.hasText(teacher.getBm()) && // 有部门信息
                StringUtils.hasText(teacher.getZc()) && // 有职称信息
                !"离职".equals(teacher.getDqzt()) && // 不是离职状态
                !"退休".equals(teacher.getDqzt()); // 不是退休状态
    }

    /**
     * 获取学科类型（自然科学/社会科学）
     * 
     * @param teacher 教师信息
     * @return 学科类型
     */
    public String getDisciplineType(RchxJzgjbxx teacher) {
        // 基于部门或专业判断学科类型（简化处理）
        String department = teacher.getBm();
        if (!StringUtils.hasText(department)) {
            return "自然科学"; // 默认为自然科学
        }

        // 社会科学相关关键词
        String[] socialScienceKeywords = {
                "文学", "历史", "哲学", "法学", "经济", "管理", "教育", "艺术",
                "外语", "语言", "政治", "社会", "心理", "新闻", "传播"
        };

        for (String keyword : socialScienceKeywords) {
            if (department.contains(keyword)) {
                return "社会科学";
            }
        }

        return "自然科学";
    }

    /**
     * 检查是否有违法违纪记录（简化实现）
     */
    private boolean hasViolationRecord(RchxJzgjbxx teacher) {
        // 实际应用中应该查询相关的违法违纪记录表
        // 这里简化处理，默认无记录
        return false;
    }

    /**
     * 检查是否有学术不端记录（简化实现）
     */
    private boolean hasAcademicMisconductRecord(RchxJzgjbxx teacher) {
        // 实际应用中应该查询学术不端记录表
        // 这里简化处理，默认无记录
        return false;
    }

    /**
     * 检查是否有师德失范记录（简化实现）
     */
    private boolean hasProfessionalMisconductRecord(RchxJzgjbxx teacher) {
        // 实际应用中应该查询师德考核记录表
        // 这里简化处理，默认无记录
        return false;
    }
}
