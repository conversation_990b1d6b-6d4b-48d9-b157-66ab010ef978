package com.gl.gl_lg_java.dto;

import lombok.Data;

/**
 * 教职工工作简历信息查询DTO
 */
@Data
public class JzggzjlxxQueryDTO {
    
    /**
     * 职工号
     */
    private String zgh;
    
    /**
     * 工作单位
     */
    private String gzdw;
    
    /**
     * 工作内容
     */
    private String gznr;
    
    /**
     * 曾任职务
     */
    private String crzw;
    
    /**
     * 从事专业
     */
    private String cszy;
    
    /**
     * 工作证明人
     */
    private String gzzmr;
    
    /**
     * 工作所在国家或地区
     */
    private String gzszd;
    
    /**
     * 工作起始日期开始
     */
    private String startDate;
    
    /**
     * 工作起始日期结束
     */
    private String endDate;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String orderBy = "gzqsrq";
    
    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "desc";
}
