package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.domain.RchxProjectAssessments;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 项目考核管理Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Mapper
public interface RchxProjectAssessmentsMapper extends BaseMapper<RchxProjectAssessments> {

    /**
     * 分页查询项目考核信息
     */
    @Select({
        "<script>",
        "SELECT * FROM t_rchx_project_assessments ",
        "WHERE 1=1 ",
        "<if test='projectCode != null and projectCode != \"\"'>",
        "AND project_code LIKE CONCAT('%', #{projectCode}, '%') ",
        "</if>",
        "<if test='projectName != null and projectName != \"\"'>",
        "AND project_name LIKE CONCAT('%', #{projectName}, '%') ",
        "</if>",
        "<if test='assessmentType != null and assessmentType != \"\"'>",
        "AND assessment_type = #{assessmentType} ",
        "</if>",
        "<if test='assessmentStatus != null and assessmentStatus != \"\"'>",
        "AND assessment_status = #{assessmentStatus} ",
        "</if>",
        "<if test='projectLeaderZgh != null and projectLeaderZgh != \"\"'>",
        "AND project_leader_zgh = #{projectLeaderZgh} ",
        "</if>",
        "<if test='assessorZgh != null and assessorZgh != \"\"'>",
        "AND assessor_zgh = #{assessorZgh} ",
        "</if>",
        "<if test='assessmentResult != null and assessmentResult != \"\"'>",
        "AND assessment_result = #{assessmentResult} ",
        "</if>",
        "ORDER BY assessment_date DESC",
        "</script>"
    })
    IPage<RchxProjectAssessments> selectPageWithConditions(
            Page<RchxProjectAssessments> page,
            @Param("projectCode") String projectCode,
            @Param("projectName") String projectName,
            @Param("assessmentType") String assessmentType,
            @Param("assessmentStatus") String assessmentStatus,
            @Param("projectLeaderZgh") String projectLeaderZgh,
            @Param("assessorZgh") String assessorZgh,
            @Param("assessmentResult") String assessmentResult
    );

    /**
     * 根据项目ID查询考核列表
     */
    @Select("SELECT * FROM t_rchx_project_assessments " +
            "WHERE project_id = #{projectId} " +
            "ORDER BY assessment_date DESC")
    List<RchxProjectAssessments> selectByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目编号查询考核列表
     */
    @Select("SELECT * FROM t_rchx_project_assessments " +
            "WHERE project_code = #{projectCode} " +
            "ORDER BY assessment_date DESC")
    List<RchxProjectAssessments> selectByProjectCode(@Param("projectCode") String projectCode);

    /**
     * 根据考核类型查询考核列表
     */
    @Select("SELECT * FROM t_rchx_project_assessments " +
            "WHERE assessment_type = #{assessmentType} " +
            "ORDER BY assessment_date DESC")
    List<RchxProjectAssessments> selectByAssessmentType(@Param("assessmentType") String assessmentType);

    /**
     * 根据考核状态查询考核列表
     */
    @Select("SELECT * FROM t_rchx_project_assessments " +
            "WHERE assessment_status = #{assessmentStatus} " +
            "ORDER BY assessment_date DESC")
    List<RchxProjectAssessments> selectByAssessmentStatus(@Param("assessmentStatus") String assessmentStatus);

    /**
     * 根据项目负责人查询考核列表
     */
    @Select("SELECT * FROM t_rchx_project_assessments " +
            "WHERE project_leader_zgh = #{projectLeaderZgh} " +
            "ORDER BY assessment_date DESC")
    List<RchxProjectAssessments> selectByProjectLeader(@Param("projectLeaderZgh") String projectLeaderZgh);

    /**
     * 更新考核得分和结果
     */
    @Update("UPDATE t_rchx_project_assessments SET " +
            "assessment_score = #{assessmentScore}, " +
            "assessment_result = #{assessmentResult}, " +
            "assessment_comments = #{assessmentComments}, " +
            "assessment_status = 'COMPLETED', " +
            "assessment_end_time = #{assessmentEndTime}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int updateAssessmentScore(@Param("id") Long id,
                             @Param("assessmentScore") BigDecimal assessmentScore,
                             @Param("assessmentResult") String assessmentResult,
                             @Param("assessmentComments") String assessmentComments,
                             @Param("assessmentEndTime") LocalDateTime assessmentEndTime,
                             @Param("updateBy") String updateBy);

    /**
     * 更新考核状态
     */
    @Update("UPDATE t_rchx_project_assessments SET " +
            "assessment_status = #{assessmentStatus}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int updateAssessmentStatus(@Param("id") Long id,
                              @Param("assessmentStatus") String assessmentStatus,
                              @Param("updateBy") String updateBy);

    /**
     * 更新提醒状态
     */
    @Update("UPDATE t_rchx_project_assessments SET " +
            "reminder_sent = #{reminderSent}, " +
            "last_reminder_time = #{lastReminderTime} " +
            "WHERE id = #{id}")
    int updateReminderStatus(@Param("id") Long id,
                            @Param("reminderSent") Boolean reminderSent,
                            @Param("lastReminderTime") LocalDateTime lastReminderTime);

    /**
     * 获取考核统计信息
     */
    @Select({
        "SELECT ",
        "COUNT(*) AS totalAssessments, ",
        "SUM(CASE WHEN assessment_status = 'PENDING' THEN 1 ELSE 0 END) AS pendingAssessments, ",
        "SUM(CASE WHEN assessment_status = 'ONGOING' THEN 1 ELSE 0 END) AS ongoingAssessments, ",
        "SUM(CASE WHEN assessment_status = 'COMPLETED' THEN 1 ELSE 0 END) AS completedAssessments, ",
        "SUM(CASE WHEN assessment_status = 'OVERDUE' THEN 1 ELSE 0 END) AS overdueAssessments, ",
        "AVG(CASE WHEN assessment_score IS NOT NULL THEN assessment_score ELSE NULL END) AS avgScore, ",
        "SUM(CASE WHEN assessment_result = 'EXCELLENT' THEN 1 ELSE 0 END) AS excellentCount, ",
        "SUM(CASE WHEN assessment_result = 'GOOD' THEN 1 ELSE 0 END) AS goodCount, ",
        "SUM(CASE WHEN assessment_result = 'PASS' THEN 1 ELSE 0 END) AS passCount, ",
        "SUM(CASE WHEN assessment_result = 'FAIL' THEN 1 ELSE 0 END) AS failCount ",
        "FROM t_rchx_project_assessments"
    })
    Map<String, Object> getAssessmentStatistics();

    /**
     * 获取考核类型统计
     */
    @Select({
        "SELECT ",
        "assessment_type AS assessmentType, ",
        "COUNT(*) AS count, ",
        "AVG(CASE WHEN assessment_score IS NOT NULL THEN assessment_score ELSE NULL END) AS avgScore ",
        "FROM t_rchx_project_assessments ",
        "GROUP BY assessment_type ",
        "ORDER BY count DESC"
    })
    List<Map<String, Object>> getAssessmentTypeStatistics();

    /**
     * 获取项目负责人考核统计
     */
    @Select({
        "SELECT ",
        "project_leader_name AS projectLeaderName, ",
        "project_leader_zgh AS projectLeaderZgh, ",
        "COUNT(*) AS assessmentCount, ",
        "AVG(CASE WHEN assessment_score IS NOT NULL THEN assessment_score ELSE NULL END) AS avgScore, ",
        "SUM(CASE WHEN assessment_result = 'EXCELLENT' THEN 1 ELSE 0 END) AS excellentCount, ",
        "SUM(CASE WHEN assessment_result = 'GOOD' THEN 1 ELSE 0 END) AS goodCount ",
        "FROM t_rchx_project_assessments ",
        "WHERE assessment_score IS NOT NULL ",
        "GROUP BY project_leader_zgh, project_leader_name ",
        "ORDER BY avgScore DESC"
    })
    List<Map<String, Object>> getProjectLeaderAssessmentStatistics();

    /**
     * 获取即将到期的考核任务
     */
    @Select("SELECT * FROM t_rchx_project_assessments " +
            "WHERE assessment_status IN ('PENDING', 'ONGOING') " +
            "AND assessment_deadline BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY) " +
            "ORDER BY assessment_deadline ASC")
    List<RchxProjectAssessments> getAssessmentsNearDeadline(@Param("days") Integer days);

    /**
     * 获取逾期的考核任务
     */
    @Select("SELECT * FROM t_rchx_project_assessments " +
            "WHERE assessment_status IN ('PENDING', 'ONGOING') " +
            "AND assessment_deadline < NOW() " +
            "ORDER BY assessment_deadline ASC")
    List<RchxProjectAssessments> getOverdueAssessments();

    /**
     * 根据考核编号查询
     */
    @Select("SELECT * FROM t_rchx_project_assessments " +
            "WHERE assessment_code = #{assessmentCode}")
    RchxProjectAssessments selectByAssessmentCode(@Param("assessmentCode") String assessmentCode);

    /**
     * 检查考核编号是否存在
     */
    @Select("SELECT COUNT(*) FROM t_rchx_project_assessments " +
            "WHERE assessment_code = #{assessmentCode}")
    int countByAssessmentCode(@Param("assessmentCode") String assessmentCode);

    /**
     * 获取项目的最新考核信息
     */
    @Select("SELECT * FROM t_rchx_project_assessments " +
            "WHERE project_id = #{projectId} " +
            "ORDER BY assessment_date DESC " +
            "LIMIT 1")
    RchxProjectAssessments getLatestAssessmentByProjectId(@Param("projectId") Long projectId);
}
