package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxDepartments;
import com.gl.gl_lg_java.service.RchxDepartmentsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理部门表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@RestController
@RequestMapping("/api/departments")
public class RchxDepartmentsController {

    @Autowired
    private RchxDepartmentsService departmentsService;

    /**
     * 分页查询管理部门
     */
    @GetMapping("/page")
    public Result<IPage<RchxDepartments>> page(@RequestParam(defaultValue = "1") Integer current,
                                              @RequestParam(defaultValue = "10") Integer size,
                                              @RequestParam(required = false) String deptName,
                                              @RequestParam(required = false) Boolean isEnabled) {
        try {
            Page<RchxDepartments> page = new Page<>(current, size);
            IPage<RchxDepartments> result = departmentsService.page(page);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询管理部门失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的管理部门
     */
    @GetMapping("/enabled")
    public Result<List<RchxDepartments>> getEnabledDepartments() {
        try {
            List<RchxDepartments> departments = departmentsService.getEnabledDepartments();
            return Result.success(departments);
        } catch (Exception e) {
            log.error("获取启用的管理部门失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询管理部门
     */
    @GetMapping("/{id}")
    public Result<RchxDepartments> getById(@PathVariable Integer id) {
        try {
            RchxDepartments department = departmentsService.getById(id);
            if (department == null) {
                return Result.error("管理部门不存在");
            }
            return Result.success(department);
        } catch (Exception e) {
            log.error("查询管理部门失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据部门编码查询管理部门
     */
    @GetMapping("/code/{deptCode}")
    public Result<RchxDepartments> getByDeptCode(@PathVariable String deptCode) {
        try {
            RchxDepartments department = departmentsService.getByDeptCode(deptCode);
            if (department == null) {
                return Result.error("管理部门不存在");
            }
            return Result.success(department);
        } catch (Exception e) {
            log.error("根据编码查询管理部门失败: deptCode={}", deptCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增管理部门
     */
    @PostMapping
    public Result<String> save(@RequestBody RchxDepartments department) {
        try {
            // 检查编码是否重复
            if (departmentsService.existsByDeptCode(department.getDeptCode(), null)) {
                return Result.error("部门编码已存在");
            }
            
            boolean success = departmentsService.save(department);
            return success ? Result.success("新增成功") : Result.error("新增失败");
        } catch (Exception e) {
            log.error("新增管理部门失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新管理部门
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Integer id, @RequestBody RchxDepartments department) {
        try {
            // 检查编码是否重复
            if (departmentsService.existsByDeptCode(department.getDeptCode(), id)) {
                return Result.error("部门编码已存在");
            }
            
            department.setId(id);
            boolean success = departmentsService.updateById(department);
            return success ? Result.success("更新成功") : Result.error("更新失败");
        } catch (Exception e) {
            log.error("更新管理部门失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除管理部门
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Integer id) {
        try {
            boolean success = departmentsService.removeById(id);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除管理部门失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用管理部门
     */
    @PutMapping("/{id}/enabled")
    public Result<String> updateEnabled(@PathVariable Integer id, @RequestParam Boolean enabled) {
        try {
            boolean success = departmentsService.updateEnabled(id, enabled);
            return success ? Result.success("操作成功") : Result.error("操作失败");
        } catch (Exception e) {
            log.error("更新管理部门启用状态失败: id={}, enabled={}", id, enabled, e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
}
