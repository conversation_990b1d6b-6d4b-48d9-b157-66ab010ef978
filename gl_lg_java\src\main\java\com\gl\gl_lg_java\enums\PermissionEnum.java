package com.gl.gl_lg_java.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权限枚举
 */
@Getter
@AllArgsConstructor
public enum PermissionEnum {
    
    /**
     * 教师 - 个人权限
     */
    TEACHER("教师", 1, "普通教师，只能查看和修改自己的信息"),

    /**
     * 评审 - 评审权限
     */
    REVIEWER("评审", 2, "评审人员，只能进行评审相关操作"),

    /**
     * 学院管理员 - 学院管理权限
     */
    COLLEGE_ADMIN("学院管理员", 3, "学院管理员，只能管理本学院的教职工信息"),

    /**
     * 系统管理员 - 最高权限
     */
    SYSTEM_ADMIN("系统管理员", 4, "系统管理员，拥有所有权限");
    
    /**
     * 权限名称
     */
    private final String name;
    
    /**
     * 权限级别（数字越大权限越高）
     */
    private final Integer level;
    
    /**
     * 权限描述
     */
    private final String description;
    
    /**
     * 根据权限名称获取枚举
     */
    public static PermissionEnum getByName(String name) {
        for (PermissionEnum permission : values()) {
            if (permission.getName().equals(name)) {
                return permission;
            }
        }
        return TEACHER; // 默认返回教师权限
    }
    
    /**
     * 检查是否有指定权限
     * 只有系统管理员拥有所有权限，其他权限只能访问自己对应的功能
     */
    public boolean hasPermission(PermissionEnum requiredPermission) {
        // 系统管理员拥有所有权限
        if (this == SYSTEM_ADMIN) {
            return true;
        }
        // 其他权限只能访问自己对应的功能
        return this == requiredPermission;
    }
    
    /**
     * 检查是否是指定权限
     */
    public boolean isPermission(PermissionEnum targetPermission) {
        return this == targetPermission;
    }
    
    /**
     * 获取所有权限名称
     */
    public static String[] getAllPermissionNames() {
        return new String[]{
            TEACHER.getName(),
            REVIEWER.getName(),
            COLLEGE_ADMIN.getName(),
            SYSTEM_ADMIN.getName()
        };
    }
}
