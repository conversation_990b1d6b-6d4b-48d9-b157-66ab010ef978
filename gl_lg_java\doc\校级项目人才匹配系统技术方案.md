# 校级项目人才匹配系统技术方案

## 1. 项目概述

### 1.1 需求背景
基于学校六大校级项目规则，实现教师与校级项目的智能匹配分析，为项目申报提供科学的人才推荐方案。

### 1.2 核心功能
- 校级项目与教师的匹配度分析
- 基于规则的智能筛选（无需权重配置）
- 匹配结果可视化展示
- 匹配依据详细说明

### 1.3 六大校级项目类型
1. 桂林理工大学创新团队支持计划项目
2. 桂林理工大学各类服务管理人才支持计划项目
3. 桂林理工大学领军人才引育计划项目
4. 桂林理工大学屏风学者引育计划项目
5. 桂林理工大学青年人才未来工程项目
6. 桂林理工大学社会服务人才培育计划项目

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端展示层     │    │   后端服务层     │    │   数据存储层     │
│                │    │                │    │                │
│ Vue.js         │◄──►│ Spring Boot    │◄──►│ MySQL          │
│ Element UI     │    │ MyBatis        │    │ Redis(缓存)     │
│ TalentAnalysis │    │ 匹配算法引擎    │    │ 现有业务表      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心模块
- **匹配引擎模块**：实现六大项目的匹配算法
- **规则配置模块**：管理项目申请条件和规则
- **结果分析模块**：生成匹配报告和可视化展示
- **数据集成模块**：整合教师、成果、项目等数据

## 3. 数据库设计

### 3.1 新增数据表

#### 3.1.1 校级项目类型表 (t_school_project_types)
```sql
CREATE TABLE t_school_project_types (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_code VARCHAR(50) NOT NULL UNIQUE COMMENT '项目编码',
    project_name VARCHAR(200) NOT NULL COMMENT '项目名称',
    project_description TEXT COMMENT '项目描述',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3.1.2 校级项目规则表 (t_school_project_rules)
```sql
CREATE TABLE t_school_project_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型',
    rule_name VARCHAR(200) NOT NULL COMMENT '规则名称',
    rule_condition TEXT NOT NULL COMMENT '规则条件(JSON格式)',
    is_required TINYINT(1) DEFAULT 1 COMMENT '是否必需条件',
    weight_score INT DEFAULT 0 COMMENT '权重分数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_project_code (project_code),
    INDEX idx_rule_type (rule_type)
);
```

#### 3.1.3 匹配分析记录表 (t_school_project_analysis)
```sql
CREATE TABLE t_school_project_analysis (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    analysis_id VARCHAR(50) NOT NULL UNIQUE COMMENT '分析ID',
    project_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    analysis_params TEXT COMMENT '分析参数(JSON)',
    total_teachers INT DEFAULT 0 COMMENT '总教师数',
    matched_teachers INT DEFAULT 0 COMMENT '匹配教师数',
    analysis_status VARCHAR(20) DEFAULT 'RUNNING' COMMENT '分析状态',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_time DATETIME COMMENT '完成时间',
    INDEX idx_analysis_id (analysis_id),
    INDEX idx_project_code (project_code)
);
```

#### 3.1.4 匹配结果表 (t_school_project_match_results)
```sql
CREATE TABLE t_school_project_match_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    analysis_id VARCHAR(50) NOT NULL COMMENT '分析ID',
    teacher_zgh VARCHAR(50) NOT NULL COMMENT '教师职工号',
    project_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    match_score DECIMAL(5,2) DEFAULT 0 COMMENT '匹配分数',
    match_level VARCHAR(20) COMMENT '匹配等级',
    match_details TEXT COMMENT '匹配详情(JSON)',
    is_qualified TINYINT(1) DEFAULT 0 COMMENT '是否符合条件',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_analysis_id (analysis_id),
    INDEX idx_teacher_zgh (teacher_zgh),
    INDEX idx_match_score (match_score DESC)
);
```

### 3.2 现有数据表利用
- `t_rchx_jzgjbxx` - 教职工基本信息
- `t_rchx_hjcgjbxx` - 获奖成果基本信息  
- `t_rchx_lwcgjbxx` - 论文成果基本信息
- `t_rchx_zlcgjbxx` - 专利成果基本信息
- `t_rchx_kjxmjbxx` - 科技项目基本信息

## 4. 后端API设计

### 4.1 核心接口定义

#### 4.1.1 获取校级项目列表
```
GET /api/school-project-matching/projects
Response: {
    "code": 200,
    "message": "success",
    "data": [
        {
            "projectCode": "CXTD",
            "projectName": "创新团队支持计划项目",
            "description": "协助团队负责人制定和实施团队创新发展规划...",
            "isEnabled": true
        }
    ]
}
```

#### 4.1.2 执行匹配分析
```
POST /api/school-project-matching/analyze
Request: {
    "projectCode": "CXTD",
    "analysisParams": {
        "timeRange": "5", // 近5年
        "minMatchScore": 60,
        "maxResults": 100,
        "includeDetails": true
    }
}
Response: {
    "code": 200,
    "message": "success", 
    "data": {
        "analysisId": "ANALYSIS_20241206_001",
        "status": "COMPLETED",
        "summary": {
            "totalTeachers": 150,
            "matchedTeachers": 25,
            "averageScore": 72.5
        }
    }
}
```

#### 4.1.3 获取匹配结果
```
GET /api/school-project-matching/results/{analysisId}
Response: {
    "code": 200,
    "data": {
        "analysisInfo": {...},
        "results": [
            {
                "teacherZgh": "T001",
                "teacherName": "张三",
                "department": "计算机学院",
                "title": "教授",
                "matchScore": 85.5,
                "matchLevel": "HIGH",
                "qualificationStatus": true,
                "matchDetails": {
                    "basicConditions": {...},
                    "academicAchievements": {...},
                    "projectExperience": {...}
                }
            }
        ]
    }
}
```

## 5. 前端组件设计

### 5.1 TalentAnalysisPage.vue 修改方案

#### 5.1.1 校级项目分析标签页内容替换
```vue
<el-tab-pane label="校级项目分析" name="school-project">
  <div class="school-project-analysis">
    <!-- 项目选择区域 -->
    <div class="project-selection-section">
      <h3><i class="el-icon-school"></i>选择校级项目类型</h3>
      <el-radio-group v-model="selectedProjectCode" @change="onProjectChange">
        <el-radio-button 
          v-for="project in schoolProjects" 
          :key="project.projectCode"
          :label="project.projectCode"
        >
          {{ project.projectName }}
        </el-radio-button>
      </el-radio-group>
    </div>
    
    <!-- 分析配置区域 -->
    <div class="analysis-config-section" v-if="selectedProjectCode">
      <h3><i class="el-icon-setting"></i>分析配置</h3>
      <el-form :model="analysisConfig" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="时间范围">
              <el-select v-model="analysisConfig.timeRange">
                <el-option label="近3年" value="3"></el-option>
                <el-option label="近5年" value="5"></el-option>
                <el-option label="近10年" value="10"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最低匹配分">
              <el-input-number v-model="analysisConfig.minMatchScore" :min="0" :max="100"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大结果数">
              <el-input-number v-model="analysisConfig.maxResults" :min="10" :max="500"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 开始分析按钮 -->
      <div class="analysis-actions">
        <el-button type="primary" @click="startSchoolProjectAnalysis" :loading="analyzing">
          <i class="el-icon-data-analysis"></i>开始分析
        </el-button>
        <el-button @click="resetAnalysisConfig">重置配置</el-button>
      </div>
    </div>
  </div>
</el-tab-pane>
```

### 5.2 新增API调用方法

#### 5.2.1 创建 school-project-matching.js API文件
```javascript
/**
 * 校级项目匹配分析API
 */
import request from '@/utils/request'

/**
 * 获取校级项目列表
 */
export function getSchoolProjects() {
  return request({
    url: '/school-project-matching/projects',
    method: 'get'
  })
}

/**
 * 执行校级项目匹配分析
 */
export function analyzeSchoolProjectMatching(params) {
  return request({
    url: '/school-project-matching/analyze',
    method: 'post',
    data: params
  })
}

/**
 * 获取分析结果
 */
export function getAnalysisResults(analysisId, params = {}) {
  return request({
    url: `/school-project-matching/results/${analysisId}`,
    method: 'get',
    params
  })
}

/**
 * 获取项目规则详情
 */
export function getProjectRules(projectCode) {
  return request({
    url: `/school-project-matching/rules/${projectCode}`,
    method: 'get'
  })
}
```

## 6. 匹配算法设计

### 6.1 算法核心架构

#### 6.1.1 匹配引擎接口
```java
public interface SchoolProjectMatchingEngine {
    /**
     * 执行项目匹配分析
     */
    MatchingResult analyzeMatching(String projectCode, AnalysisParams params);

    /**
     * 获取项目规则
     */
    List<ProjectRule> getProjectRules(String projectCode);

    /**
     * 验证教师是否符合基本条件
     */
    boolean validateBasicConditions(Teacher teacher, String projectCode);
}
```

#### 6.1.2 具体项目匹配算法

##### 6.1.2.1 创新团队支持计划项目匹配算法
```java
@Component
public class InnovationTeamMatcher implements ProjectMatcher {

    @Override
    public MatchResult matchTeacher(Teacher teacher, AnalysisParams params) {
        MatchResult result = new MatchResult();

        // 1. 基础条件检查
        if (!validateBasicConditions(teacher)) {
            result.setQualified(false);
            result.setReason("不满足基础政治素养要求");
            return result;
        }

        // 2. 团队协作能力评估
        double teamScore = evaluateTeamCollaboration(teacher);

        // 3. 科研项目参与评估
        double projectScore = evaluateProjectParticipation(teacher);

        // 4. 成果转化能力评估
        double transferScore = evaluateResultTransfer(teacher);

        // 5. 综合评分
        double totalScore = (teamScore * 0.4 + projectScore * 0.4 + transferScore * 0.2);

        result.setMatchScore(totalScore);
        result.setQualified(totalScore >= params.getMinMatchScore());
        result.setMatchDetails(buildMatchDetails(teacher, teamScore, projectScore, transferScore));

        return result;
    }

    private double evaluateTeamCollaboration(Teacher teacher) {
        // 评估团队协作能力
        // 1. 查看多人合作项目数量
        // 2. 查看作为非第一作者的论文数量
        // 3. 查看跨学科合作经历
        return 0.0; // 具体实现
    }
}
```

##### ******* 领军人才引育计划项目匹配算法
```java
@Component
public class LeaderTalentMatcher implements ProjectMatcher {

    @Override
    public MatchResult matchTeacher(Teacher teacher, AnalysisParams params) {
        MatchResult result = new MatchResult();

        // 1. 年龄检查（不超过55周岁）
        if (teacher.getAge() > 55) {
            result.setQualified(false);
            result.setReason("年龄超过55周岁");
            return result;
        }

        // 2. 重大项目主持经历检查
        boolean hasNationalProject = checkNationalProjects(teacher);
        boolean hasProvincialMajorProject = checkProvincialMajorProjects(teacher);
        boolean hasMajorAwards = checkMajorAwards(teacher);
        boolean hasServiceFunding = checkServiceFunding(teacher, 10000000); // 1000万元

        // 3. 必须满足至少一项重大成果条件
        if (!hasNationalProject && !hasProvincialMajorProject && !hasMajorAwards && !hasServiceFunding) {
            result.setQualified(false);
            result.setReason("未满足重大科研成果要求");
            return result;
        }

        // 4. 学术影响力评估
        double academicInfluence = evaluateAcademicInfluence(teacher);

        // 5. 创新能力评估
        double innovationAbility = evaluateInnovationAbility(teacher);

        double totalScore = (academicInfluence * 0.6 + innovationAbility * 0.4);

        result.setMatchScore(totalScore);
        result.setQualified(true); // 已通过硬性条件检查

        return result;
    }
}
```

##### 6.1.2.3 青年人才未来工程项目匹配算法
```java
@Component
public class YoungTalentMatcher implements ProjectMatcher {

    @Override
    public MatchResult matchTeacher(Teacher teacher, AnalysisParams params) {
        MatchResult result = new MatchResult();

        // 1. 基本条件检查
        if (!validateBasicConditions(teacher)) {
            result.setQualified(false);
            result.setReason("不满足基本政治素养或学术道德要求");
            return result;
        }

        // 2. 年龄限制检查
        String discipline = teacher.getDisciplineType();
        int ageLimit = "自然科学".equals(discipline) ? 36 : 38;

        if (teacher.getAge() > ageLimit) {
            result.setQualified(false);
            result.setReason(String.format("%s类申请者年龄不能超过%d周岁", discipline, ageLimit));
            return result;
        }

        // 3. 重要成果检查（两种情形任选其一）
        boolean situation1 = checkSituation1(teacher); // 高级别奖项或重大项目
        boolean situation2 = checkSituation2(teacher); // 多项成果组合

        if (!situation1 && !situation2) {
            result.setQualified(false);
            result.setReason("未满足近五年重要成果要求");
            return result;
        }

        // 4. 发展潜力评估
        double potentialScore = evaluateDevelopmentPotential(teacher);

        // 5. 创新能力评估
        double innovationScore = evaluateInnovationCapability(teacher);

        double totalScore = (potentialScore * 0.6 + innovationScore * 0.4);

        result.setMatchScore(totalScore);
        result.setQualified(true);
        result.setMatchDetails(buildYouthTalentDetails(teacher, situation1, situation2));

        return result;
    }

    private boolean checkSituation1(Teacher teacher) {
        // 检查情形1：省部级二等奖以上、国家基金面上项目、国家社科基金、杰出青年基金
        return checkProvincialAwards(teacher, "二等奖", 1) ||
               checkNationalFundProjects(teacher, "面上项目") ||
               checkNationalSocialScienceFund(teacher) ||
               checkGuangxiOutstandingYouthFund(teacher);
    }

    private boolean checkSituation2(Teacher teacher) {
        // 检查情形2：多项成果组合（省部级奖项、科研项目、高质量论文等）
        int achievementCount = 0;

        if (checkProvincialAwards(teacher, "三等奖", 1)) achievementCount++;
        if (checkResearchFunding(teacher)) achievementCount++;
        if (checkHighQualityPapers(teacher)) achievementCount++;
        if (checkAcademicBooks(teacher)) achievementCount++;
        if (checkAcademicPositions(teacher)) achievementCount++;
        if (checkInventionPatents(teacher, 2)) achievementCount++;
        if (checkPolicyAdoption(teacher)) achievementCount++;

        return achievementCount >= 1; // 满足任意1项即可
    }

    private boolean validateBasicConditions(Teacher teacher) {
        // 检查政治素养、学术道德等基本条件
        return checkPoliticalQuality(teacher) &&
               checkAcademicEthics(teacher) &&
               checkTeachingEvaluation(teacher, 3); // 近三年教学评价合格
    }
}
```

##### 6.1.2.4 服务管理人才支持计划项目匹配算法
```java
@Component
public class ServiceManagementMatcher implements ProjectMatcher {

    @Override
    public MatchResult matchTeacher(Teacher teacher, AnalysisParams params) {
        MatchResult result = new MatchResult();

        // 1. 基本条件检查
        if (!validateBasicConditions(teacher)) {
            result.setQualified(false);
            result.setReason("不满足基本政治素养或职业操守要求");
            return result;
        }

        // 2. 任职资质检查
        if (!checkQualificationRequirements(teacher)) {
            result.setQualified(false);
            result.setReason("不满足任职资质要求");
            return result;
        }

        // 3. 具体条件检查（近五年内需满足任意一项）
        boolean conditionMet = checkSpecificConditions(teacher);
        if (!conditionMet) {
            result.setQualified(false);
            result.setReason("未满足近五年具体条件要求");
            return result;
        }

        // 4. 综合评分
        double managementScore = evaluateManagementCapability(teacher);
        double serviceScore = evaluateServiceContribution(teacher);
        double performanceScore = evaluateWorkPerformance(teacher);

        double totalScore = (managementScore * 0.4 + serviceScore * 0.3 + performanceScore * 0.3);

        result.setMatchScore(totalScore);
        result.setQualified(true);
        result.setMatchDetails(buildServiceManagementDetails(teacher));

        return result;
    }

    private boolean validateBasicConditions(Teacher teacher) {
        return checkPoliticalQuality(teacher) &&
               checkProfessionalEthics(teacher) &&
               checkWorkAttitude(teacher) &&
               checkPersonalConduct(teacher);
    }

    private boolean checkQualificationRequirements(Teacher teacher) {
        // 1. 为学校全职在岗人员
        if (!teacher.isFullTimeEmployee()) {
            return false;
        }

        // 2. 在校从事服务管理工作满3年
        if (teacher.getServiceManagementYears() < 3) {
            return false;
        }

        // 3. 身体与心理条件
        if (!teacher.isHealthyForWork()) {
            return false;
        }

        // 4. 年龄要求（距离法定退休年龄尚有一个支持期）
        int yearsToRetirement = calculateYearsToRetirement(teacher);
        int supportPeriod = getSupportPeriod(); // 支持期年限
        return yearsToRetirement >= supportPeriod;
    }

    private boolean checkSpecificConditions(Teacher teacher) {
        // 近五年内需满足任意一项
        return checkHonorsAndAwards(teacher) ||
               checkAnnualAssessment(teacher) ||
               checkOutstandingPerformance(teacher);
    }

    private boolean checkHonorsAndAwards(Teacher teacher) {
        // 本人获得厅局级及以上表彰或奖励
        return teacher.getHonors().stream()
            .anyMatch(honor ->
                honor.isWithinRecentYears(5) &&
                honor.getLevel().isAbove("厅局级") &&
                !honor.isSchoolLevel()
            );
    }

    private boolean checkAnnualAssessment(Teacher teacher) {
        // 年度考核结果均为合格及以上，且至少有2次"优秀"
        List<AssessmentRecord> assessments = teacher.getAnnualAssessments(5);

        boolean allQualified = assessments.stream()
            .allMatch(record -> record.getResult().isQualifiedOrAbove());

        long excellentCount = assessments.stream()
            .filter(record -> record.getResult().isExcellent())
            .count();

        boolean moralAssessmentPassed = teacher.getMoralAssessments(5).stream()
            .allMatch(record -> record.getResult().isQualified());

        return allQualified && excellentCount >= 2 && moralAssessmentPassed;
    }

    private boolean checkOutstandingPerformance(Teacher teacher) {
        // 在关键时刻或承担急难险重任务中表现突出
        return teacher.hasOutstandingPerformanceInCriticalMoments() ||
               teacher.hasOtherRecognizedAchievements();
    }
}
```

##### 6.1.2.5 屏风学者引育计划项目匹配算法

###### 6.1.2.5.1 屏风学者Ⅰ类匹配算法
```java
@Component
public class PingfengScholarType1Matcher implements ProjectMatcher {

    @Override
    public MatchResult matchTeacher(Teacher teacher, AnalysisParams params) {
        MatchResult result = new MatchResult();

        // 1. 通用基本条件检查
        if (!validateCommonBasicConditions(teacher)) {
            result.setQualified(false);
            result.setReason("不满足通用基本条件要求");
            return result;
        }

        // 2. Ⅰ类特定条件检查
        boolean qualificationMet = checkType1Qualifications(teacher);
        if (!qualificationMet) {
            result.setQualified(false);
            result.setReason("未达到广西D层次人才标准");
            return result;
        }

        // 3. 综合评分
        double academicScore = evaluateAcademicAchievement(teacher);
        double innovationScore = evaluateInnovationCapability(teacher);
        double influenceScore = evaluateAcademicInfluence(teacher);

        double totalScore = (academicScore * 0.4 + innovationScore * 0.3 + influenceScore * 0.3);

        result.setMatchScore(totalScore);
        result.setQualified(true);
        result.setMatchDetails(buildPingfengType1Details(teacher));

        return result;
    }

    private boolean checkType1Qualifications(Teacher teacher) {
        // 参照《广西壮族自治区高层次人才认定参考目录》D层次标准
        return teacher.isGuangxiDLevelTalent() ||
               (teacher.isGuangxiCLevelOrAbove() && !teacher.isSelectedAsLeadingTalent());
    }
}
```

###### 6.1.2.5.2 屏风学者Ⅱ类匹配算法
```java
@Component
public class PingfengScholarType2Matcher implements ProjectMatcher {

    @Override
    public MatchResult matchTeacher(Teacher teacher, AnalysisParams params) {
        MatchResult result = new MatchResult();

        // 1. 通用基本条件检查
        if (!validateCommonBasicConditions(teacher)) {
            result.setQualified(false);
            result.setReason("不满足通用基本条件要求");
            return result;
        }

        // 2. Ⅱ类特定条件检查
        boolean qualificationMet = checkType2Qualifications(teacher);
        if (!qualificationMet) {
            result.setQualified(false);
            result.setReason("未达到广西E层次人才标准");
            return result;
        }

        // 3. 综合评分
        double academicScore = evaluateAcademicAchievement(teacher);
        double potentialScore = evaluateDevelopmentPotential(teacher);

        double totalScore = (academicScore * 0.6 + potentialScore * 0.4);

        result.setMatchScore(totalScore);
        result.setQualified(true);
        result.setMatchDetails(buildPingfengType2Details(teacher));

        return result;
    }

    private boolean checkType2Qualifications(Teacher teacher) {
        // 按照广西E层次标准遴选认定
        return teacher.isGuangxiELevelTalent();
    }
}
```

###### 6.1.2.5.3 屏风学者Ⅲ类匹配算法
```java
@Component
public class PingfengScholarType3Matcher implements ProjectMatcher {

    @Override
    public MatchResult matchTeacher(Teacher teacher, AnalysisParams params) {
        MatchResult result = new MatchResult();

        // 1. 通用基本条件检查
        if (!validateCommonBasicConditions(teacher)) {
            result.setQualified(false);
            result.setReason("不满足通用基本条件要求");
            return result;
        }

        // 2. Ⅲ类特定条件检查
        boolean qualificationMet = checkQualificationConditions(teacher);
        boolean achievementMet = false;

        if (!qualificationMet) {
            // 如果不满足资格类条件，检查成果类条件
            achievementMet = checkAchievementConditions(teacher);
        }

        if (!qualificationMet && !achievementMet) {
            result.setQualified(false);
            result.setReason("既不满足资格类条件，也不满足成果类条件要求");
            return result;
        }

        // 3. 综合评分
        double academicScore = evaluateAcademicAchievement(teacher);
        double innovationScore = evaluateInnovationCapability(teacher);
        double serviceScore = evaluateSocialService(teacher);

        double totalScore = (academicScore * 0.5 + innovationScore * 0.3 + serviceScore * 0.2);

        result.setMatchScore(totalScore);
        result.setQualified(true);
        result.setMatchDetails(buildPingfengType3Details(teacher, qualificationMet, achievementMet));

        return result;
    }

    private boolean checkQualificationConditions(Teacher teacher) {
        // 资格类条件检查
        return checkGuangxiTalentPrograms(teacher) ||
               checkNationalFundProjects(teacher) ||
               checkNationalSocialScienceFundExcellent(teacher) ||
               checkGuangxiInnovationTeamOrYouthFund(teacher);
    }

    private boolean checkAchievementConditions(Teacher teacher) {
        // 成果类条件（近五年内满足任意3项）
        int achievementCount = 0;

        if (checkNationalAwards(teacher)) achievementCount++;
        if (checkResearchFundingAchievements(teacher)) achievementCount++;
        if (checkHighQualityPapers(teacher)) achievementCount++;
        if (checkAcademicBooks(teacher)) achievementCount++;
        if (checkAcademicPositions(teacher)) achievementCount++;
        if (checkPatentTransfer(teacher)) achievementCount++;
        if (checkPolicyAdoption(teacher)) achievementCount++;

        return achievementCount >= 3;
    }

    private boolean checkGuangxiTalentPrograms(Teacher teacher) {
        // 检查广西各类人才计划
        return teacher.isGuangxiNewCenturyTalent() ||
               teacher.isBaguiYoungScholar() ||
               teacher.isSpecialYoungExpert() ||
               teacher.isGuangxiExcellentScholar() ||
               teacher.isGuangxiCulturalTalent() ||
               teacher.isLijiangScholar() ||
               teacher.isEducationMinistryCommitteeMember();
    }

    private boolean checkNationalAwards(Teacher teacher) {
        // 获国家级科技/教学成果二等奖及以上（排名前6）；
        // 或省部级二等奖及以上1项（排名前2）、三等奖2项（排名前2）
        boolean nationalAward = teacher.getAwards().stream()
            .anyMatch(award ->
                award.isNationalLevel() &&
                award.isSecondPrizeOrAbove() &&
                award.getRanking() <= 6 &&
                award.isWithinRecentYears(5)
            );

        if (nationalAward) return true;

        // 检查省部级奖励组合
        long provincialSecondOrAbove = teacher.getAwards().stream()
            .filter(award ->
                award.isProvincialLevel() &&
                award.isSecondPrizeOrAbove() &&
                award.getRanking() <= 2 &&
                award.isWithinRecentYears(5)
            ).count();

        long provincialThird = teacher.getAwards().stream()
            .filter(award ->
                award.isProvincialLevel() &&
                award.isThirdPrize() &&
                award.getRanking() <= 2 &&
                award.isWithinRecentYears(5)
            ).count();

        return provincialSecondOrAbove >= 1 && provincialThird >= 2;
    }

    private boolean checkResearchFundingAchievements(Teacher teacher) {
        // 新增主持国家级科研项目；或自然科学类科研到账经费≥100万元、人文社会科学类≥20万元
        boolean hasNationalProject = teacher.getProjects().stream()
            .anyMatch(project ->
                project.isNationalLevel() &&
                project.isPrincipalInvestigator(teacher.getZgh()) &&
                project.isWithinRecentYears(5)
            );

        if (hasNationalProject) return true;

        // 检查到账经费
        double totalFunding = teacher.getProjects().stream()
            .filter(project ->
                project.isPrincipalInvestigator(teacher.getZgh()) &&
                project.isWithinRecentYears(5)
            )
            .mapToDouble(project -> project.getActualFunding())
            .sum();

        String discipline = teacher.getDisciplineType();
        double fundingThreshold = "自然科学".equals(discipline) ? 1000000 : 200000;

        return totalFunding >= fundingThreshold;
    }

    private boolean checkHighQualityPapers(Teacher teacher) {
        // 以第一/通讯作者发表论文：
        // 自然科学类SCI/中文EI≥8篇（不含会议EI）；
        // 人文社会科学类SSCI/CSSCI或人大复印资料收录≥3篇
        String discipline = teacher.getDisciplineType();

        if ("自然科学".equals(discipline)) {
            long sciEiCount = teacher.getPapers().stream()
                .filter(paper ->
                    (paper.isSCI() || paper.isChineseEI()) &&
                    !paper.isConferenceEI() &&
                    (paper.isFirstAuthor(teacher.getZgh()) || paper.isCorrespondingAuthor(teacher.getZgh())) &&
                    paper.isWithinRecentYears(5)
                ).count();
            return sciEiCount >= 8;
        } else {
            long highQualityCount = teacher.getPapers().stream()
                .filter(paper ->
                    (paper.isSSCI() || paper.isCSSCI() || paper.isRenminUniversityReprint()) &&
                    (paper.isFirstAuthor(teacher.getZgh()) || paper.isCorrespondingAuthor(teacher.getZgh())) &&
                    paper.isWithinRecentYears(5)
                ).count();
            return highQualityCount >= 3;
        }
    }

    private boolean checkPatentTransfer(Teacher teacher) {
        // 专利成果转让≥3项（单项转让费≥3万元）
        long transferCount = teacher.getPatentTransfers().stream()
            .filter(transfer ->
                transfer.getTransferAmount() >= 30000 &&
                transfer.isWithinRecentYears(5)
            ).count();
        return transferCount >= 3;
    }

    private boolean checkPolicyAdoption(Teacher teacher) {
        // 研究报告获省部级以上党委政府采纳或领导批示
        return teacher.getPolicyReports().stream()
            .anyMatch(report ->
                report.isProvincialOrAboveLevel() &&
                (report.isAdoptedByGovernment() || report.hasLeadershipEndorsement()) &&
                report.isWithinRecentYears(5)
            );
    }
}
```

##### ******* 社会服务人才培育计划项目匹配算法
```java
@Component
public class SocialServiceMatcher implements ProjectMatcher {

    @Override
    public MatchResult matchTeacher(Teacher teacher, AnalysisParams params) {
        MatchResult result = new MatchResult();

        // 1. 基本条件检查
        if (!validateBasicConditions(teacher)) {
            result.setQualified(false);
            result.setReason("不满足基本条件要求");
            return result;
        }

        // 2. 年龄条件检查
        if (!checkAgeRequirement(teacher)) {
            result.setQualified(false);
            result.setReason("年龄超过限制要求");
            return result;
        }

        // 3. 业绩条件检查
        if (!checkPerformanceRequirement(teacher)) {
            result.setQualified(false);
            result.setReason("未满足近五年技术开发项目经费要求");
            return result;
        }

        // 4. 综合评分
        double academicScore = evaluateAcademicLevel(teacher);
        double serviceScore = evaluateSocialServiceCapability(teacher);
        double impactScore = evaluateIndustryImpact(teacher);
        double fundingScore = evaluateFundingCapability(teacher);

        double totalScore = (academicScore * 0.25 + serviceScore * 0.35 + impactScore * 0.2 + fundingScore * 0.2);

        result.setMatchScore(totalScore);
        result.setQualified(true);
        result.setMatchDetails(buildSocialServiceDetails(teacher));

        return result;
    }

    private boolean validateBasicConditions(Teacher teacher) {
        // 1. 政治素养与职业操守
        if (!checkPoliticalQuality(teacher) || !checkProfessionalEthics(teacher)) {
            return false;
        }

        // 2. 学术与教学能力
        if (!checkAcademicCapability(teacher) || !checkTeachingEvaluation(teacher, 3)) {
            return false;
        }

        // 3. 身体健康
        if (!teacher.isHealthyForWork()) {
            return false;
        }

        // 4. 行为规范
        if (teacher.hasAcademicMisconduct() || teacher.hasMoralMisconduct()) {
            return false;
        }

        return true;
    }

    private boolean checkAgeRequirement(Teacher teacher) {
        int age = teacher.getAge();

        // 原则上不超过50周岁
        if (age <= 50) {
            return true;
        }

        // 成果和实绩特别突出者可放宽至55周岁
        if (age <= 55 && hasOutstandingAchievements(teacher)) {
            return true;
        }

        return false;
    }

    private boolean checkPerformanceRequirement(Teacher teacher) {
        // 作为项目负责人，近五年在技术开发、转移、转化、服务等领域
        // 主持项目的累计到位经费需达到100万元
        double totalFunding = teacher.getTechnicalProjects().stream()
            .filter(project ->
                project.isPrincipalInvestigator(teacher.getZgh()) &&
                project.isTechnicalDevelopmentProject() &&
                project.isWithinRecentYears(5)
            )
            .mapToDouble(project -> project.getActualFunding())
            .sum();

        return totalFunding >= 1000000; // 100万元
    }

    private boolean hasOutstandingAchievements(Teacher teacher) {
        // 判断是否有特别突出的成果和实绩
        return hasHighLevelAwards(teacher) ||
               hasSignificantTechnicalBreakthrough(teacher) ||
               hasExceptionalIndustryImpact(teacher) ||
               hasOutstandingServiceContribution(teacher);
    }

    private double evaluateFundingCapability(Teacher teacher) {
        // 评估获取经费能力
        double recentFunding = teacher.getTechnicalProjects().stream()
            .filter(project ->
                project.isPrincipalInvestigator(teacher.getZgh()) &&
                project.isWithinRecentYears(5)
            )
            .mapToDouble(project -> project.getActualFunding())
            .sum();

        // 基于经费数额计算分数
        if (recentFunding >= 5000000) return 100; // 500万以上
        if (recentFunding >= 3000000) return 90;  // 300万以上
        if (recentFunding >= 2000000) return 80;  // 200万以上
        if (recentFunding >= 1000000) return 70;  // 100万以上（及格线）
        if (recentFunding >= 500000) return 50;   // 50万以上

        return recentFunding / 10000; // 低于50万按比例计算
    }
}
```

#### 6.1.3 通用基本条件验证方法
```java
/**
 * 通用基本条件验证工具类
 */
@Component
public class CommonConditionValidator {

    /**
     * 验证政治素养
     */
    public boolean checkPoliticalQuality(Teacher teacher) {
        // 拥护中国共产党的领导和中国特色社会主义制度
        // 以习近平新时代中国特色社会主义思想为指导
        // 自觉践行"两个确立""四个意识""四个自信""两个维护"
        // 作风正派，遵纪守法
        return teacher.hasPoliticalLoyalty() &&
               teacher.hasCorrectPoliticalStance() &&
               teacher.isLawAbiding() &&
               teacher.hasGoodWorkStyle();
    }

    /**
     * 验证学术道德和职业道德
     */
    public boolean checkAcademicAndProfessionalEthics(Teacher teacher) {
        return !teacher.hasAcademicMisconduct() &&
               !teacher.hasProfessionalMisconduct() &&
               teacher.hasGoodAcademicReputation() &&
               teacher.hasGoodProfessionalConduct();
    }

    /**
     * 验证教学评价和师德考核
     */
    public boolean checkTeachingEvaluation(Teacher teacher, int years) {
        List<TeachingEvaluation> evaluations = teacher.getTeachingEvaluations(years);
        List<MoralAssessment> moralAssessments = teacher.getMoralAssessments(years);

        boolean teachingQualified = evaluations.stream()
            .allMatch(eval -> eval.getResult().isQualified());

        boolean moralQualified = moralAssessments.stream()
            .allMatch(assessment -> assessment.getResult().isQualified());

        return teachingQualified && moralQualified;
    }

    /**
     * 计算距离法定退休年龄的年数
     */
    public int calculateYearsToRetirement(Teacher teacher) {
        int currentAge = teacher.getAge();
        int retirementAge = teacher.getGender().equals("男") ? 60 : 55;
        return Math.max(0, retirementAge - currentAge);
    }
}
```

## 7. 技术实现细节

#### 7.1.1 匹配分析服务
```java
@Service
@Transactional
public class SchoolProjectMatchingService {

    @Autowired
    private TeacherService teacherService;

    @Autowired
    private ProjectMatcherFactory matcherFactory;

    @Autowired
    private AnalysisResultService analysisResultService;

    /**
     * 执行校级项目匹配分析
     */
    public AnalysisResult analyzeProjectMatching(String projectCode, AnalysisParams params) {
        // 1. 创建分析记录
        String analysisId = generateAnalysisId();
        ProjectAnalysis analysis = createAnalysisRecord(analysisId, projectCode, params);

        // 2. 获取候选教师列表
        List<Teacher> teachers = teacherService.getAllActiveTeachers();

        // 3. 获取项目匹配器
        ProjectMatcher matcher = matcherFactory.getMatcher(projectCode);

        // 4. 执行匹配分析
        List<MatchResult> results = new ArrayList<>();
        for (Teacher teacher : teachers) {
            try {
                MatchResult result = matcher.matchTeacher(teacher, params);
                if (result.getMatchScore() >= params.getMinMatchScore()) {
                    results.add(result);
                }
            } catch (Exception e) {
                log.error("教师{}匹配分析失败", teacher.getZgh(), e);
            }
        }

        // 5. 排序和限制结果数量
        results.sort((a, b) -> Double.compare(b.getMatchScore(), a.getMatchScore()));
        if (results.size() > params.getMaxResults()) {
            results = results.subList(0, params.getMaxResults());
        }

        // 6. 保存分析结果
        analysisResultService.saveResults(analysisId, results);

        // 7. 更新分析状态
        updateAnalysisStatus(analysisId, "COMPLETED", results.size());

        return buildAnalysisResult(analysis, results);
    }
}
```

#### 7.1.2 匹配器工厂类
```java
@Component
public class ProjectMatcherFactory {

    private final Map<String, ProjectMatcher> matchers = new HashMap<>();

    @PostConstruct
    public void initMatchers() {
        matchers.put("CXTD", new InnovationTeamMatcher());
        matchers.put("FWGL", new ServiceManagementMatcher());
        matchers.put("LJRC", new LeaderTalentMatcher());
        matchers.put("PFXZ1", new PingfengScholarType1Matcher());
        matchers.put("PFXZ2", new PingfengScholarType2Matcher());
        matchers.put("PFXZ3", new PingfengScholarType3Matcher());
        matchers.put("QNRC", new YoungTalentMatcher());
        matchers.put("SHFW", new SocialServiceMatcher());
    }

    public ProjectMatcher getMatcher(String projectCode) {
        ProjectMatcher matcher = matchers.get(projectCode);
        if (matcher == null) {
            throw new IllegalArgumentException("不支持的项目类型: " + projectCode);
        }
        return matcher;
    }
}
```

### 7.2 前端核心方法

#### 7.2.1 TalentAnalysisPage.vue 新增方法
```javascript
export default {
  data() {
    return {
      // 校级项目分析相关数据
      schoolProjects: [],
      selectedProjectCode: '',
      analysisConfig: {
        timeRange: '5',
        minMatchScore: 60,
        maxResults: 100,
        includeDetails: true
      },
      currentAnalysisId: '',
      schoolProjectResults: []
    }
  },

  methods: {
    // 加载校级项目列表
    async loadSchoolProjects() {
      try {
        const response = await getSchoolProjects()
        if (response.data.code === 200) {
          this.schoolProjects = response.data.data
        }
      } catch (error) {
        this.$message.error('加载校级项目列表失败')
        console.error(error)
      }
    },

    // 项目选择变化
    onProjectChange(projectCode) {
      this.selectedProjectCode = projectCode
      this.schoolProjectResults = []
    },

    // 开始校级项目分析
    async startSchoolProjectAnalysis() {
      if (!this.selectedProjectCode) {
        this.$message.warning('请选择校级项目类型')
        return
      }

      this.analyzing = true
      this.currentAnalysisType = '校级项目分析'
      this.startProgressSimulation('detailed')

      try {
        // 执行分析
        const analysisParams = {
          projectCode: this.selectedProjectCode,
          analysisParams: this.analysisConfig
        }

        const response = await analyzeSchoolProjectMatching(analysisParams)

        if (response.data.code === 200) {
          this.currentAnalysisId = response.data.data.analysisId

          // 获取分析结果
          await this.loadSchoolProjectResults()

          this.finishProgress()
          this.$message.success('校级项目分析完成！')
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        this.$message.error('校级项目分析失败，请重试')
        console.error('校级项目分析错误:', error)
      } finally {
        this.analyzing = false
        this.clearProgressTimer()
      }
    },

    // 加载分析结果
    async loadSchoolProjectResults() {
      if (!this.currentAnalysisId) return

      try {
        const response = await getAnalysisResults(this.currentAnalysisId)
        if (response.data.code === 200) {
          this.schoolProjectResults = response.data.data.results
          this.analysisResults = this.schoolProjectResults // 复用现有的结果展示组件
        }
      } catch (error) {
        this.$message.error('加载分析结果失败')
        console.error(error)
      }
    },

    // 重置分析配置
    resetAnalysisConfig() {
      this.analysisConfig = {
        timeRange: '5',
        minMatchScore: 60,
        maxResults: 100,
        includeDetails: true
      }
      this.selectedProjectCode = ''
      this.schoolProjectResults = []
      this.analysisResults = []
    }
  },

  mounted() {
    // 加载校级项目列表
    this.loadSchoolProjects()
  }
}
```

## 8. 部署和配置说明

### 8.1 数据库初始化

#### 8.1.1 创建基础数据
```sql
-- 插入六大校级项目类型
INSERT INTO t_school_project_types (project_code, project_name, project_description, sort_order) VALUES
('CXTD', '创新团队支持计划项目', '协助团队负责人制定和实施团队创新发展规划', 1),
('FWGL', '各类服务管理人才支持计划项目', '支持在服务管理工作中表现突出的人才', 2),
('LJRC', '领军人才引育计划项目', '学校最高层次人才引育项目', 3),
('PFXZ1', '屏风学者引育计划项目-Ⅰ类', '参照广西D层次标准遴选认定', 4),
('PFXZ2', '屏风学者引育计划项目-Ⅱ类', '参照广西E层次标准遴选认定', 5),
('PFXZ3', '屏风学者引育计划项目-Ⅲ类', '满足特定成果条件的人才', 6),
('QNRC', '青年人才未来工程项目', '支持有发展潜力的青年人才', 7),
('SHFW', '社会服务人才培育计划项目', '支持在社会服务领域表现突出的人才', 8);
```

#### 8.1.2 配置项目规则
```sql
-- 青年人才未来工程项目规则
INSERT INTO t_school_project_rules (project_code, rule_type, rule_name, rule_condition, is_required, weight_score) VALUES
('QNRC', 'AGE_LIMIT', '年龄限制', '{"naturalScience": 36, "socialScience": 38}', 1, 0),
('QNRC', 'ACHIEVEMENT_SITUATION1', '重要成果情形1', '{"awards": "省部级二等奖以上", "projects": "国家基金面上项目"}', 0, 50),
('QNRC', 'ACHIEVEMENT_SITUATION2', '重要成果情形2', '{"minCount": 1, "options": ["省部级奖项", "科研项目", "高质量论文"]}', 0, 50);

-- 领军人才引育计划项目规则
INSERT INTO t_school_project_rules (project_code, rule_type, rule_name, rule_condition, is_required, weight_score) VALUES
('LJRC', 'AGE_LIMIT', '年龄限制', '{"maxAge": 55}', 1, 0),
('LJRC', 'TALENT_LEVEL', '人才层次', '{"level": "自治区C类及以上"}', 1, 0),
('LJRC', 'MAJOR_ACHIEVEMENTS', '重大成果', '{"options": ["国家级项目", "省部级重大项目", "重大奖项", "社会服务经费1000万"]}', 1, 100);

-- 服务管理人才支持计划项目规则
INSERT INTO t_school_project_rules (project_code, rule_type, rule_name, rule_condition, is_required, weight_score) VALUES
('FWGL', 'WORK_YEARS', '工作年限', '{"minYears": 3}', 1, 0),
('FWGL', 'ASSESSMENT', '考核要求', '{"excellentCount": 2, "allQualified": true}', 1, 0),
('FWGL', 'HONORS', '表彰奖励', '{"level": "厅局级及以上"}', 0, 50),
('FWGL', 'OUTSTANDING_PERFORMANCE', '突出实绩', '{"criticalMoments": true}', 0, 50);

-- 屏风学者Ⅲ类项目规则
INSERT INTO t_school_project_rules (project_code, rule_type, rule_name, rule_condition, is_required, weight_score) VALUES
('PFXZ3', 'QUALIFICATION_CONDITIONS', '资格类条件', '{"guangxiTalent": true, "nationalFund": true}', 0, 60),
('PFXZ3', 'ACHIEVEMENT_CONDITIONS', '成果类条件', '{"minCount": 3, "totalOptions": 7}', 0, 60);

-- 社会服务人才培育计划项目规则
INSERT INTO t_school_project_rules (project_code, rule_type, rule_name, rule_condition, is_required, weight_score) VALUES
('SHFW', 'AGE_LIMIT', '年龄限制', '{"normalAge": 50, "exceptionalAge": 55}', 1, 0),
('SHFW', 'FUNDING_REQUIREMENT', '经费要求', '{"minFunding": 1000000, "years": 5}', 1, 100);
```

### 8.2 应用配置

#### 8.2.1 Spring Boot 配置
```yaml
# application.yml
school-project-matching:
  # 分析结果缓存时间（小时）
  cache-duration: 24
  # 最大并发分析数
  max-concurrent-analysis: 5
  # 默认分析参数
  default-params:
    time-range: 5
    min-match-score: 60
    max-results: 100
```

#### 8.2.2 前端路由配置
```javascript
// router/index.js
{
  path: '/talent/analysis',
  name: 'TalentAnalysis',
  component: () => import('@/views/talent/TalentAnalysisPage.vue'),
  meta: {
    title: '人才-项目适配度分析',
    requiresAuth: true,
    permissions: ['talent:analysis']
  }
}
```

## 9. 测试方案

### 9.1 单元测试

#### 9.1.1 匹配算法测试
```java
@SpringBootTest
class ProjectMatcherTest {

    @Test
    void testYoungTalentMatcher() {
        // 准备测试数据
        Teacher teacher = createTestTeacher();
        AnalysisParams params = createTestParams();

        // 执行匹配
        YoungTalentMatcher matcher = new YoungTalentMatcher();
        MatchResult result = matcher.matchTeacher(teacher, params);

        // 验证结果
        assertThat(result.isQualified()).isTrue();
        assertThat(result.getMatchScore()).isGreaterThan(60.0);
    }
}
```

### 9.2 集成测试

#### 9.2.1 API接口测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase
class SchoolProjectMatchingControllerTest {

    @Test
    void testAnalyzeProjectMatching() throws Exception {
        mockMvc.perform(post("/api/school-project-matching/analyze")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data.analysisId").exists());
    }
}
```

## 10. 后续优化建议

### 10.1 性能优化
1. **数据库优化**：添加复合索引，优化复杂查询
2. **缓存策略**：使用Redis缓存分析结果和教师数据
3. **异步处理**：大批量分析采用异步处理机制
4. **分页查询**：结果展示支持分页加载

### 10.2 功能扩展
1. **规则配置界面**：提供可视化的规则配置管理
2. **分析报告导出**：支持PDF、Excel格式的详细报告
3. **历史分析对比**：支持不同时期的分析结果对比
4. **智能推荐**：基于历史数据提供项目申报建议

### 10.3 系统监控
1. **分析性能监控**：监控分析耗时和成功率
2. **数据质量监控**：监控教师数据的完整性和准确性
3. **用户行为分析**：分析用户使用模式，优化体验

---

## 总结

本技术方案提供了完整的校级项目人才匹配系统设计，包括：

1. **完整的系统架构**：前后端分离，模块化设计
2. **详细的数据库设计**：新增4个核心表，充分利用现有数据
3. **规范的API接口**：RESTful设计，支持完整的CRUD操作
4. **智能的匹配算法**：基于六大项目规则的精确匹配
5. **友好的用户界面**：集成到现有系统，保持一致的用户体验
6. **可扩展的架构**：支持新项目类型和规则的灵活配置

该方案可以有效解决校级项目人才匹配的需求，为学校的人才项目申报提供科学的决策支持。

## 附录：关键代码示例

### A.1 Controller层示例
```java
@RestController
@RequestMapping("/api/school-project-matching")
@Slf4j
public class SchoolProjectMatchingController {

    @Autowired
    private SchoolProjectMatchingService matchingService;

    @GetMapping("/projects")
    public Result<List<SchoolProject>> getSchoolProjects() {
        try {
            List<SchoolProject> projects = matchingService.getAllSchoolProjects();
            return Result.success(projects);
        } catch (Exception e) {
            log.error("获取校级项目列表失败", e);
            return Result.error("获取校级项目列表失败");
        }
    }

    @PostMapping("/analyze")
    public Result<AnalysisResult> analyzeMatching(@RequestBody AnalysisRequest request) {
        try {
            AnalysisResult result = matchingService.analyzeProjectMatching(
                request.getProjectCode(),
                request.getAnalysisParams()
            );
            return Result.success(result);
        } catch (Exception e) {
            log.error("执行项目匹配分析失败", e);
            return Result.error("执行项目匹配分析失败");
        }
    }

    @GetMapping("/results/{analysisId}")
    public Result<AnalysisDetailResult> getAnalysisResults(@PathVariable String analysisId) {
        try {
            AnalysisDetailResult result = matchingService.getAnalysisResults(analysisId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取分析结果失败", e);
            return Result.error("获取分析结果失败");
        }
    }
}
```

### A.2 数据模型示例
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchResult {
    private String teacherZgh;
    private String teacherName;
    private String department;
    private String title;
    private Double matchScore;
    private String matchLevel;
    private Boolean isQualified;
    private String reason;
    private MatchDetails matchDetails;
    private LocalDateTime createdTime;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchDetails {
    private BasicConditionResult basicConditions;
    private AcademicAchievementResult academicAchievements;
    private ProjectExperienceResult projectExperience;
    private Map<String, Object> additionalInfo;
}
```

## 附录B：六大校级项目匹配算法总结

### B.1 匹配算法对比表

| 项目类型 | 项目代码 | 主要条件 | 关键检查点 | 评分权重 |
|---------|---------|---------|-----------|---------|
| **创新团队支持计划** | CXTD | 政治素养 | 团队协作能力、科研参与度、成果转化 | 团队40% + 项目40% + 转化20% |
| **服务管理人才支持计划** | FWGL | 工作满3年 + 考核优秀2次 | 表彰奖励、年度考核、突出实绩 | 管理40% + 服务30% + 绩效30% |
| **领军人才引育计划** | LJRC | 年龄≤55岁 + C类人才 | 重大项目、重大奖项、社会服务经费 | 学术影响60% + 创新能力40% |
| **屏风学者Ⅰ类** | PFXZ1 | 广西D层次标准 | 人才层次认定 | 学术40% + 创新30% + 影响30% |
| **屏风学者Ⅱ类** | PFXZ2 | 广西E层次标准 | 人才层次认定 | 学术60% + 潜力40% |
| **屏风学者Ⅲ类** | PFXZ3 | 资格类或成果类(3项) | 人才计划、基金项目、成果数量 | 学术50% + 创新30% + 服务20% |
| **青年人才未来工程** | QNRC | 年龄≤36/38岁 | 情形1或情形2成果要求 | 潜力60% + 创新40% |
| **社会服务人才培育计划** | SHFW | 年龄≤50/55岁 + 经费100万 | 技术开发项目经费 | 学术25% + 服务35% + 影响20% + 经费20% |

### B.2 数据依赖分析

#### B.2.1 现有数据表利用情况
| 数据表 | 利用程度 | 主要用途 | 缺失字段 |
|-------|---------|---------|---------|
| `t_rchx_jzgjbxx` | 90% | 基本信息、年龄计算 | 人才层次、考核记录 |
| `t_rchx_hjcgjbxx` | 80% | 获奖成果检查 | 排名信息、奖励级别 |
| `t_rchx_kjlwjbxx` | 85% | 论文成果统计 | 作者排名、期刊级别 |
| `t_rchx_zlcgjbxx` | 70% | 专利成果 | 转让记录、转让金额 |
| `t_rchx_kjxmjbxx` | 75% | 科研项目 | 实际到账经费、项目级别 |

#### B.2.2 需要补充的数据
1. **年度考核记录** - 服务管理人才必需
2. **师德考核记录** - 所有项目通用
3. **表彰奖励记录** - 服务管理人才必需
4. **人才层次认定** - 领军人才、屏风学者必需
5. **专利转让记录** - 屏风学者Ⅲ类、青年人才
6. **政策采纳记录** - 屏风学者Ⅲ类、青年人才
7. **学术兼职记录** - 屏风学者Ⅲ类、青年人才

### B.3 实施优先级建议

#### 第一阶段（核心功能）
1. **青年人才未来工程** - 条件相对简单，数据较完整
2. **领军人才引育计划** - 高层次人才，影响重大
3. **社会服务人才培育计划** - 条件明确，易于实现

#### 第二阶段（完善功能）
1. **屏风学者Ⅲ类** - 条件复杂，需要更多数据
2. **服务管理人才支持计划** - 需要考核数据补充
3. **创新团队支持计划** - 团队协作评估较复杂

#### 第三阶段（高级功能）
1. **屏风学者Ⅰ、Ⅱ类** - 依赖外部人才认定
2. **智能推荐优化** - 基于历史数据训练
3. **预测分析功能** - 发展潜力预测

### B.4 技术难点和解决方案

#### B.4.1 数据完整性问题
**问题**：现有数据库缺少关键字段
**解决方案**：
- 基于现有数据进行智能推断
- 提供数据补充界面
- 逐步完善数据质量

#### B.4.2 规则复杂性问题
**问题**：屏风学者Ⅲ类条件复杂
**解决方案**：
- 分步骤检查，逐项验证
- 提供详细的匹配报告
- 支持人工审核确认

#### B.4.3 评分标准问题
**问题**：部分项目缺少明确的评分标准
**解决方案**：
- 基于成果数量和质量设计评分算法
- 参考同类高校的评价标准
- 支持评分权重配置

该技术方案文档详细描述了校级项目人才匹配系统的完整实现方案，包含了所有六大校级项目的匹配算法，可以作为开发团队的技术指导文档使用。
