# 教职工总数统计接口

## 接口信息
- **URL**: `GET /api/jzgjbxx/total-count`
- **权限**: 教师及以上
- **功能**: 获取教职工总数及各维度统计

## 响应示例

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "totalCount": 1250,
        "timestamp": 1721376000000,
        "genderStats": {
            "male": 680,
            "female": 520,
            "other": 50
        },
        "statusStats": {
            "active": 1100,
            "retired": 120,
            "other": 30
        },
        "permissionStats": {
            "teacher": 1000,
            "admin": 20,
            "sysAdmin": 5,
            "other": 225
        }
    }
}
```

## 字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| `totalCount` | Long | 教职工总数 |
| `timestamp` | Long | 统计时间戳 |
| `genderStats` | Object | 性别统计 |
| `genderStats.male` | Long | 男性人数 |
| `genderStats.female` | Long | 女性人数 |
| `genderStats.other` | Long | 其他/未知性别人数 |
| `statusStats` | Object | 状态统计 |
| `statusStats.active` | Long | 在岗人数 |
| `statusStats.retired` | Long | 退休人数 |
| `statusStats.other` | Long | 其他状态人数 |
| `permissionStats` | Object | 权限统计 |
| `permissionStats.teacher` | Long | 教师权限人数 |
| `permissionStats.admin` | Long | 学院管理员人数 |
| `permissionStats.sysAdmin` | Long | 系统管理员人数 |
| `permissionStats.other` | Long | 其他权限人数 |

## 前端调用示例

```javascript
// Vue.js 示例
async getTeacherStats() {
    try {
        const response = await this.$http.get('/api/jzgjbxx/total-count')
        if (response.data.code === 200) {
            const stats = response.data.data
            console.log(`教职工总数: ${stats.totalCount}`)
            console.log(`男性: ${stats.genderStats.male}, 女性: ${stats.genderStats.female}`)
            console.log(`在岗: ${stats.statusStats.active}, 退休: ${stats.statusStats.retired}`)
            
            // 可以用于图表展示
            this.chartData = {
                gender: [
                    { name: '男性', value: stats.genderStats.male },
                    { name: '女性', value: stats.genderStats.female }
                ],
                status: [
                    { name: '在岗', value: stats.statusStats.active },
                    { name: '退休', value: stats.statusStats.retired }
                ]
            }
        }
    } catch (error) {
        console.error('获取统计数据失败:', error)
    }
}
```

## 使用场景

1. **首页仪表盘** - 显示教职工总数概览
2. **统计报表** - 生成各维度统计图表
3. **数据监控** - 实时监控教职工数量变化
4. **权限管理** - 查看各权限级别人员分布
