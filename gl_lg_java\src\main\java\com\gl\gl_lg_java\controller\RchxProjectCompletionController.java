package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectCompletion;
import com.gl.gl_lg_java.service.RchxProjectCompletionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目结项表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@RestController
@RequestMapping("/api/project-completion")
public class RchxProjectCompletionController {

    @Autowired
    private RchxProjectCompletionService projectCompletionService;

    /**
     * 分页查询项目结项
     */
    @GetMapping("/page")
    public Result<IPage<RchxProjectCompletion>> page(@RequestParam(defaultValue = "1") Integer current,
                                                    @RequestParam(defaultValue = "10") Integer size,
                                                    @RequestParam(required = false) Integer categoryId,
                                                    @RequestParam(required = false) Integer typeId,
                                                    @RequestParam(required = false) Integer deptId,
                                                    @RequestParam(required = false) String status,
                                                    @RequestParam(required = false) String projectCode,
                                                    @RequestParam(required = false) String projectLeaderZgh) {
        try {
            Page<RchxProjectCompletion> page = new Page<>(current, size);
            IPage<RchxProjectCompletion> result = projectCompletionService.pageQuery(page, categoryId, typeId, deptId, status, projectCode, projectLeaderZgh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目结项失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目结项
     */
    @GetMapping("/{id}")
    public Result<RchxProjectCompletion> getById(@PathVariable Long id) {
        try {
            RchxProjectCompletion completion = projectCompletionService.getById(id);
            if (completion == null) {
                return Result.error("项目结项不存在");
            }
            return Result.success(completion);
        } catch (Exception e) {
            log.error("查询项目结项失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目编号获取结项信息
     */
    @GetMapping("/project/{projectCode}")
    public Result<List<RchxProjectCompletion>> getByProjectCode(@PathVariable String projectCode) {
        try {
            List<RchxProjectCompletion> completions = projectCompletionService.getByProjectCode(projectCode);
            return Result.success(completions);
        } catch (Exception e) {
            log.error("根据项目编号查询结项失败: projectCode={}", projectCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目负责人获取结项列表
     */
    @GetMapping("/leader/{projectLeaderZgh}")
    public Result<List<RchxProjectCompletion>> getByProjectLeader(@PathVariable String projectLeaderZgh) {
        try {
            List<RchxProjectCompletion> completions = projectCompletionService.getByProjectLeader(projectLeaderZgh);
            return Result.success(completions);
        } catch (Exception e) {
            log.error("根据项目负责人查询结项失败: projectLeaderZgh={}", projectLeaderZgh, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目结项
     */
    @PostMapping
    public Result<String> save(@RequestBody RchxProjectCompletion completion) {
        try {
            boolean success = projectCompletionService.save(completion);
            return success ? Result.success("新增成功") : Result.error("新增失败");
        } catch (Exception e) {
            log.error("新增项目结项失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目结项
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Long id, @RequestBody RchxProjectCompletion completion) {
        try {
            completion.setId(id);
            boolean success = projectCompletionService.updateById(completion);
            return success ? Result.success("更新成功") : Result.error("更新失败");
        } catch (Exception e) {
            log.error("更新项目结项失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目结项
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        try {
            boolean success = projectCompletionService.removeById(id);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除项目结项失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 提交结项
     */
    @PutMapping("/{id}/submit")
    public Result<String> submitCompletion(@PathVariable Long id, @RequestParam String submitBy) {
        try {
            boolean success = projectCompletionService.submitCompletion(id, submitBy);
            return success ? Result.success("提交成功") : Result.error("提交失败");
        } catch (Exception e) {
            log.error("提交结项失败: id={}, submitBy={}", id, submitBy, e);
            return Result.error("提交失败: " + e.getMessage());
        }
    }

    /**
     * 审核结项
     */
    @PutMapping("/{id}/review")
    public Result<String> reviewCompletion(@PathVariable Long id, 
                                          @RequestParam boolean approved,
                                          @RequestParam String reviewerZgh,
                                          @RequestParam(required = false) String reviewComments,
                                          @RequestParam(required = false) BigDecimal completionScore) {
        try {
            boolean success = projectCompletionService.reviewCompletion(id, approved, reviewerZgh, reviewComments, completionScore);
            return success ? Result.success("审核成功") : Result.error("审核失败");
        } catch (Exception e) {
            log.error("审核结项失败: id={}, approved={}, reviewerZgh={}", id, approved, reviewerZgh, e);
            return Result.error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 获取结项统计信息
     */
    @GetMapping("/statistics")
    public Result<Object> getCompletionStatistics(@RequestParam(required = false) Integer deptId,
                                                  @RequestParam(required = false) Integer categoryId) {
        try {
            Object statistics = projectCompletionService.getCompletionStatistics(deptId, categoryId);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取结项统计信息失败: deptId={}, categoryId={}", deptId, categoryId, e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }
}
