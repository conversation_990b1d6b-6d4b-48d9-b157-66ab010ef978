package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectMidlateFiles;
import com.gl.gl_lg_java.service.RchxProjectMidlateFilesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 项目中后期文件管理Controller
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@RestController
@RequestMapping("/api/rchx/project-midlate-files")
@Api(tags = "项目中后期文件管理")
@Slf4j
public class RchxProjectMidlateFilesController {

    @Autowired
    private RchxProjectMidlateFilesService midlateFilesService;

    /**
     * 分页查询文件信息
     */
    @GetMapping("/page")
    @ApiOperation("分页查询文件信息")
    public Result<IPage<RchxProjectMidlateFiles>> pageQuery(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("业务类型") @RequestParam(required = false) String businessType,
            @ApiParam("业务ID") @RequestParam(required = false) Long businessId,
            @ApiParam("项目编号") @RequestParam(required = false) String projectCode,
            @ApiParam("文件类别") @RequestParam(required = false) String fileCategory,
            @ApiParam("上传人") @RequestParam(required = false) String uploadBy,
            @ApiParam("是否必需") @RequestParam(required = false) Boolean isRequired,
            @ApiParam("是否机密") @RequestParam(required = false) Boolean isConfidential) {
        try {
            Page<RchxProjectMidlateFiles> page = new Page<>(current, size);
            IPage<RchxProjectMidlateFiles> result = midlateFilesService.pageQuery(
                    page, businessType, businessId, projectCode,
                    fileCategory, uploadBy, isRequired, isConfidential);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询文件信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询文件详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询文件详情")
    public Result<RchxProjectMidlateFiles> getById(@ApiParam("文件ID") @PathVariable Long id) {
        try {
            RchxProjectMidlateFiles file = midlateFilesService.getById(id);
            if (file == null || file.getIsDeleted()) {
                return Result.error("文件不存在");
            }
            return Result.success(file);
        } catch (Exception e) {
            log.error("查询文件详情失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @ApiOperation("上传文件")
    public Result<RchxProjectMidlateFiles> uploadFile(
            @ApiParam("文件") @RequestParam("file") MultipartFile file,
            @ApiParam("业务类型") @RequestParam String businessType,
            @ApiParam("业务ID") @RequestParam Long businessId,
            @ApiParam("项目编号") @RequestParam String projectCode,
            @ApiParam("文件类别") @RequestParam String fileCategory,
            @ApiParam("文件描述") @RequestParam(required = false) String fileDescription,
            @ApiParam("是否必需") @RequestParam(defaultValue = "false") Boolean isRequired,
            @ApiParam("是否机密") @RequestParam(defaultValue = "false") Boolean isConfidential,
            @ApiParam("版本号") @RequestParam(defaultValue = "1.0") String versionNumber,
            @ApiParam("上传人") @RequestParam String uploadBy,
            @ApiParam("上传人姓名") @RequestParam String uploadByName) {
        try {
            // 验证文件类型（可选）
            List<String> allowedTypes = Arrays.asList(".pdf", ".doc", ".docx", ".xls", ".xlsx",
                    ".ppt", ".pptx", ".txt", ".jpg", ".jpeg",
                    ".png", ".gif", ".zip", ".rar");
            if (!midlateFilesService.validateFileType(file, allowedTypes)) {
                return Result.error("不支持的文件类型");
            }

            // 验证文件大小（最大100MB）
            if (!midlateFilesService.validateFileSize(file, 100L)) {
                return Result.error("文件大小不能超过100MB");
            }

            RchxProjectMidlateFiles uploadedFile = midlateFilesService.uploadFile(
                    file, businessType, businessId, projectCode, fileCategory,
                    fileDescription, isRequired, isConfidential, versionNumber,
                    uploadBy, uploadByName);

            return Result.success(uploadedFile);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     */
    @GetMapping("/{id}/download")
    @ApiOperation("下载文件")
    public ResponseEntity<byte[]> downloadFile(@ApiParam("文件ID") @PathVariable Long id) {
        try {
            RchxProjectMidlateFiles file = midlateFilesService.getById(id);
            if (file == null || file.getIsDeleted()) {
                return ResponseEntity.notFound().build();
            }

            byte[] fileData = midlateFilesService.downloadFile(id);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", file.getFileOriginalName());
            headers.setContentLength(fileData.length);

            return new ResponseEntity<>(fileData, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("文件下载失败: id={}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取文件下载URL（预签名URL）
     */
    @GetMapping("/{id}/download-url")
    @ApiOperation("获取文件下载URL")
    public Result<String> getFileDownloadUrl(@ApiParam("文件ID") @PathVariable Long id) {
        try {
            String downloadUrl = midlateFilesService.getFileDownloadUrl(id);
            return Result.success(downloadUrl);
        } catch (Exception e) {
            log.error("获取文件下载URL失败: id={}", id, e);
            return Result.error("获取下载URL失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除文件")
    public Result<String> deleteFile(@ApiParam("文件ID") @PathVariable Long id,
            @ApiParam("删除人") @RequestParam String deleteBy) {
        try {
            boolean success = midlateFilesService.deleteFile(id, deleteBy);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除文件失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除文件
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除文件")
    public Result<String> batchDeleteFiles(@ApiParam("文件ID列表") @RequestBody List<Long> fileIds,
            @ApiParam("删除人") @RequestParam String deleteBy) {
        try {
            boolean success = midlateFilesService.batchDeleteFiles(fileIds, deleteBy);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除文件失败: fileIds={}", fileIds, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件信息
     */
    @PutMapping("/{id}")
    @ApiOperation("更新文件信息")
    public Result<String> updateFileInfo(@ApiParam("文件ID") @PathVariable Long id,
            @ApiParam("文件描述") @RequestParam(required = false) String fileDescription,
            @ApiParam("是否必需") @RequestParam(required = false) Boolean isRequired,
            @ApiParam("是否机密") @RequestParam(required = false) Boolean isConfidential,
            @ApiParam("版本号") @RequestParam(required = false) String versionNumber) {
        try {
            boolean success = midlateFilesService.updateFileInfo(id, fileDescription, isRequired, isConfidential,
                    versionNumber);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新文件信息失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 替换文件（上传新版本）
     */
    @PostMapping("/{id}/replace")
    @ApiOperation("替换文件")
    public Result<RchxProjectMidlateFiles> replaceFile(@ApiParam("原文件ID") @PathVariable Long id,
            @ApiParam("新文件") @RequestParam("file") MultipartFile newFile,
            @ApiParam("版本号") @RequestParam String versionNumber,
            @ApiParam("上传人") @RequestParam String uploadBy,
            @ApiParam("上传人姓名") @RequestParam String uploadByName) {
        try {
            RchxProjectMidlateFiles newFileRecord = midlateFilesService.replaceFile(id, newFile, versionNumber,
                    uploadBy, uploadByName);
            return Result.success(newFileRecord);
        } catch (Exception e) {
            log.error("替换文件失败: id={}", id, e);
            return Result.error("替换文件失败: " + e.getMessage());
        }
    }

    /**
     * 根据业务类型和业务ID查询文件列表
     */
    @GetMapping("/by-business")
    @ApiOperation("根据业务类型和业务ID查询文件列表")
    public Result<List<RchxProjectMidlateFiles>> getByBusiness(@ApiParam("业务类型") @RequestParam String businessType,
            @ApiParam("业务ID") @RequestParam Long businessId) {
        try {
            List<RchxProjectMidlateFiles> files = midlateFilesService.getByBusiness(businessType, businessId);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据业务类型和业务ID查询文件列表失败: businessType={}, businessId={}", businessType, businessId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目编号查询文件列表
     */
    @GetMapping("/by-project/{projectCode}")
    @ApiOperation("根据项目编号查询文件列表")
    public Result<List<RchxProjectMidlateFiles>> getByProjectCode(@ApiParam("项目编号") @PathVariable String projectCode) {
        try {
            List<RchxProjectMidlateFiles> files = midlateFilesService.getByProjectCode(projectCode);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据项目编号查询文件列表失败: projectCode={}", projectCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件类别查询文件列表
     */
    @GetMapping("/by-category")
    @ApiOperation("根据文件类别查询文件列表")
    public Result<List<RchxProjectMidlateFiles>> getByCategory(@ApiParam("业务类型") @RequestParam String businessType,
            @ApiParam("业务ID") @RequestParam Long businessId,
            @ApiParam("文件类别") @RequestParam String fileCategory) {
        try {
            List<RchxProjectMidlateFiles> files = midlateFilesService.getByCategory(businessType, businessId,
                    fileCategory);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据文件类别查询文件列表失败: businessType={}, businessId={}, fileCategory={}",
                    businessType, businessId, fileCategory, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据上传人查询文件列表
     */
    @GetMapping("/by-uploader/{uploadBy}")
    @ApiOperation("根据上传人查询文件列表")
    public Result<List<RchxProjectMidlateFiles>> getByUploader(@ApiParam("上传人") @PathVariable String uploadBy) {
        try {
            List<RchxProjectMidlateFiles> files = midlateFilesService.getByUploader(uploadBy);
            return Result.success(files);
        } catch (Exception e) {
            log.error("根据上传人查询文件列表失败: uploadBy={}", uploadBy, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询机密文件列表
     */
    @GetMapping("/confidential")
    @ApiOperation("查询机密文件列表")
    public Result<List<RchxProjectMidlateFiles>> getConfidentialFiles() {
        try {
            List<RchxProjectMidlateFiles> files = midlateFilesService.getConfidentialFiles();
            return Result.success(files);
        } catch (Exception e) {
            log.error("查询机密文件列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询必需文件列表
     */
    @GetMapping("/required")
    @ApiOperation("查询必需文件列表")
    public Result<List<RchxProjectMidlateFiles>> getRequiredFiles() {
        try {
            List<RchxProjectMidlateFiles> files = midlateFilesService.getRequiredFiles();
            return Result.success(files);
        } catch (Exception e) {
            log.error("查询必需文件列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取文件统计信息")
    public Result<Map<String, Object>> getFileStatistics() {
        try {
            Map<String, Object> statistics = midlateFilesService.getFileStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取文件统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件类别统计
     */
    @GetMapping("/statistics/category")
    @ApiOperation("获取文件类别统计")
    public Result<List<Map<String, Object>>> getFileCategoryStatistics() {
        try {
            List<Map<String, Object>> statistics = midlateFilesService.getFileCategoryStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取文件类别统计失败", e);
            return Result.error("获取文件类别统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取业务类型文件统计
     */
    @GetMapping("/statistics/business-type")
    @ApiOperation("获取业务类型文件统计")
    public Result<List<Map<String, Object>>> getBusinessTypeFileStatistics() {
        try {
            List<Map<String, Object>> statistics = midlateFilesService.getBusinessTypeFileStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取业务类型文件统计失败", e);
            return Result.error("获取业务类型文件统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目文件统计
     */
    @GetMapping("/statistics/project")
    @ApiOperation("获取项目文件统计")
    public Result<List<Map<String, Object>>> getProjectFileStatistics() {
        try {
            List<Map<String, Object>> statistics = midlateFilesService.getProjectFileStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取项目文件统计失败", e);
            return Result.error("获取项目文件统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取上传人文件统计
     */
    @GetMapping("/statistics/uploader")
    @ApiOperation("获取上传人文件统计")
    public Result<List<Map<String, Object>>> getUploaderFileStatistics() {
        try {
            List<Map<String, Object>> statistics = midlateFilesService.getUploaderFileStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取上传人文件统计失败", e);
            return Result.error("获取上传人文件统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近上传的文件
     */
    @GetMapping("/recent")
    @ApiOperation("获取最近上传的文件")
    public Result<List<RchxProjectMidlateFiles>> getRecentUploadedFiles(
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<RchxProjectMidlateFiles> files = midlateFilesService.getRecentUploadedFiles(limit);
            return Result.success(files);
        } catch (Exception e) {
            log.error("获取最近上传的文件失败: limit={}", limit, e);
            return Result.error("获取最近上传的文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门下载文件
     */
    @GetMapping("/popular")
    @ApiOperation("获取热门下载文件")
    public Result<List<RchxProjectMidlateFiles>> getPopularDownloadFiles(
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<RchxProjectMidlateFiles> files = midlateFilesService.getPopularDownloadFiles(limit);
            return Result.success(files);
        } catch (Exception e) {
            log.error("获取热门下载文件失败: limit={}", limit, e);
            return Result.error("获取热门下载文件失败: " + e.getMessage());
        }
    }

    /**
     * 检查文件重复
     */
    @PostMapping("/check-duplicate")
    @ApiOperation("检查文件重复")
    public Result<List<RchxProjectMidlateFiles>> checkDuplicateFiles(
            @ApiParam("文件") @RequestParam("file") MultipartFile file) {
        try {
            String fileMd5 = midlateFilesService.calculateFileMd5(file);
            List<RchxProjectMidlateFiles> duplicateFiles = midlateFilesService.checkDuplicateFiles(fileMd5);
            return Result.success(duplicateFiles);
        } catch (Exception e) {
            log.error("检查文件重复失败", e);
            return Result.error("检查文件重复失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件版本历史
     */
    @GetMapping("/version-history")
    @ApiOperation("获取文件版本历史")
    public Result<List<RchxProjectMidlateFiles>> getFileVersionHistory(
            @ApiParam("业务类型") @RequestParam String businessType,
            @ApiParam("业务ID") @RequestParam Long businessId,
            @ApiParam("原始文件名") @RequestParam String originalFileName) {
        try {
            List<RchxProjectMidlateFiles> files = midlateFilesService.getFileVersionHistory(businessType, businessId,
                    originalFileName);
            return Result.success(files);
        } catch (Exception e) {
            log.error("获取文件版本历史失败: businessType={}, businessId={}, originalFileName={}",
                    businessType, businessId, originalFileName, e);
            return Result.error("获取文件版本历史失败: " + e.getMessage());
        }
    }

    /**
     * 检查文件名是否存在
     */
    @GetMapping("/exists")
    @ApiOperation("检查文件名是否存在")
    public Result<Boolean> existsByFileName(@ApiParam("文件名") @RequestParam String fileName,
            @ApiParam("业务类型") @RequestParam String businessType,
            @ApiParam("业务ID") @RequestParam Long businessId) {
        try {
            boolean exists = midlateFilesService.existsByFileName(fileName, businessType, businessId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查文件名是否存在失败: fileName={}, businessType={}, businessId={}",
                    fileName, businessType, businessId, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }
}
