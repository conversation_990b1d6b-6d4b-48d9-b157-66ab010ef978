# 项目管理系统API接口总览

## 📋 接口概览

本文档提供项目管理系统所有API接口的快速参考。

### 🔗 基础信息
- **服务地址**: `http://localhost:8080`
- **API前缀**: `/api`
- **认证方式**: Bearer Token
- **数据格式**: JSON

---

## 🏢 1. 基础字典管理

### 1.1 项目大类管理 `/api/project-categories`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| GET | `/page` | 分页查询 | current, size, categoryName, isEnabled |
| GET | `/enabled` | 获取启用的大类 | - |
| GET | `/{id}` | 根据ID查询 | id |
| GET | `/code/{categoryCode}` | 根据编码查询 | categoryCode |
| POST | `/` | 新增大类 | 请求体：大类信息 |
| PUT | `/{id}` | 更新大类 | id, 请求体：大类信息 |
| DELETE | `/{id}` | 删除大类 | id |
| PUT | `/{id}/enabled` | 启用/禁用 | id, enabled |

### 1.2 项目类别管理 `/api/project-types`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| GET | `/page` | 分页查询 | current, size, categoryId, typeName, isEnabled |
| GET | `/enabled` | 获取启用的类别 | - |
| GET | `/enabled/category/{categoryId}` | 根据大类获取类别 | categoryId |
| GET | `/{id}` | 根据ID查询 | id |
| GET | `/code/{typeCode}` | 根据编码查询 | typeCode |
| POST | `/` | 新增类别 | 请求体：类别信息 |
| PUT | `/{id}` | 更新类别 | id, 请求体：类别信息 |
| DELETE | `/{id}` | 删除类别 | id |
| PUT | `/{id}/enabled` | 启用/禁用 | id, enabled |

### 1.3 管理部门管理 `/api/departments`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| GET | `/page` | 分页查询 | current, size, deptName, isEnabled |
| GET | `/enabled` | 获取启用的部门 | - |
| GET | `/{id}` | 根据ID查询 | id |
| GET | `/code/{deptCode}` | 根据编码查询 | deptCode |
| POST | `/` | 新增部门 | 请求体：部门信息 |
| PUT | `/{id}` | 更新部门 | id, 请求体：部门信息 |
| DELETE | `/{id}` | 删除部门 | id |
| PUT | `/{id}/enabled` | 启用/禁用 | id, enabled |

---

## 📊 2. 项目业务流程管理

### 2.1 项目征集管理 `/api/project-collection`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| GET | `/page` | 分页查询 | current, size, categoryId, typeId, deptId, status, collectionName |
| GET | `/active` | 获取有效征集 | - |
| GET | `/{id}` | 根据ID查询 | id |
| GET | `/code/{collectionCode}` | 根据编号查询 | collectionCode |
| POST | `/` | 新增征集 | 请求体：征集信息 |
| PUT | `/{id}` | 更新征集 | id, 请求体：征集信息 |
| DELETE | `/{id}` | 删除征集 | id |
| PUT | `/{id}/submit` | 提交征集 | id, submitBy |
| PUT | `/{id}/review` | 审核征集 | id, approved, reviewerZgh, reviewComments |
| PUT | `/{id}/publish` | 发布征集 | id |
| PUT | `/{id}/close` | 关闭征集 | id |

### 2.2 项目申报管理 `/api/project-application`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| GET | `/page` | 分页查询 | current, size, categoryId, typeId, deptId, status, projectName, applicantZgh |
| GET | `/{id}` | 根据ID查询 | id |
| GET | `/code/{projectCode}` | 根据项目编号查询 | projectCode |
| GET | `/applicant/{applicantZgh}` | 根据申报人查询 | applicantZgh |
| POST | `/` | 新增申报 | 请求体：申报信息 |
| PUT | `/{id}` | 更新申报 | id, 请求体：申报信息 |
| DELETE | `/{id}` | 删除申报 | id |
| PUT | `/{id}/submit` | 提交申报 | id, submitBy |
| PUT | `/{id}/review` | 审核申报 | id, approved, reviewerZgh, reviewComments |
| GET | `/statistics` | 获取统计信息 | deptId, categoryId |

### 2.3 项目中检管理 `/api/project-inspection`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| GET | `/page` | 分页查询 | current, size, categoryId, typeId, deptId, status, projectCode, projectLeaderZgh |
| GET | `/{id}` | 根据ID查询 | id |
| GET | `/project/{projectCode}` | 根据项目编号查询 | projectCode |
| GET | `/leader/{projectLeaderZgh}` | 根据负责人查询 | projectLeaderZgh |
| POST | `/` | 新增中检 | 请求体：中检信息 |
| PUT | `/{id}` | 更新中检 | id, 请求体：中检信息 |
| DELETE | `/{id}` | 删除中检 | id |
| PUT | `/{id}/submit` | 提交中检 | id, submitBy |
| PUT | `/{id}/review` | 审核中检 | id, approved, reviewerZgh, reviewComments |
| GET | `/statistics` | 获取统计信息 | deptId, categoryId |

### 2.4 项目结项管理 `/api/project-completion`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| GET | `/page` | 分页查询 | current, size, categoryId, typeId, deptId, status, projectCode, projectLeaderZgh |
| GET | `/{id}` | 根据ID查询 | id |
| GET | `/project/{projectCode}` | 根据项目编号查询 | projectCode |
| GET | `/leader/{projectLeaderZgh}` | 根据负责人查询 | projectLeaderZgh |
| POST | `/` | 新增结项 | 请求体：结项信息 |
| PUT | `/{id}` | 更新结项 | id, 请求体：结项信息 |
| DELETE | `/{id}` | 删除结项 | id |
| PUT | `/{id}/submit` | 提交结项 | id, submitBy |
| PUT | `/{id}/review` | 审核结项 | id, approved, reviewerZgh, reviewComments, completionScore |
| GET | `/statistics` | 获取统计信息 | deptId, categoryId |

---

## 📁 3. 文件管理

### 3.1 项目文件管理 `/api/project-files`

| 方法 | 路径 | 说明 | 参数 |
|------|------|------|------|
| GET | `/page` | 分页查询 | current, size, businessType, businessId, projectCode, fileCategory, uploadBy |
| GET | `/{id}` | 根据ID查询 | id |
| GET | `/business/{businessType}/{businessId}` | 根据业务查询 | businessType, businessId |
| GET | `/project/{projectCode}` | 根据项目查询 | projectCode |
| GET | `/category/{businessType}/{businessId}/{fileCategory}` | 根据分类查询 | businessType, businessId, fileCategory |
| POST | `/upload` | 上传文件 | FormData: file, businessType, businessId, projectCode, fileCategory, description, uploadBy |
| DELETE | `/{id}` | 删除文件 | id, deleteBy |
| DELETE | `/batch` | 批量删除 | 请求体：{ids, deleteBy} |
| GET | `/{id}/download-url` | 获取下载URL | id |
| GET | `/statistics` | 获取统计信息 | businessType, projectCode |

---

## 📝 4. 状态枚举

### 4.1 业务状态
- **征集状态**: DRAFT(草稿), PUBLISHED(已发布), CLOSED(已关闭)
- **申报状态**: DRAFT(草稿), SUBMITTED(已提交), REVIEWING(审核中), APPROVED(已通过), REJECTED(已拒绝)
- **中检状态**: DRAFT(草稿), SUBMITTED(已提交), REVIEWING(审核中), APPROVED(已通过), REJECTED(已拒绝)
- **结项状态**: DRAFT(草稿), SUBMITTED(已提交), REVIEWING(审核中), APPROVED(已通过), REJECTED(已拒绝)

### 4.2 文件分类
- **GUIDE**: 填报指南
- **MATERIAL**: 申报材料
- **REPORT**: 报告文档
- **OTHER**: 其他文件

### 4.3 业务类型
- **COLLECTION**: 项目征集
- **APPLICATION**: 项目申报
- **INSPECTION**: 项目中检
- **COMPLETION**: 项目结项

---

## 🔧 5. 通用响应格式

### 5.1 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 5.2 错误响应
```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

### 5.3 分页响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

---

## 📞 6. 技术支持

- **开发团队**: GL开发团队
- **文档版本**: v1.0
- **最后更新**: 2024-08-04

**注意**: 详细的API使用说明和前端对接指南请参考《项目管理系统API文档.md》
