package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 科技项目基本信息
 * @TableName t_rchx_kjxmjbxx
 */
@TableName(value ="t_rchx_kjxmjbxx")
@Data
public class RchxKjxmjbxx implements Serializable {
    /**
     * 项目主键
     */
    @TableId(value = "xmid", type = IdType.INPUT)
    private String xmid;

    /**
     * 项目编号
     */
    @TableField(value = "xmbh")
    private String xmbh;

    /**
     * 项目名称
     */
    @TableField(value = "xmmc")
    private String xmmc;

    /**
     * 校内编号
     */
    @TableField(value = "xnbh")
    private String xnbh;

    /**
     * 单位号
     */
    @TableField(value = "dwh")
    private String dwh;

    /**
     * 单位名称
     */
    @TableField(value = "dwmc")
    private String dwmc;

    /**
     * 教研室编号
     */
    @TableField(value = "jysbh")
    private String jysbh;

    /**
     * 教研室名称
     */
    @TableField(value = "jysmc")
    private String jysmc;

    /**
     * 立项日期
     */
    @TableField(value = "lxrq")
    private String lxrq;

    /**
     * 开始日期
     */
    @TableField(value = "ksrq")
    private String ksrq;

    /**
     * 计划完成日期
     */
    @TableField(value = "jhwcrq")
    private String jhwcrq;

    /**
     * 结项日期
     */
    @TableField(value = "jxrq")
    private String jxrq;

    /**
     * 是否结转
     */
    @TableField(value = "sfjz")
    private String sfjz;

    /**
     * 是否结转名称
     */
    @TableField(value = "sfjzmc")
    private String sfjzmc;

    /**
     * 是否为子课题
     */
    @TableField(value = "sfwzkt")
    private String sfwzkt;

    /**
     * 是否为子课题名称
     */
    @TableField(value = "sfwzktmc")
    private String sfwzktmc;

    /**
     * 项目负责人号
     */
    @TableField(value = "xmfzrh")
    private String xmfzrh;

    /**
     * 负责人姓名
     */
    @TableField(value = "fzrxm")
    private String fzrxm;

    /**
     * 负责人类型码
     */
    @TableField(value = "fzrlxm")
    private String fzrlxm;

    /**
     * 负责人类型
     */
    @TableField(value = "fzrlx")
    private String fzrlx;

    /**
     * 负责人电话
     */
    @TableField(value = "fzrdh")
    private String fzrdh;

    /**
     * 项目委托单位
     */
    @TableField(value = "xmwtdw")
    private String xmwtdw;

    /**
     * 合作形式码
     */
    @TableField(value = "hzxsm")
    private String hzxsm;

    /**
     * 合作形式
     */
    @TableField(value = "hzxs")
    private String hzxs;

    /**
     * 委托单位类别码
     */
    @TableField(value = "wtdwlbm")
    private String wtdwlbm;

    /**
     * 委托单位类别
     */
    @TableField(value = "wtdwlb")
    private String wtdwlb;

    /**
     * 委托单位所属省市区县
     */
    @TableField(value = "wtdwssssqx")
    private String wtdwssssqx;

    /**
     * 委托单位所属省市
     */
    @TableField(value = "wtdwssss")
    private String wtdwssss;

    /**
     * 委托单位地址
     */
    @TableField(value = "wtdwdz")
    private String wtdwdz;

    /**
     * 委托单位邮编
     */
    @TableField(value = "wtdwyb")
    private String wtdwyb;

    /**
     * 委托单位电话
     */
    @TableField(value = "wtdwdh")
    private String wtdwdh;

    /**
     * 委托单位代表
     */
    @TableField(value = "wtdwdb")
    private String wtdwdb;

    /**
     * 支付方式码
     */
    @TableField(value = "zffsm")
    private String zffsm;

    /**
     * 支付方式
     */
    @TableField(value = "zffs")
    private String zffs;

    /**
     * 合作单位
     */
    @TableField(value = "hzdw")
    private String hzdw;

    /**
     * 项目来源码
     */
    @TableField(value = "xmlym")
    private String xmlym;

    /**
     * 项目来源
     */
    @TableField(value = "xmly")
    private String xmly;

    /**
     * 项目性质码
     */
    @TableField(value = "xmxzm")
    private String xmxzm;

    /**
     * 项目性质
     */
    @TableField(value = "xmxz")
    private String xmxz;

    /**
     * 项目类别码
     */
    @TableField(value = "xmlbm")
    private String xmlbm;

    /**
     * 项目类别
     */
    @TableField(value = "xmlb")
    private String xmlb;

    /**
     * 项目子类别码
     */
    @TableField(value = "xmzlbm")
    private String xmzlbm;

    /**
     * 项目子类别
     */
    @TableField(value = "xmzlb")
    private String xmzlb;

    /**
     * 项目级别码
     */
    @TableField(value = "xmjbm")
    private String xmjbm;

    /**
     * 项目级别
     */
    @TableField(value = "xmjb")
    private String xmjb;

    /**
     * 研究类别代码
     */
    @TableField(value = "yjlbdm")
    private String yjlbdm;

    /**
     * 研究类别
     */
    @TableField(value = "yjlb")
    private String yjlb;

    /**
     * 密级码
     */
    @TableField(value = "mjm")
    private String mjm;

    /**
     * 涉密级别
     */
    @TableField(value = "smjb")
    private String smjb;

    /**
     * 社会经济目标码
     */
    @TableField(value = "shjjmbm")
    private String shjjmbm;

    /**
     * 社会经济目标
     */
    @TableField(value = "shjjmb")
    private String shjjmb;

    /**
     * 国民经济行业码
     */
    @TableField(value = "gmjjhym")
    private String gmjjhym;

    /**
     * 国民经济行业
     */
    @TableField(value = "gmjjhy")
    private String gmjjhy;

    /**
     * 所属行业码
     */
    @TableField(value = "sshym")
    private String sshym;

    /**
     * 所属行业
     */
    @TableField(value = "sshy")
    private String sshy;

    /**
     * 预期研究成果及形式
     */
    @TableField(value = "yqyjcgjxs")
    private String yqyjcgjxs;

    /**
     * 学科分类码
     */
    @TableField(value = "xkflm")
    private String xkflm;

    /**
     * 学科分类
     */
    @TableField(value = "xkfl")
    private String xkfl;

    /**
     * 学科类别码
     */
    @TableField(value = "xklbm")
    private String xklbm;

    /**
     * 学科类别
     */
    @TableField(value = "xklb")
    private String xklb;

    /**
     * 项目批准号
     */
    @TableField(value = "xmpzh")
    private String xmpzh;

    /**
     * 承担单位排名
     */
    @TableField(value = "cddwpm")
    private String cddwpm;

    /**
     * 合同类型码
     */
    @TableField(value = "htlxm")
    private String htlxm;

    /**
     * 合同类型
     */
    @TableField(value = "htlx")
    private String htlx;

    /**
     * 合同编号
     */
    @TableField(value = "htbh")
    private String htbh;

    /**
     * 合同经费
     */
    @TableField(value = "htjf")
    private String htjf;

    /**
     * 配套经费
     */
    @TableField(value = "ptjf")
    private String ptjf;

    /**
     * 经费账号
     */
    @TableField(value = "jfzh")
    private String jfzh;

    /**
     * 创建人工号
     */
    @TableField(value = "cjrgh")
    private String cjrgh;

    /**
     * 创建人姓名
     */
    @TableField(value = "cjrxm")
    private String cjrxm;

    /**
     * 创建日期
     */
    @TableField(value = "cjrq")
    private String cjrq;

    /**
     * 项目执行状态码
     */
    @TableField(value = "xmzxztm")
    private String xmzxztm;

    /**
     * 项目执行状态
     */
    @TableField(value = "xmzxzt")
    private String xmzxzt;

    /**
     * 审核状态编号
     */
    @TableField(value = "shztm")
    private String shztm;

    /**
     * 审核状态
     */
    @TableField(value = "shzt")
    private String shzt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RchxKjxmjbxx other = (RchxKjxmjbxx) that;
        return (this.getXmid() == null ? other.getXmid() == null : this.getXmid().equals(other.getXmid()))
            && (this.getXmbh() == null ? other.getXmbh() == null : this.getXmbh().equals(other.getXmbh()))
            && (this.getXmmc() == null ? other.getXmmc() == null : this.getXmmc().equals(other.getXmmc()))
            && (this.getXnbh() == null ? other.getXnbh() == null : this.getXnbh().equals(other.getXnbh()))
            && (this.getDwh() == null ? other.getDwh() == null : this.getDwh().equals(other.getDwh()))
            && (this.getDwmc() == null ? other.getDwmc() == null : this.getDwmc().equals(other.getDwmc()))
            && (this.getJysbh() == null ? other.getJysbh() == null : this.getJysbh().equals(other.getJysbh()))
            && (this.getJysmc() == null ? other.getJysmc() == null : this.getJysmc().equals(other.getJysmc()))
            && (this.getLxrq() == null ? other.getLxrq() == null : this.getLxrq().equals(other.getLxrq()))
            && (this.getKsrq() == null ? other.getKsrq() == null : this.getKsrq().equals(other.getKsrq()))
            && (this.getJhwcrq() == null ? other.getJhwcrq() == null : this.getJhwcrq().equals(other.getJhwcrq()))
            && (this.getJxrq() == null ? other.getJxrq() == null : this.getJxrq().equals(other.getJxrq()))
            && (this.getSfjz() == null ? other.getSfjz() == null : this.getSfjz().equals(other.getSfjz()))
            && (this.getSfjzmc() == null ? other.getSfjzmc() == null : this.getSfjzmc().equals(other.getSfjzmc()))
            && (this.getSfwzkt() == null ? other.getSfwzkt() == null : this.getSfwzkt().equals(other.getSfwzkt()))
            && (this.getSfwzktmc() == null ? other.getSfwzktmc() == null : this.getSfwzktmc().equals(other.getSfwzktmc()))
            && (this.getXmfzrh() == null ? other.getXmfzrh() == null : this.getXmfzrh().equals(other.getXmfzrh()))
            && (this.getFzrxm() == null ? other.getFzrxm() == null : this.getFzrxm().equals(other.getFzrxm()))
            && (this.getFzrlxm() == null ? other.getFzrlxm() == null : this.getFzrlxm().equals(other.getFzrlxm()))
            && (this.getFzrlx() == null ? other.getFzrlx() == null : this.getFzrlx().equals(other.getFzrlx()))
            && (this.getFzrdh() == null ? other.getFzrdh() == null : this.getFzrdh().equals(other.getFzrdh()))
            && (this.getXmwtdw() == null ? other.getXmwtdw() == null : this.getXmwtdw().equals(other.getXmwtdw()))
            && (this.getHzxsm() == null ? other.getHzxsm() == null : this.getHzxsm().equals(other.getHzxsm()))
            && (this.getHzxs() == null ? other.getHzxs() == null : this.getHzxs().equals(other.getHzxs()))
            && (this.getWtdwlbm() == null ? other.getWtdwlbm() == null : this.getWtdwlbm().equals(other.getWtdwlbm()))
            && (this.getWtdwlb() == null ? other.getWtdwlb() == null : this.getWtdwlb().equals(other.getWtdwlb()))
            && (this.getWtdwssssqx() == null ? other.getWtdwssssqx() == null : this.getWtdwssssqx().equals(other.getWtdwssssqx()))
            && (this.getWtdwssss() == null ? other.getWtdwssss() == null : this.getWtdwssss().equals(other.getWtdwssss()))
            && (this.getWtdwdz() == null ? other.getWtdwdz() == null : this.getWtdwdz().equals(other.getWtdwdz()))
            && (this.getWtdwyb() == null ? other.getWtdwyb() == null : this.getWtdwyb().equals(other.getWtdwyb()))
            && (this.getWtdwdh() == null ? other.getWtdwdh() == null : this.getWtdwdh().equals(other.getWtdwdh()))
            && (this.getWtdwdb() == null ? other.getWtdwdb() == null : this.getWtdwdb().equals(other.getWtdwdb()))
            && (this.getZffsm() == null ? other.getZffsm() == null : this.getZffsm().equals(other.getZffsm()))
            && (this.getZffs() == null ? other.getZffs() == null : this.getZffs().equals(other.getZffs()))
            && (this.getHzdw() == null ? other.getHzdw() == null : this.getHzdw().equals(other.getHzdw()))
            && (this.getXmlym() == null ? other.getXmlym() == null : this.getXmlym().equals(other.getXmlym()))
            && (this.getXmly() == null ? other.getXmly() == null : this.getXmly().equals(other.getXmly()))
            && (this.getXmxzm() == null ? other.getXmxzm() == null : this.getXmxzm().equals(other.getXmxzm()))
            && (this.getXmxz() == null ? other.getXmxz() == null : this.getXmxz().equals(other.getXmxz()))
            && (this.getXmlbm() == null ? other.getXmlbm() == null : this.getXmlbm().equals(other.getXmlbm()))
            && (this.getXmlb() == null ? other.getXmlb() == null : this.getXmlb().equals(other.getXmlb()))
            && (this.getXmzlbm() == null ? other.getXmzlbm() == null : this.getXmzlbm().equals(other.getXmzlbm()))
            && (this.getXmzlb() == null ? other.getXmzlb() == null : this.getXmzlb().equals(other.getXmzlb()))
            && (this.getXmjbm() == null ? other.getXmjbm() == null : this.getXmjbm().equals(other.getXmjbm()))
            && (this.getXmjb() == null ? other.getXmjb() == null : this.getXmjb().equals(other.getXmjb()))
            && (this.getYjlbdm() == null ? other.getYjlbdm() == null : this.getYjlbdm().equals(other.getYjlbdm()))
            && (this.getYjlb() == null ? other.getYjlb() == null : this.getYjlb().equals(other.getYjlb()))
            && (this.getMjm() == null ? other.getMjm() == null : this.getMjm().equals(other.getMjm()))
            && (this.getSmjb() == null ? other.getSmjb() == null : this.getSmjb().equals(other.getSmjb()))
            && (this.getShjjmbm() == null ? other.getShjjmbm() == null : this.getShjjmbm().equals(other.getShjjmbm()))
            && (this.getShjjmb() == null ? other.getShjjmb() == null : this.getShjjmb().equals(other.getShjjmb()))
            && (this.getGmjjhym() == null ? other.getGmjjhym() == null : this.getGmjjhym().equals(other.getGmjjhym()))
            && (this.getGmjjhy() == null ? other.getGmjjhy() == null : this.getGmjjhy().equals(other.getGmjjhy()))
            && (this.getSshym() == null ? other.getSshym() == null : this.getSshym().equals(other.getSshym()))
            && (this.getSshy() == null ? other.getSshy() == null : this.getSshy().equals(other.getSshy()))
            && (this.getYqyjcgjxs() == null ? other.getYqyjcgjxs() == null : this.getYqyjcgjxs().equals(other.getYqyjcgjxs()))
            && (this.getXkflm() == null ? other.getXkflm() == null : this.getXkflm().equals(other.getXkflm()))
            && (this.getXkfl() == null ? other.getXkfl() == null : this.getXkfl().equals(other.getXkfl()))
            && (this.getXklbm() == null ? other.getXklbm() == null : this.getXklbm().equals(other.getXklbm()))
            && (this.getXklb() == null ? other.getXklb() == null : this.getXklb().equals(other.getXklb()))
            && (this.getXmpzh() == null ? other.getXmpzh() == null : this.getXmpzh().equals(other.getXmpzh()))
            && (this.getCddwpm() == null ? other.getCddwpm() == null : this.getCddwpm().equals(other.getCddwpm()))
            && (this.getHtlxm() == null ? other.getHtlxm() == null : this.getHtlxm().equals(other.getHtlxm()))
            && (this.getHtlx() == null ? other.getHtlx() == null : this.getHtlx().equals(other.getHtlx()))
            && (this.getHtbh() == null ? other.getHtbh() == null : this.getHtbh().equals(other.getHtbh()))
            && (this.getHtjf() == null ? other.getHtjf() == null : this.getHtjf().equals(other.getHtjf()))
            && (this.getPtjf() == null ? other.getPtjf() == null : this.getPtjf().equals(other.getPtjf()))
            && (this.getJfzh() == null ? other.getJfzh() == null : this.getJfzh().equals(other.getJfzh()))
            && (this.getCjrgh() == null ? other.getCjrgh() == null : this.getCjrgh().equals(other.getCjrgh()))
            && (this.getCjrxm() == null ? other.getCjrxm() == null : this.getCjrxm().equals(other.getCjrxm()))
            && (this.getCjrq() == null ? other.getCjrq() == null : this.getCjrq().equals(other.getCjrq()))
            && (this.getXmzxztm() == null ? other.getXmzxztm() == null : this.getXmzxztm().equals(other.getXmzxztm()))
            && (this.getXmzxzt() == null ? other.getXmzxzt() == null : this.getXmzxzt().equals(other.getXmzxzt()))
            && (this.getShztm() == null ? other.getShztm() == null : this.getShztm().equals(other.getShztm()))
            && (this.getShzt() == null ? other.getShzt() == null : this.getShzt().equals(other.getShzt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getXmid() == null) ? 0 : getXmid().hashCode());
        result = prime * result + ((getXmbh() == null) ? 0 : getXmbh().hashCode());
        result = prime * result + ((getXmmc() == null) ? 0 : getXmmc().hashCode());
        result = prime * result + ((getXnbh() == null) ? 0 : getXnbh().hashCode());
        result = prime * result + ((getDwh() == null) ? 0 : getDwh().hashCode());
        result = prime * result + ((getDwmc() == null) ? 0 : getDwmc().hashCode());
        result = prime * result + ((getJysbh() == null) ? 0 : getJysbh().hashCode());
        result = prime * result + ((getJysmc() == null) ? 0 : getJysmc().hashCode());
        result = prime * result + ((getLxrq() == null) ? 0 : getLxrq().hashCode());
        result = prime * result + ((getKsrq() == null) ? 0 : getKsrq().hashCode());
        result = prime * result + ((getJhwcrq() == null) ? 0 : getJhwcrq().hashCode());
        result = prime * result + ((getJxrq() == null) ? 0 : getJxrq().hashCode());
        result = prime * result + ((getSfjz() == null) ? 0 : getSfjz().hashCode());
        result = prime * result + ((getSfjzmc() == null) ? 0 : getSfjzmc().hashCode());
        result = prime * result + ((getSfwzkt() == null) ? 0 : getSfwzkt().hashCode());
        result = prime * result + ((getSfwzktmc() == null) ? 0 : getSfwzktmc().hashCode());
        result = prime * result + ((getXmfzrh() == null) ? 0 : getXmfzrh().hashCode());
        result = prime * result + ((getFzrxm() == null) ? 0 : getFzrxm().hashCode());
        result = prime * result + ((getFzrlxm() == null) ? 0 : getFzrlxm().hashCode());
        result = prime * result + ((getFzrlx() == null) ? 0 : getFzrlx().hashCode());
        result = prime * result + ((getFzrdh() == null) ? 0 : getFzrdh().hashCode());
        result = prime * result + ((getXmwtdw() == null) ? 0 : getXmwtdw().hashCode());
        result = prime * result + ((getHzxsm() == null) ? 0 : getHzxsm().hashCode());
        result = prime * result + ((getHzxs() == null) ? 0 : getHzxs().hashCode());
        result = prime * result + ((getWtdwlbm() == null) ? 0 : getWtdwlbm().hashCode());
        result = prime * result + ((getWtdwlb() == null) ? 0 : getWtdwlb().hashCode());
        result = prime * result + ((getWtdwssssqx() == null) ? 0 : getWtdwssssqx().hashCode());
        result = prime * result + ((getWtdwssss() == null) ? 0 : getWtdwssss().hashCode());
        result = prime * result + ((getWtdwdz() == null) ? 0 : getWtdwdz().hashCode());
        result = prime * result + ((getWtdwyb() == null) ? 0 : getWtdwyb().hashCode());
        result = prime * result + ((getWtdwdh() == null) ? 0 : getWtdwdh().hashCode());
        result = prime * result + ((getWtdwdb() == null) ? 0 : getWtdwdb().hashCode());
        result = prime * result + ((getZffsm() == null) ? 0 : getZffsm().hashCode());
        result = prime * result + ((getZffs() == null) ? 0 : getZffs().hashCode());
        result = prime * result + ((getHzdw() == null) ? 0 : getHzdw().hashCode());
        result = prime * result + ((getXmlym() == null) ? 0 : getXmlym().hashCode());
        result = prime * result + ((getXmly() == null) ? 0 : getXmly().hashCode());
        result = prime * result + ((getXmxzm() == null) ? 0 : getXmxzm().hashCode());
        result = prime * result + ((getXmxz() == null) ? 0 : getXmxz().hashCode());
        result = prime * result + ((getXmlbm() == null) ? 0 : getXmlbm().hashCode());
        result = prime * result + ((getXmlb() == null) ? 0 : getXmlb().hashCode());
        result = prime * result + ((getXmzlbm() == null) ? 0 : getXmzlbm().hashCode());
        result = prime * result + ((getXmzlb() == null) ? 0 : getXmzlb().hashCode());
        result = prime * result + ((getXmjbm() == null) ? 0 : getXmjbm().hashCode());
        result = prime * result + ((getXmjb() == null) ? 0 : getXmjb().hashCode());
        result = prime * result + ((getYjlbdm() == null) ? 0 : getYjlbdm().hashCode());
        result = prime * result + ((getYjlb() == null) ? 0 : getYjlb().hashCode());
        result = prime * result + ((getMjm() == null) ? 0 : getMjm().hashCode());
        result = prime * result + ((getSmjb() == null) ? 0 : getSmjb().hashCode());
        result = prime * result + ((getShjjmbm() == null) ? 0 : getShjjmbm().hashCode());
        result = prime * result + ((getShjjmb() == null) ? 0 : getShjjmb().hashCode());
        result = prime * result + ((getGmjjhym() == null) ? 0 : getGmjjhym().hashCode());
        result = prime * result + ((getGmjjhy() == null) ? 0 : getGmjjhy().hashCode());
        result = prime * result + ((getSshym() == null) ? 0 : getSshym().hashCode());
        result = prime * result + ((getSshy() == null) ? 0 : getSshy().hashCode());
        result = prime * result + ((getYqyjcgjxs() == null) ? 0 : getYqyjcgjxs().hashCode());
        result = prime * result + ((getXkflm() == null) ? 0 : getXkflm().hashCode());
        result = prime * result + ((getXkfl() == null) ? 0 : getXkfl().hashCode());
        result = prime * result + ((getXklbm() == null) ? 0 : getXklbm().hashCode());
        result = prime * result + ((getXklb() == null) ? 0 : getXklb().hashCode());
        result = prime * result + ((getXmpzh() == null) ? 0 : getXmpzh().hashCode());
        result = prime * result + ((getCddwpm() == null) ? 0 : getCddwpm().hashCode());
        result = prime * result + ((getHtlxm() == null) ? 0 : getHtlxm().hashCode());
        result = prime * result + ((getHtlx() == null) ? 0 : getHtlx().hashCode());
        result = prime * result + ((getHtbh() == null) ? 0 : getHtbh().hashCode());
        result = prime * result + ((getHtjf() == null) ? 0 : getHtjf().hashCode());
        result = prime * result + ((getPtjf() == null) ? 0 : getPtjf().hashCode());
        result = prime * result + ((getJfzh() == null) ? 0 : getJfzh().hashCode());
        result = prime * result + ((getCjrgh() == null) ? 0 : getCjrgh().hashCode());
        result = prime * result + ((getCjrxm() == null) ? 0 : getCjrxm().hashCode());
        result = prime * result + ((getCjrq() == null) ? 0 : getCjrq().hashCode());
        result = prime * result + ((getXmzxztm() == null) ? 0 : getXmzxztm().hashCode());
        result = prime * result + ((getXmzxzt() == null) ? 0 : getXmzxzt().hashCode());
        result = prime * result + ((getShztm() == null) ? 0 : getShztm().hashCode());
        result = prime * result + ((getShzt() == null) ? 0 : getShzt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", xmid=").append(xmid);
        sb.append(", xmbh=").append(xmbh);
        sb.append(", xmmc=").append(xmmc);
        sb.append(", xnbh=").append(xnbh);
        sb.append(", dwh=").append(dwh);
        sb.append(", dwmc=").append(dwmc);
        sb.append(", jysbh=").append(jysbh);
        sb.append(", jysmc=").append(jysmc);
        sb.append(", lxrq=").append(lxrq);
        sb.append(", ksrq=").append(ksrq);
        sb.append(", jhwcrq=").append(jhwcrq);
        sb.append(", jxrq=").append(jxrq);
        sb.append(", sfjz=").append(sfjz);
        sb.append(", sfjzmc=").append(sfjzmc);
        sb.append(", sfwzkt=").append(sfwzkt);
        sb.append(", sfwzktmc=").append(sfwzktmc);
        sb.append(", xmfzrh=").append(xmfzrh);
        sb.append(", fzrxm=").append(fzrxm);
        sb.append(", fzrlxm=").append(fzrlxm);
        sb.append(", fzrlx=").append(fzrlx);
        sb.append(", fzrdh=").append(fzrdh);
        sb.append(", xmwtdw=").append(xmwtdw);
        sb.append(", hzxsm=").append(hzxsm);
        sb.append(", hzxs=").append(hzxs);
        sb.append(", wtdwlbm=").append(wtdwlbm);
        sb.append(", wtdwlb=").append(wtdwlb);
        sb.append(", wtdwssssqx=").append(wtdwssssqx);
        sb.append(", wtdwssss=").append(wtdwssss);
        sb.append(", wtdwdz=").append(wtdwdz);
        sb.append(", wtdwyb=").append(wtdwyb);
        sb.append(", wtdwdh=").append(wtdwdh);
        sb.append(", wtdwdb=").append(wtdwdb);
        sb.append(", zffsm=").append(zffsm);
        sb.append(", zffs=").append(zffs);
        sb.append(", hzdw=").append(hzdw);
        sb.append(", xmlym=").append(xmlym);
        sb.append(", xmly=").append(xmly);
        sb.append(", xmxzm=").append(xmxzm);
        sb.append(", xmxz=").append(xmxz);
        sb.append(", xmlbm=").append(xmlbm);
        sb.append(", xmlb=").append(xmlb);
        sb.append(", xmzlbm=").append(xmzlbm);
        sb.append(", xmzlb=").append(xmzlb);
        sb.append(", xmjbm=").append(xmjbm);
        sb.append(", xmjb=").append(xmjb);
        sb.append(", yjlbdm=").append(yjlbdm);
        sb.append(", yjlb=").append(yjlb);
        sb.append(", mjm=").append(mjm);
        sb.append(", smjb=").append(smjb);
        sb.append(", shjjmbm=").append(shjjmbm);
        sb.append(", shjjmb=").append(shjjmb);
        sb.append(", gmjjhym=").append(gmjjhym);
        sb.append(", gmjjhy=").append(gmjjhy);
        sb.append(", sshym=").append(sshym);
        sb.append(", sshy=").append(sshy);
        sb.append(", yqyjcgjxs=").append(yqyjcgjxs);
        sb.append(", xkflm=").append(xkflm);
        sb.append(", xkfl=").append(xkfl);
        sb.append(", xklbm=").append(xklbm);
        sb.append(", xklb=").append(xklb);
        sb.append(", xmpzh=").append(xmpzh);
        sb.append(", cddwpm=").append(cddwpm);
        sb.append(", htlxm=").append(htlxm);
        sb.append(", htlx=").append(htlx);
        sb.append(", htbh=").append(htbh);
        sb.append(", htjf=").append(htjf);
        sb.append(", ptjf=").append(ptjf);
        sb.append(", jfzh=").append(jfzh);
        sb.append(", cjrgh=").append(cjrgh);
        sb.append(", cjrxm=").append(cjrxm);
        sb.append(", cjrq=").append(cjrq);
        sb.append(", xmzxztm=").append(xmzxztm);
        sb.append(", xmzxzt=").append(xmzxzt);
        sb.append(", shztm=").append(shztm);
        sb.append(", shzt=").append(shzt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}