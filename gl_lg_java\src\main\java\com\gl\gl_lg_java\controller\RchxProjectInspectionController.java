package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectInspection;
import com.gl.gl_lg_java.service.RchxProjectInspectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目中检表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@RestController
@RequestMapping("/api/project-inspection")
public class RchxProjectInspectionController {

    @Autowired
    private RchxProjectInspectionService projectInspectionService;

    /**
     * 分页查询项目中检
     */
    @GetMapping("/page")
    public Result<IPage<RchxProjectInspection>> page(@RequestParam(defaultValue = "1") Integer current,
                                                    @RequestParam(defaultValue = "10") Integer size,
                                                    @RequestParam(required = false) Integer categoryId,
                                                    @RequestParam(required = false) Integer typeId,
                                                    @RequestParam(required = false) Integer deptId,
                                                    @RequestParam(required = false) String status,
                                                    @RequestParam(required = false) String projectCode,
                                                    @RequestParam(required = false) String projectLeaderZgh) {
        try {
            Page<RchxProjectInspection> page = new Page<>(current, size);
            IPage<RchxProjectInspection> result = projectInspectionService.pageQuery(page, categoryId, typeId, deptId, status, projectCode, projectLeaderZgh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目中检失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目中检
     */
    @GetMapping("/{id}")
    public Result<RchxProjectInspection> getById(@PathVariable Long id) {
        try {
            RchxProjectInspection inspection = projectInspectionService.getById(id);
            if (inspection == null) {
                return Result.error("项目中检不存在");
            }
            return Result.success(inspection);
        } catch (Exception e) {
            log.error("查询项目中检失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目编号获取中检信息
     */
    @GetMapping("/project/{projectCode}")
    public Result<List<RchxProjectInspection>> getByProjectCode(@PathVariable String projectCode) {
        try {
            List<RchxProjectInspection> inspections = projectInspectionService.getByProjectCode(projectCode);
            return Result.success(inspections);
        } catch (Exception e) {
            log.error("根据项目编号查询中检失败: projectCode={}", projectCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目负责人获取中检列表
     */
    @GetMapping("/leader/{projectLeaderZgh}")
    public Result<List<RchxProjectInspection>> getByProjectLeader(@PathVariable String projectLeaderZgh) {
        try {
            List<RchxProjectInspection> inspections = projectInspectionService.getByProjectLeader(projectLeaderZgh);
            return Result.success(inspections);
        } catch (Exception e) {
            log.error("根据项目负责人查询中检失败: projectLeaderZgh={}", projectLeaderZgh, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目中检
     */
    @PostMapping
    public Result<String> save(@RequestBody RchxProjectInspection inspection) {
        try {
            boolean success = projectInspectionService.save(inspection);
            return success ? Result.success("新增成功") : Result.error("新增失败");
        } catch (Exception e) {
            log.error("新增项目中检失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目中检
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Long id, @RequestBody RchxProjectInspection inspection) {
        try {
            inspection.setId(id);
            boolean success = projectInspectionService.updateById(inspection);
            return success ? Result.success("更新成功") : Result.error("更新失败");
        } catch (Exception e) {
            log.error("更新项目中检失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目中检
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        try {
            boolean success = projectInspectionService.removeById(id);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除项目中检失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 提交中检
     */
    @PutMapping("/{id}/submit")
    public Result<String> submitInspection(@PathVariable Long id, @RequestParam String submitBy) {
        try {
            boolean success = projectInspectionService.submitInspection(id, submitBy);
            return success ? Result.success("提交成功") : Result.error("提交失败");
        } catch (Exception e) {
            log.error("提交中检失败: id={}, submitBy={}", id, submitBy, e);
            return Result.error("提交失败: " + e.getMessage());
        }
    }

    /**
     * 审核中检
     */
    @PutMapping("/{id}/review")
    public Result<String> reviewInspection(@PathVariable Long id, 
                                          @RequestParam boolean approved,
                                          @RequestParam String reviewerZgh,
                                          @RequestParam(required = false) String reviewComments) {
        try {
            boolean success = projectInspectionService.reviewInspection(id, approved, reviewerZgh, reviewComments);
            return success ? Result.success("审核成功") : Result.error("审核失败");
        } catch (Exception e) {
            log.error("审核中检失败: id={}, approved={}, reviewerZgh={}", id, approved, reviewerZgh, e);
            return Result.error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 获取中检统计信息
     */
    @GetMapping("/statistics")
    public Result<Object> getInspectionStatistics(@RequestParam(required = false) Integer deptId,
                                                  @RequestParam(required = false) Integer categoryId) {
        try {
            Object statistics = projectInspectionService.getInspectionStatistics(deptId, categoryId);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取中检统计信息失败: deptId={}, categoryId={}", deptId, categoryId, e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }
}
