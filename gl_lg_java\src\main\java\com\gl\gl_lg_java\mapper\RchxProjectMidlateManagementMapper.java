package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.domain.RchxProjectMidlateManagement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 项目中后期管理Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Mapper
public interface RchxProjectMidlateManagementMapper extends BaseMapper<RchxProjectMidlateManagement> {

    /**
     * 分页查询项目中后期管理信息（包含关联信息）
     */
    @Select({
        "<script>",
        "SELECT pml.*, ",
        "pc.category_name AS categoryName, ",
        "pt.type_name AS typeName, ",
        "d.dept_name AS deptName ",
        "FROM t_rchx_project_midlate_management pml ",
        "LEFT JOIN t_rchx_project_categories pc ON pml.category_id = pc.id ",
        "LEFT JOIN t_rchx_project_types pt ON pml.type_id = pt.id ",
        "LEFT JOIN t_rchx_departments d ON pml.dept_id = d.id ",
        "WHERE pml.is_active = 1 ",
        "<if test='projectCode != null and projectCode != \"\"'>",
        "AND pml.project_code LIKE CONCAT('%', #{projectCode}, '%') ",
        "</if>",
        "<if test='projectName != null and projectName != \"\"'>",
        "AND pml.project_name LIKE CONCAT('%', #{projectName}, '%') ",
        "</if>",
        "<if test='projectStatus != null and projectStatus != \"\"'>",
        "AND pml.project_status = #{projectStatus} ",
        "</if>",
        "<if test='categoryId != null'>",
        "AND pml.category_id = #{categoryId} ",
        "</if>",
        "<if test='typeId != null'>",
        "AND pml.type_id = #{typeId} ",
        "</if>",
        "<if test='deptId != null'>",
        "AND pml.dept_id = #{deptId} ",
        "</if>",
        "<if test='projectLeaderZgh != null and projectLeaderZgh != \"\"'>",
        "AND pml.project_leader_zgh = #{projectLeaderZgh} ",
        "</if>",
        "<if test='riskLevel != null and riskLevel != \"\"'>",
        "AND pml.risk_level = #{riskLevel} ",
        "</if>",
        "ORDER BY pml.create_time DESC",
        "</script>"
    })
    IPage<Map<String, Object>> selectPageWithDetails(
            Page<RchxProjectMidlateManagement> page,
            @Param("projectCode") String projectCode,
            @Param("projectName") String projectName,
            @Param("projectStatus") String projectStatus,
            @Param("categoryId") Integer categoryId,
            @Param("typeId") Integer typeId,
            @Param("deptId") Integer deptId,
            @Param("projectLeaderZgh") String projectLeaderZgh,
            @Param("riskLevel") String riskLevel
    );

    /**
     * 根据项目负责人查询项目列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_management " +
            "WHERE project_leader_zgh = #{projectLeaderZgh} AND is_active = 1 " +
            "ORDER BY create_time DESC")
    List<RchxProjectMidlateManagement> selectByProjectLeader(@Param("projectLeaderZgh") String projectLeaderZgh);

    /**
     * 根据部门查询项目列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_management " +
            "WHERE dept_id = #{deptId} AND is_active = 1 " +
            "ORDER BY create_time DESC")
    List<RchxProjectMidlateManagement> selectByDepartment(@Param("deptId") Integer deptId);

    /**
     * 根据项目状态查询项目列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_management " +
            "WHERE project_status = #{projectStatus} AND is_active = 1 " +
            "ORDER BY create_time DESC")
    List<RchxProjectMidlateManagement> selectByStatus(@Param("projectStatus") String projectStatus);

    /**
     * 更新项目进度
     */
    @Update("UPDATE t_rchx_project_midlate_management SET " +
            "progress_percentage = #{progressPercentage}, " +
            "progress_description = #{progressDescription}, " +
            "last_update_time = #{lastUpdateTime}, " +
            "last_update_by = #{lastUpdateBy}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int updateProgress(@Param("id") Long id,
                      @Param("progressPercentage") BigDecimal progressPercentage,
                      @Param("progressDescription") String progressDescription,
                      @Param("lastUpdateTime") LocalDateTime lastUpdateTime,
                      @Param("lastUpdateBy") String lastUpdateBy,
                      @Param("updateBy") String updateBy);

    /**
     * 更新项目状态
     */
    @Update("UPDATE t_rchx_project_midlate_management SET " +
            "project_status = #{projectStatus}, " +
            "last_update_time = #{lastUpdateTime}, " +
            "last_update_by = #{lastUpdateBy}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int updateStatus(@Param("id") Long id,
                    @Param("projectStatus") String projectStatus,
                    @Param("lastUpdateTime") LocalDateTime lastUpdateTime,
                    @Param("lastUpdateBy") String lastUpdateBy,
                    @Param("updateBy") String updateBy);

    /**
     * 更新风险等级
     */
    @Update("UPDATE t_rchx_project_midlate_management SET " +
            "risk_level = #{riskLevel}, " +
            "risk_description = #{riskDescription}, " +
            "last_update_time = #{lastUpdateTime}, " +
            "last_update_by = #{lastUpdateBy}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int updateRiskLevel(@Param("id") Long id,
                       @Param("riskLevel") String riskLevel,
                       @Param("riskDescription") String riskDescription,
                       @Param("lastUpdateTime") LocalDateTime lastUpdateTime,
                       @Param("lastUpdateBy") String lastUpdateBy,
                       @Param("updateBy") String updateBy);

    /**
     * 获取项目统计信息
     */
    @Select({
        "SELECT ",
        "COUNT(*) AS totalProjects, ",
        "SUM(CASE WHEN project_status = 'ONGOING' THEN 1 ELSE 0 END) AS ongoingProjects, ",
        "SUM(CASE WHEN project_status = 'COMPLETED' THEN 1 ELSE 0 END) AS completedProjects, ",
        "SUM(CASE WHEN project_status = 'MIDTERM_REVIEW' THEN 1 ELSE 0 END) AS midtermReviewProjects, ",
        "SUM(CASE WHEN project_status = 'FINAL_REVIEW' THEN 1 ELSE 0 END) AS finalReviewProjects, ",
        "AVG(progress_percentage) AS avgProgress, ",
        "SUM(project_budget) AS totalBudget, ",
        "COUNT(DISTINCT project_leader_zgh) AS totalLeaders ",
        "FROM t_rchx_project_midlate_management ",
        "WHERE is_active = 1"
    })
    Map<String, Object> getProjectStatistics();

    /**
     * 获取部门项目统计
     */
    @Select({
        "SELECT ",
        "d.dept_name AS deptName, ",
        "COUNT(*) AS projectCount, ",
        "SUM(CASE WHEN pml.project_status = 'ONGOING' THEN 1 ELSE 0 END) AS ongoingCount, ",
        "SUM(CASE WHEN pml.project_status = 'COMPLETED' THEN 1 ELSE 0 END) AS completedCount, ",
        "AVG(pml.progress_percentage) AS avgProgress, ",
        "SUM(pml.project_budget) AS totalBudget ",
        "FROM t_rchx_project_midlate_management pml ",
        "LEFT JOIN t_rchx_departments d ON pml.dept_id = d.id ",
        "WHERE pml.is_active = 1 ",
        "GROUP BY pml.dept_id, d.dept_name ",
        "ORDER BY projectCount DESC"
    })
    List<Map<String, Object>> getDepartmentStatistics();

    /**
     * 获取风险项目列表
     */
    @Select("SELECT * FROM t_rchx_project_midlate_management " +
            "WHERE risk_level IN ('HIGH', 'CRITICAL') AND is_active = 1 " +
            "ORDER BY FIELD(risk_level, 'CRITICAL', 'HIGH'), create_time DESC")
    List<RchxProjectMidlateManagement> getHighRiskProjects();

    /**
     * 获取即将到期的项目
     */
    @Select("SELECT * FROM t_rchx_project_midlate_management " +
            "WHERE project_end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL #{days} DAY) " +
            "AND project_status NOT IN ('COMPLETED', 'TERMINATED') " +
            "AND is_active = 1 " +
            "ORDER BY project_end_date ASC")
    List<RchxProjectMidlateManagement> getProjectsNearDeadline(@Param("days") Integer days);
}
