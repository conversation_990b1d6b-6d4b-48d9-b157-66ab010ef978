package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxHjcgjbxx;
import com.gl.gl_lg_java.dto.HjcgjbxxQueryDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_hjcgjbxx(获奖成果基本信息)】的数据库操作Service
 * @createDate 2025-07-18 11:11:41
 */
public interface RchxHjcgjbxxService extends IService<RchxHjcgjbxx> {

    /**
     * 根据获奖成果编号查询
     */
    RchxHjcgjbxx getByHjcgbh(String hjcgbh);

    /**
     * 根据获奖成果名称模糊查询
     */
    List<RchxHjcgjbxx> listByHjcgmcLike(String hjcgmc);

    /**
     * 根据第一完成人职工号查询
     */
    List<RchxHjcgjbxx> listByDywcrzgh(String dywcrzgh);

    /**
     * 根据第一完成人姓名模糊查询
     */
    List<RchxHjcgjbxx> listByDywcrxmLike(String dywcrxm);

    /**
     * 根据奖励名称模糊查询
     */
    List<RchxHjcgjbxx> listByJlmcLike(String jlmc);

    /**
     * 根据审核状态查询
     */
    List<RchxHjcgjbxx> listByShzt(String shzt);

    /**
     * 根据获奖级别查询
     */
    List<RchxHjcgjbxx> listByHjjb(String hjjb);

    /**
     * 根据奖励等级查询
     */
    List<RchxHjcgjbxx> listByJldj(String jldj);

    /**
     * 根据获奖日期范围查询
     */
    List<RchxHjcgjbxx> listByHjrqBetween(String startDate, String endDate);

    /**
     * 多条件查询
     */
    List<RchxHjcgjbxx> listByMultiConditions(HjcgjbxxQueryDTO queryDTO);

    /**
     * 分页多条件查询
     */
    IPage<RchxHjcgjbxx> pageByMultiConditions(HjcgjbxxQueryDTO queryDTO);

    /**
     * 新增获奖成果信息
     */
    boolean saveHjcgjbxx(RchxHjcgjbxx hjcgjbxx);

    /**
     * 根据编号更新获奖成果信息
     */
    boolean updateByHjcgbh(RchxHjcgjbxx hjcgjbxx);

    /**
     * 根据编号删除获奖成果信息
     */
    boolean removeByHjcgbh(String hjcgbh);

    /**
     * 批量删除获奖成果信息
     */
    boolean removeBatchByHjcgbhs(List<String> hjcgbhs);

    /**
     * 批量新增获奖成果信息
     */
    boolean saveBatchHjcgjbxx(List<RchxHjcgjbxx> hjcgjbxxList);

    /**
     * 批量更新获奖成果信息
     */
    boolean updateBatchHjcgjbxx(List<RchxHjcgjbxx> hjcgjbxxList);

    /**
     * 获取获奖成果总数统计
     */
    Map<String, Object> getTotalCount();

    /**
     * 按审核状态统计
     */
    Map<String, Integer> getStatsByShzt();

    /**
     * 按教研室名称统计
     */
    Map<String, Integer> getStatsByJysmc();

    /**
     * 按颁奖单位统计
     */
    Map<String, Integer> getStatsByBjdw();

    /**
     * 按成果形式统计
     */
    Map<String, Integer> getStatsByCgxs();

    /**
     * 按获奖级别统计
     */
    Map<String, Integer> getStatsByHjjb();

    /**
     * 按奖励等级统计
     */
    Map<String, Integer> getStatsByJldj();

    /**
     * 按单位名称统计
     */
    Map<String, Integer> getStatsByDwmc();

    /**
     * 获取奖励等级选项列表（用于前端筛选）
     */
    List<Map<String, Object>> getJldjOptions();

    /**
     * 获取获奖级别选项列表（用于前端筛选）
     */
    List<Map<String, Object>> getHjjbOptions();

    /**
     * 获取审核状态选项列表（用于前端筛选）
     */
    List<Map<String, Object>> getShztOptions();

    // ========== 人才匹配算法专用方法 ==========

    /**
     * 检查教师是否有省部级二等奖及以上（排名第1）
     */
    boolean hasProvincialSecondPrizeOrAbove(String teacherZgh);

    /**
     * 检查教师是否有省部级三等奖及以上
     */
    boolean hasProvincialThirdPrizeOrAbove(String teacherZgh);

    /**
     * 检查教师是否有国家级奖励
     */
    boolean hasNationalAwards(String teacherZgh);

    /**
     * 统计教师获奖数量（按级别）
     */
    Map<String, Integer> countAwardsByLevel(String teacherZgh);
}
