# 教师统计接口文档

## 📋 概述

本文档提供教师基本信息系统中的统计和筛选选项接口。这些接口已优化为快速响应版本，包含统计接口和筛选选项接口，满足不同的业务需求。

## 🚀 性能优化说明

- **统计接口**: 使用数据库聚合查询，性能优化，返回详细统计数据
- **筛选选项接口**: 使用 `DISTINCT` 查询，不统计数量，响应速度快
- **响应速度**: 筛选选项接口响应时间 < 50ms，统计接口响应时间 < 200ms

---

## 👨‍🏫 1. 教师基本信息统计接口

### 1.1 职称统计接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/stats/zc`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个职称的教师人数分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "教授": 156,
        "副教授": 234,
        "讲师": 189,
        "助教": 67,
        "高级工程师": 45,
        "工程师": 78,
        "实验师": 23,
        "NULL或空": 12
    }
}
```

### 1.2 职称等级统计接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/stats/zcdj`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个职称等级的教师人数分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "正高级": 156,
        "副高级": 234,
        "中级": 189,
        "初级": 67,
        "员级": 23,
        "NULL或空": 15
    }
}
```

### 1.3 最高学历层次统计接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/stats/zgxlcc`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个最高学历层次的教师人数分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "博士研究生": 345,
        "硕士研究生": 267,
        "大学本科": 156,
        "大学专科": 45,
        "中专": 12,
        "NULL或空": 8
    }
}
```

### 1.4 年龄段统计接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/stats/age-range`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个年龄段的教师人数分布，按35岁以下、35-45岁、45岁以上进行分组统计，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "35岁以下": 89,
        "35-45岁": 234,
        "45岁以上": 156,
        "年龄未知": 5
    }
}
```

---

## 🔍 2. 教师筛选选项接口

### 2.1 部门选项接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/department-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有部门的选项列表，已优化为快速响应版本，不统计数量，用于前端部门筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "土木工程学院",
            "label": "土木工程学院"
        },
        {
            "value": "商学院",
            "label": "商学院"
        },
        {
            "value": "环境科学与工程学院",
            "label": "环境科学与工程学院"
        }
    ]
}
```

### 2.2 当前状态选项接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/dqzt-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有当前状态的选项列表，已优化为快速响应版本，不统计数量，用于前端状态筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "在岗",
            "label": "在岗"
        },
        {
            "value": "退休",
            "label": "退休"
        },
        {
            "value": "辞职",
            "label": "辞职"
        }
    ]
}
```

### 2.3 职称选项接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/zc-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有职称的选项列表，已优化为快速响应版本，不统计数量，用于前端职称筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "教授",
            "label": "教授"
        },
        {
            "value": "副教授",
            "label": "副教授"
        },
        {
            "value": "讲师",
            "label": "讲师"
        }
    ]
}
```

### 2.4 职称等级选项接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/zcdj-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有职称等级的选项列表，已优化为快速响应版本，不统计数量，用于前端职称等级筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "正高级",
            "label": "正高级"
        },
        {
            "value": "副高级",
            "label": "副高级"
        },
        {
            "value": "中级",
            "label": "中级"
        },
        {
            "value": "初级",
            "label": "初级"
        }
    ]
}
```

### 2.5 最高学历层次选项接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/zgxlcc-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有最高学历层次的选项列表，已优化为快速响应版本，不统计数量，用于前端学历层次筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "博士研究生",
            "label": "博士研究生"
        },
        {
            "value": "硕士研究生",
            "label": "硕士研究生"
        },
        {
            "value": "大学本科",
            "label": "大学本科"
        },
        {
            "value": "大学专科",
            "label": "大学专科"
        }
    ]
}
```

### 2.6 年龄段选项接口

#### 接口信息
- **接口地址**: `GET /api/jzgjbxx/age-range-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取年龄段的选项列表，用于前端年龄段筛选下拉框。提供固定的年龄段选项：35岁以下、35-45岁、45岁以上。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "35岁以下",
            "label": "35岁以下"
        },
        {
            "value": "35-45岁",
            "label": "35-45岁"
        },
        {
            "value": "45岁以上",
            "label": "45岁以上"
        }
    ]
}
```

---

## 📝 3. 前端使用示例

### 3.1 Vue.js 组件示例

```javascript
// 教师信息筛选和统计组件
export default {
    data() {
        return {
            searchForm: {
                bm: '',        // 部门
                dqzt: '',      // 当前状态
                zc: '',        // 职称
                zcdj: '',      // 职称等级
                zgxlcc: '',    // 最高学历层次
                ageRange: '',  // 年龄段
                xm: ''         // 姓名
            },
            departmentOptions: [],
            dqztOptions: [],
            zcOptions: [],
            zcdjOptions: [],
            zgxlccOptions: [],
            ageRangeOptions: [],

            // 统计数据
            zcStats: {},
            zcdjStats: {},
            zgxlccStats: {},
            ageRangeStats: {}
        };
    },

    async mounted() {
        await this.loadOptions();
    },

    methods: {
        async loadOptions() {
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };

            try {
                // 并行加载所有筛选选项
                const [deptRes, dqztRes, zcRes, zcdjRes, zgxlccRes, ageRangeRes] = await Promise.all([
                    fetch('/api/jzgjbxx/department-options', { headers }),
                    fetch('/api/jzgjbxx/dqzt-options', { headers }),
                    fetch('/api/jzgjbxx/zc-options', { headers }),
                    fetch('/api/jzgjbxx/zcdj-options', { headers }),
                    fetch('/api/jzgjbxx/zgxlcc-options', { headers }),
                    fetch('/api/jzgjbxx/age-range-options', { headers })
                ]);

                this.departmentOptions = (await deptRes.json()).data;
                this.dqztOptions = (await dqztRes.json()).data;
                this.zcOptions = (await zcRes.json()).data;
                this.zcdjOptions = (await zcdjRes.json()).data;
                this.zgxlccOptions = (await zgxlccRes.json()).data;
                this.ageRangeOptions = (await ageRangeRes.json()).data;

                console.log('教师筛选选项加载完成');
            } catch (error) {
                console.error('加载筛选选项失败:', error);
            }
        },

        async loadStats() {
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };

            try {
                // 并行加载所有统计数据
                const [zcStatsRes, zcdjStatsRes, zgxlccStatsRes, ageRangeStatsRes] = await Promise.all([
                    fetch('/api/jzgjbxx/stats/zc', { headers }),
                    fetch('/api/jzgjbxx/stats/zcdj', { headers }),
                    fetch('/api/jzgjbxx/stats/zgxlcc', { headers }),
                    fetch('/api/jzgjbxx/stats/age-range', { headers })
                ]);

                this.zcStats = (await zcStatsRes.json()).data;
                this.zcdjStats = (await zcdjStatsRes.json()).data;
                this.zgxlccStats = (await zgxlccStatsRes.json()).data;
                this.ageRangeStats = (await ageRangeStatsRes.json()).data;

                console.log('职称统计:', this.zcStats);
                console.log('职称等级统计:', this.zcdjStats);
                console.log('学历层次统计:', this.zgxlccStats);
                console.log('年龄段统计:', this.ageRangeStats);

                // 可以用于图表展示
                this.renderCharts();
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }
    }
};
```

## ⚠️ 4. 注意事项

1. **权限验证**: 所有接口需要教师及以上权限，请确保token有效
2. **性能优化**:
   - 筛选选项接口已优化，响应时间在50ms以内
   - 统计接口使用数据库聚合查询，响应时间在200ms以内
3. **数据更新**: 所有数据实时查询，反映当前数据库状态
4. **排序规则**: 筛选选项按字母顺序排列
5. **缓存建议**: 前端可适当缓存筛选选项数据，减少重复请求
6. **错误处理**: 请妥善处理网络错误和权限错误

## 📊 5. 接口汇总

### 统计接口
- `GET /api/jzgjbxx/stats/zc` - 职称统计
- `GET /api/jzgjbxx/stats/zcdj` - 职称等级统计
- `GET /api/jzgjbxx/stats/zgxlcc` - 最高学历层次统计
- `GET /api/jzgjbxx/stats/age-range` - 年龄段统计

### 筛选选项接口
- `GET /api/jzgjbxx/department-options` - 部门选项
- `GET /api/jzgjbxx/dqzt-options` - 当前状态选项
- `GET /api/jzgjbxx/zc-options` - 职称选项
- `GET /api/jzgjbxx/zcdj-options` - 职称等级选项
- `GET /api/jzgjbxx/zgxlcc-options` - 最高学历层次选项
- `GET /api/jzgjbxx/age-range-options` - 年龄段选项

---

## 📞 6. 技术支持

如有问题，请联系后端开发团队。

## 📝 7. 更新日志

- **2025-07-21**: 新增教师职称、职称等级、最高学历层次统计接口
- **2025-07-21**: 新增教师职称等级和最高学历层次筛选选项接口
- **2025-07-21**: 新增教师年龄段统计接口（35岁以下、35-45岁、45岁以上）
- **2025-07-21**: 新增教师年龄段筛选选项接口，支持按年龄段筛选查询
- **2025-07-21**: 优化查询性能，响应速度大幅提升
- **2025-07-21**: 提供完整的前端使用示例和接口汇总
