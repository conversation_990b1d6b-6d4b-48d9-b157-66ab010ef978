package com.gl.gl_lg_java.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限验证注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePermission {
    
    /**
     * 需要的权限
     * 可选值：教师、评审、学院管理员、系统管理员
     */
    String value();
    
    /**
     * 是否需要精确匹配权限（默认false，表示只要权限级别够就行）
     */
    boolean exact() default false;
}
