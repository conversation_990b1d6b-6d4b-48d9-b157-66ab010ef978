package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxNotificationTypes;
import com.gl.gl_lg_java.mapper.NotificationTypeMapper;
import com.gl.gl_lg_java.service.RchxNotificationTypesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_notification_types(通知类型表)】的数据库操作Service实现
 * @createDate 2025-07-24 13:00:00
 */
@Service
@Slf4j
public class RchxNotificationTypesServiceImpl extends ServiceImpl<NotificationTypeMapper, RchxNotificationTypes>
        implements RchxNotificationTypesService {

    @Autowired
    private NotificationTypeMapper notificationTypeMapper;

    @Override
    public List<RchxNotificationTypes> getAllNotificationTypes() {
        try {
            List<RchxNotificationTypes> types = notificationTypeMapper.selectAllTypes();
            log.debug("获取所有通知类型: count={}", types.size());
            return types;
        } catch (Exception e) {
            log.error("获取所有通知类型失败: {}", e.getMessage());
            throw new RuntimeException("获取通知类型失败", e);
        }
    }

    @Override
    public RchxNotificationTypes getNotificationTypeByCode(String typeCode) {
        try {
            RchxNotificationTypes type = notificationTypeMapper.selectByTypeCode(typeCode);
            log.debug("根据类型编码获取通知类型: typeCode={}, found={}", typeCode, type != null);
            return type;
        } catch (Exception e) {
            log.error("根据类型编码获取通知类型失败: typeCode={}, error={}", typeCode, e.getMessage());
            return null;
        }
    }
}
