package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxNationalArchiveFiles;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_national_archive_files(国家/省部级项目归档文件表)】的数据库操作Service
 * @createDate 2025-01-04 10:00:00
 */
public interface RchxNationalArchiveFilesService extends IService<RchxNationalArchiveFiles> {

    /**
     * 根据归档ID查询文件
     */
    List<RchxNationalArchiveFiles> listByArchiveId(Long archiveId);

    /**
     * 根据项目编号查询文件
     */
    List<RchxNationalArchiveFiles> listByProjectCode(String projectCode);

    /**
     * 根据文件类别查询
     */
    List<RchxNationalArchiveFiles> listByFileCategory(String fileCategory);

    /**
     * 根据归档ID和文件类别查询
     */
    List<RchxNationalArchiveFiles> listByArchiveIdAndCategory(Long archiveId, String fileCategory);

    /**
     * 根据文件名模糊查询
     */
    List<RchxNationalArchiveFiles> listByFileNameLike(String fileName);

    /**
     * 根据上传人查询
     */
    List<RchxNationalArchiveFiles> listByUploadBy(String uploadBy);

    /**
     * 根据上传时间范围查询
     */
    List<RchxNationalArchiveFiles> listByUploadTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询必需文件
     */
    List<RchxNationalArchiveFiles> listRequiredFiles();

    /**
     * 查询机密文件
     */
    List<RchxNationalArchiveFiles> listConfidentialFiles();

    /**
     * 根据MinIO存储信息查询
     */
    RchxNationalArchiveFiles getByMinioInfo(String bucketName, String objectName);

    /**
     * 根据文件MD5查询（用于重复文件检测）
     */
    List<RchxNationalArchiveFiles> listByFileMd5(String fileMd5);

    /**
     * 统计各文件类别的数量
     */
    List<Map<String, Object>> countByFileCategory();

    /**
     * 统计各归档项目的文件数量
     */
    List<Map<String, Object>> countByArchiveId();

    /**
     * 获取文件存储统计信息
     */
    Map<String, Object> getFileStatistics();

    /**
     * 上传文件到MinIO并保存记录
     */
    RchxNationalArchiveFiles uploadFile(Long archiveId, String projectCode, String fileCategory, 
                                       MultipartFile file, String uploadBy, String uploadByName, 
                                       String description, Boolean isRequired, Boolean isConfidential);

    /**
     * 批量上传文件
     */
    List<RchxNationalArchiveFiles> batchUploadFiles(Long archiveId, String projectCode, String fileCategory,
                                                   List<MultipartFile> files, String uploadBy, String uploadByName,
                                                   Boolean isRequired, Boolean isConfidential);

    /**
     * 下载文件（更新下载统计）
     */
    byte[] downloadFile(Long fileId);

    /**
     * 获取文件下载URL
     */
    String getFileDownloadUrl(Long fileId);

    /**
     * 逻辑删除文件
     */
    boolean deleteFile(Long fileId, String deleteBy);

    /**
     * 批量逻辑删除文件
     */
    boolean batchDeleteFiles(List<Long> fileIds, String deleteBy);

    /**
     * 物理删除文件（从MinIO和数据库中删除）
     */
    boolean physicalDeleteFile(Long fileId);

    /**
     * 更新文件信息
     */
    boolean updateFileInfo(Long fileId, String fileName, String description, Boolean isRequired, Boolean isConfidential);

    /**
     * 检查文件是否重复（基于MD5）
     */
    boolean isFileDuplicate(String fileMd5);

    /**
     * 验证文件类型是否允许
     */
    boolean isFileTypeAllowed(String fileExtension);

    /**
     * 验证文件大小是否符合限制
     */
    boolean isFileSizeAllowed(Long fileSize);

    /**
     * 获取归档项目的文件完整性检查
     */
    Map<String, Object> checkArchiveFileCompleteness(Long archiveId);

    /**
     * 生成文件存储路径
     */
    String generateFilePath(String projectCode, String fileCategory, String originalFileName);

    /**
     * 计算文件MD5值
     */
    String calculateFileMd5(MultipartFile file);

    /**
     * 获取用户的文件访问权限
     */
    boolean hasFileAccessPermission(String userZgh, Long fileId);
}
