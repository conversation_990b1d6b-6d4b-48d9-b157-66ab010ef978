# 教师管理系统通知数据库表结构说明

## 概述

本文档详细说明了教师管理系统实时通知功能的数据库表结构设计，包括表结构、索引优化、性能考虑等。

## 数据库信息

- **数据库名**: `glrchx` (复用现有教师系统数据库)
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **存储引擎**: `InnoDB`

## 表结构设计

### 1. 主要数据表

#### 1.1 通知主表 (`t_rchx_notifications`)

**用途**: 存储所有通知的核心信息

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | - | 是 | AUTO_INCREMENT | 通知ID，主键 |
| zgh | varchar | 50 | 是 | - | 接收教师职工号 |
| title | varchar | 200 | 是 | - | 通知标题 |
| content | text | - | 否 | NULL | 通知内容 |
| type | varchar | 50 | 是 | - | 通知类型编码 |
| related_id | varchar | 100 | 否 | NULL | 关联业务ID |
| is_read | tinyint | 1 | 是 | 0 | 是否已读(0未读,1已读) |
| priority | tinyint | 1 | 是 | 1 | 优先级(1普通,2重要,3紧急) |
| create_time | datetime | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| read_time | datetime | - | 否 | NULL | 阅读时间 |
| expire_time | datetime | - | 否 | NULL | 过期时间 |
| sender_zgh | varchar | 50 | 否 | NULL | 发送者职工号 |
| sender_name | varchar | 100 | 否 | NULL | 发送者姓名 |
| extra_data | json | - | 否 | NULL | 扩展数据 |
| is_deleted | tinyint | 1 | 是 | 0 | 是否删除(0未删除,1已删除) |
| update_time | datetime | - | 否 | CURRENT_TIMESTAMP | 更新时间 |

**核心索引**:
- `idx_zgh_read_deleted_time`: (zgh, is_read, is_deleted, create_time DESC) - 最重要的复合索引
- `idx_type_time_read`: (type, create_time DESC, is_read) - 按类型查询
- `idx_priority_zgh_time`: (priority DESC, zgh, create_time DESC) - 优先级查询

#### 1.2 通知类型配置表 (`t_rchx_notification_types`)

**用途**: 配置通知类型的基本信息和显示样式

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | AUTO_INCREMENT | 类型ID，主键 |
| type_code | varchar | 50 | 是 | - | 类型编码，唯一 |
| type_name | varchar | 100 | 是 | - | 类型名称 |
| description | varchar | 200 | 否 | NULL | 类型描述 |
| icon | varchar | 50 | 否 | NULL | 图标名称 |
| color | varchar | 20 | 否 | NULL | 颜色代码 |
| is_enabled | tinyint | 1 | 是 | 1 | 是否启用 |
| sort_order | int | - | 是 | 0 | 排序 |

**预置通知类型**:
- `teacher_info_updated`: 教师信息更新
- `permission_changed`: 权限变更
- `system_maintenance`: 系统维护
- `data_import_result`: 数据导入结果
- `password_reset_success`: 密码重置成功
- `account_locked`: 账户锁定
- `batch_operation_result`: 批量操作结果
- `system_announcement`: 系统公告

#### 1.3 通知模板表 (`t_rchx_notification_templates`)

**用途**: 存储通知内容模板，支持变量替换

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | AUTO_INCREMENT | 模板ID，主键 |
| template_code | varchar | 50 | 是 | - | 模板编码，唯一 |
| template_name | varchar | 100 | 是 | - | 模板名称 |
| type_code | varchar | 50 | 是 | - | 通知类型编码 |
| title_template | varchar | 200 | 是 | - | 标题模板 |
| content_template | text | - | 是 | - | 内容模板 |
| variables | json | - | 否 | NULL | 变量说明 |
| is_enabled | tinyint | 1 | 是 | 1 | 是否启用 |

**模板变量示例**:
```json
{
  "teacherName": "教师姓名",
  "adminName": "管理员姓名", 
  "updateTime": "更新时间",
  "updateFields": "更新字段"
}
```

#### 1.4 通知操作日志表 (`t_rchx_notification_logs`)

**用途**: 记录通知的发送、阅读、删除等操作日志

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | - | 是 | AUTO_INCREMENT | 日志ID，主键 |
| notification_id | bigint | - | 是 | - | 通知ID |
| zgh | varchar | 50 | 是 | - | 操作者职工号 |
| action | varchar | 20 | 是 | - | 操作类型 |
| status | varchar | 20 | 是 | - | 操作状态 |
| error_message | text | - | 否 | NULL | 错误信息 |
| ip_address | varchar | 50 | 否 | NULL | IP地址 |
| user_agent | varchar | 500 | 否 | NULL | 用户代理 |
| create_time | datetime | - | 是 | CURRENT_TIMESTAMP | 创建时间 |

**操作类型**:
- `SEND`: 发送通知
- `READ`: 阅读通知
- `DELETE`: 删除通知
- `CLEANUP`: 系统清理

#### 1.5 用户通知设置表 (`t_rchx_notification_settings`)

**用途**: 存储每个用户的通知偏好设置

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | AUTO_INCREMENT | 设置ID，主键 |
| zgh | varchar | 50 | 是 | - | 教师职工号 |
| type_code | varchar | 50 | 是 | - | 通知类型编码 |
| is_enabled | tinyint | 1 | 是 | 1 | 是否启用 |
| push_method | varchar | 50 | 是 | websocket | 推送方式 |

### 2. 视图设计

#### 2.1 未读通知统计视图 (`v_teacher_unread_count`)

**用途**: 快速查询每个教师的未读通知数量

```sql
SELECT 
    zgh,
    COUNT(*) as unread_count,
    MAX(create_time) as latest_notification_time
FROM t_rchx_notifications 
WHERE is_read = 0 AND is_deleted = 0 
  AND (expire_time IS NULL OR expire_time > NOW())
GROUP BY zgh;
```

#### 2.2 通知详情视图 (`v_notification_details`)

**用途**: 包含通知类型信息的详细视图

```sql
SELECT 
    n.id, n.zgh, n.title, n.content, n.type,
    nt.type_name, nt.icon, nt.color,
    n.priority, n.is_read, n.create_time,
    n.sender_zgh, n.sender_name,
    t.xm as receiver_name
FROM t_rchx_notifications n
LEFT JOIN t_rchx_notification_types nt ON n.type = nt.type_code
LEFT JOIN t_rchx_jzgjbxx t ON n.zgh = t.zgh
WHERE n.is_deleted = 0;
```

### 3. 存储过程

#### 3.1 批量标记已读 (`sp_mark_notifications_read`)

**用途**: 批量将指定通知标记为已读状态

**参数**:
- `p_zgh`: 教师职工号
- `p_notification_ids`: 通知ID列表(逗号分隔)

#### 3.2 清理过期通知 (`sp_cleanup_expired_notifications`)

**用途**: 定时清理过期的通知数据

**功能**:
- 软删除过期通知
- 记录清理日志
- 返回清理数量

### 4. 索引优化策略

#### 4.1 核心查询场景

1. **获取某教师的未读通知** (最频繁)
   - 索引: `idx_zgh_read_deleted_time`
   - 查询: `WHERE zgh = ? AND is_read = 0 AND is_deleted = 0`

2. **统计未读通知数量**
   - 索引: `idx_cover_unread_count`
   - 查询: `COUNT(*) WHERE zgh = ? AND is_read = 0`

3. **按类型查询通知**
   - 索引: `idx_type_time_read`
   - 查询: `WHERE type = ? ORDER BY create_time DESC`

4. **管理员查看发送的通知**
   - 索引: `idx_sender_time`
   - 查询: `WHERE sender_zgh = ? ORDER BY create_time DESC`

#### 4.2 覆盖索引设计

**通知列表查询覆盖索引**:
```sql
CREATE INDEX idx_cover_notification_list ON t_rchx_notifications 
(zgh, is_read, is_deleted, create_time DESC, id, title, type, priority);
```

**优势**: 避免回表查询，直接从索引获取所需字段

#### 4.3 性能预估

基于3000教师，每人平均50条通知的数据量：

- **总通知数**: ~150,000条
- **未读通知查询**: < 1ms (使用覆盖索引)
- **通知列表分页**: < 5ms (使用复合索引)
- **统计查询**: < 10ms (使用聚合索引)

### 5. 数据维护策略

#### 5.1 定期清理

- **已读通知**: 保留3个月，之后软删除
- **操作日志**: 保留6个月，之后物理删除
- **过期通知**: 每日自动清理

#### 5.2 索引维护

- **每月执行**: `ANALYZE TABLE` 更新统计信息
- **监控慢查询**: 设置 `long_query_time = 1`
- **检查未使用索引**: 定期清理无效索引

#### 5.3 备份策略

- **全量备份**: 每日凌晨2点
- **增量备份**: 每4小时一次
- **日志备份**: 实时备份binlog

### 6. 安全考虑

#### 6.1 数据权限

- 教师只能查看自己的通知
- 管理员可以查看发送的通知
- 系统管理员拥有全部权限

#### 6.2 SQL注入防护

- 使用参数化查询
- 存储过程参数验证
- 输入数据过滤和转义

#### 6.3 数据加密

- 敏感通知内容可考虑加密存储
- 传输过程使用HTTPS
- 数据库连接使用SSL

### 7. 监控指标

#### 7.1 性能指标

- 查询响应时间
- 索引命中率
- 慢查询数量
- 连接池使用率

#### 7.2 业务指标

- 通知发送成功率
- 平均阅读时间
- 未读通知积压量
- 用户活跃度

### 8. 扩展性考虑

#### 8.1 水平扩展

- 支持读写分离
- 可按时间分区
- 支持分库分表

#### 8.2 功能扩展

- 支持富文本通知
- 支持附件上传
- 支持通知模板自定义
- 支持多语言通知

---

**文档版本**: v1.0  
**创建时间**: 2025-07-24  
**维护人员**: 教师系统开发团队
