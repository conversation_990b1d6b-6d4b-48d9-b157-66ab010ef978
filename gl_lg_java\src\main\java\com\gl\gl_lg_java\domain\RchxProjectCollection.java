package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目选题征集表实体类
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_rchx_project_collection")
public class RchxProjectCollection {

    /**
     * 征集ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 征集编号
     */
    @TableField("collection_code")
    private String collectionCode;

    /**
     * 征集名称
     */
    @TableField("collection_name")
    private String collectionName;

    /**
     * 项目大类ID
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 项目类别ID
     */
    @TableField("type_id")
    private Integer typeId;

    /**
     * 管理部门ID
     */
    @TableField("dept_id")
    private Integer deptId;

    /**
     * 填报指南内容
     */
    @TableField("guide_content")
    private String guideContent;

    /**
     * 受理开始时间
     */
    @TableField("accept_start_time")
    private LocalDateTime acceptStartTime;

    /**
     * 受理结束时间
     */
    @TableField("accept_end_time")
    private LocalDateTime acceptEndTime;

    /**
     * 状态(DRAFT草稿,PUBLISHED发布,CLOSED关闭)
     */
    @TableField("status")
    private String status;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 审核人职工号
     */
    @TableField("reviewer_zgh")
    private String reviewerZgh;

    /**
     * 审核意见
     */
    @TableField("review_comments")
    private String reviewComments;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人职工号
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人职工号
     */
    @TableField("update_by")
    private String updateBy;
}
