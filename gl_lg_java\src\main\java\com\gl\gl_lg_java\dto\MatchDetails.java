package com.gl.gl_lg_java.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;
import java.util.Map;

/**
 * 详细匹配信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchDetails {
    
    /**
     * 基本条件检查结果
     */
    private BasicConditionResult basicConditions;
    
    /**
     * 学术成果检查结果
     */
    private AchievementConditionResult achievementConditions;
    
    /**
     * 项目经验检查结果
     */
    private ProjectExperienceResult projectExperience;
    
    /**
     * 统计数据
     */
    private StatisticalData statisticalData;
    
    /**
     * 附加信息
     */
    private Map<String, Object> additionalInfo;
}

/**
 * 基本条件检查结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class BasicConditionResult {
    
    /**
     * 是否全部通过
     */
    private Boolean overallPass;
    
    /**
     * 条件检查列表
     */
    private List<ConditionItem> conditions;
}

/**
 * 学术成果条件检查结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class AchievementConditionResult {
    
    /**
     * 情形1检查结果
     */
    private SituationResult situation1;
    
    /**
     * 情形2检查结果
     */
    private SituationResult situation2;
    
    /**
     * 是否任一情形通过
     */
    private Boolean isEitherSituationPassed;
}

/**
 * 项目经验检查结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class ProjectExperienceResult {
    
    /**
     * 项目总数
     */
    private Integer totalProjects;
    
    /**
     * 国家级项目数
     */
    private Integer nationalProjects;
    
    /**
     * 省部级项目数
     */
    private Integer provincialProjects;
    
    /**
     * 主持项目数
     */
    private Integer principalProjects;
    
    /**
     * 总经费
     */
    private Double totalFunding;
}

/**
 * 情形检查结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class SituationResult {
    
    /**
     * 是否通过
     */
    private Boolean passed;
    
    /**
     * 通过的条件数量
     */
    private Integer passedCount;
    
    /**
     * 总条件数量
     */
    private Integer totalCount;
    
    /**
     * 条件检查列表
     */
    private List<ConditionItem> conditions;
    
    /**
     * 检查原因
     */
    private String reason;
}

/**
 * 条件检查项
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class ConditionItem {
    
    /**
     * 条件名称
     */
    private String name;
    
    /**
     * 是否通过
     */
    private Boolean passed;
    
    /**
     * 当前值
     */
    private String value;
    
    /**
     * 要求值
     */
    private String requirement;
    
    /**
     * 得分
     */
    private Integer score;
    
    /**
     * 详细说明
     */
    private String details;
    
    /**
     * 证据材料
     */
    private List<String> evidence;
    
    /**
     * 备注
     */
    private String note;
}

/**
 * 统计数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class StatisticalData {
    
    /**
     * 论文统计
     */
    private PaperStatistics papers;
    
    /**
     * 项目统计
     */
    private ProjectStatistics projects;
    
    /**
     * 专利统计
     */
    private PatentStatistics patents;
    
    /**
     * 获奖统计
     */
    private AwardStatistics awards;
}

/**
 * 论文统计
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class PaperStatistics {
    private Integer total;
    private Integer sciEi;
    private Integer firstAuthor;
    private Integer correspondingAuthor;
    private Integer recentFiveYears;
}

/**
 * 项目统计
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class ProjectStatistics {
    private Integer total;
    private Integer national;
    private Integer provincial;
    private Integer principal;
    private Double totalFunding;
}

/**
 * 专利统计
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class PatentStatistics {
    private Integer total;
    private Integer invention;
    private Integer utility;
    private Integer authorized;
}

/**
 * 获奖统计
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class AwardStatistics {
    private Integer total;
    private Integer national;
    private Integer provincial;
    private Integer municipal;
}
