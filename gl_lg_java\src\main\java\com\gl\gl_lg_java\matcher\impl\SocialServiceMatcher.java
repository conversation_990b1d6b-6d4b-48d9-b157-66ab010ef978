package com.gl.gl_lg_java.matcher.impl;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.matcher.ProjectMatcher;
import com.gl.gl_lg_java.util.CommonConditionValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;

/**
 * 社会服务人才培育计划项目匹配算法（简化版）
 */
@Component
@Slf4j
public class SocialServiceMatcher implements ProjectMatcher {
    
    @Autowired
    private CommonConditionValidator validator;
    
    @Override
    public String getProjectCode() {
        return "SHFW";
    }
    
    @Override
    public String getProjectName() {
        return "社会服务人才培育计划项目";
    }
    
    @Override
    public MatchResult matchTeacher(RchxJzgjbxx teacher, AnalysisParams params) {
        log.info("开始分析教师{}的社会服务匹配度", teacher.getZgh());
        
        MatchResult result = MatchResult.builder()
            .teacherZgh(teacher.getZgh())
            .teacherName(teacher.getXm())
            .department(teacher.getBm())
            .title(teacher.getZc())
            .age(validator.calculateAge(teacher))
            .createdTime(LocalDateTime.now())
            .strengths(new ArrayList<>())
            .weaknesses(new ArrayList<>())
            .recommendations(new ArrayList<>())
            .build();
        
        try {
            // 年龄检查
            Integer age = validator.calculateAge(teacher);
            if (age != null && age > 55) {
                result.setIsQualified(false);
                result.setReason("年龄超过55周岁限制");
                result.setMatchScore(0.0);
                result.calculateMatchLevel();
                return result;
            }
            
            // 简化的匹配逻辑
            double score = 50.0; // 基础分
            
            // 年龄优势
            if (age != null) {
                if (age <= 50) {
                    score += 15.0;
                    result.getStrengths().add(String.format("年龄符合要求（%d岁≤50岁）", age));
                } else if (age <= 55) {
                    score += 10.0;
                    result.getStrengths().add(String.format("年龄在放宽范围内（%d岁≤55岁）", age));
                }
            }
            
            // 基于职称推断技术水平
            if (teacher.getZc() != null) {
                if (teacher.getZc().contains("教授")) {
                    score += 20.0;
                    result.getStrengths().add("具有教授职称，技术水平较高");
                } else if (teacher.getZc().contains("副教授")) {
                    score += 15.0;
                    result.getStrengths().add("具有副教授职称");
                } else if (teacher.getZc().contains("讲师")) {
                    score += 10.0;
                    result.getStrengths().add("具有讲师职称");
                }
            }
            
            // 社会服务能力推断
            if (teacher.getBm() != null && !teacher.getBm().isEmpty()) {
                score += 10.0;
                result.getStrengths().add("有明确的专业领域，便于开展社会服务");
            }
            
            result.setMatchScore(score);
            result.setIsQualified(score >= params.getMinMatchScore());
            result.calculateMatchLevel();
            
            // 生成建议
            if (result.getIsQualified()) {
                result.getRecommendations().add("建议准备技术开发项目经费证明材料");
                result.getRecommendations().add("完善社会服务能力和行业影响力证明");
            } else {
                result.getRecommendations().add("建议积累技术开发项目经验");
                result.getRecommendations().add("争取近五年技术开发项目累计经费达到100万元");
            }
            
            log.info("教师{}社会服务匹配分析完成，得分: {}", teacher.getZgh(), score);
            
        } catch (Exception e) {
            log.error("教师{}社会服务匹配分析失败", teacher.getZgh(), e);
            result.setIsQualified(false);
            result.setReason("分析过程中发生错误: " + e.getMessage());
            result.setMatchScore(0.0);
            result.calculateMatchLevel();
        }
        
        return result;
    }
    
    @Override
    public boolean validateBasicConditions(RchxJzgjbxx teacher) {
        return teacher.getXm() != null && !teacher.getXm().isEmpty() &&
               teacher.getZgh() != null && !teacher.getZgh().isEmpty();
    }
}
