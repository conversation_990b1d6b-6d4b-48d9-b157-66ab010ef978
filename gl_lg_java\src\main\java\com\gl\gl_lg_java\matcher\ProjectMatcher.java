package com.gl.gl_lg_java.matcher;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;

/**
 * 项目匹配器接口
 */
public interface ProjectMatcher {
    
    /**
     * 执行教师匹配分析
     * 
     * @param teacher 教师信息
     * @param params 分析参数
     * @return 匹配结果
     */
    MatchResult matchTeacher(RchxJzgjbxx teacher, AnalysisParams params);
    
    /**
     * 获取项目代码
     * 
     * @return 项目代码
     */
    String getProjectCode();
    
    /**
     * 获取项目名称
     * 
     * @return 项目名称
     */
    String getProjectName();
    
    /**
     * 验证基本条件
     * 
     * @param teacher 教师信息
     * @return 是否满足基本条件
     */
    boolean validateBasicConditions(RchxJzgjbxx teacher);
}
