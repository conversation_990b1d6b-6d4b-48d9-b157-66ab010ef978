# 完整API接口文档

## 系统概述

本系统是一个基于Spring Boot + MyBatis-Plus + JWT的教职工管理系统，支持四级权限管理和完整的CRUD操作。

## 基础信息

- **基础URL**: `http://localhost:8080`
- **认证方式**: JWT Token
- **Token有效期**: 24小时
- **数据格式**: JSON
- **字符编码**: UTF-8

## 权限系统

### 权限类型
| 权限名称 | 权限描述 | 功能范围 |
|---------|---------|---------|
| 教师 | 普通教师 | 查看个人信息、修改个人资料 |
| 评审 | 评审人员 | 评审相关操作（不包含教师功能） |
| 学院管理员 | 学院管理员 | 管理学院教职工（不包含评审功能） |
| 系统管理员 | 系统管理员 | 所有功能（包含以上所有权限） |

### 权限特点
- 每种权限只能访问自己对应的功能
- 只有系统管理员拥有所有权限
- 权限采用独立制，不存在继承关系

## API模块

### 1. 认证模块 (`/api/auth`)

| 接口 | 方法 | 权限 | 说明 |
|------|------|------|------|
| `/login` | POST | 无 | 用户登录 |
| `/me` | GET | 登录用户 | 获取当前用户信息 |
| `/refresh` | POST | 登录用户 | 刷新Token |
| `/logout` | POST | 登录用户 | 用户登出 |

### 2. 教职工管理模块 (`/api/jzgjbxx`)

| 接口 | 方法 | 权限 | 说明 |
|------|------|------|------|
| `/{zgh}` | GET | 教师+ | 根据职工号查询 |
| `/page` | POST | 学院管理员+ | 分页查询 |
| `/list` | POST | 学院管理员+ | 多条件查询 |
| `/name/{xm}` | GET | 学院管理员+ | 根据姓名模糊查询 |
| `/dept/{bm}` | GET | 学院管理员+ | 根据部门查询 |
| `/title/{zc}` | GET | 学院管理员+ | 根据职称查询 |
| `/permission/{qx}` | GET | 系统管理员 | 根据权限查询 |
| `/` | POST | 学院管理员+ | 新增教职工 |
| `/batch` | POST | 学院管理员+ | 批量新增 |
| `/` | PUT | 学院管理员+ | 更新教职工 |
| `/batch` | PUT | 学院管理员+ | 批量更新 |
| `/{zgh}` | DELETE | 系统管理员 | 删除教职工 |
| `/batch` | DELETE | 系统管理员 | 批量删除 |

### 3. 专利成果管理模块 (`/api/zlcgjbxx`)

| 接口 | 方法 | 权限 | 说明 |
|------|------|------|------|
| `/{zlcgbh}` | GET | 教师+ | 根据专利编号查询 |
| `/page` | POST | 教师+ | 分页查询 |
| `/list` | POST | 教师+ | 多条件查询 |
| `/name/{zlcgmc}` | GET | 教师+ | 根据名称模糊查询 |
| `/inventor/{dyfmrzgh}` | GET | 教师+ | 根据发明人查询 |
| `/type/{zllx}` | GET | 教师+ | 根据专利类型查询 |
| `/status/{zlzt}` | GET | 教师+ | 根据专利状态查询 |
| `/audit/{shzt}` | GET | 评审+ | 根据审核状态查询 |
| `/` | POST | 学院管理员+ | 新增专利 |
| `/batch` | POST | 学院管理员+ | 批量新增 |
| `/` | PUT | 学院管理员+ | 更新专利 |
| `/batch` | PUT | 学院管理员+ | 批量更新 |
| `/{zlcgbh}` | DELETE | 系统管理员 | 删除专利 |
| `/batch` | DELETE | 系统管理员 | 批量删除 |

## 统一响应格式

所有接口都返回统一的响应格式：

```json
{
    "code": 200,           // 状态码：200-成功，500-失败，401-未授权，403-权限不足
    "message": "操作成功",  // 响应消息
    "data": {}            // 响应数据（可能为null）
}
```

## 认证说明

### JWT Token使用
- 除了登录接口外，所有API都需要在请求头中携带JWT Token
- Header格式：`Authorization: Bearer {token}`
- Token有效期：24小时
- Token过期后需要重新登录或使用刷新接口

### 登录支持的账号类型
1. **职工号**：如 `admin`、`T001`、`T002` 等
2. **手机号**：如 `13800138000`、`13900139000` 等
3. **身份证号**：如 `110101199001011234` 等
4. **邮箱**：如 `<EMAIL>`、`<EMAIL>` 等

## 权限控制

### 接口权限
- 使用 `@RequirePermission` 注解控制接口访问权限
- 支持精确匹配和权限级别匹配

### 数据权限
- **教师**：只能查看和修改自己的数据
- **评审**：可以查看评审相关数据
- **学院管理员**：可以管理本学院的数据
- **系统管理员**：可以管理所有数据

## 查询功能

### 支持的查询类型
1. **精确查询**：根据具体字段值查询
2. **模糊查询**：支持姓名、部门、专利名称等模糊查询
3. **范围查询**：支持日期范围查询
4. **多条件查询**：支持复合条件查询
5. **分页查询**：支持分页和排序

### 查询参数
- 所有字段都支持查询
- 字符串字段支持模糊查询
- 支持分页参数：pageNum、pageSize
- 支持排序参数：orderBy、orderDirection

## 批量操作

### 支持的批量操作
1. **批量新增**：一次性新增多条记录
2. **批量更新**：一次性更新多条记录
3. **批量删除**：一次性删除多条记录

### 事务保护
- 所有写操作都有事务保护
- 批量操作失败时会回滚
- 提供详细的错误信息

## 错误处理

### 常见错误码
- **200**：操作成功
- **401**：未授权（Token无效或过期）
- **403**：权限不足
- **500**：服务器内部错误

### 错误响应示例
```json
{
    "code": 403,
    "message": "权限不足，无法访问该资源",
    "data": null
}
```

## 前端对接

### Vue.js示例
```javascript
// Axios配置
const service = axios.create({
    baseURL: 'http://localhost:8080',
    timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(config => {
    const token = localStorage.getItem('token')
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
})

// 响应拦截器
service.interceptors.response.use(
    response => {
        const res = response.data
        if (res.code !== 200) {
            if (res.code === 401) {
                // Token过期，跳转登录
                localStorage.removeItem('token')
                window.location.href = '/login'
            }
            return Promise.reject(new Error(res.message))
        }
        return response
    },
    error => {
        return Promise.reject(error)
    }
)
```

## 测试账号

### 默认管理员账号
- **职工号**：admin
- **密码**：admin
- **权限**：系统管理员
- **邮箱**：<EMAIL>

## 开发规范

### 接口设计规范
1. **RESTful风格**：使用标准的HTTP方法
2. **统一响应格式**：所有接口返回相同的数据结构
3. **权限注解**：使用注解控制接口权限
4. **参数验证**：完善的参数校验和错误提示
5. **事务管理**：写操作都有事务保护

### 代码规范
1. **分层架构**：Controller -> Service -> Mapper
2. **异常处理**：统一的异常处理和日志记录
3. **权限控制**：基于注解的权限验证
4. **数据验证**：完善的数据校验机制

## 扩展说明

### 添加新实体类
1. 创建实体类并添加MyBatis-Plus注解
2. 创建对应的Mapper、Service、Controller
3. 添加权限注解控制访问权限
4. 编写API文档

### 添加新权限
1. 在权限枚举中添加新权限
2. 更新权限工具类
3. 在接口上使用新权限注解
4. 更新前端权限控制逻辑

本文档涵盖了系统的所有API接口，为前端开发提供了完整的对接指南。
