# 教职工基本信息管理系统 - 前端对接API文档

## 📋 目录
- [1. 认证相关接口](#1-认证相关接口)
- [2. 教职工信息查询接口](#2-教职工信息查询接口)
- [3. 筛选选项接口](#3-筛选选项接口)
- [4. 数据字典](#4-数据字典)
- [5. 错误码说明](#5-错误码说明)

---

## 1. 认证相关接口

### 1.1 用户登录
**接口地址**: `POST /api/auth/login`

**请求参数**:
```json
{
    "zgh": "admin",        // 职工号
    "mm": "admin"          // 密码
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiJ9...",
        "userInfo": {
            "zgh": "admin",
            "xm": "系统管理员",
            "qx": "系统管理员",
            "bm": "系统管理部"
        }
    }
}
```

### 1.2 获取用户信息
**接口地址**: `GET /api/auth/me`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "zgh": "admin",
        "xm": "系统管理员",
        "qx": "系统管理员",
        "bm": "系统管理部"
    }
}
```

---

## 2. 教职工信息查询接口

### 2.1 分页查询教职工信息
**接口地址**: `POST /api/jzgjbxx/page`

**权限要求**: 教师及以上权限

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "zgh": "T001",              // 职工号（可选）
    "xm": "张三",               // 姓名（可选）
    "sfzjh": "110101",          // 身份证号（可选）
    "xb": "男",                 // 性别（可选）
    "bm": "计算机学院",         // 部门（可选）
    "zc": "教授",               // 职称（可选）
    "qx": "教师",               // 权限（可选）
    "dqzt": "在岗",             // 当前状态（可选）
    "pageNum": 1,               // 页码（默认1）
    "pageSize": 20,             // 页大小（默认10）
    "orderBy": "zgh",           // 排序字段（默认zgh）
    "orderDirection": "asc"     // 排序方向（默认asc）
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "zgh": "T001",
                "xm": "张三",
                "xb": "男",
                "bm": "计算机学院",
                "zc": "教授",
                "qx": "教师",
                "dqzt": "在岗",
                "dh": "13800138000",
                "dzxx": "<EMAIL>"
            }
        ],
        "total": 100,
        "size": 20,
        "current": 1,
        "pages": 5
    }
}
```

### 2.2 列表查询教职工信息
**接口地址**: `POST /api/jzgjbxx/list`

**权限要求**: 教师及以上权限

**请求参数**: 同分页查询，但无分页参数

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "zgh": "T001",
            "xm": "张三",
            "xb": "男",
            "bm": "计算机学院"
        }
    ]
}
```

---

## 3. 筛选选项接口

### 3.1 获取部门选项列表
**接口地址**: `GET /api/jzgjbxx/department-options`

**权限要求**: 教师及以上权限

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "离退休工作处",
            "label": "离退休工作处 (569人)",
            "count": 569
        },
        {
            "value": "土木工程学院",
            "label": "土木工程学院 (172人)",
            "count": 172
        },
        {
            "value": "商学院",
            "label": "商学院 (163人)",
            "count": 163
        }
    ]
}
```

### 3.2 获取当前状态选项列表
**接口地址**: `GET /api/jzgjbxx/dqzt-options`

**权限要求**: 教师及以上权限

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "在岗",
            "label": "在岗 (2134人)",
            "count": 2134
        },
        {
            "value": "退休",
            "label": "退休 (776人)",
            "count": 776
        },
        {
            "value": "辞职",
            "label": "辞职 (222人)",
            "count": 222
        }
    ]
}
```

---

## 4. 数据字典

### 4.1 当前状态（dqzt）可选值
| 值 | 说明 | 人数 |
|---|---|---|
| 在岗 | 正常在职工作状态 | 2134 |
| 退休 | 已退休人员 | 776 |
| 辞职 | 主动辞职离开 | 222 |
| 调出 | 调到其他单位 | 15 |
| 博士后出站 | 博士后研究结束 | 12 |
| 死亡 | 已故人员 | 8 |
| 返聘 | 退休后返聘 | 7 |
| 不续聘 | 合同到期不续聘 | 7 |
| 解除合同 | 解除劳动合同 | 6 |
| 终止合同 | 终止劳动合同 | 1 |
| 开除 | 被开除人员 | 1 |

### 4.2 权限等级
| 权限 | 说明 | 权限级别 |
|---|---|---|
| 系统管理员 | 最高权限，可管理所有数据 | 4 |
| 学院管理员 | 可管理学院内数据 | 3 |
| 评审 | 可进行评审相关操作 | 2 |
| 教师 | 基础权限，可查看相关数据 | 1 |

### 4.3 性别
- 男
- 女

---

## 5. 错误码说明

| 错误码 | 说明 |
|---|---|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 6. 测试接口（无需权限）

> **注意**: 以下测试接口仅用于开发调试，无需权限验证，生产环境请使用正式接口

### 6.1 测试 - 获取部门选项
**接口地址**: `GET /api/test/department-options`

**无需权限验证**

**响应格式**: 同正式接口

### 6.2 测试 - 获取状态选项
**接口地址**: `GET /api/test/dqzt-options`

**无需权限验证**

**响应格式**: 同正式接口

### 6.3 测试 - 部门统计
**接口地址**: `GET /api/test/departments`

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "离退休工作处": 569,
        "土木工程学院": 172,
        "商学院": 163
    }
}
```

### 6.4 测试 - 状态统计
**接口地址**: `GET /api/test/dqzt-status`

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "在岗": 2134,
        "退休": 776,
        "辞职": 222
    }
}
```

---

## 7. 前端使用示例

### 6.1 Vue.js 示例

```javascript
// 1. 登录获取token
async login(credentials) {
    const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
    });
    const result = await response.json();
    
    if (result.code === 200) {
        localStorage.setItem('token', result.data.token);
        return result.data;
    }
    throw new Error(result.message);
}

// 2. 获取部门选项
async getDepartmentOptions() {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/jzgjbxx/department-options', {
        headers: { 'Authorization': `Bearer ${token}` }
    });
    const result = await response.json();
    return result.data;
}

// 3. 分页查询教职工
async getJzgjbxxPage(params) {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/jzgjbxx/page', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(params)
    });
    const result = await response.json();
    return result.data;
}
```

### 6.2 React 示例

```javascript
// 使用 axios
import axios from 'axios';

// 设置请求拦截器
axios.interceptors.request.use(config => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// API 调用
const api = {
    // 登录
    login: (data) => axios.post('/api/auth/login', data),
    
    // 获取部门选项
    getDepartmentOptions: () => axios.get('/api/jzgjbxx/department-options'),
    
    // 获取状态选项
    getDqztOptions: () => axios.get('/api/jzgjbxx/dqzt-options'),
    
    // 分页查询
    getJzgjbxxPage: (data) => axios.post('/api/jzgjbxx/page', data)
};
```

---

## 8. 注意事项

1. **认证**: 除登录接口外，所有接口都需要在请求头中携带 `Authorization: Bearer {token}`
2. **权限**: 不同接口有不同的权限要求，请确保用户有足够权限
3. **分页**: 分页查询默认每页10条，最大支持100条
4. **排序**: 支持按任意字段排序，默认按职工号升序
5. **筛选**: 所有筛选条件都是可选的，支持组合查询
6. **跨域**: 后端已配置CORS，支持跨域请求

---

## 9. 联系方式

如有问题，请联系后端开发团队。
