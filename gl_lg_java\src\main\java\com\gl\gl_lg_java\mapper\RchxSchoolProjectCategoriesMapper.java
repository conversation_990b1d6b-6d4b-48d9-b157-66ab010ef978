package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.domain.RchxSchoolProjectCategories;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 校级项目类别Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
@Mapper
public interface RchxSchoolProjectCategoriesMapper extends BaseMapper<RchxSchoolProjectCategories> {

    /**
     * 分页查询项目类别（包含统计信息）
     */
    @Select({
        "<script>",
        "SELECT c.*, ",
        "COALESCE(stats.application_count, 0) AS application_count, ",
        "COALESCE(stats.approved_count, 0) AS approved_count, ",
        "COALESCE(stats.total_funding_requested, 0) AS total_funding_requested, ",
        "COALESCE(stats.total_funding_approved, 0) AS total_funding_approved ",
        "FROM t_rchx_school_project_categories c ",
        "LEFT JOIN ( ",
        "    SELECT category_id, ",
        "           COUNT(*) AS application_count, ",
        "           SUM(CASE WHEN status = 'APPROVED' THEN 1 ELSE 0 END) AS approved_count, ",
        "           SUM(funding_amount) AS total_funding_requested, ",
        "           SUM(approval_amount) AS total_funding_approved ",
        "    FROM t_rchx_school_project_applications ",
        "    GROUP BY category_id ",
        ") stats ON c.id = stats.category_id ",
        "WHERE 1=1 ",
        "<if test='categoryName != null and categoryName != \"\"'>",
        "AND c.category_name LIKE CONCAT('%', #{categoryName}, '%') ",
        "</if>",
        "<if test='isEnabled != null'>",
        "AND c.is_enabled = #{isEnabled} ",
        "</if>",
        "ORDER BY c.sort_order ASC, c.create_time DESC",
        "</script>"
    })
    IPage<Map<String, Object>> selectPageWithStats(
            Page<RchxSchoolProjectCategories> page,
            @Param("categoryName") String categoryName,
            @Param("isEnabled") Boolean isEnabled
    );

    /**
     * 查询启用的项目类别（按排序）
     */
    @Select("SELECT * FROM t_rchx_school_project_categories " +
            "WHERE is_enabled = 1 " +
            "ORDER BY sort_order ASC, create_time DESC")
    List<RchxSchoolProjectCategories> selectEnabledCategories();

    /**
     * 根据类别编码查询
     */
    @Select("SELECT * FROM t_rchx_school_project_categories " +
            "WHERE category_code = #{categoryCode}")
    RchxSchoolProjectCategories selectByCategoryCode(@Param("categoryCode") String categoryCode);

    /**
     * 检查类别编码是否存在
     */
    @Select("SELECT COUNT(*) FROM t_rchx_school_project_categories " +
            "WHERE category_code = #{categoryCode} " +
            "<if test='excludeId != null'> AND id != #{excludeId} </if>")
    int countByCategoryCode(@Param("categoryCode") String categoryCode, @Param("excludeId") Integer excludeId);

    /**
     * 获取最大排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) FROM t_rchx_school_project_categories")
    Integer getMaxSortOrder();

    /**
     * 获取类别统计信息
     */
    @Select({
        "SELECT ",
        "c.id, ",
        "c.category_name, ",
        "c.category_code, ",
        "c.funding_range_min, ",
        "c.funding_range_max, ",
        "COUNT(a.id) AS application_count, ",
        "SUM(CASE WHEN a.status = 'APPROVED' THEN 1 ELSE 0 END) AS approved_count, ",
        "SUM(CASE WHEN a.status = 'SUBMITTED' THEN 1 ELSE 0 END) AS submitted_count, ",
        "SUM(CASE WHEN a.status = 'DRAFT' THEN 1 ELSE 0 END) AS draft_count, ",
        "SUM(a.funding_amount) AS total_funding_requested, ",
        "SUM(a.approval_amount) AS total_funding_approved, ",
        "ROUND(SUM(CASE WHEN a.status = 'APPROVED' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(a.id), 0), 2) AS approval_rate ",
        "FROM t_rchx_school_project_categories c ",
        "LEFT JOIN t_rchx_school_project_applications a ON c.id = a.category_id ",
        "WHERE c.is_enabled = 1 ",
        "GROUP BY c.id, c.category_name, c.category_code, c.funding_range_min, c.funding_range_max ",
        "ORDER BY c.sort_order ASC"
    })
    List<Map<String, Object>> getCategoryStatistics();

    /**
     * 根据资助金额范围查询类别
     */
    @Select({
        "<script>",
        "SELECT * FROM t_rchx_school_project_categories ",
        "WHERE is_enabled = 1 ",
        "<if test='fundingAmount != null'>",
        "AND (funding_range_min IS NULL OR funding_range_min &lt;= #{fundingAmount}) ",
        "AND (funding_range_max IS NULL OR funding_range_max &gt;= #{fundingAmount}) ",
        "</if>",
        "ORDER BY sort_order ASC",
        "</script>"
    })
    List<RchxSchoolProjectCategories> selectByFundingRange(@Param("fundingAmount") java.math.BigDecimal fundingAmount);
}
