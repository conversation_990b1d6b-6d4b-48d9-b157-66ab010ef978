package com.gl.gl_lg_java.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 分析结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisResult {

    /**
     * 分析ID
     */
    private String analysisId;

    /**
     * 项目代码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 分析状态
     */
    private String status;

    /**
     * 分析摘要
     */
    private AnalysisSummary summary;

    /**
     * 匹配结果列表
     */
    private List<MatchResult> results;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 完成时间
     */
    private LocalDateTime completedTime;
}
