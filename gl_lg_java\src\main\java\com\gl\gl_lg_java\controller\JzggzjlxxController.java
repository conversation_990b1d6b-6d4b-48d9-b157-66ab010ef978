package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gl.gl_lg_java.annotation.RequirePermission;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxJzggzjlxx;
import com.gl.gl_lg_java.dto.JzggzjlxxQueryDTO;
import com.gl.gl_lg_java.service.RchxJzggzjlxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 教职工工作简历信息控制器
 */
@RestController
@RequestMapping("/api/jzggzjlxx")
@Slf4j
public class JzggzjlxxController {
    
    @Autowired
    private RchxJzggzjlxxService jzggzjlxxService;
    
    /**
     * 根据职工号查询工作简历
     */
    @GetMapping("/{zgh}")
    @RequirePermission("教师")
    public Result<List<RchxJzggzjlxx>> getByZgh(@PathVariable String zgh, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的工作简历
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(zgh)) {
                return Result.error(403, "权限不足，只能查看自己的工作简历");
            }
            
            List<RchxJzggzjlxx> result = jzggzjlxxService.listByZgh(zgh);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询工作简历信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询工作简历信息
     */
    @PostMapping("/page")
    @RequirePermission("教师")
    public Result<IPage<RchxJzggzjlxx>> getPage(@RequestBody JzggzjlxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的工作简历
            if ("教师".equals(currentUserQx)) {
                queryDTO.setZgh(currentUserZgh);
            }
            
            IPage<RchxJzggzjlxx> result = jzggzjlxxService.pageByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询工作简历信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 多条件查询工作简历信息
     */
    @PostMapping("/list")
    @RequirePermission("教师")
    public Result<List<RchxJzggzjlxx>> getList(@RequestBody JzggzjlxxQueryDTO queryDTO, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能查看自己的工作简历
            if ("教师".equals(currentUserQx)) {
                queryDTO.setZgh(currentUserZgh);
            }
            
            List<RchxJzggzjlxx> result = jzggzjlxxService.listByMultiConditions(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询工作简历信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据工作单位模糊查询
     */
    @GetMapping("/company/{gzdw}")
    @RequirePermission("学院管理员")
    public Result<List<RchxJzggzjlxx>> getByGzdwLike(@PathVariable String gzdw) {
        try {
            List<RchxJzggzjlxx> result = jzggzjlxxService.listByGzdwLike(gzdw);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据工作单位查询工作简历信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据工作内容模糊查询
     */
    @GetMapping("/content/{gznr}")
    @RequirePermission("学院管理员")
    public Result<List<RchxJzggzjlxx>> getByGznrLike(@PathVariable String gznr) {
        try {
            List<RchxJzggzjlxx> result = jzggzjlxxService.listByGznrLike(gznr);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据工作内容查询工作简历信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据曾任职务模糊查询
     */
    @GetMapping("/position/{crzw}")
    @RequirePermission("学院管理员")
    public Result<List<RchxJzggzjlxx>> getByCrzwLike(@PathVariable String crzw) {
        try {
            List<RchxJzggzjlxx> result = jzggzjlxxService.listByCrzwLike(crzw);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据曾任职务查询工作简历信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据从事专业模糊查询
     */
    @GetMapping("/major/{cszy}")
    @RequirePermission("学院管理员")
    public Result<List<RchxJzggzjlxx>> getByCszyLike(@PathVariable String cszy) {
        try {
            List<RchxJzggzjlxx> result = jzggzjlxxService.listByCszyLike(cszy);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据从事专业查询工作简历信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据工作所在地查询
     */
    @GetMapping("/location/{gzszd}")
    @RequirePermission("学院管理员")
    public Result<List<RchxJzggzjlxx>> getByGzszd(@PathVariable String gzszd) {
        try {
            List<RchxJzggzjlxx> result = jzggzjlxxService.listByGzszd(gzszd);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据工作所在地查询工作简历信息失败: {}", e.getMessage());
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增工作简历信息
     */
    @PostMapping
    @RequirePermission("教师")
    public Result<String> create(@RequestBody RchxJzggzjlxx jzggzjlxx, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能添加自己的工作简历
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(jzggzjlxx.getZgh())) {
                return Result.error(403, "权限不足，只能添加自己的工作简历");
            }
            
            boolean success = jzggzjlxxService.saveJzggzjlxx(jzggzjlxx);
            if (success) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增工作简历信息失败: {}", e.getMessage());
            return Result.error("新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新工作简历信息
     */
    @PutMapping
    @RequirePermission("教师")
    public Result<String> update(@RequestBody RchxJzggzjlxx jzggzjlxx, HttpServletRequest request) {
        try {
            String currentUserZgh = (String) request.getAttribute("currentUserZgh");
            String currentUserQx = (String) request.getAttribute("currentUserQx");
            
            // 权限控制：教师只能修改自己的工作简历
            if ("教师".equals(currentUserQx) && !currentUserZgh.equals(jzggzjlxx.getZgh())) {
                return Result.error(403, "权限不足，只能修改自己的工作简历");
            }
            
            boolean success = jzggzjlxxService.updateJzggzjlxx(jzggzjlxx);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新工作简历信息失败: {}", e.getMessage());
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除工作简历信息
     */
    @DeleteMapping("/{zgh}")
    @RequirePermission("系统管理员")
    public Result<String> delete(@PathVariable String zgh) {
        try {
            boolean success = jzggzjlxxService.removeByZgh(zgh);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除工作简历信息失败: {}", e.getMessage());
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除工作简历信息
     */
    @DeleteMapping("/batch")
    @RequirePermission("系统管理员")
    public Result<String> deleteBatch(@RequestBody List<String> zghs) {
        try {
            boolean success = jzggzjlxxService.removeBatchByZghs(zghs);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除工作简历信息失败: {}", e.getMessage());
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量新增工作简历信息
     */
    @PostMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> createBatch(@RequestBody List<RchxJzggzjlxx> jzggzjlxxList) {
        try {
            boolean success = jzggzjlxxService.saveBatchJzggzjlxx(jzggzjlxxList);
            if (success) {
                return Result.success("批量新增成功");
            } else {
                return Result.error("批量新增失败");
            }
        } catch (Exception e) {
            log.error("批量新增工作简历信息失败: {}", e.getMessage());
            return Result.error("批量新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新工作简历信息
     */
    @PutMapping("/batch")
    @RequirePermission("学院管理员")
    public Result<String> updateBatch(@RequestBody List<RchxJzggzjlxx> jzggzjlxxList) {
        try {
            boolean success = jzggzjlxxService.updateBatchJzggzjlxx(jzggzjlxxList);
            if (success) {
                return Result.success("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新工作简历信息失败: {}", e.getMessage());
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }
}
