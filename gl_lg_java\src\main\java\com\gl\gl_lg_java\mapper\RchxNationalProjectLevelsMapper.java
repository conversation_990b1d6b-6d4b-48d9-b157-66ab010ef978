package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gl.gl_lg_java.domain.RchxNationalProjectLevels;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_national_project_levels(国家/省部级项目级别表)】的数据库操作Mapper
 * @createDate 2025-01-04 10:00:00
 * @Entity com.gl.gl_lg_java.domain.RchxNationalProjectLevels
 */
@Mapper
public interface RchxNationalProjectLevelsMapper extends BaseMapper<RchxNationalProjectLevels> {

        /**
         * 核心字段列表 - 用于列表查询优化
         */
        String CORE_FIELDS = "id, level_code, level_name, level_type, level_weight, funding_range_min, funding_range_max, sort_order, is_enabled";

        /**
         * 根据级别编码查询
         */
        @Select("SELECT * FROM t_rchx_national_project_levels WHERE level_code = #{levelCode}")
        RchxNationalProjectLevels findByLevelCode(@Param("levelCode") String levelCode);

        /**
         * 根据级别类型查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_levels WHERE level_type = #{levelType} AND is_enabled = 1 ORDER BY level_weight DESC, sort_order ASC")
        List<RchxNationalProjectLevels> findByLevelType(@Param("levelType") String levelType);

        /**
         * 根据级别名称模糊查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_levels WHERE level_name LIKE CONCAT('%', #{levelName}, '%') ORDER BY level_weight DESC")
        List<RchxNationalProjectLevels> findByLevelNameLike(@Param("levelName") String levelName);

        /**
         * 查询启用的级别（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_levels WHERE is_enabled = 1 ORDER BY level_weight DESC, sort_order ASC")
        List<RchxNationalProjectLevels> findEnabledLevels();

        /**
         * 根据级别权重范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_project_levels WHERE level_weight >= #{minWeight} AND level_weight <= #{maxWeight} ORDER BY level_weight DESC")
        List<RchxNationalProjectLevels> findByLevelWeightBetween(@Param("minWeight") Integer minWeight,
                        @Param("maxWeight") Integer maxWeight);

        /**
         * 根据资助金额范围查询（优化：只查询核心字段）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_national_project_levels WHERE " +
                        "(funding_range_min IS NULL OR funding_range_min <= #{amount}) AND " +
                        "(funding_range_max IS NULL OR funding_range_max >= #{amount}) " +
                        "ORDER BY level_weight DESC")
        List<RchxNationalProjectLevels> findByFundingAmount(@Param("amount") java.math.BigDecimal amount);

        // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错

        /**
         * 统计各级别类型的数量
         */
        @Select("SELECT level_type, COUNT(*) as count FROM t_rchx_national_project_levels WHERE is_enabled = 1 GROUP BY level_type")
        List<java.util.Map<String, Object>> countByLevelType();

        /**
         * 获取最大权重值
         */
        @Select("SELECT MAX(level_weight) FROM t_rchx_national_project_levels")
        Integer getMaxLevelWeight();

        /**
         * 获取最大排序号
         */
        @Select("SELECT MAX(sort_order) FROM t_rchx_national_project_levels")
        Integer getMaxSortOrder();
}
