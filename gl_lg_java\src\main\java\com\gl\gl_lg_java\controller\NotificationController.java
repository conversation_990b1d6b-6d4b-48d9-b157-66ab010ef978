package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxNotifications;
import com.gl.gl_lg_java.domain.RchxNotificationTypes;
import com.gl.gl_lg_java.service.RchxNotificationsService;
import com.gl.gl_lg_java.service.RchxNotificationTypesService;
import com.gl.gl_lg_java.service.NotificationSendService;

import com.gl.gl_lg_java.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通知控制器
 * 提供通知相关的REST API接口
 */
@RestController
@RequestMapping("/api/notifications")
@Slf4j
public class NotificationController {

    @Autowired
    private RchxNotificationsService rchxNotificationsService;

    @Autowired
    private RchxNotificationTypesService rchxNotificationTypesService;

    @Autowired
    private JwtUtil jwtUtil;


    @Autowired
    private NotificationSendService notificationSendService;


    /**
     * 分页查询通知列表
     * 
     * @param current 当前页
     * @param size    每页大小
     * @param isRead  是否已读（可选）
     * @param type    通知类型（可选）
     * @param request HTTP请求
     * @return 分页结果
     */
    @GetMapping("/list")
    public Result<IPage<RchxNotifications>> getNotificationList(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "20") Long size,
            @RequestParam(required = false) Integer isRead,
            @RequestParam(required = false) String type,
            HttpServletRequest request) {

        try {
            String zgh = getCurrentUserZgh(request);
            if (zgh == null) {
                return Result.error("用户未登录");
            }

            IPage<RchxNotifications> page = rchxNotificationsService.getNotificationPage(
                    zgh, current, size, isRead, type);

            log.info("查询通知列表: zgh={}, current={}, size={}, total={}",
                    zgh, current, size, page.getTotal());

            return Result.success(page);

        } catch (Exception e) {
            log.error("查询通知列表失败: {}", e.getMessage());
            return Result.error("查询通知列表失败");
        }
    }

    /**
     * 获取未读通知数量
     * 
     * @param request HTTP请求
     * @return 未读数量
     */
    @GetMapping("/unread-count")
    public Result<Map<String, Object>> getUnreadCount(HttpServletRequest request) {
        try {
            String zgh = getCurrentUserZgh(request);
            if (zgh == null) {
                return Result.error("用户未登录");
            }

            long count = rchxNotificationsService.getUnreadCount(zgh);

            Map<String, Object> result = new HashMap<>();
            result.put("zgh", zgh);
            result.put("unreadCount", count);
            result.put("timestamp", System.currentTimeMillis());

            log.debug("获取未读通知数量: zgh={}, count={}", zgh, count);
            return Result.success(result);

        } catch (Exception e) {
            log.error("获取未读通知数量失败: {}", e.getMessage());
            return Result.error("获取未读通知数量失败");
        }
    }

    /**
     * 获取最新通知
     * 
     * @param limit   数量限制
     * @param request HTTP请求
     * @return 通知列表
     */
    @GetMapping("/latest")
    public Result<List<RchxNotifications>> getLatestNotifications(
            @RequestParam(defaultValue = "10") Integer limit,
            HttpServletRequest request) {

        try {
            String zgh = getCurrentUserZgh(request);
            if (zgh == null) {
                return Result.error("用户未登录");
            }

            List<RchxNotifications> notifications = rchxNotificationsService.getLatestNotifications(zgh, limit);

            log.debug("获取最新通知: zgh={}, limit={}, count={}", zgh, limit, notifications.size());
            return Result.success(notifications);

        } catch (Exception e) {
            log.error("获取最新通知失败: {}", e.getMessage());
            return Result.error("获取最新通知失败");
        }
    }

    /**
     * 标记通知为已读
     * 
     * @param id      通知ID
     * @param request HTTP请求
     * @return 操作结果
     */
    @PostMapping("/{id}/read")
    public Result<String> markAsRead(@PathVariable Long id, HttpServletRequest request) {
        try {
            String zgh = getCurrentUserZgh(request);
            if (zgh == null) {
                return Result.error("用户未登录");
            }

            boolean success = rchxNotificationsService.markAsRead(zgh, id);

            if (success) {
                log.info("标记通知已读成功: zgh={}, notificationId={}", zgh, id);
                return Result.success("标记已读成功");
            } else {
                return Result.error("标记已读失败");
            }

        } catch (Exception e) {
            log.error("标记通知已读失败: id={}, error={}", id, e.getMessage());
            return Result.error("标记已读失败");
        }
    }

    /**
     * 批量标记通知为已读
     * 
     * @param ids     通知ID列表
     * @param request HTTP请求
     * @return 操作结果
     */
    @PostMapping("/batch-read")
    public Result<Map<String, Object>> batchMarkAsRead(
            @RequestBody List<Long> ids,
            HttpServletRequest request) {

        try {
            String zgh = getCurrentUserZgh(request);
            if (zgh == null) {
                return Result.error("用户未登录");
            }

            int count = rchxNotificationsService.batchMarkAsRead(zgh, ids);

            Map<String, Object> result = new HashMap<>();
            result.put("count", count);
            result.put("message", "批量标记已读成功");

            log.info("批量标记通知已读成功: zgh={}, count={}", zgh, count);
            return Result.success(result);

        } catch (Exception e) {
            log.error("批量标记通知已读失败: error={}", e.getMessage());
            return Result.error("批量标记已读失败");
        }
    }

    /**
     * 标记所有通知为已读
     * 
     * @param request HTTP请求
     * @return 操作结果
     */
    @PostMapping("/read-all")
    public Result<Map<String, Object>> markAllAsRead(HttpServletRequest request) {
        try {
            String zgh = getCurrentUserZgh(request);
            if (zgh == null) {
                return Result.error("用户未登录");
            }

            int count = rchxNotificationsService.markAllAsRead(zgh);

            Map<String, Object> result = new HashMap<>();
            result.put("count", count);
            result.put("message", "全部标记已读成功");

            log.info("标记所有通知已读成功: zgh={}, count={}", zgh, count);
            return Result.success(result);

        } catch (Exception e) {
            log.error("标记所有通知已读失败: error={}", e.getMessage());
            return Result.error("全部标记已读失败");
        }
    }

    /**
     * 获取通知类型列表
     * 
     * @return 通知类型列表
     */
    @GetMapping("/types")
    public Result<List<RchxNotificationTypes>> getNotificationTypes() {
        try {
            List<RchxNotificationTypes> types = rchxNotificationTypesService.getAllNotificationTypes();
            log.debug("获取通知类型列表: count={}", types.size());
            return Result.success(types);

        } catch (Exception e) {
            log.error("获取通知类型列表失败: {}", e.getMessage());
            return Result.error("获取通知类型列表失败");
        }
    }

    /**
     * 发送测试通知（仅管理员）
     * 
     * @param title     标题
     * @param content   内容
     * @param type      类型
     * @param targetZgh 目标用户职工号
     * @param request   HTTP请求
     * @return 操作结果
     */
    @PostMapping("/send-test")
    public Result<Map<String, Object>> sendTestNotification(
            @RequestParam String title,
            @RequestParam String content,
            @RequestParam(defaultValue = "system_announcement") String type,
            @RequestParam String targetZgh,
            HttpServletRequest request) {

        try {
            String senderZgh = getCurrentUserZgh(request);
            String permission = getCurrentUserPermission(request);

            if (senderZgh == null) {
                return Result.error("用户未登录");
            }

            // 检查权限（只有管理员可以发送测试通知）
            if (!"系统管理员".equals(permission)) {
                return Result.error("权限不足，只有系统管理员可以发送测试通知");
            }

            Long notificationId = rchxNotificationsService.createNotification(
                    targetZgh, title, content, type, 2, senderZgh, "系统管理员");

            Map<String, Object> result = new HashMap<>();
            result.put("notificationId", notificationId);
            result.put("message", "测试通知发送成功");

            log.info("发送测试通知成功: sender={}, target={}, title={}, id={}",
                    senderZgh, targetZgh, title, notificationId);

            return Result.success(result);

        } catch (Exception e) {
            log.error("发送测试通知失败: title={}, error={}", title, e.getMessage());
            return Result.error("发送测试通知失败");
        }
    }

    /**
     * 获取系统统计信息（仅管理员）
     * 
     * @param request HTTP请求
     * @return 统计信息
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getSystemStats(HttpServletRequest request) {
        try {
            String permission = getCurrentUserPermission(request);

            // 检查权限
            if (!"系统管理员".equals(permission)) {
                return Result.error("权限不足");
            }

            Map<String, Object> stats = rchxNotificationsService.getSystemStats();
            log.debug("获取系统统计信息: {}", stats);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取系统统计信息失败: {}", e.getMessage());
            return Result.error("获取系统统计信息失败");
        }
    }

    /**
     * 从请求中获取当前用户职工号
     */
    private String getCurrentUserZgh(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            return token != null ? jwtUtil.getZghFromToken(token) : null;
        } catch (Exception e) {
            log.error("获取当前用户职工号失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从请求中获取当前用户权限
     */
    private String getCurrentUserPermission(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            return token != null ? jwtUtil.getQxFromToken(token) : null;
        } catch (Exception e) {
            log.error("获取当前用户权限失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }
}
