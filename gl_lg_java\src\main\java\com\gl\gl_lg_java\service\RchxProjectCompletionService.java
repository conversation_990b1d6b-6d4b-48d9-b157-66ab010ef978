package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectCompletion;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目结项表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface RchxProjectCompletionService extends IService<RchxProjectCompletion> {

    /**
     * 分页查询项目结项
     * 
     * @param page 分页参数
     * @param categoryId 项目大类ID
     * @param typeId 项目类别ID
     * @param deptId 管理部门ID
     * @param status 状态
     * @param projectCode 项目编号
     * @param projectLeaderZgh 项目负责人职工号
     * @return 分页结果
     */
    IPage<RchxProjectCompletion> pageQuery(Page<RchxProjectCompletion> page, 
                                          Integer categoryId, 
                                          Integer typeId, 
                                          Integer deptId, 
                                          String status, 
                                          String projectCode,
                                          String projectLeaderZgh);

    /**
     * 根据项目编号获取结项信息
     * 
     * @param projectCode 项目编号
     * @return 项目结项列表
     */
    List<RchxProjectCompletion> getByProjectCode(String projectCode);

    /**
     * 根据项目负责人获取结项列表
     * 
     * @param projectLeaderZgh 项目负责人职工号
     * @return 结项列表
     */
    List<RchxProjectCompletion> getByProjectLeader(String projectLeaderZgh);

    /**
     * 提交结项
     * 
     * @param id 结项ID
     * @param submitBy 提交人
     * @return 是否成功
     */
    boolean submitCompletion(Long id, String submitBy);

    /**
     * 审核结项
     * 
     * @param id 结项ID
     * @param approved 是否通过
     * @param reviewerZgh 审核人职工号
     * @param reviewComments 审核意见
     * @param completionScore 结项评分
     * @return 是否成功
     */
    boolean reviewCompletion(Long id, boolean approved, String reviewerZgh, String reviewComments, BigDecimal completionScore);

    /**
     * 获取结项统计信息
     * 
     * @param deptId 部门ID（可选）
     * @param categoryId 大类ID（可选）
     * @return 统计信息
     */
    Object getCompletionStatistics(Integer deptId, Integer categoryId);
}
