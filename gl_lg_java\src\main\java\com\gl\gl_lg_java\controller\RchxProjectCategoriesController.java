package com.gl.gl_lg_java.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.domain.RchxProjectCategories;
import com.gl.gl_lg_java.service.RchxProjectCategoriesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目大类表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Slf4j
@RestController
@RequestMapping("/api/project-categories")
public class RchxProjectCategoriesController {

    @Autowired
    private RchxProjectCategoriesService projectCategoriesService;

    /**
     * 分页查询项目大类
     */
    @GetMapping("/page")
    public Result<IPage<RchxProjectCategories>> page(@RequestParam(defaultValue = "1") Integer current,
                                                     @RequestParam(defaultValue = "10") Integer size,
                                                     @RequestParam(required = false) String categoryName,
                                                     @RequestParam(required = false) Boolean isEnabled) {
        try {
            Page<RchxProjectCategories> page = new Page<>(current, size);
            // 这里可以添加查询条件
            IPage<RchxProjectCategories> result = projectCategoriesService.page(page);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询项目大类失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的项目大类
     */
    @GetMapping("/enabled")
    public Result<List<RchxProjectCategories>> getEnabledCategories() {
        try {
            List<RchxProjectCategories> categories = projectCategoriesService.getEnabledCategories();
            return Result.success(categories);
        } catch (Exception e) {
            log.error("获取启用的项目大类失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目大类
     */
    @GetMapping("/{id}")
    public Result<RchxProjectCategories> getById(@PathVariable Integer id) {
        try {
            RchxProjectCategories category = projectCategoriesService.getById(id);
            if (category == null) {
                return Result.error("项目大类不存在");
            }
            return Result.success(category);
        } catch (Exception e) {
            log.error("查询项目大类失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据大类编码查询项目大类
     */
    @GetMapping("/code/{categoryCode}")
    public Result<RchxProjectCategories> getByCategoryCode(@PathVariable String categoryCode) {
        try {
            RchxProjectCategories category = projectCategoriesService.getByCategoryCode(categoryCode);
            if (category == null) {
                return Result.error("项目大类不存在");
            }
            return Result.success(category);
        } catch (Exception e) {
            log.error("根据编码查询项目大类失败: categoryCode={}", categoryCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增项目大类
     */
    @PostMapping
    public Result<String> save(@RequestBody RchxProjectCategories category) {
        try {
            // 检查编码是否重复
            if (projectCategoriesService.existsByCategoryCode(category.getCategoryCode(), null)) {
                return Result.error("大类编码已存在");
            }
            
            boolean success = projectCategoriesService.save(category);
            return success ? Result.success("新增成功") : Result.error("新增失败");
        } catch (Exception e) {
            log.error("新增项目大类失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目大类
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Integer id, @RequestBody RchxProjectCategories category) {
        try {
            // 检查编码是否重复
            if (projectCategoriesService.existsByCategoryCode(category.getCategoryCode(), id)) {
                return Result.error("大类编码已存在");
            }
            
            category.setId(id);
            boolean success = projectCategoriesService.updateById(category);
            return success ? Result.success("更新成功") : Result.error("更新失败");
        } catch (Exception e) {
            log.error("更新项目大类失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目大类
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Integer id) {
        try {
            boolean success = projectCategoriesService.removeById(id);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除项目大类失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用项目大类
     */
    @PutMapping("/{id}/enabled")
    public Result<String> updateEnabled(@PathVariable Integer id, @RequestParam Boolean enabled) {
        try {
            boolean success = projectCategoriesService.updateEnabled(id, enabled);
            return success ? Result.success("操作成功") : Result.error("操作失败");
        } catch (Exception e) {
            log.error("更新项目大类启用状态失败: id={}, enabled={}", id, enabled, e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
}
