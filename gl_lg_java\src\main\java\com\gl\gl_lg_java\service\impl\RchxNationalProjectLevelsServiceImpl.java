package com.gl.gl_lg_java.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gl.gl_lg_java.domain.RchxNationalProjectLevels;
import com.gl.gl_lg_java.mapper.RchxNationalProjectLevelsMapper;
import com.gl.gl_lg_java.service.RchxNationalProjectLevelsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_national_project_levels(国家/省部级项目级别表)】的数据库操作Service实现
 * @createDate 2025-01-04 10:00:00
 */
@Service
@Slf4j
public class RchxNationalProjectLevelsServiceImpl extends ServiceImpl<RchxNationalProjectLevelsMapper, RchxNationalProjectLevels>
        implements RchxNationalProjectLevelsService {

    @Autowired
    private RchxNationalProjectLevelsMapper levelsMapper;

    @Override
    public RchxNationalProjectLevels getByLevelCode(String levelCode) {
        return levelsMapper.findByLevelCode(levelCode);
    }

    @Override
    public List<RchxNationalProjectLevels> listByLevelType(String levelType) {
        return levelsMapper.findByLevelType(levelType);
    }

    @Override
    public List<RchxNationalProjectLevels> listByLevelNameLike(String levelName) {
        return levelsMapper.findByLevelNameLike(levelName);
    }

    @Override
    public List<RchxNationalProjectLevels> listEnabledLevels() {
        return levelsMapper.findEnabledLevels();
    }

    @Override
    public List<RchxNationalProjectLevels> listByLevelWeightBetween(Integer minWeight, Integer maxWeight) {
        return levelsMapper.findByLevelWeightBetween(minWeight, maxWeight);
    }

    @Override
    public List<RchxNationalProjectLevels> listByFundingAmount(BigDecimal amount) {
        return levelsMapper.findByFundingAmount(amount);
    }

    @Override
    public List<Map<String, Object>> countByLevelType() {
        return levelsMapper.countByLevelType();
    }

    @Override
    public Integer getMaxLevelWeight() {
        Integer maxWeight = levelsMapper.getMaxLevelWeight();
        return maxWeight != null ? maxWeight : 0;
    }

    @Override
    public Integer getMaxSortOrder() {
        Integer maxSortOrder = levelsMapper.getMaxSortOrder();
        return maxSortOrder != null ? maxSortOrder : 0;
    }

    @Override
    @Transactional
    public boolean updateEnabledStatus(Integer id, Boolean enabled) {
        try {
            UpdateWrapper<RchxNationalProjectLevels> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id)
                        .set("is_enabled", enabled);
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("更新级别启用状态失败: id={}, enabled={}", id, enabled, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean batchUpdateEnabledStatus(List<Integer> ids, Boolean enabled) {
        try {
            UpdateWrapper<RchxNationalProjectLevels> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", ids)
                        .set("is_enabled", enabled);
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("批量更新级别启用状态失败: ids={}, enabled={}", ids, enabled, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateSortOrder(Integer id, Integer sortOrder) {
        try {
            UpdateWrapper<RchxNationalProjectLevels> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id)
                        .set("sort_order", sortOrder);
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("更新级别排序失败: id={}, sortOrder={}", id, sortOrder, e);
            return false;
        }
    }

    @Override
    public boolean isLevelCodeUnique(String levelCode, Integer excludeId) {
        QueryWrapper<RchxNationalProjectLevels> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("level_code", levelCode);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) == 0;
    }

    @Override
    public Integer getRecommendedWeight(String levelType) {
        // 根据级别类型推荐权重值
        switch (levelType.toUpperCase()) {
            case "NATIONAL":
                return 100;
            case "PROVINCIAL":
                return 70;
            case "MUNICIPAL":
                return 40;
            default:
                return 10;
        }
    }
}
