package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxProjectInspection;

import java.util.List;

/**
 * 项目中检表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface RchxProjectInspectionService extends IService<RchxProjectInspection> {

    /**
     * 分页查询项目中检
     * 
     * @param page 分页参数
     * @param categoryId 项目大类ID
     * @param typeId 项目类别ID
     * @param deptId 管理部门ID
     * @param status 状态
     * @param projectCode 项目编号
     * @param projectLeaderZgh 项目负责人职工号
     * @return 分页结果
     */
    IPage<RchxProjectInspection> pageQuery(Page<RchxProjectInspection> page, 
                                          Integer categoryId, 
                                          Integer typeId, 
                                          Integer deptId, 
                                          String status, 
                                          String projectCode,
                                          String projectLeaderZgh);

    /**
     * 根据项目编号获取中检信息
     * 
     * @param projectCode 项目编号
     * @return 项目中检列表
     */
    List<RchxProjectInspection> getByProjectCode(String projectCode);

    /**
     * 根据项目负责人获取中检列表
     * 
     * @param projectLeaderZgh 项目负责人职工号
     * @return 中检列表
     */
    List<RchxProjectInspection> getByProjectLeader(String projectLeaderZgh);

    /**
     * 提交中检
     * 
     * @param id 中检ID
     * @param submitBy 提交人
     * @return 是否成功
     */
    boolean submitInspection(Long id, String submitBy);

    /**
     * 审核中检
     * 
     * @param id 中检ID
     * @param approved 是否通过
     * @param reviewerZgh 审核人职工号
     * @param reviewComments 审核意见
     * @return 是否成功
     */
    boolean reviewInspection(Long id, boolean approved, String reviewerZgh, String reviewComments);

    /**
     * 获取中检统计信息
     * 
     * @param deptId 部门ID（可选）
     * @param categoryId 大类ID（可选）
     * @return 统计信息
     */
    Object getInspectionStatistics(Integer deptId, Integer categoryId);
}
