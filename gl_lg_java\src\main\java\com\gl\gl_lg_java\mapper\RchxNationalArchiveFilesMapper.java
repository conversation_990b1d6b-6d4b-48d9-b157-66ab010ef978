package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gl.gl_lg_java.domain.RchxNationalArchiveFiles;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_national_archive_files(国家/省部级项目归档文件表)】的数据库操作Mapper
 * @createDate 2025-01-04 10:00:00
 * @Entity com.gl.gl_lg_java.domain.RchxNationalArchiveFiles
 */
@Mapper
public interface RchxNationalArchiveFilesMapper extends BaseMapper<RchxNationalArchiveFiles> {

        /**
         * 核心字段列表 - 用于列表查询优化
         */
        String CORE_FIELDS = "id, archive_id, project_code, file_category, file_name, file_original_name, " +
                        "file_size, file_type, file_extension, is_required, is_confidential, upload_time, " +
                        "upload_by_name, download_count, is_deleted";

        /**
         * 根据归档ID查询文件（优化：只查询核心字段，排除已删除）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_archive_files WHERE archive_id = #{archiveId} AND is_deleted = 0 ORDER BY upload_time DESC")
        List<RchxNationalArchiveFiles> findByArchiveId(@Param("archiveId") Long archiveId);

        /**
         * 根据项目编号查询文件（优化：只查询核心字段，排除已删除）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_archive_files WHERE project_code = #{projectCode} AND is_deleted = 0 ORDER BY upload_time DESC")
        List<RchxNationalArchiveFiles> findByProjectCode(@Param("projectCode") String projectCode);

        /**
         * 根据文件类别查询（优化：只查询核心字段，排除已删除）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_archive_files WHERE file_category = #{fileCategory} AND is_deleted = 0 ORDER BY upload_time DESC")
        List<RchxNationalArchiveFiles> findByFileCategory(@Param("fileCategory") String fileCategory);

        /**
         * 根据归档ID和文件类别查询（优化：只查询核心字段，排除已删除）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_archive_files WHERE archive_id = #{archiveId} AND file_category = #{fileCategory} AND is_deleted = 0 ORDER BY upload_time DESC")
        List<RchxNationalArchiveFiles> findByArchiveIdAndCategory(@Param("archiveId") Long archiveId,
                        @Param("fileCategory") String fileCategory);

        /**
         * 根据文件名模糊查询（优化：只查询核心字段，排除已删除）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_archive_files WHERE file_name LIKE CONCAT('%', #{fileName}, '%') AND is_deleted = 0 ORDER BY upload_time DESC")
        List<RchxNationalArchiveFiles> findByFileNameLike(@Param("fileName") String fileName);

        /**
         * 根据上传人查询（优化：只查询核心字段，排除已删除）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_archive_files WHERE upload_by = #{uploadBy} AND is_deleted = 0 ORDER BY upload_time DESC")
        List<RchxNationalArchiveFiles> findByUploadBy(@Param("uploadBy") String uploadBy);

        /**
         * 根据上传时间范围查询（优化：只查询核心字段，排除已删除）
         */
        @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_national_archive_files WHERE " +
                        "upload_time >= #{startTime} AND upload_time <= #{endTime} AND is_deleted = 0 " +
                        "ORDER BY upload_time DESC")
        List<RchxNationalArchiveFiles> findByUploadTimeRange(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询必需文件（优化：只查询核心字段，排除已删除）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_archive_files WHERE is_required = 1 AND is_deleted = 0 ORDER BY upload_time DESC")
        List<RchxNationalArchiveFiles> findRequiredFiles();

        /**
         * 查询机密文件（优化：只查询核心字段，排除已删除）
         */
        @Select("SELECT " + CORE_FIELDS
                        + " FROM t_rchx_national_archive_files WHERE is_confidential = 1 AND is_deleted = 0 ORDER BY upload_time DESC")
        List<RchxNationalArchiveFiles> findConfidentialFiles();

        /**
         * 根据MinIO存储信息查询
         */
        @Select("SELECT * FROM t_rchx_national_archive_files WHERE bucket_name = #{bucketName} AND object_name = #{objectName} AND is_deleted = 0")
        RchxNationalArchiveFiles findByMinioInfo(@Param("bucketName") String bucketName,
                        @Param("objectName") String objectName);

        /**
         * 根据文件MD5查询（用于重复文件检测）
         */
        @Select("SELECT * FROM t_rchx_national_archive_files WHERE file_md5 = #{fileMd5} AND is_deleted = 0")
        List<RchxNationalArchiveFiles> findByFileMd5(@Param("fileMd5") String fileMd5);

        /**
         * 更新下载次数和最后下载时间
         */
        @Update("UPDATE t_rchx_national_archive_files SET download_count = download_count + 1, last_download_time = NOW() WHERE id = #{id}")
        int updateDownloadInfo(@Param("id") Long id);

        /**
         * 逻辑删除文件
         */
        @Update("UPDATE t_rchx_national_archive_files SET is_deleted = 1, delete_time = NOW(), delete_by = #{deleteBy} WHERE id = #{id}")
        int logicalDeleteFile(@Param("id") Long id, @Param("deleteBy") String deleteBy);

        // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错

        /**
         * 统计各文件类别的数量
         */
        @Select("SELECT file_category, COUNT(*) as count FROM t_rchx_national_archive_files WHERE is_deleted = 0 GROUP BY file_category")
        List<Map<String, Object>> countByFileCategory();

        /**
         * 统计各归档项目的文件数量
         */
        @Select("SELECT archive_id, COUNT(*) as total_files, " +
                        "SUM(CASE WHEN is_required = 1 THEN 1 ELSE 0 END) as required_files, " +
                        "SUM(CASE WHEN is_confidential = 1 THEN 1 ELSE 0 END) as confidential_files, " +
                        "SUM(file_size) as total_size " +
                        "FROM t_rchx_national_archive_files WHERE is_deleted = 0 GROUP BY archive_id")
        List<Map<String, Object>> countByArchiveId();

        /**
         * 获取文件存储统计信息
         */
        @Select("SELECT " +
                        "COUNT(*) as total_files, " +
                        "SUM(file_size) as total_size, " +
                        "SUM(download_count) as total_downloads, " +
                        "COUNT(DISTINCT archive_id) as archive_count " +
                        "FROM t_rchx_national_archive_files WHERE is_deleted = 0")
        Map<String, Object> getFileStatistics();
}
