# 项目管理系统完整接口文档

## 📋 文档说明

本文档详细描述了项目管理系统所有API接口的入参、返回值、示例等信息，供前端开发人员对接使用。

### 📝 通用响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

#### 分页响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

---

## 🏢 1. 项目大类管理

### 1.1 分页查询项目大类

**接口地址**: `GET /api/project-categories/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryName | String | 否 | - | 大类名称（模糊查询） |
| isEnabled | Boolean | 否 | - | 是否启用 |

**请求示例**:
```http
GET /api/project-categories/page?current=1&size=10&categoryName=技术&isEnabled=true
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "categoryCode": "TECH",
        "categoryName": "技术类项目",
        "description": "技术研发类项目",
        "sortOrder": 1,
        "isEnabled": true,
        "createTime": "2024-08-04T10:00:00",
        "updateTime": "2024-08-04T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 1.2 获取所有启用的项目大类

**接口地址**: `GET /api/project-categories/enabled`

**请求参数**: 无

**请求示例**:
```http
GET /api/project-categories/enabled
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "categoryCode": "TECH",
      "categoryName": "技术类项目",
      "description": "技术研发类项目",
      "sortOrder": 1,
      "isEnabled": true,
      "createTime": "2024-08-04T10:00:00",
      "updateTime": "2024-08-04T10:00:00"
    }
  ]
}
```

### 1.3 根据ID查询项目大类

**接口地址**: `GET /api/project-categories/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Integer | 是 | 大类ID |

**请求示例**:
```http
GET /api/project-categories/1
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "categoryCode": "TECH",
    "categoryName": "技术类项目",
    "description": "技术研发类项目",
    "sortOrder": 1,
    "isEnabled": true,
    "createTime": "2024-08-04T10:00:00",
    "updateTime": "2024-08-04T10:00:00"
  }
}
```

### 1.4 根据大类编码查询项目大类

**接口地址**: `GET /api/project-categories/code/{categoryCode}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryCode | String | 是 | 大类编码 |

**请求示例**:
```http
GET /api/project-categories/code/TECH
```

**返回参数**: 同1.3

### 1.5 新增项目大类

**接口地址**: `POST /api/project-categories`

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
  "categoryCode": "TECH",
  "categoryName": "技术类项目",
  "description": "技术研发类项目",
  "sortOrder": 1,
  "isEnabled": true
}
```

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryCode | String | 是 | 大类编码，唯一 |
| categoryName | String | 是 | 大类名称 |
| description | String | 否 | 描述 |
| sortOrder | Integer | 否 | 排序号 |
| isEnabled | Boolean | 否 | 是否启用，默认true |

**返回参数**:
```json
{
  "code": 200,
  "message": "新增成功",
  "data": "新增成功"
}
```

### 1.6 更新项目大类

**接口地址**: `PUT /api/project-categories/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Integer | 是 | 大类ID |

**请求体**: 同1.5

**返回参数**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": "更新成功"
}
```

### 1.7 删除项目大类

**接口地址**: `DELETE /api/project-categories/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Integer | 是 | 大类ID |

**请求示例**:
```http
DELETE /api/project-categories/1
```

**返回参数**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": "删除成功"
}
```

### 1.8 启用/禁用项目大类

**接口地址**: `PUT /api/project-categories/{id}/enabled`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Integer | 是 | 大类ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| enabled | Boolean | 是 | 是否启用 |

**请求示例**:
```http
PUT /api/project-categories/1/enabled?enabled=true
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "操作成功"
}
```

---

## 🏷️ 2. 项目类别管理

### 2.1 分页查询项目类别

**接口地址**: `GET /api/project-types/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryId | Integer | 否 | - | 所属大类ID |
| typeName | String | 否 | - | 类别名称（模糊查询） |
| isEnabled | Boolean | 否 | - | 是否启用 |

**请求示例**:
```http
GET /api/project-types/page?current=1&size=10&categoryId=1&typeName=AI
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "typeCode": "TECH_AI",
        "typeName": "人工智能技术",
        "categoryId": 1,
        "description": "人工智能相关技术研发",
        "sortOrder": 1,
        "isEnabled": true,
        "createTime": "2024-08-04T10:00:00",
        "updateTime": "2024-08-04T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2.2 获取所有启用的项目类别

**接口地址**: `GET /api/project-types/enabled`

**请求参数**: 无

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "typeCode": "TECH_AI",
      "typeName": "人工智能技术",
      "categoryId": 1,
      "description": "人工智能相关技术研发",
      "sortOrder": 1,
      "isEnabled": true,
      "createTime": "2024-08-04T10:00:00",
      "updateTime": "2024-08-04T10:00:00"
    }
  ]
}
```

### 2.3 根据大类ID获取启用的项目类别

**接口地址**: `GET /api/project-types/enabled/category/{categoryId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryId | Integer | 是 | 大类ID |

**请求示例**:
```http
GET /api/project-types/enabled/category/1
```

**返回参数**: 同2.2

### 2.4 新增项目类别

**接口地址**: `POST /api/project-types`

**请求体**:
```json
{
  "typeCode": "TECH_AI",
  "typeName": "人工智能技术",
  "categoryId": 1,
  "description": "人工智能相关技术研发",
  "sortOrder": 1,
  "isEnabled": true
}
```

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| typeCode | String | 是 | 类别编码，唯一 |
| typeName | String | 是 | 类别名称 |
| categoryId | Integer | 是 | 所属大类ID |
| description | String | 否 | 描述 |
| sortOrder | Integer | 否 | 排序号 |
| isEnabled | Boolean | 否 | 是否启用，默认true |

**返回参数**:
```json
{
  "code": 200,
  "message": "新增成功",
  "data": "新增成功"
}
```

---

## 🏛️ 3. 管理部门管理

### 3.1 分页查询管理部门

**接口地址**: `GET /api/departments/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| deptName | String | 否 | - | 部门名称（模糊查询） |
| isEnabled | Boolean | 否 | - | 是否启用 |

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "deptCode": "TECH_DEPT",
        "deptName": "技术部",
        "contactPerson": "张三",
        "contactPhone": "13800138000",
        "contactEmail": "<EMAIL>",
        "description": "负责技术类项目管理",
        "sortOrder": 1,
        "isEnabled": true,
        "createTime": "2024-08-04T10:00:00",
        "updateTime": "2024-08-04T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 3.2 新增管理部门

**接口地址**: `POST /api/departments`

**请求体**:
```json
{
  "deptCode": "TECH_DEPT",
  "deptName": "技术部",
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>",
  "description": "负责技术类项目管理",
  "sortOrder": 1,
  "isEnabled": true
}
```

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deptCode | String | 是 | 部门编码，唯一 |
| deptName | String | 是 | 部门名称 |
| contactPerson | String | 否 | 联系人 |
| contactPhone | String | 否 | 联系电话 |
| contactEmail | String | 否 | 联系邮箱 |
| description | String | 否 | 描述 |
| sortOrder | Integer | 否 | 排序号 |
| isEnabled | Boolean | 否 | 是否启用，默认true |

---

## 📊 4. 项目征集管理

### 4.1 分页查询项目征集

**接口地址**: `GET /api/project-collection/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryId | Integer | 否 | - | 项目大类ID |
| typeId | Integer | 否 | - | 项目类别ID |
| deptId | Integer | 否 | - | 管理部门ID |
| status | String | 否 | - | 状态(DRAFT,PUBLISHED,CLOSED) |
| collectionName | String | 否 | - | 征集名称（模糊查询） |

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "collectionCode": "COLLECT_2024_001",
        "collectionName": "2024年技术创新项目征集",
        "categoryId": 1,
        "typeId": 1,
        "deptId": 1,
        "guideContent": "征集指南内容...",
        "acceptStartTime": "2024-08-01T00:00:00",
        "acceptEndTime": "2024-08-31T23:59:59",
        "status": "PUBLISHED",
        "submitTime": null,
        "reviewTime": null,
        "reviewerZgh": null,
        "reviewComments": null,
        "createTime": "2024-08-04T10:00:00",
        "updateTime": "2024-08-04T10:00:00",
        "createBy": "admin",
        "updateBy": "admin"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 4.2 获取当前有效的征集

**接口地址**: `GET /api/project-collection/active`

**请求参数**: 无

**返回参数**: 返回状态为PUBLISHED且在受理时间内的征集列表，格式同4.1

### 4.3 新增项目征集

**接口地址**: `POST /api/project-collection`

**请求体**:
```json
{
  "collectionCode": "COLLECT_2024_001",
  "collectionName": "2024年技术创新项目征集",
  "categoryId": 1,
  "typeId": 1,
  "deptId": 1,
  "guideContent": "征集指南内容...",
  "acceptStartTime": "2024-08-01T00:00:00",
  "acceptEndTime": "2024-08-31T23:59:59",
  "status": "DRAFT",
  "createBy": "admin"
}
```

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| collectionCode | String | 是 | 征集编号，唯一 |
| collectionName | String | 是 | 征集名称 |
| categoryId | Integer | 是 | 项目大类ID |
| typeId | Integer | 是 | 项目类别ID |
| deptId | Integer | 是 | 管理部门ID |
| guideContent | String | 否 | 填报指南内容 |
| acceptStartTime | String | 是 | 受理开始时间(ISO格式) |
| acceptEndTime | String | 是 | 受理结束时间(ISO格式) |
| status | String | 否 | 状态，默认DRAFT |
| createBy | String | 是 | 创建人职工号 |

### 4.4 提交征集

**接口地址**: `PUT /api/project-collection/{id}/submit`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 征集ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| submitBy | String | 是 | 提交人职工号 |

**请求示例**:
```http
PUT /api/project-collection/1/submit?submitBy=admin
```

**返回参数**:
```json
{
  "code": 200,
  "message": "提交成功",
  "data": "提交成功"
}
```

### 4.5 审核征集

**接口地址**: `PUT /api/project-collection/{id}/review`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 征集ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| approved | Boolean | 是 | 是否通过 |
| reviewerZgh | String | 是 | 审核人职工号 |
| reviewComments | String | 否 | 审核意见 |

**请求示例**:
```http
PUT /api/project-collection/1/review?approved=true&reviewerZgh=admin&reviewComments=审核通过
```

**返回参数**:
```json
{
  "code": 200,
  "message": "审核成功",
  "data": "审核成功"
}
```

### 4.6 发布征集

**接口地址**: `PUT /api/project-collection/{id}/publish`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 征集ID |

**请求示例**:
```http
PUT /api/project-collection/1/publish
```

**返回参数**:
```json
{
  "code": 200,
  "message": "发布成功",
  "data": "发布成功"
}
```

### 4.7 关闭征集

**接口地址**: `PUT /api/project-collection/{id}/close`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 征集ID |

**请求示例**:
```http
PUT /api/project-collection/1/close
```

**返回参数**:
```json
{
  "code": 200,
  "message": "关闭成功",
  "data": "关闭成功"
}
```

---

## 📝 5. 项目申报管理

### 5.1 分页查询项目申报

**接口地址**: `GET /api/project-application/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryId | Integer | 否 | - | 项目大类ID |
| typeId | Integer | 否 | - | 项目类别ID |
| deptId | Integer | 否 | - | 管理部门ID |
| status | String | 否 | - | 状态 |
| projectName | String | 否 | - | 项目名称（模糊查询） |
| applicantZgh | String | 否 | - | 申报人职工号 |

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "projectCode": "PROJ_2024_001",
        "projectName": "AI智能识别系统",
        "categoryId": 1,
        "typeId": 1,
        "deptId": 1,
        "applicantZgh": "T001",
        "applicantName": "张三",
        "guideContent": "申报指南内容...",
        "acceptStartTime": "2024-08-01T00:00:00",
        "acceptEndTime": "2024-08-31T23:59:59",
        "status": "DRAFT",
        "submitTime": null,
        "reviewTime": null,
        "reviewerZgh": null,
        "reviewComments": null,
        "createTime": "2024-08-04T10:00:00",
        "updateTime": "2024-08-04T10:00:00",
        "createBy": "T001",
        "updateBy": "T001"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 5.2 根据项目编号查询申报信息

**接口地址**: `GET /api/project-application/code/{projectCode}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectCode | String | 是 | 项目编号 |

**请求示例**:
```http
GET /api/project-application/code/PROJ_2024_001
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "projectCode": "PROJ_2024_001",
    "projectName": "AI智能识别系统",
    "categoryId": 1,
    "typeId": 1,
    "deptId": 1,
    "applicantZgh": "T001",
    "applicantName": "张三",
    "guideContent": "申报指南内容...",
    "acceptStartTime": "2024-08-01T00:00:00",
    "acceptEndTime": "2024-08-31T23:59:59",
    "status": "DRAFT",
    "submitTime": null,
    "reviewTime": null,
    "reviewerZgh": null,
    "reviewComments": null,
    "createTime": "2024-08-04T10:00:00",
    "updateTime": "2024-08-04T10:00:00",
    "createBy": "T001",
    "updateBy": "T001"
  }
}
```

### 5.3 根据申报人获取申报列表

**接口地址**: `GET /api/project-application/applicant/{applicantZgh}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| applicantZgh | String | 是 | 申报人职工号 |

**请求示例**:
```http
GET /api/project-application/applicant/T001
```

**返回参数**: 返回该申报人的所有申报记录数组，格式同5.2

### 5.4 新增项目申报

**接口地址**: `POST /api/project-application`

**请求体**:
```json
{
  "projectCode": "PROJ_2024_001",
  "projectName": "AI智能识别系统",
  "categoryId": 1,
  "typeId": 1,
  "deptId": 1,
  "applicantZgh": "T001",
  "applicantName": "张三",
  "guideContent": "申报指南内容...",
  "acceptStartTime": "2024-08-01T00:00:00",
  "acceptEndTime": "2024-08-31T23:59:59",
  "status": "DRAFT",
  "createBy": "T001"
}
```

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectCode | String | 是 | 项目编号，唯一 |
| projectName | String | 是 | 项目名称 |
| categoryId | Integer | 是 | 项目大类ID |
| typeId | Integer | 是 | 项目类别ID |
| deptId | Integer | 是 | 管理部门ID |
| applicantZgh | String | 是 | 申报人职工号 |
| applicantName | String | 是 | 申报人姓名 |
| guideContent | String | 否 | 填报指南内容 |
| acceptStartTime | String | 否 | 受理开始时间 |
| acceptEndTime | String | 否 | 受理结束时间 |
| status | String | 否 | 状态，默认DRAFT |
| createBy | String | 是 | 创建人职工号 |

**返回参数**:
```json
{
  "code": 200,
  "message": "新增成功",
  "data": "新增成功"
}
```

### 5.5 提交申报

**接口地址**: `PUT /api/project-application/{id}/submit`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 申报ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| submitBy | String | 是 | 提交人职工号 |

**请求示例**:
```http
PUT /api/project-application/1/submit?submitBy=T001
```

**返回参数**:
```json
{
  "code": 200,
  "message": "提交成功",
  "data": "提交成功"
}
```

### 5.6 审核申报

**接口地址**: `PUT /api/project-application/{id}/review`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 申报ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| approved | Boolean | 是 | 是否通过 |
| reviewerZgh | String | 是 | 审核人职工号 |
| reviewComments | String | 否 | 审核意见 |

**请求示例**:
```http
PUT /api/project-application/1/review?approved=true&reviewerZgh=admin&reviewComments=申报材料完整，同意立项
```

**返回参数**:
```json
{
  "code": 200,
  "message": "审核成功",
  "data": "审核成功"
}
```

### 5.7 获取申报统计信息

**接口地址**: `GET /api/project-application/statistics`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deptId | Integer | 否 | 部门ID |
| categoryId | Integer | 否 | 大类ID |

**请求示例**:
```http
GET /api/project-application/statistics?deptId=1&categoryId=1
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalCount": 100,
    "statusCount": {
      "DRAFT": 20,
      "SUBMITTED": 30,
      "REVIEWING": 15,
      "APPROVED": 25,
      "REJECTED": 10
    }
  }
}
```

---

## 🔍 6. 项目中检管理

### 6.1 分页查询项目中检

**接口地址**: `GET /api/project-inspection/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryId | Integer | 否 | - | 项目大类ID |
| typeId | Integer | 否 | - | 项目类别ID |
| deptId | Integer | 否 | - | 管理部门ID |
| status | String | 否 | - | 状态 |
| projectCode | String | 否 | - | 项目编号 |
| projectLeaderZgh | String | 否 | - | 项目负责人职工号 |

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "projectCode": "PROJ_2024_001",
        "inspectionName": "AI智能识别系统中期检查",
        "categoryId": 1,
        "typeId": 1,
        "deptId": 1,
        "projectLeaderZgh": "T001",
        "projectLeaderName": "张三",
        "guideContent": "中检指南内容...",
        "acceptStartTime": "2024-09-01T00:00:00",
        "acceptEndTime": "2024-09-30T23:59:59",
        "inspectionProgress": 60.00,
        "progressDescription": "项目进展顺利，已完成60%",
        "status": "DRAFT",
        "submitTime": null,
        "reviewTime": null,
        "reviewerZgh": null,
        "reviewComments": null,
        "createTime": "2024-08-04T10:00:00",
        "updateTime": "2024-08-04T10:00:00",
        "createBy": "T001",
        "updateBy": "T001"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 6.2 根据项目编号获取中检信息

**接口地址**: `GET /api/project-inspection/project/{projectCode}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectCode | String | 是 | 项目编号 |

**请求示例**:
```http
GET /api/project-inspection/project/PROJ_2024_001
```

**返回参数**: 返回该项目的所有中检记录数组

### 6.3 根据项目负责人获取中检列表

**接口地址**: `GET /api/project-inspection/leader/{projectLeaderZgh}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectLeaderZgh | String | 是 | 项目负责人职工号 |

**请求示例**:
```http
GET /api/project-inspection/leader/T001
```

**返回参数**: 返回该负责人的所有中检记录数组

### 6.4 新增项目中检

**接口地址**: `POST /api/project-inspection`

**请求体**:
```json
{
  "projectCode": "PROJ_2024_001",
  "inspectionName": "AI智能识别系统中期检查",
  "categoryId": 1,
  "typeId": 1,
  "deptId": 1,
  "projectLeaderZgh": "T001",
  "projectLeaderName": "张三",
  "guideContent": "中检指南内容...",
  "acceptStartTime": "2024-09-01T00:00:00",
  "acceptEndTime": "2024-09-30T23:59:59",
  "inspectionProgress": 60.00,
  "progressDescription": "项目进展顺利，已完成60%",
  "status": "DRAFT",
  "createBy": "T001"
}
```

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectCode | String | 是 | 项目编号 |
| inspectionName | String | 是 | 中检名称 |
| categoryId | Integer | 是 | 项目大类ID |
| typeId | Integer | 是 | 项目类别ID |
| deptId | Integer | 是 | 管理部门ID |
| projectLeaderZgh | String | 是 | 项目负责人职工号 |
| projectLeaderName | String | 是 | 项目负责人姓名 |
| guideContent | String | 否 | 填报指南内容 |
| acceptStartTime | String | 否 | 受理开始时间 |
| acceptEndTime | String | 否 | 受理结束时间 |
| inspectionProgress | BigDecimal | 否 | 项目进度百分比 |
| progressDescription | String | 否 | 进度说明 |
| status | String | 否 | 状态，默认DRAFT |
| createBy | String | 是 | 创建人职工号 |

**返回参数**:
```json
{
  "code": 200,
  "message": "新增成功",
  "data": "新增成功"
}
```

### 6.5 提交中检

**接口地址**: `PUT /api/project-inspection/{id}/submit`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 中检ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| submitBy | String | 是 | 提交人职工号 |

**请求示例**:
```http
PUT /api/project-inspection/1/submit?submitBy=T001
```

**返回参数**:
```json
{
  "code": 200,
  "message": "提交成功",
  "data": "提交成功"
}
```

### 6.6 审核中检

**接口地址**: `PUT /api/project-inspection/{id}/review`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 中检ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| approved | Boolean | 是 | 是否通过 |
| reviewerZgh | String | 是 | 审核人职工号 |
| reviewComments | String | 否 | 审核意见 |

**请求示例**:
```http
PUT /api/project-inspection/1/review?approved=true&reviewerZgh=admin&reviewComments=中检材料完整，进度符合要求
```

**返回参数**:
```json
{
  "code": 200,
  "message": "审核成功",
  "data": "审核成功"
}
```

### 6.7 获取中检统计信息

**接口地址**: `GET /api/project-inspection/statistics`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deptId | Integer | 否 | 部门ID |
| categoryId | Integer | 否 | 大类ID |

**请求示例**:
```http
GET /api/project-inspection/statistics?deptId=1&categoryId=1
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalCount": 50,
    "statusCount": {
      "DRAFT": 10,
      "SUBMITTED": 15,
      "REVIEWING": 8,
      "APPROVED": 12,
      "REJECTED": 5
    }
  }
}
```

---

## ✅ 7. 项目结项管理

### 7.1 分页查询项目结项

**接口地址**: `GET /api/project-completion/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| categoryId | Integer | 否 | - | 项目大类ID |
| typeId | Integer | 否 | - | 项目类别ID |
| deptId | Integer | 否 | - | 管理部门ID |
| status | String | 否 | - | 状态 |
| projectCode | String | 否 | - | 项目编号 |
| projectLeaderZgh | String | 否 | - | 项目负责人职工号 |

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "projectCode": "PROJ_2024_001",
        "completionName": "AI智能识别系统结项",
        "categoryId": 1,
        "typeId": 1,
        "deptId": 1,
        "projectLeaderZgh": "T001",
        "projectLeaderName": "张三",
        "guideContent": "结项指南内容...",
        "acceptStartTime": "2024-11-01T00:00:00",
        "acceptEndTime": "2024-11-30T23:59:59",
        "completionResult": "成功开发AI智能识别系统",
        "achievementSummary": "项目成果总结...",
        "status": "DRAFT",
        "submitTime": null,
        "reviewTime": null,
        "reviewerZgh": null,
        "reviewComments": null,
        "completionScore": null,
        "createTime": "2024-08-04T10:00:00",
        "updateTime": "2024-08-04T10:00:00",
        "createBy": "T001",
        "updateBy": "T001"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 7.2 新增项目结项

**接口地址**: `POST /api/project-completion`

**请求体**:
```json
{
  "projectCode": "PROJ_2024_001",
  "completionName": "AI智能识别系统结项",
  "categoryId": 1,
  "typeId": 1,
  "deptId": 1,
  "projectLeaderZgh": "T001",
  "projectLeaderName": "张三",
  "guideContent": "结项指南内容...",
  "acceptStartTime": "2024-11-01T00:00:00",
  "acceptEndTime": "2024-11-30T23:59:59",
  "completionResult": "成功开发AI智能识别系统",
  "achievementSummary": "项目成果总结...",
  "status": "DRAFT",
  "createBy": "T001"
}
```

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectCode | String | 是 | 项目编号 |
| completionName | String | 是 | 结项名称 |
| categoryId | Integer | 是 | 项目大类ID |
| typeId | Integer | 是 | 项目类别ID |
| deptId | Integer | 是 | 管理部门ID |
| projectLeaderZgh | String | 是 | 项目负责人职工号 |
| projectLeaderName | String | 是 | 项目负责人姓名 |
| guideContent | String | 否 | 填报指南内容 |
| acceptStartTime | String | 否 | 受理开始时间 |
| acceptEndTime | String | 否 | 受理结束时间 |
| completionResult | String | 否 | 完成成果描述 |
| achievementSummary | String | 否 | 成果总结 |
| status | String | 否 | 状态，默认DRAFT |
| createBy | String | 是 | 创建人职工号 |

### 7.3 审核结项

**接口地址**: `PUT /api/project-completion/{id}/review`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 结项ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| approved | Boolean | 是 | 是否通过 |
| reviewerZgh | String | 是 | 审核人职工号 |
| reviewComments | String | 否 | 审核意见 |
| completionScore | BigDecimal | 否 | 结项评分 |

**请求示例**:
```http
PUT /api/project-completion/1/review?approved=true&reviewerZgh=admin&reviewComments=结项材料完整，成果显著&completionScore=95.5
```

**返回参数**:
```json
{
  "code": 200,
  "message": "审核成功",
  "data": "审核成功"
}
```

---

## 📁 8. 文件管理

### 8.1 分页查询项目文件

**接口地址**: `GET /api/project-files/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 当前页码 |
| size | Integer | 否 | 10 | 每页大小 |
| businessType | String | 否 | - | 业务类型 |
| businessId | Long | 否 | - | 业务ID |
| projectCode | String | 否 | - | 项目编号 |
| fileCategory | String | 否 | - | 文件分类 |
| uploadBy | String | 否 | - | 上传人 |

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "businessType": "APPLICATION",
        "businessId": 1,
        "projectCode": "PROJ_2024_001",
        "fileName": "uuid-string.pdf",
        "fileOriginalName": "项目申报书.pdf",
        "filePath": "project-files/application/2024/08/04/uuid-string.pdf",
        "fileSize": 1024000,
        "fileType": "application/pdf",
        "fileExtension": ".pdf",
        "bucketName": "rchx",
        "objectKey": "project-files/application/2024/08/04/uuid-string.pdf",
        "contentType": "application/pdf",
        "fileCategory": "MATERIAL",
        "description": "项目申报书",
        "uploadTime": "2024-08-04T10:00:00",
        "uploadBy": "T001",
        "isDeleted": false,
        "deleteTime": null,
        "deleteBy": null
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 8.2 根据业务信息获取文件列表

**接口地址**: `GET /api/project-files/business/{businessType}/{businessId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| businessType | String | 是 | 业务类型(COLLECTION,APPLICATION,INSPECTION,COMPLETION) |
| businessId | Long | 是 | 业务ID |

**请求示例**:
```http
GET /api/project-files/business/APPLICATION/1
```

**返回参数**: 返回该业务的所有文件记录数组

### 8.3 根据项目编号获取文件列表

**接口地址**: `GET /api/project-files/project/{projectCode}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectCode | String | 是 | 项目编号 |

**请求示例**:
```http
GET /api/project-files/project/PROJ_2024_001
```

**返回参数**: 返回该项目的所有文件记录数组

### 8.4 上传文件

**接口地址**: `POST /api/project-files/upload`

**请求头**:
```
Content-Type: multipart/form-data
```

**请求参数（Form Data）**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 上传的文件 |
| businessType | String | 是 | 业务类型 |
| businessId | Long | 是 | 业务ID |
| projectCode | String | 否 | 项目编号 |
| fileCategory | String | 是 | 文件分类(GUIDE,MATERIAL,REPORT,OTHER) |
| description | String | 否 | 文件描述 |
| uploadBy | String | 是 | 上传人职工号 |

**请求示例**:
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('businessType', 'APPLICATION');
formData.append('businessId', '1');
formData.append('projectCode', 'PROJ_2024_001');
formData.append('fileCategory', 'MATERIAL');
formData.append('description', '项目申报书');
formData.append('uploadBy', 'T001');

fetch('/api/project-files/upload', {
  method: 'POST',
  body: formData
});
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "businessType": "APPLICATION",
    "businessId": 1,
    "projectCode": "PROJ_2024_001",
    "fileName": "uuid-string.pdf",
    "fileOriginalName": "项目申报书.pdf",
    "filePath": "project-files/application/2024/08/04/uuid-string.pdf",
    "fileSize": 1024000,
    "fileType": "application/pdf",
    "fileExtension": ".pdf",
    "bucketName": "rchx",
    "objectKey": "project-files/application/2024/08/04/uuid-string.pdf",
    "contentType": "application/pdf",
    "fileCategory": "MATERIAL",
    "description": "项目申报书",
    "uploadTime": "2024-08-04T10:00:00",
    "uploadBy": "T001",
    "isDeleted": false,
    "deleteTime": null,
    "deleteBy": null
  }
}
```

### 8.5 删除文件

**接口地址**: `DELETE /api/project-files/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 文件ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deleteBy | String | 是 | 删除人职工号 |

**请求示例**:
```http
DELETE /api/project-files/1?deleteBy=T001
```

**返回参数**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": "删除成功"
}
```

### 8.6 批量删除文件

**接口地址**: `DELETE /api/project-files/batch`

**请求体**:
```json
{
  "ids": [1, 2, 3],
  "deleteBy": "T001"
}
```

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | Array<Long> | 是 | 文件ID数组 |
| deleteBy | String | 是 | 删除人职工号 |

**返回参数**:
```json
{
  "code": 200,
  "message": "批量删除成功",
  "data": "批量删除成功"
}
```

### 8.7 获取文件下载URL

**接口地址**: `GET /api/project-files/{id}/download-url`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 文件ID |

**请求示例**:
```http
GET /api/project-files/1/download-url
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "http://42.193.131.107:9000/rchx/project-files/application/2024/08/04/uuid-string.pdf"
}
```

### 8.8 获取文件统计信息

**接口地址**: `GET /api/project-files/statistics`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| businessType | String | 否 | 业务类型 |
| projectCode | String | 否 | 项目编号 |

**请求示例**:
```http
GET /api/project-files/statistics?businessType=APPLICATION&projectCode=PROJ_2024_001
```

**返回参数**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalCount": 100,
    "businessTypeCount": {
      "COLLECTION": 20,
      "APPLICATION": 40,
      "INSPECTION": 25,
      "COMPLETION": 15
    },
    "categoryCount": {
      "GUIDE": 10,
      "MATERIAL": 60,
      "REPORT": 25,
      "OTHER": 5
    }
  }
}
```

---

## 📝 9. 状态枚举说明

### 9.1 业务状态

#### 征集状态
- `DRAFT`: 草稿
- `PUBLISHED`: 已发布
- `CLOSED`: 已关闭

#### 申报/中检/结项状态
- `DRAFT`: 草稿
- `SUBMITTED`: 已提交
- `REVIEWING`: 审核中
- `APPROVED`: 已通过
- `REJECTED`: 已拒绝

### 9.2 文件分类
- `GUIDE`: 填报指南
- `MATERIAL`: 申报材料
- `REPORT`: 报告文档
- `OTHER`: 其他文件

### 9.3 业务类型
- `COLLECTION`: 项目征集
- `APPLICATION`: 项目申报
- `INSPECTION`: 项目中检
- `COMPLETION`: 项目结项

---

## 🔧 10. 错误码说明

### 10.1 HTTP状态码

| 状态码 | 说明 | 处理方式 |
|--------|------|----------|
| 200 | 请求成功 | 正常处理 |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 未授权 | 重新登录获取Token |
| 403 | 权限不足 | 联系管理员分配权限 |
| 404 | 资源不存在 | 检查请求路径和参数 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 10.2 业务错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 大类编码已存在 | 项目大类编码重复 |
| 400 | 类别编码已存在 | 项目类别编码重复 |
| 400 | 部门编码已存在 | 管理部门编码重复 |
| 400 | 征集编号已存在 | 项目征集编号重复 |
| 400 | 项目编号已存在 | 项目申报编号重复 |
| 400 | 文件不能为空 | 文件上传时未选择文件 |
| 404 | 项目大类不存在 | 查询的大类ID不存在 |
| 404 | 项目类别不存在 | 查询的类别ID不存在 |
| 404 | 管理部门不存在 | 查询的部门ID不存在 |
| 404 | 项目征集不存在 | 查询的征集ID不存在 |
| 404 | 项目申报不存在 | 查询的申报ID不存在 |
| 404 | 文件不存在 | 查询的文件ID不存在或已删除 |

---

## 🚀 11. 前端对接指南

### 11.1 请求头设置

```javascript
// 通用请求头
const headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'Authorization': 'Bearer ' + localStorage.getItem('token')
};

// 文件上传请求头
const uploadHeaders = {
  'Authorization': 'Bearer ' + localStorage.getItem('token')
  // 注意：文件上传时不要设置Content-Type，让浏览器自动设置
};
```

### 11.2 响应处理

```javascript
// 统一响应处理
function handleResponse(response) {
  if (response.code === 200) {
    return response.data;
  } else {
    throw new Error(response.message || '请求失败');
  }
}

// 使用示例
fetch('/api/project-categories/enabled', {
  headers: headers
})
.then(response => response.json())
.then(handleResponse)
.then(data => {
  console.log('获取成功:', data);
})
.catch(error => {
  console.error('请求失败:', error.message);
});
```

### 11.3 分页查询处理

```javascript
// 分页查询函数
async function fetchPageData(url, params = {}) {
  const queryString = new URLSearchParams({
    current: params.current || 1,
    size: params.size || 10,
    ...params
  }).toString();

  const response = await fetch(`${url}?${queryString}`, {
    headers: headers
  });

  const result = await response.json();
  return handleResponse(result);
}

// 使用示例
const pageData = await fetchPageData('/api/project-categories/page', {
  current: 1,
  size: 10,
  categoryName: '技术',
  isEnabled: true
});
```

### 11.4 文件上传处理

```javascript
// 文件上传函数
async function uploadFile(file, params) {
  const formData = new FormData();
  formData.append('file', file);

  Object.keys(params).forEach(key => {
    formData.append(key, params[key]);
  });

  const response = await fetch('/api/project-files/upload', {
    method: 'POST',
    headers: uploadHeaders,
    body: formData
  });

  const result = await response.json();
  return handleResponse(result);
}

// 使用示例
const fileInput = document.getElementById('fileInput');
const file = fileInput.files[0];

const uploadResult = await uploadFile(file, {
  businessType: 'APPLICATION',
  businessId: 1,
  projectCode: 'PROJ_2024_001',
  fileCategory: 'MATERIAL',
  description: '项目申报书',
  uploadBy: 'T001'
});
```

### 11.5 错误处理

```javascript
// 全局错误处理
function handleError(error) {
  if (error.message.includes('401')) {
    // 未授权，跳转到登录页
    window.location.href = '/login';
  } else if (error.message.includes('403')) {
    // 权限不足
    alert('权限不足，请联系管理员');
  } else {
    // 其他错误
    alert('操作失败：' + error.message);
  }
}

// 在所有请求中使用
fetch('/api/project-categories/page')
  .then(response => response.json())
  .then(handleResponse)
  .then(data => {
    // 处理成功数据
  })
  .catch(handleError);
```

---

## 📞 12. 技术支持

### 12.1 联系方式
- **开发团队**: GL开发团队
- **邮箱**: <EMAIL>
- **技术支持**: 工作日 9:00-18:00

### 12.2 文档信息
- **文档版本**: v1.0
- **最后更新**: 2024-08-04
- **接口总数**: 76个
- **支持的业务模块**: 8个

### 12.3 更新日志
- **v1.0 (2024-08-04)**: 初始版本，包含完整的项目管理系统API接口

---

**注意事项**:
1. 所有时间格式均为ISO 8601格式：`YYYY-MM-DDTHH:mm:ss`
2. 文件上传支持的格式：jpg, png, gif, pdf, doc, docx, xls, xlsx
3. 文件大小限制：单个文件不超过10MB
4. 所有接口都需要在请求头中携带有效的Authorization Token
5. 分页查询的页码从1开始
6. 逻辑删除的记录不会在查询结果中返回
