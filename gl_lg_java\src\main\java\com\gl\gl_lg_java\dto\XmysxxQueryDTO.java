package com.gl.gl_lg_java.dto;

import lombok.Data;

/**
 * 项目预算信息查询DTO
 */
@Data
public class XmysxxQueryDTO {
    
    /**
     * 项目编号
     */
    private String xmbh;
    
    /**
     * 项目名称
     */
    private String xmmc;
    
    /**
     * 支出金额开始
     */
    private String startAmount;
    
    /**
     * 支出金额结束
     */
    private String endAmount;
    
    /**
     * 支出时间开始
     */
    private String startDate;
    
    /**
     * 支出时间结束
     */
    private String endDate;
    
    /**
     * 凭单号
     */
    private String pdh;
    
    /**
     * 经办人
     */
    private String jbr;
    
    /**
     * 报销人
     */
    private String bxr;
    
    /**
     * 创建人工号
     */
    private String cjrgh;
    
    /**
     * 创建人姓名
     */
    private String cjrxm;
    
    /**
     * 审核状态
     */
    private String shzt;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String orderBy = "zcsj";
    
    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "desc";
}
