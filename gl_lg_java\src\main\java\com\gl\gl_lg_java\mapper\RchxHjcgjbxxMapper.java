package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gl.gl_lg_java.domain.RchxHjcgjbxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_hjcgjbxx(获奖成果基本信息)】的数据库操作Mapper
 * @createDate 2025-07-18 11:11:41
 * @Entity com.gl.gl_lg_java.domain.RchxHjcgjbxx
 */
@Mapper
public interface RchxHjcgjbxxMapper extends BaseMapper<RchxHjcgjbxx> {

    /**
     * 核心字段列表 - 用于列表查询优化
     */
    String CORE_FIELDS = "hjcgbh, hjcgmc, jlmc, jldj, hjjb, hjrq, dywcrxm, dywcrzgh, dwmc, shzt, czsj";

    /**
     * 根据获奖成果编号查询（详情查询，返回所有字段）
     */
    @Select("SELECT * FROM t_rchx_hjcgjbxx WHERE hjcgbh = #{hjcgbh}")
    RchxHjcgjbxx findByHjcgbh(@Param("hjcgbh") String hjcgbh);

    /**
     * 根据获奖成果名称模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_hjcgjbxx WHERE hjcgmc LIKE CONCAT('%', #{hjcgmc}, '%')")
    List<RchxHjcgjbxx> findByHjcgmcLike(@Param("hjcgmc") String hjcgmc);

    /**
     * 根据第一完成人职工号查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_hjcgjbxx WHERE dywcrzgh = #{dywcrzgh}")
    List<RchxHjcgjbxx> findByDywcrzgh(@Param("dywcrzgh") String dywcrzgh);

    /**
     * 根据第一完成人姓名模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_hjcgjbxx WHERE dywcrxm LIKE CONCAT('%', #{dywcrxm}, '%')")
    List<RchxHjcgjbxx> findByDywcrxmLike(@Param("dywcrxm") String dywcrxm);

    /**
     * 根据奖励名称模糊查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_hjcgjbxx WHERE jlmc LIKE CONCAT('%', #{jlmc}, '%')")
    List<RchxHjcgjbxx> findByJlmcLike(@Param("jlmc") String jlmc);

    /**
     * 根据审核状态查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_hjcgjbxx WHERE shzt = #{shzt}")
    List<RchxHjcgjbxx> findByShzt(@Param("shzt") String shzt);

    /**
     * 根据获奖级别查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_hjcgjbxx WHERE hjjb = #{hjjb}")
    List<RchxHjcgjbxx> findByHjjb(@Param("hjjb") String hjjb);

    /**
     * 根据奖励等级查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_hjcgjbxx WHERE jldj = #{jldj}")
    List<RchxHjcgjbxx> findByJldj(@Param("jldj") String jldj);

    /**
     * 根据获奖日期范围查询（优化：只查询核心字段）
     */
    @Select("SELECT " + CORE_FIELDS + " FROM t_rchx_hjcgjbxx WHERE hjrq &gt;= #{startDate} AND hjrq &lt;= #{endDate}")
    List<RchxHjcgjbxx> findByHjrqBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

    // 多条件查询已移至Service层使用QueryWrapper实现，更灵活且不易出错

    // 分页查询已移至Service层使用QueryWrapper + selectPage实现，更灵活且不易出错

    /**
     * 获取总数统计
     */
    @Select("SELECT COUNT(*) as totalCount FROM t_rchx_hjcgjbxx")
    Long getTotalCount();

    /**
     * 按审核状态统计
     */
    @Select("SELECT COALESCE(shzt, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_hjcgjbxx GROUP BY shzt")
    List<Map<String, Object>> getStatsByShzt();

    /**
     * 按教研室名称统计
     */
    @Select("SELECT COALESCE(jysmc, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_hjcgjbxx GROUP BY jysmc")
    List<Map<String, Object>> getStatsByJysmc();

    /**
     * 按颁奖单位统计
     */
    @Select("SELECT COALESCE(bjdw, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_hjcgjbxx GROUP BY bjdw")
    List<Map<String, Object>> getStatsByBjdw();

    /**
     * 按成果形式统计
     */
    @Select("SELECT COALESCE(cgxs, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_hjcgjbxx GROUP BY cgxs")
    List<Map<String, Object>> getStatsByCgxs();

    /**
     * 按获奖级别统计
     */
    @Select("SELECT COALESCE(hjjb, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_hjcgjbxx GROUP BY hjjb")
    List<Map<String, Object>> getStatsByHjjb();

    /**
     * 按奖励等级统计
     */
    @Select("SELECT COALESCE(jldj, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_hjcgjbxx GROUP BY jldj")
    List<Map<String, Object>> getStatsByJldj();

    /**
     * 按单位名称统计
     */
    @Select("SELECT COALESCE(dwmc, 'NULL或空') as fieldValue, COUNT(*) as count FROM t_rchx_hjcgjbxx GROUP BY dwmc")
    List<Map<String, Object>> getStatsByDwmc();

    /**
     * 快速获取所有奖励等级选项（不统计数量）
     */
    @Select("SELECT DISTINCT jldj FROM t_rchx_hjcgjbxx WHERE jldj IS NOT NULL AND jldj != '' ORDER BY jldj")
    List<String> findDistinctJldj();

    /**
     * 快速获取所有获奖级别选项（不统计数量）
     */
    @Select("SELECT DISTINCT hjjb FROM t_rchx_hjcgjbxx WHERE hjjb IS NOT NULL AND hjjb != '' ORDER BY hjjb")
    List<String> findDistinctHjjb();

    /**
     * 快速获取所有审核状态选项（不统计数量）
     */
    @Select("SELECT DISTINCT shzt FROM t_rchx_hjcgjbxx WHERE shzt IS NOT NULL AND shzt != '' ORDER BY shzt")
    List<String> findDistinctShzt();
}