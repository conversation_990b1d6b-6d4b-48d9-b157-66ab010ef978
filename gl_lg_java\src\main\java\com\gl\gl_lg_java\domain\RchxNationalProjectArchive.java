package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 国家/省部级项目归档主表
 * @TableName t_rchx_national_project_archive
 */
@TableName(value = "t_rchx_national_project_archive")
@Data
public class RchxNationalProjectArchive implements Serializable {
    
    /**
     * 归档ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 归档编号
     */
    @TableField(value = "archive_code")
    private String archiveCode;

    /**
     * 项目编号
     */
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 项目级别ID
     */
    @TableField(value = "level_id")
    private Integer levelId;

    /**
     * 所属部门ID
     */
    @TableField(value = "dept_id")
    private Integer deptId;

    /**
     * 项目负责人职工号
     */
    @TableField(value = "project_leader_zgh")
    private String projectLeaderZgh;

    /**
     * 项目负责人姓名
     */
    @TableField(value = "project_leader_name")
    private String projectLeaderName;

    /**
     * 项目负责人电话
     */
    @TableField(value = "project_leader_phone")
    private String projectLeaderPhone;

    /**
     * 项目负责人邮箱
     */
    @TableField(value = "project_leader_email")
    private String projectLeaderEmail;

    /**
     * 项目负责人职称
     */
    @TableField(value = "project_leader_title")
    private String projectLeaderTitle;

    /**
     * 项目开始日期
     */
    @TableField(value = "project_start_date")
    private LocalDate projectStartDate;

    /**
     * 项目结束日期
     */
    @TableField(value = "project_end_date")
    private LocalDate projectEndDate;

    /**
     * 项目周期(月)
     */
    @TableField(value = "project_duration_months")
    private Integer projectDurationMonths;

    /**
     * 项目预算(万元)
     */
    @TableField(value = "project_budget")
    private BigDecimal projectBudget;

    /**
     * 实际资助金额(万元)
     */
    @TableField(value = "actual_funding")
    private BigDecimal actualFunding;

    /**
     * 资助来源
     */
    @TableField(value = "funding_source")
    private String fundingSource;

    /**
     * 项目描述
     */
    @TableField(value = "project_description")
    private String projectDescription;

    /**
     * 成果总结
     */
    @TableField(value = "achievement_summary")
    private String achievementSummary;

    /**
     * 关键词(逗号分隔)
     */
    @TableField(value = "keywords")
    private String keywords;

    /**
     * 研究领域
     */
    @TableField(value = "research_field")
    private String researchField;

    /**
     * 合作单位
     */
    @TableField(value = "cooperation_units")
    private String cooperationUnits;

    /**
     * 归档状态(PENDING待归档,ARCHIVED已归档,REVIEWING审核中,APPROVED审核通过,REJECTED审核拒绝,INVALID失效)
     */
    @TableField(value = "archive_status")
    private String archiveStatus;

    /**
     * 归档时间
     */
    @TableField(value = "archive_time")
    private LocalDateTime archiveTime;

    /**
     * 归档人职工号
     */
    @TableField(value = "archive_by")
    private String archiveBy;

    /**
     * 归档人姓名
     */
    @TableField(value = "archive_by_name")
    private String archiveByName;

    /**
     * 审核时间
     */
    @TableField(value = "review_time")
    private LocalDateTime reviewTime;

    /**
     * 审核人职工号
     */
    @TableField(value = "reviewer_zgh")
    private String reviewerZgh;

    /**
     * 审核人姓名
     */
    @TableField(value = "reviewer_name")
    private String reviewerName;

    /**
     * 审核意见
     */
    @TableField(value = "review_comments")
    private String reviewComments;

    /**
     * 审核评分
     */
    @TableField(value = "review_score")
    private BigDecimal reviewScore;

    /**
     * 来源类型(MANUAL手动录入,IMPORT从业务系统导入)
     */
    @TableField(value = "source_type")
    private String sourceType;

    /**
     * 来源业务ID(关联申报/结项等表)
     */
    @TableField(value = "source_business_id")
    private Long sourceBusinessId;

    /**
     * 优先级(HIGH高,NORMAL普通,LOW低)
     */
    @TableField(value = "priority_level")
    private String priorityLevel;

    /**
     * 保密级别(PUBLIC公开,INTERNAL内部,CONFIDENTIAL机密,SECRET秘密)
     */
    @TableField(value = "confidentiality_level")
    private String confidentialityLevel;

    /**
     * 归档截止时间
     */
    @TableField(value = "archive_deadline")
    private LocalDateTime archiveDeadline;

    /**
     * 是否已发送提醒(0否,1是)
     */
    @TableField(value = "reminder_sent")
    private Boolean reminderSent;

    /**
     * 最后提醒时间
     */
    @TableField(value = "last_reminder_time")
    private LocalDateTime lastReminderTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人职工号
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 更新人职工号
     */
    @TableField(value = "update_by")
    private String updateBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
