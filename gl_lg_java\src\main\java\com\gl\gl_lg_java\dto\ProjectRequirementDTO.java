package com.gl.gl_lg_java.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目需求配置DTO
 */
@Data
public class ProjectRequirementDTO {
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 项目类型
     */
    private String projectType;
    
    /**
     * 项目描述
     */
    private String projectDescription;
    
    /**
     * 要求的研究领域
     */
    private List<String> requiredFields;
    
    /**
     * 要求的学科门类
     */
    private List<String> requiredXkml;
    
    /**
     * 要求的一级学科
     */
    private List<String> requiredYjxk;
    
    /**
     * 最低职称等级
     */
    private String minTitleLevel;
    
    /**
     * 最低学历要求
     */
    private String minEducation;
    
    /**
     * 项目关键词
     */
    private List<String> projectKeywords;
    
    // ========== 权重配置 ==========
    
    /**
     * 学术影响力权重 (0-100)
     */
    private BigDecimal academicWeight = new BigDecimal("25");
    
    /**
     * 领域深耕度权重 (0-100)
     */
    private BigDecimal fieldWeight = new BigDecimal("25");
    
    /**
     * 团队适配值权重 (0-100)
     */
    private BigDecimal teamWeight = new BigDecimal("25");
    
    /**
     * 成果落地性权重 (0-100)
     */
    private BigDecimal practicalWeight = new BigDecimal("25");
    
    // ========== 筛选条件 ==========
    
    /**
     * 最低获奖数量要求
     */
    private Integer minAwardCount = 0;
    
    /**
     * 最低项目数量要求
     */
    private Integer minProjectCount = 0;
    
    /**
     * 是否要求近期获奖 (近3年)
     */
    private Boolean requireRecentAwards = false;
    
    /**
     * 是否要求合作经验
     */
    private Boolean requireCollaboration = false;
    
    /**
     * 排除的教师ID列表
     */
    private List<String> excludeTeacherIds;
    
    /**
     * 最大返回结果数量
     */
    private Integer maxResults = 50;
    
    /**
     * 最低适配度分数阈值
     */
    private BigDecimal minScoreThreshold = new BigDecimal("60");
    
    /**
     * 验证权重总和是否为100
     */
    public boolean isWeightValid() {
        BigDecimal total = academicWeight.add(fieldWeight).add(teamWeight).add(practicalWeight);
        return total.compareTo(new BigDecimal("100")) == 0;
    }
    
    /**
     * 获取权重配置描述
     */
    public String getWeightDescription() {
        return String.format("学术影响力:%s%%, 领域深耕度:%s%%, 团队适配值:%s%%, 成果落地性:%s%%",
                academicWeight, fieldWeight, teamWeight, practicalWeight);
    }
}
