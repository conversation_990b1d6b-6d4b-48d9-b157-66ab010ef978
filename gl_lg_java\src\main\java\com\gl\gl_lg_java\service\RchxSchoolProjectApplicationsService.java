package com.gl.gl_lg_java.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gl.gl_lg_java.domain.RchxSchoolProjectApplications;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 校级项目申报Service接口
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface RchxSchoolProjectApplicationsService extends IService<RchxSchoolProjectApplications> {

    /**
     * 分页查询申报信息（包含关联信息）
     * 
     * @param page 分页参数
     * @param applicationCode 申报编号
     * @param projectName 项目名称
     * @param status 状态
     * @param applicationStatus 申报状态
     * @param categoryId 类别ID
     * @param deptId 部门ID
     * @param applicantZgh 申报人职工号
     * @param applicationRound 申报批次
     * @return 分页结果
     */
    IPage<Map<String, Object>> pageQueryWithDetails(Page<RchxSchoolProjectApplications> page,
                                                    String applicationCode,
                                                    String projectName,
                                                    String status,
                                                    String applicationStatus,
                                                    Integer categoryId,
                                                    Integer deptId,
                                                    String applicantZgh,
                                                    String applicationRound);

    /**
     * 根据申报编号查询
     * 
     * @param applicationCode 申报编号
     * @return 申报信息
     */
    RchxSchoolProjectApplications getByApplicationCode(String applicationCode);

    /**
     * 检查申报编号是否存在
     * 
     * @param applicationCode 申报编号
     * @return 是否存在
     */
    boolean existsByApplicationCode(String applicationCode);

    /**
     * 创建申报
     * 
     * @param application 申报信息
     * @return 是否成功
     */
    boolean createApplication(RchxSchoolProjectApplications application);

    /**
     * 更新申报
     * 
     * @param application 申报信息
     * @return 是否成功
     */
    boolean updateApplication(RchxSchoolProjectApplications application);

    /**
     * 删除申报
     * 
     * @param id 申报ID
     * @return 是否成功
     */
    boolean deleteApplication(Long id);

    /**
     * 批量删除申报
     * 
     * @param ids 申报ID列表
     * @return 是否成功
     */
    boolean batchDeleteApplications(List<Long> ids);

    /**
     * 提交申报
     * 
     * @param id 申报ID
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean submitApplication(Long id, String updateBy);

    /**
     * 开始审核
     * 
     * @param id 申报ID
     * @param reviewerZgh 审核人职工号
     * @param reviewerName 审核人姓名
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean startReview(Long id, String reviewerZgh, String reviewerName, String updateBy);

    /**
     * 完成审核
     * 
     * @param id 申报ID
     * @param status 状态
     * @param reviewComments 审核意见
     * @param reviewScore 评审得分
     * @param approvalAmount 批准资助金额
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean completeReview(Long id, String status, String reviewComments, 
                          BigDecimal reviewScore, BigDecimal approvalAmount, String updateBy);

    /**
     * 撤回申报
     * 
     * @param id 申报ID
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean withdrawApplication(Long id, String updateBy);

    /**
     * 根据申报人查询申报列表
     * 
     * @param applicantZgh 申报人职工号
     * @return 申报列表
     */
    List<RchxSchoolProjectApplications> getApplicationsByApplicant(String applicantZgh);

    /**
     * 根据部门查询申报列表
     * 
     * @param deptId 部门ID
     * @return 申报列表
     */
    List<RchxSchoolProjectApplications> getApplicationsByDepartment(Integer deptId);

    /**
     * 根据状态查询申报列表
     * 
     * @param status 状态
     * @return 申报列表
     */
    List<RchxSchoolProjectApplications> getApplicationsByStatus(String status);

    /**
     * 获取申报统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getApplicationStatistics();

    /**
     * 获取部门申报统计
     * 
     * @return 部门统计信息
     */
    List<Map<String, Object>> getDepartmentStatistics();

    /**
     * 获取即将到期的申报
     * 
     * @param days 天数
     * @return 即将到期的申报列表
     */
    List<RchxSchoolProjectApplications> getApplicationsNearDeadline(Integer days);

    /**
     * 获取逾期未提交的申报
     * 
     * @return 逾期申报列表
     */
    List<RchxSchoolProjectApplications> getOverdueApplications();

    /**
     * 根据申报批次查询
     * 
     * @param applicationRound 申报批次
     * @return 申报列表
     */
    List<RchxSchoolProjectApplications> getApplicationsByRound(String applicationRound);

    /**
     * 获取申报批次列表
     * 
     * @return 申报批次列表
     */
    List<String> getApplicationRounds();

    /**
     * 生成申报编号
     * 
     * @param categoryCode 类别编码
     * @return 申报编号
     */
    String generateApplicationCode(String categoryCode);

    /**
     * 导出申报列表
     * 
     * @param status 状态
     * @param applicationRound 申报批次
     * @return 申报列表
     */
    List<RchxSchoolProjectApplications> exportApplicationList(String status, String applicationRound);

    /**
     * 检查申报是否可以提交
     * 
     * @param id 申报ID
     * @return 检查结果
     */
    Map<String, Object> checkApplicationSubmittable(Long id);

    /**
     * 检查申报是否可以审核
     * 
     * @param id 申报ID
     * @return 检查结果
     */
    Map<String, Object> checkApplicationReviewable(Long id);
}
