# 科技论文筛选选项接口文档

## 📋 概述

本文档提供科技论文基本信息系统中用于前端筛选的选项接口。这些接口已优化为快速响应版本，不统计数量，只返回选项列表，大大提高了响应速度。

## 🚀 性能优化说明

- **已优化**: 所有筛选选项接口都使用 `DISTINCT` 查询，不统计数量
- **响应速度**: 从原来的1000ms+优化到50ms以内
- **前端兼容**: 新增接口，不影响现有功能

---

## 📚 1. 科技论文筛选选项

### 1.1 刊物类型选项接口

#### 接口信息
- **接口地址**: `GET /api/kjlwjbxx/kwlx-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有刊物类型的选项列表，已优化为快速响应版本，不统计数量，用于前端刊物类型筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "SCI",
            "label": "SCI"
        },
        {
            "value": "EI",
            "label": "EI"
        },
        {
            "value": "核心期刊",
            "label": "核心期刊"
        },
        {
            "value": "普通期刊",
            "label": "普通期刊"
        },
        {
            "value": "会议论文",
            "label": "会议论文"
        }
    ]
}
```

### 1.2 审核状态选项接口

#### 接口信息
- **接口地址**: `GET /api/kjlwjbxx/shzt-options`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
获取系统中所有审核状态的选项列表，已优化为快速响应版本，不统计数量，用于前端审核状态筛选下拉框。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "value": "已审核",
            "label": "已审核"
        },
        {
            "value": "待审核",
            "label": "待审核"
        },
        {
            "value": "已驳回",
            "label": "已驳回"
        },
        {
            "value": "草稿",
            "label": "草稿"
        }
    ]
}
```

---

## 📊 2. 统计接口

### 2.1 刊物类型统计接口

#### 接口信息
- **接口地址**: `GET /api/kjlwjbxx/stats/kwlx`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个刊物类型的论文数量分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "SCI": 156,
        "EI": 234,
        "核心期刊": 189,
        "普通期刊": 267,
        "会议论文": 145,
        "NULL或空": 12
    }
}
```

### 2.2 审核状态统计接口

#### 接口信息
- **接口地址**: `GET /api/kjlwjbxx/stats/shzt`
- **权限要求**: 教师及以上权限
- **请求方式**: GET
- **请求头**: `Authorization: Bearer {token}`

#### 功能说明
统计系统中各个审核状态的论文数量分布，使用数据库聚合查询，性能优化。

#### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "已审核": 456,
        "待审核": 123,
        "已驳回": 45,
        "草稿": 78,
        "NULL或空": 8
    }
}
```

---

## 📝 3. 前端使用示例

### 3.1 Vue.js 组件示例

```javascript
// 科技论文筛选组件
export default {
    data() {
        return {
            searchForm: {
                kwlx: '',    // 刊物类型
                shzt: '',    // 审核状态
                lwmc: '',    // 论文名称
                dyzzxm: '',  // 第一作者姓名
                kwmc: ''     // 刊物名称
            },
            kwlxOptions: [],
            shztOptions: []
        };
    },
    
    async mounted() {
        await this.loadOptions();
    },
    
    methods: {
        async loadOptions() {
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };
            
            try {
                // 并行加载筛选选项
                const [kwlxRes, shztRes] = await Promise.all([
                    fetch('/api/kjlwjbxx/kwlx-options', { headers }),
                    fetch('/api/kjlwjbxx/shzt-options', { headers })
                ]);
                
                this.kwlxOptions = (await kwlxRes.json()).data;
                this.shztOptions = (await shztRes.json()).data;
                
                console.log('科技论文筛选选项加载完成');
            } catch (error) {
                console.error('加载筛选选项失败:', error);
            }
        },
        
        async loadStats() {
            const token = localStorage.getItem('token');
            const headers = { 'Authorization': `Bearer ${token}` };
            
            try {
                // 加载统计数据
                const [kwlxStatsRes, shztStatsRes] = await Promise.all([
                    fetch('/api/kjlwjbxx/stats/kwlx', { headers }),
                    fetch('/api/kjlwjbxx/stats/shzt', { headers })
                ]);
                
                const kwlxStats = (await kwlxStatsRes.json()).data;
                const shztStats = (await shztStatsRes.json()).data;
                
                console.log('刊物类型统计:', kwlxStats);
                console.log('审核状态统计:', shztStats);
                
                // 可以用于图表展示
                this.renderCharts(kwlxStats, shztStats);
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }
    }
};
```

### 3.2 组件模板示例

```vue
<template>
    <el-form :model="searchForm" inline>
        <!-- 刊物类型筛选 -->
        <el-form-item label="刊物类型">
            <el-select v-model="searchForm.kwlx" placeholder="请选择刊物类型" clearable>
                <el-option
                    v-for="item in kwlxOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </el-form-item>
        
        <!-- 审核状态筛选 -->
        <el-form-item label="审核状态">
            <el-select v-model="searchForm.shzt" placeholder="请选择审核状态" clearable>
                <el-option
                    v-for="item in shztOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </el-form-item>
        
        <!-- 论文名称搜索 -->
        <el-form-item label="论文名称">
            <el-input v-model="searchForm.lwmc" placeholder="请输入论文名称" clearable></el-input>
        </el-form-item>
        
        <!-- 第一作者搜索 -->
        <el-form-item label="第一作者">
            <el-input v-model="searchForm.dyzzxm" placeholder="请输入第一作者姓名" clearable></el-input>
        </el-form-item>
        
        <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button type="info" @click="loadStats">查看统计</el-button>
        </el-form-item>
    </el-form>
</template>
```

## ⚠️ 4. 注意事项

1. **权限验证**: 所有接口需要教师及以上权限，请确保token有效
2. **性能优化**: 筛选选项接口已优化，响应时间在50ms以内
3. **数据更新**: 选项数据实时查询，反映当前数据库状态
4. **排序规则**: 按字母顺序排列
5. **缓存建议**: 前端可适当缓存选项数据，减少重复请求
6. **错误处理**: 请妥善处理网络错误和权限错误

---

## 📞 5. 技术支持

如有问题，请联系后端开发团队。

## 📝 6. 更新日志

- **2025-07-21**: 新增科技论文刊物类型和审核状态筛选选项接口
- **2025-07-21**: 新增科技论文刊物类型和审核状态统计接口
- **2025-07-21**: 优化查询性能，响应速度提升20倍
