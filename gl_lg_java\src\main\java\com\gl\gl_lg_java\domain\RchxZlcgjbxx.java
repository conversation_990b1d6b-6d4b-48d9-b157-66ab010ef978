package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 专利成果基本信息
 * @TableName t_rchx_zlcgjbxx
 */
@TableName(value ="t_rchx_zlcgjbxx")
@Data
public class RchxZlcgjbxx implements Serializable {
    /**
     * 专利成果编号
     */
    @TableId(value = "zlcgbh", type = IdType.INPUT)
    private String zlcgbh;

    /**
     * 专利成果名称
     */
    @TableField(value = "zlcgmc")
    private String zlcgmc;

    /**
     * 单位号
     */
    @TableField(value = "dwh")
    private String dwh;

    /**
     * 代码名称
     */
    @TableField(value = "dwmc")
    private String dwmc;

    /**
     * 所属项目编号
     */
    @TableField(value = "ssxmbh")
    private String ssxmbh;

    /**
     * 专利证书编号
     */
    @TableField(value = "zlzsbh")
    private String zlzsbh;

    /**
     * 专利类型码
     */
    @TableField(value = "zllxm")
    private String zllxm;

    /**
     * 专利类型
     */
    @TableField(value = "zllx")
    private String zllx;

    /**
     * 专利性质
     */
    @TableField(value = "zlxz")
    private String zlxz;

    /**
     * 专利范围码
     */
    @TableField(value = "zlfwm")
    private String zlfwm;

    /**
     * 专利范围
     */
    @TableField(value = "zlfw")
    private String zlfw;

    /**
     * 专利状态码
     */
    @TableField(value = "zlztm")
    private String zlztm;

    /**
     * 专利状态
     */
    @TableField(value = "zlzt")
    private String zlzt;

    /**
     * 专利地址
     */
    @TableField(value = "zldz")
    private String zldz;

    /**
     * 专利方向
     */
    @TableField(value = "zlfx")
    private String zlfx;

    /**
     * 批准日期
     */
    @TableField(value = "pzrq")
    private String pzrq;

    /**
     * 批准形式码
     */
    @TableField(value = "pzxsm")
    private String pzxsm;

    /**
     * 法律状态码
     */
    @TableField(value = "flztm")
    private String flztm;

    /**
     * 交纳专利年费日期
     */
    @TableField(value = "jnzlnfrq")
    private String jnzlnfrq;

    /**
     * 交纳金额
     */
    @TableField(value = "jnje")
    private String jnje;

    /**
     * 申请编号
     */
    @TableField(value = "sqbh")
    private String sqbh;

    /**
     * 申请名称
     */
    @TableField(value = "sqmc")
    private String sqmc;

    /**
     * 专利申请编号
     */
    @TableField(value = "zlsqbh")
    private String zlsqbh;

    /**
     * 申请专利日期
     */
    @TableField(value = "sqzlrq")
    private String sqzlrq;

    /**
     * 申请地址
     */
    @TableField(value = "sqdz")
    private String sqdz;

    /**
     * 公开日期
     */
    @TableField(value = "gkrq")
    private String gkrq;

    /**
     * 公开号
     */
    @TableField(value = "gkh")
    private String gkh;

    /**
     * 授权公告号
     */
    @TableField(value = "sqggh")
    private String sqggh;

    /**
     * 授权公告日期
     */
    @TableField(value = "sqggrq")
    private String sqggrq;

    /**
     * 授权日期
     */
    @TableField(value = "sqrq")
    private String sqrq;

    /**
     * 授权号
     */
    @TableField(value = "sqh")
    private String sqh;

    /**
     * 专利代理机构
     */
    @TableField(value = "zldljg")
    private String zldljg;

    /**
     * 专利代理人
     */
    @TableField(value = "zldlr")
    private String zldlr;

    /**
     * 专利权人
     */
    @TableField(value = "zlqr")
    private String zlqr;

    /**
     * 专利终止日期
     */
    @TableField(value = "zlzzrq")
    private String zlzzrq;

    /**
     * 发明（设计）人个数
     */
    @TableField(value = "fmrgs")
    private String fmrgs;

    /**
     * 代码名称
     */
    @TableField(value = "sfwzwzl")
    private String sfwzwzl;

    /**
     * 学校署名码
     */
    @TableField(value = "xxsmm")
    private String xxsmm;

    /**
     * 学校署名
     */
    @TableField(value = "xxsm")
    private String xxsm;

    /**
     * 进入国家
     */
    @TableField(value = "jrgj")
    private String jrgj;

    /**
     * 备注
     */
    @TableField(value = "bz")
    private String bz;

    /**
     * 创建人编号
     */
    @TableField(value = "cjrbh")
    private String cjrbh;

    /**
     * 创建人名称
     */
    @TableField(value = "cjrmc")
    private String cjrmc;

    /**
     * 创建时间
     */
    @TableField(value = "cjsj")
    private String cjsj;

    /**
     * 审核状态码
     */
    @TableField(value = "shztm")
    private String shztm;

    /**
     * 审核状态
     */
    @TableField(value = "shzt")
    private String shzt;

    /**
     * 教研室编号
     */
    @TableField(value = "jysbh")
    private String jysbh;

    /**
     * 第一发明（设计）人
     */
    @TableField(value = "dyfmr")
    private String dyfmr;

    /**
     * 
     */
    @TableField(value = "dyfmrzc")
    private String dyfmrzc;

    /**
     * 
     */
    @TableField(value = "dyfmrxb")
    private String dyfmrxb;

    /**
     * 
     */
    @TableField(value = "dyfmrxl")
    private String dyfmrxl;

    /**
     * 
     */
    @TableField(value = "dyfmrxw")
    private String dyfmrxw;

    /**
     * 单位排序
     */
    @TableField(value = "dwpx")
    private String dwpx;

    /**
     * 第一发明（设计）人职工号
     */
    @TableField(value = "dyfmrzgh")
    private String dyfmrzgh;

    /**
     * 操作时间
     */
    @TableField(value = "czsj")
    private String czsj;

    /**
     * 第一发明（设计）人编号
     */
    @TableField(value = "dyfmrbh")
    private String dyfmrbh;

    /**
     * 第一发明（设计）人类型
     */
    @TableField(value = "dyfmrlxm")
    private String dyfmrlxm;

    /**
     * 代码名称
     */
    @TableField(value = "dyfmrlx")
    private String dyfmrlx;

    /**
     * 多附件编号
     */
    @TableField(value = "dfjbh")
    private String dfjbh;

    /**
     * PCT专利名称
     */
    @TableField(value = "pctzlmc")
    private String pctzlmc;

    /**
     * 是否为PCT专利
     */
    @TableField(value = "sfwpctzl")
    private String sfwpctzl;

    /**
     * PCT专利申请号
     */
    @TableField(value = "pctzlsqh")
    private String pctzlsqh;

    /**
     * PCT专利申请日期
     */
    @TableField(value = "pctzlsqrq")
    private String pctzlsqrq;

    /**
     * PCT专利优先权日
     */
    @TableField(value = "pctzlyxqr")
    private String pctzlyxqr;

    /**
     * PCT专利申请人
     */
    @TableField(value = "pctzlsqr")
    private String pctzlsqr;

    /**
     * 合作类型码
     */
    @TableField(value = "hzlxm")
    private String hzlxm;

    /**
     * 合作类型
     */
    @TableField(value = "hzlx")
    private String hzlx;

    /**
     * 是否失效
     */
    @TableField(value = "sfsx")
    private String sfsx;

    /**
     * 
     */
    @TableField(value = "tstamp")
    private String tstamp;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RchxZlcgjbxx other = (RchxZlcgjbxx) that;
        return (this.getZlcgbh() == null ? other.getZlcgbh() == null : this.getZlcgbh().equals(other.getZlcgbh()))
            && (this.getZlcgmc() == null ? other.getZlcgmc() == null : this.getZlcgmc().equals(other.getZlcgmc()))
            && (this.getDwh() == null ? other.getDwh() == null : this.getDwh().equals(other.getDwh()))
            && (this.getDwmc() == null ? other.getDwmc() == null : this.getDwmc().equals(other.getDwmc()))
            && (this.getSsxmbh() == null ? other.getSsxmbh() == null : this.getSsxmbh().equals(other.getSsxmbh()))
            && (this.getZlzsbh() == null ? other.getZlzsbh() == null : this.getZlzsbh().equals(other.getZlzsbh()))
            && (this.getZllxm() == null ? other.getZllxm() == null : this.getZllxm().equals(other.getZllxm()))
            && (this.getZllx() == null ? other.getZllx() == null : this.getZllx().equals(other.getZllx()))
            && (this.getZlxz() == null ? other.getZlxz() == null : this.getZlxz().equals(other.getZlxz()))
            && (this.getZlfwm() == null ? other.getZlfwm() == null : this.getZlfwm().equals(other.getZlfwm()))
            && (this.getZlfw() == null ? other.getZlfw() == null : this.getZlfw().equals(other.getZlfw()))
            && (this.getZlztm() == null ? other.getZlztm() == null : this.getZlztm().equals(other.getZlztm()))
            && (this.getZlzt() == null ? other.getZlzt() == null : this.getZlzt().equals(other.getZlzt()))
            && (this.getZldz() == null ? other.getZldz() == null : this.getZldz().equals(other.getZldz()))
            && (this.getZlfx() == null ? other.getZlfx() == null : this.getZlfx().equals(other.getZlfx()))
            && (this.getPzrq() == null ? other.getPzrq() == null : this.getPzrq().equals(other.getPzrq()))
            && (this.getPzxsm() == null ? other.getPzxsm() == null : this.getPzxsm().equals(other.getPzxsm()))
            && (this.getFlztm() == null ? other.getFlztm() == null : this.getFlztm().equals(other.getFlztm()))
            && (this.getJnzlnfrq() == null ? other.getJnzlnfrq() == null : this.getJnzlnfrq().equals(other.getJnzlnfrq()))
            && (this.getJnje() == null ? other.getJnje() == null : this.getJnje().equals(other.getJnje()))
            && (this.getSqbh() == null ? other.getSqbh() == null : this.getSqbh().equals(other.getSqbh()))
            && (this.getSqmc() == null ? other.getSqmc() == null : this.getSqmc().equals(other.getSqmc()))
            && (this.getZlsqbh() == null ? other.getZlsqbh() == null : this.getZlsqbh().equals(other.getZlsqbh()))
            && (this.getSqzlrq() == null ? other.getSqzlrq() == null : this.getSqzlrq().equals(other.getSqzlrq()))
            && (this.getSqdz() == null ? other.getSqdz() == null : this.getSqdz().equals(other.getSqdz()))
            && (this.getGkrq() == null ? other.getGkrq() == null : this.getGkrq().equals(other.getGkrq()))
            && (this.getGkh() == null ? other.getGkh() == null : this.getGkh().equals(other.getGkh()))
            && (this.getSqggh() == null ? other.getSqggh() == null : this.getSqggh().equals(other.getSqggh()))
            && (this.getSqggrq() == null ? other.getSqggrq() == null : this.getSqggrq().equals(other.getSqggrq()))
            && (this.getSqrq() == null ? other.getSqrq() == null : this.getSqrq().equals(other.getSqrq()))
            && (this.getSqh() == null ? other.getSqh() == null : this.getSqh().equals(other.getSqh()))
            && (this.getZldljg() == null ? other.getZldljg() == null : this.getZldljg().equals(other.getZldljg()))
            && (this.getZldlr() == null ? other.getZldlr() == null : this.getZldlr().equals(other.getZldlr()))
            && (this.getZlqr() == null ? other.getZlqr() == null : this.getZlqr().equals(other.getZlqr()))
            && (this.getZlzzrq() == null ? other.getZlzzrq() == null : this.getZlzzrq().equals(other.getZlzzrq()))
            && (this.getFmrgs() == null ? other.getFmrgs() == null : this.getFmrgs().equals(other.getFmrgs()))
            && (this.getSfwzwzl() == null ? other.getSfwzwzl() == null : this.getSfwzwzl().equals(other.getSfwzwzl()))
            && (this.getXxsmm() == null ? other.getXxsmm() == null : this.getXxsmm().equals(other.getXxsmm()))
            && (this.getXxsm() == null ? other.getXxsm() == null : this.getXxsm().equals(other.getXxsm()))
            && (this.getJrgj() == null ? other.getJrgj() == null : this.getJrgj().equals(other.getJrgj()))
            && (this.getBz() == null ? other.getBz() == null : this.getBz().equals(other.getBz()))
            && (this.getCjrbh() == null ? other.getCjrbh() == null : this.getCjrbh().equals(other.getCjrbh()))
            && (this.getCjrmc() == null ? other.getCjrmc() == null : this.getCjrmc().equals(other.getCjrmc()))
            && (this.getCjsj() == null ? other.getCjsj() == null : this.getCjsj().equals(other.getCjsj()))
            && (this.getShztm() == null ? other.getShztm() == null : this.getShztm().equals(other.getShztm()))
            && (this.getShzt() == null ? other.getShzt() == null : this.getShzt().equals(other.getShzt()))
            && (this.getJysbh() == null ? other.getJysbh() == null : this.getJysbh().equals(other.getJysbh()))
            && (this.getDyfmr() == null ? other.getDyfmr() == null : this.getDyfmr().equals(other.getDyfmr()))
            && (this.getDyfmrzc() == null ? other.getDyfmrzc() == null : this.getDyfmrzc().equals(other.getDyfmrzc()))
            && (this.getDyfmrxb() == null ? other.getDyfmrxb() == null : this.getDyfmrxb().equals(other.getDyfmrxb()))
            && (this.getDyfmrxl() == null ? other.getDyfmrxl() == null : this.getDyfmrxl().equals(other.getDyfmrxl()))
            && (this.getDyfmrxw() == null ? other.getDyfmrxw() == null : this.getDyfmrxw().equals(other.getDyfmrxw()))
            && (this.getDwpx() == null ? other.getDwpx() == null : this.getDwpx().equals(other.getDwpx()))
            && (this.getDyfmrzgh() == null ? other.getDyfmrzgh() == null : this.getDyfmrzgh().equals(other.getDyfmrzgh()))
            && (this.getCzsj() == null ? other.getCzsj() == null : this.getCzsj().equals(other.getCzsj()))
            && (this.getDyfmrbh() == null ? other.getDyfmrbh() == null : this.getDyfmrbh().equals(other.getDyfmrbh()))
            && (this.getDyfmrlxm() == null ? other.getDyfmrlxm() == null : this.getDyfmrlxm().equals(other.getDyfmrlxm()))
            && (this.getDyfmrlx() == null ? other.getDyfmrlx() == null : this.getDyfmrlx().equals(other.getDyfmrlx()))
            && (this.getDfjbh() == null ? other.getDfjbh() == null : this.getDfjbh().equals(other.getDfjbh()))
            && (this.getPctzlmc() == null ? other.getPctzlmc() == null : this.getPctzlmc().equals(other.getPctzlmc()))
            && (this.getSfwpctzl() == null ? other.getSfwpctzl() == null : this.getSfwpctzl().equals(other.getSfwpctzl()))
            && (this.getPctzlsqh() == null ? other.getPctzlsqh() == null : this.getPctzlsqh().equals(other.getPctzlsqh()))
            && (this.getPctzlsqrq() == null ? other.getPctzlsqrq() == null : this.getPctzlsqrq().equals(other.getPctzlsqrq()))
            && (this.getPctzlyxqr() == null ? other.getPctzlyxqr() == null : this.getPctzlyxqr().equals(other.getPctzlyxqr()))
            && (this.getPctzlsqr() == null ? other.getPctzlsqr() == null : this.getPctzlsqr().equals(other.getPctzlsqr()))
            && (this.getHzlxm() == null ? other.getHzlxm() == null : this.getHzlxm().equals(other.getHzlxm()))
            && (this.getHzlx() == null ? other.getHzlx() == null : this.getHzlx().equals(other.getHzlx()))
            && (this.getSfsx() == null ? other.getSfsx() == null : this.getSfsx().equals(other.getSfsx()))
            && (this.getTstamp() == null ? other.getTstamp() == null : this.getTstamp().equals(other.getTstamp()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getZlcgbh() == null) ? 0 : getZlcgbh().hashCode());
        result = prime * result + ((getZlcgmc() == null) ? 0 : getZlcgmc().hashCode());
        result = prime * result + ((getDwh() == null) ? 0 : getDwh().hashCode());
        result = prime * result + ((getDwmc() == null) ? 0 : getDwmc().hashCode());
        result = prime * result + ((getSsxmbh() == null) ? 0 : getSsxmbh().hashCode());
        result = prime * result + ((getZlzsbh() == null) ? 0 : getZlzsbh().hashCode());
        result = prime * result + ((getZllxm() == null) ? 0 : getZllxm().hashCode());
        result = prime * result + ((getZllx() == null) ? 0 : getZllx().hashCode());
        result = prime * result + ((getZlxz() == null) ? 0 : getZlxz().hashCode());
        result = prime * result + ((getZlfwm() == null) ? 0 : getZlfwm().hashCode());
        result = prime * result + ((getZlfw() == null) ? 0 : getZlfw().hashCode());
        result = prime * result + ((getZlztm() == null) ? 0 : getZlztm().hashCode());
        result = prime * result + ((getZlzt() == null) ? 0 : getZlzt().hashCode());
        result = prime * result + ((getZldz() == null) ? 0 : getZldz().hashCode());
        result = prime * result + ((getZlfx() == null) ? 0 : getZlfx().hashCode());
        result = prime * result + ((getPzrq() == null) ? 0 : getPzrq().hashCode());
        result = prime * result + ((getPzxsm() == null) ? 0 : getPzxsm().hashCode());
        result = prime * result + ((getFlztm() == null) ? 0 : getFlztm().hashCode());
        result = prime * result + ((getJnzlnfrq() == null) ? 0 : getJnzlnfrq().hashCode());
        result = prime * result + ((getJnje() == null) ? 0 : getJnje().hashCode());
        result = prime * result + ((getSqbh() == null) ? 0 : getSqbh().hashCode());
        result = prime * result + ((getSqmc() == null) ? 0 : getSqmc().hashCode());
        result = prime * result + ((getZlsqbh() == null) ? 0 : getZlsqbh().hashCode());
        result = prime * result + ((getSqzlrq() == null) ? 0 : getSqzlrq().hashCode());
        result = prime * result + ((getSqdz() == null) ? 0 : getSqdz().hashCode());
        result = prime * result + ((getGkrq() == null) ? 0 : getGkrq().hashCode());
        result = prime * result + ((getGkh() == null) ? 0 : getGkh().hashCode());
        result = prime * result + ((getSqggh() == null) ? 0 : getSqggh().hashCode());
        result = prime * result + ((getSqggrq() == null) ? 0 : getSqggrq().hashCode());
        result = prime * result + ((getSqrq() == null) ? 0 : getSqrq().hashCode());
        result = prime * result + ((getSqh() == null) ? 0 : getSqh().hashCode());
        result = prime * result + ((getZldljg() == null) ? 0 : getZldljg().hashCode());
        result = prime * result + ((getZldlr() == null) ? 0 : getZldlr().hashCode());
        result = prime * result + ((getZlqr() == null) ? 0 : getZlqr().hashCode());
        result = prime * result + ((getZlzzrq() == null) ? 0 : getZlzzrq().hashCode());
        result = prime * result + ((getFmrgs() == null) ? 0 : getFmrgs().hashCode());
        result = prime * result + ((getSfwzwzl() == null) ? 0 : getSfwzwzl().hashCode());
        result = prime * result + ((getXxsmm() == null) ? 0 : getXxsmm().hashCode());
        result = prime * result + ((getXxsm() == null) ? 0 : getXxsm().hashCode());
        result = prime * result + ((getJrgj() == null) ? 0 : getJrgj().hashCode());
        result = prime * result + ((getBz() == null) ? 0 : getBz().hashCode());
        result = prime * result + ((getCjrbh() == null) ? 0 : getCjrbh().hashCode());
        result = prime * result + ((getCjrmc() == null) ? 0 : getCjrmc().hashCode());
        result = prime * result + ((getCjsj() == null) ? 0 : getCjsj().hashCode());
        result = prime * result + ((getShztm() == null) ? 0 : getShztm().hashCode());
        result = prime * result + ((getShzt() == null) ? 0 : getShzt().hashCode());
        result = prime * result + ((getJysbh() == null) ? 0 : getJysbh().hashCode());
        result = prime * result + ((getDyfmr() == null) ? 0 : getDyfmr().hashCode());
        result = prime * result + ((getDyfmrzc() == null) ? 0 : getDyfmrzc().hashCode());
        result = prime * result + ((getDyfmrxb() == null) ? 0 : getDyfmrxb().hashCode());
        result = prime * result + ((getDyfmrxl() == null) ? 0 : getDyfmrxl().hashCode());
        result = prime * result + ((getDyfmrxw() == null) ? 0 : getDyfmrxw().hashCode());
        result = prime * result + ((getDwpx() == null) ? 0 : getDwpx().hashCode());
        result = prime * result + ((getDyfmrzgh() == null) ? 0 : getDyfmrzgh().hashCode());
        result = prime * result + ((getCzsj() == null) ? 0 : getCzsj().hashCode());
        result = prime * result + ((getDyfmrbh() == null) ? 0 : getDyfmrbh().hashCode());
        result = prime * result + ((getDyfmrlxm() == null) ? 0 : getDyfmrlxm().hashCode());
        result = prime * result + ((getDyfmrlx() == null) ? 0 : getDyfmrlx().hashCode());
        result = prime * result + ((getDfjbh() == null) ? 0 : getDfjbh().hashCode());
        result = prime * result + ((getPctzlmc() == null) ? 0 : getPctzlmc().hashCode());
        result = prime * result + ((getSfwpctzl() == null) ? 0 : getSfwpctzl().hashCode());
        result = prime * result + ((getPctzlsqh() == null) ? 0 : getPctzlsqh().hashCode());
        result = prime * result + ((getPctzlsqrq() == null) ? 0 : getPctzlsqrq().hashCode());
        result = prime * result + ((getPctzlyxqr() == null) ? 0 : getPctzlyxqr().hashCode());
        result = prime * result + ((getPctzlsqr() == null) ? 0 : getPctzlsqr().hashCode());
        result = prime * result + ((getHzlxm() == null) ? 0 : getHzlxm().hashCode());
        result = prime * result + ((getHzlx() == null) ? 0 : getHzlx().hashCode());
        result = prime * result + ((getSfsx() == null) ? 0 : getSfsx().hashCode());
        result = prime * result + ((getTstamp() == null) ? 0 : getTstamp().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", zlcgbh=").append(zlcgbh);
        sb.append(", zlcgmc=").append(zlcgmc);
        sb.append(", dwh=").append(dwh);
        sb.append(", dwmc=").append(dwmc);
        sb.append(", ssxmbh=").append(ssxmbh);
        sb.append(", zlzsbh=").append(zlzsbh);
        sb.append(", zllxm=").append(zllxm);
        sb.append(", zllx=").append(zllx);
        sb.append(", zlxz=").append(zlxz);
        sb.append(", zlfwm=").append(zlfwm);
        sb.append(", zlfw=").append(zlfw);
        sb.append(", zlztm=").append(zlztm);
        sb.append(", zlzt=").append(zlzt);
        sb.append(", zldz=").append(zldz);
        sb.append(", zlfx=").append(zlfx);
        sb.append(", pzrq=").append(pzrq);
        sb.append(", pzxsm=").append(pzxsm);
        sb.append(", flztm=").append(flztm);
        sb.append(", jnzlnfrq=").append(jnzlnfrq);
        sb.append(", jnje=").append(jnje);
        sb.append(", sqbh=").append(sqbh);
        sb.append(", sqmc=").append(sqmc);
        sb.append(", zlsqbh=").append(zlsqbh);
        sb.append(", sqzlrq=").append(sqzlrq);
        sb.append(", sqdz=").append(sqdz);
        sb.append(", gkrq=").append(gkrq);
        sb.append(", gkh=").append(gkh);
        sb.append(", sqggh=").append(sqggh);
        sb.append(", sqggrq=").append(sqggrq);
        sb.append(", sqrq=").append(sqrq);
        sb.append(", sqh=").append(sqh);
        sb.append(", zldljg=").append(zldljg);
        sb.append(", zldlr=").append(zldlr);
        sb.append(", zlqr=").append(zlqr);
        sb.append(", zlzzrq=").append(zlzzrq);
        sb.append(", fmrgs=").append(fmrgs);
        sb.append(", sfwzwzl=").append(sfwzwzl);
        sb.append(", xxsmm=").append(xxsmm);
        sb.append(", xxsm=").append(xxsm);
        sb.append(", jrgj=").append(jrgj);
        sb.append(", bz=").append(bz);
        sb.append(", cjrbh=").append(cjrbh);
        sb.append(", cjrmc=").append(cjrmc);
        sb.append(", cjsj=").append(cjsj);
        sb.append(", shztm=").append(shztm);
        sb.append(", shzt=").append(shzt);
        sb.append(", jysbh=").append(jysbh);
        sb.append(", dyfmr=").append(dyfmr);
        sb.append(", dyfmrzc=").append(dyfmrzc);
        sb.append(", dyfmrxb=").append(dyfmrxb);
        sb.append(", dyfmrxl=").append(dyfmrxl);
        sb.append(", dyfmrxw=").append(dyfmrxw);
        sb.append(", dwpx=").append(dwpx);
        sb.append(", dyfmrzgh=").append(dyfmrzgh);
        sb.append(", czsj=").append(czsj);
        sb.append(", dyfmrbh=").append(dyfmrbh);
        sb.append(", dyfmrlxm=").append(dyfmrlxm);
        sb.append(", dyfmrlx=").append(dyfmrlx);
        sb.append(", dfjbh=").append(dfjbh);
        sb.append(", pctzlmc=").append(pctzlmc);
        sb.append(", sfwpctzl=").append(sfwpctzl);
        sb.append(", pctzlsqh=").append(pctzlsqh);
        sb.append(", pctzlsqrq=").append(pctzlsqrq);
        sb.append(", pctzlyxqr=").append(pctzlyxqr);
        sb.append(", pctzlsqr=").append(pctzlsqr);
        sb.append(", hzlxm=").append(hzlxm);
        sb.append(", hzlx=").append(hzlx);
        sb.append(", sfsx=").append(sfsx);
        sb.append(", tstamp=").append(tstamp);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}