package com.gl.gl_lg_java.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.gl_lg_java.service.NotificationRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 教师通知WebSocket处理器
 * 处理WebSocket连接、消息发送等
 */
@Component
@Slf4j
public class TeacherNotificationHandler implements WebSocketHandler {

    @Autowired
    private ConnectionManager connectionManager;

    @Autowired
    private NotificationRedisService notificationRedisService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        try {
            // 从会话属性中获取用户信息
            String zgh = (String) session.getAttributes().get("zgh");
            String permission = (String) session.getAttributes().get("permission");

            if (zgh == null) {
                log.warn("WebSocket连接建立失败: 缺少职工号信息, sessionId: {}", session.getId());
                session.close();
                return;
            }

            // 设置会话属性（用于长连接管理）
            session.getAttributes().put("zgh", zgh);
            session.getAttributes().put("permission", permission);
            session.getAttributes().put("connectTime", System.currentTimeMillis());
            session.getAttributes().put("lastHeartbeat", System.currentTimeMillis());

            // 添加到连接管理器
            connectionManager.addConnection(zgh, session);

            // 发送连接成功消息
            Map<String, Object> welcomeMessage = new HashMap<>();
            welcomeMessage.put("type", "connection");
            welcomeMessage.put("status", "connected");
            welcomeMessage.put("message", "通知系统连接成功");
            welcomeMessage.put("zgh", zgh);
            welcomeMessage.put("permission", permission);
            welcomeMessage.put("timestamp", System.currentTimeMillis());

            sendMessage(session, welcomeMessage);

            log.info("教师WebSocket长连接建立成功: zgh={}, permission={}, sessionId={}, 连接时间={}",
                    zgh, permission, session.getId(), new java.util.Date());

        } catch (Exception e) {
            log.error("WebSocket连接建立异常: sessionId={}, error={}", session.getId(), e.getMessage());
            session.close();
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        try {
            String zgh = (String) session.getAttributes().get("zgh");
            String payload = message.getPayload().toString();

            log.debug("收到WebSocket消息: zgh={}, message={}", zgh, payload);

            // 解析消息
            @SuppressWarnings("unchecked")
            Map<String, Object> messageData = objectMapper.readValue(payload, Map.class);
            String messageType = (String) messageData.get("type");

            // 处理不同类型的消息
            switch (messageType) {
                case "heartbeat":
                    handleHeartbeat(session, zgh);
                    break;
                case "mark_read":
                    handleMarkRead(session, zgh, messageData);
                    break;
                case "get_unread_count":
                    handleGetUnreadCount(session, zgh);
                    break;
                default:
                    log.warn("未知的消息类型: type={}, zgh={}", messageType, zgh);
                    break;
            }

        } catch (Exception e) {
            log.error("处理WebSocket消息异常: sessionId={}, error={}", session.getId(), e.getMessage());
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String zgh = (String) session.getAttributes().get("zgh");
        log.error("WebSocket传输异常: zgh={}, sessionId={}, error={}", 
                zgh, session.getId(), exception.getMessage());
        
        // 移除连接
        if (zgh != null) {
            connectionManager.removeConnection(zgh);
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String zgh = (String) session.getAttributes().get("zgh");
        Long connectTime = (Long) session.getAttributes().get("connectTime");

        // 计算连接持续时间
        long connectionDuration = connectTime != null ?
                System.currentTimeMillis() - connectTime : 0;

        // 移除连接
        connectionManager.removeConnectionBySessionId(session.getId());

        // 记录连接关闭信息（包含连接时长）
        log.info("WebSocket长连接关闭: zgh={}, sessionId={}, status={}, 连接时长={}ms ({}分钟)",
                zgh, session.getId(), closeStatus, connectionDuration, connectionDuration / 60000);

        // 如果是异常关闭，记录详细信息
        if (closeStatus.getCode() != 1000) {
            log.warn("WebSocket异常关闭: zgh={}, code={}, reason={}",
                    zgh, closeStatus.getCode(), closeStatus.getReason());
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 处理心跳消息（长连接优化）
     */
    private void handleHeartbeat(WebSocketSession session, String zgh) throws Exception {
        // 更新会话的最后心跳时间
        session.getAttributes().put("lastHeartbeat", System.currentTimeMillis());

        // 更新Redis中的心跳时间
        notificationRedisService.updateTeacherHeartbeat(zgh);

        // 回复心跳响应
        Map<String, Object> response = new HashMap<>();
        response.put("type", "heartbeat");
        response.put("status", "ok");
        response.put("timestamp", System.currentTimeMillis());

        // 添加连接状态信息
        Long connectTime = (Long) session.getAttributes().get("connectTime");
        if (connectTime != null) {
            response.put("connectionDuration", System.currentTimeMillis() - connectTime);
        }

        sendMessage(session, response);
        log.debug("处理长连接心跳: zgh={}, 连接时长={}ms", zgh,
                connectTime != null ? System.currentTimeMillis() - connectTime : 0);
    }

    /**
     * 处理标记已读消息
     */
    private void handleMarkRead(WebSocketSession session, String zgh, Map<String, Object> messageData) throws Exception {
        // 这里可以调用通知服务标记已读
        // 暂时只回复确认消息
        Map<String, Object> response = new HashMap<>();
        response.put("type", "mark_read");
        response.put("status", "success");
        response.put("message", "标记已读成功");

        sendMessage(session, response);
        log.debug("处理标记已读消息: zgh={}", zgh);
    }

    /**
     * 处理获取未读数量消息
     */
    private void handleGetUnreadCount(WebSocketSession session, String zgh) throws Exception {
        // 从Redis获取未读数量
        long unreadCount = notificationRedisService.getTeacherUnreadCount(zgh);
        
        Map<String, Object> response = new HashMap<>();
        response.put("type", "unread_count");
        response.put("count", unreadCount >= 0 ? unreadCount : 0);
        response.put("zgh", zgh);

        sendMessage(session, response);
        log.debug("处理获取未读数量消息: zgh={}, count={}", zgh, unreadCount);
    }

    /**
     * 发送消息到WebSocket客户端
     */
    private void sendMessage(WebSocketSession session, Object message) throws Exception {
        if (session.isOpen()) {
            String jsonMessage = objectMapper.writeValueAsString(message);
            session.sendMessage(new TextMessage(jsonMessage));
        }
    }

    /**
     * 向指定用户发送通知消息
     * @param zgh 职工号
     * @param notification 通知内容
     */
    public void sendNotificationToUser(String zgh, Map<String, Object> notification) {
        try {
            WebSocketSession session = connectionManager.getConnection(zgh);
            if (session != null && session.isOpen()) {
                Map<String, Object> message = new HashMap<>();
                message.put("type", "notification");
                message.put("data", notification);
                message.put("timestamp", System.currentTimeMillis());

                sendMessage(session, message);
                log.info("发送通知消息成功: zgh={}, title={}", zgh, notification.get("title"));
            } else {
                log.debug("用户不在线，无法发送WebSocket通知: zgh={}", zgh);
            }
        } catch (Exception e) {
            log.error("发送通知消息失败: zgh={}, error={}", zgh, e.getMessage());
        }
    }
}
