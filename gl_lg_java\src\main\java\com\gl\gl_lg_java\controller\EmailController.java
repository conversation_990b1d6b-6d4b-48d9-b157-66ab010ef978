package com.gl.gl_lg_java.controller;

import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.service.EmailService;
import com.gl.gl_lg_java.service.BtEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 邮件服务控制器
 */
@RestController
@RequestMapping("/api/email")
@Slf4j
public class EmailController {

    @Autowired
    private EmailService emailService;

    @Autowired
    private BtEmailService btEmailService;

    @Value("${mail.service.type:spring}")
    private String mailServiceType;



    /**
     * 发送自定义邮件
     * @param emailRequest 邮件请求参数
     */
    @PostMapping("/send")
    public Result<String> sendCustomEmail(@RequestBody Map<String, String> emailRequest) {
        try {
            String to = emailRequest.get("to");
            String subject = emailRequest.get("subject");
            String content = emailRequest.get("content");

            log.info("发送自定义邮件到: {}, 主题: {}", to, subject);

            // 参数验证
            if (to == null || to.trim().isEmpty()) {
                return Result.error("收件人邮箱不能为空");
            }
            if (subject == null || subject.trim().isEmpty()) {
                return Result.error("邮件主题不能为空");
            }
            if (content == null || content.trim().isEmpty()) {
                return Result.error("邮件内容不能为空");
            }

            // 邮箱格式验证
            if (!isValidEmail(to)) {
                return Result.error("收件人邮箱格式不正确");
            }

            boolean success = sendEmailByProvider(to, subject, content, false);

            if (success) {
                return Result.success("邮件发送成功");
            } else {
                return Result.error("邮件发送失败，请检查网络连接");
            }

        } catch (Exception e) {
            log.error("发送自定义邮件失败: {}", e.getMessage());
            return Result.error("发送失败: " + e.getMessage());
        }
    }

    /**
     * 批量发送邮件
     * @param batchEmailRequest 批量邮件请求参数
     */
    @PostMapping("/batch-send")
    public Result<String> sendBatchEmails(@RequestBody Map<String, Object> batchEmailRequest) {
        try {
            @SuppressWarnings("unchecked")
            java.util.List<String> toList = (java.util.List<String>) batchEmailRequest.get("toList");
            String subject = (String) batchEmailRequest.get("subject");
            String content = (String) batchEmailRequest.get("content");

            log.info("批量发送邮件，收件人数量: {}, 主题: {}", toList != null ? toList.size() : 0, subject);

            // 参数验证
            if (toList == null || toList.isEmpty()) {
                return Result.error("收件人列表不能为空");
            }
            if (subject == null || subject.trim().isEmpty()) {
                return Result.error("邮件主题不能为空");
            }
            if (content == null || content.trim().isEmpty()) {
                return Result.error("邮件内容不能为空");
            }

            int successCount = 0;
            int failCount = 0;

            for (String email : toList) {
                if (email != null && !email.trim().isEmpty() && isValidEmail(email)) {
                    boolean success = sendEmailByProvider(email, subject, content, false);
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } else {
                    failCount++;
                    log.warn("跳过无效邮箱: {}", email);
                }
            }

            String message = String.format("批量发送完成，成功: %d, 失败: %d", successCount, failCount);
            log.info(message);

            if (successCount > 0) {
                return Result.success(message);
            } else {
                return Result.error("所有邮件发送失败");
            }

        } catch (Exception e) {
            log.error("批量发送邮件失败: {}", e.getMessage());
            return Result.error("批量发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTML邮件
     * @param emailRequest 邮件请求参数
     */
    @PostMapping("/send-html")
    public Result<String> sendHtmlEmail(@RequestBody Map<String, String> emailRequest) {
        try {
            String to = emailRequest.get("to");
            String subject = emailRequest.get("subject");
            String content = emailRequest.get("content");

            log.info("发送HTML邮件到: {}, 主题: {}", to, subject);

            // 参数验证
            if (to == null || to.trim().isEmpty()) {
                return Result.error("收件人邮箱不能为空");
            }
            if (subject == null || subject.trim().isEmpty()) {
                return Result.error("邮件主题不能为空");
            }
            if (content == null || content.trim().isEmpty()) {
                return Result.error("邮件内容不能为空");
            }

            // 邮箱格式验证
            if (!isValidEmail(to)) {
                return Result.error("收件人邮箱格式不正确");
            }

            boolean success = sendEmailByProvider(to, subject, content, true);

            if (success) {
                return Result.success("HTML邮件发送成功");
            } else {
                return Result.error("HTML邮件发送失败，请检查网络连接");
            }

        } catch (Exception e) {
            log.error("发送HTML邮件失败: {}", e.getMessage());
            return Result.error("发送失败: " + e.getMessage());
        }
    }

    /**
     * 根据配置选择邮件服务商发送邮件
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param isHtml 是否为HTML格式
     * @return 发送结果
     */
    private boolean sendEmailByProvider(String to, String subject, String content, boolean isHtml) {
        try {
            if ("bt".equals(mailServiceType)) {
                // 使用宝塔邮局服务
                if (isHtml) {
                    return btEmailService.sendHtmlEmail(to, subject, content);
                } else {
                    return btEmailService.sendSimpleEmail(to, subject, content);
                }
            } else {
                // 使用Spring邮件服务（QQ邮箱等）
                return emailService.sendSimpleEmail(to, subject, content);
            }
        } catch (Exception e) {
            log.error("邮件发送失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.matches(emailRegex);
    }
}
