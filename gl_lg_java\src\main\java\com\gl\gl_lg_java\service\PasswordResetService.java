package com.gl.gl_lg_java.service;

import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.mapper.RchxJzgjbxxMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 密码重置服务
 */
@Service
@Slf4j
public class PasswordResetService {

    @Autowired
    private RchxJzgjbxxMapper rchxJzgjbxxMapper;

    @Autowired
    private EmailService emailService;

    @Autowired
    private BtEmailService btEmailService;

    @org.springframework.beans.factory.annotation.Value("${mail.service.type:spring}")
    private String mailServiceType;

    // 验证码存储 (邮箱 -> 验证码信息)
    private final Map<String, VerificationCode> verificationCodes = new ConcurrentHashMap<>();

    /**
     * 验证码信息类
     */
    private static class VerificationCode {
        private String code;
        private LocalDateTime expireTime;
        private String email;

        public VerificationCode(String code, LocalDateTime expireTime, String email) {
            this.code = code;
            this.expireTime = expireTime;
            this.email = email;
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expireTime);
        }

        public String getCode() {
            return code;
        }

        public LocalDateTime getExpireTime() {
            return expireTime;
        }

        public String getEmail() {
            return email;
        }
    }

    /**
     * 发送密码重置验证码
     * 
     * @param account 账号（支持职工号、手机号、身份证号、邮箱）
     * @return 发送结果
     */
    public boolean sendResetCode(String account) {
        try {
            // 查找用户
            RchxJzgjbxx teacher = rchxJzgjbxxMapper.findByAccount(account);
            if (teacher == null) {
                log.warn("密码重置失败，用户不存在: {}", account);
                return false;
            }

            // 检查用户是否有邮箱
            if (teacher.getDzxx() == null || teacher.getDzxx().trim().isEmpty()) {
                log.warn("密码重置失败，用户未设置邮箱: {}", account);
                return false;
            }

            String email = teacher.getDzxx().trim();

            // 检查是否在冷却期内（1分钟内只能发送一次）
            if (verificationCodes.containsKey(email)) {
                VerificationCode existingCode = verificationCodes.get(email);
                if (existingCode.getExpireTime().isAfter(LocalDateTime.now().minusMinutes(14))) {
                    log.warn("密码重置验证码发送过于频繁: {}", email);
                    return false;
                }
            }

            // 生成6位随机验证码
            String code = generateVerificationCode();

            // 设置15分钟有效期
            LocalDateTime expireTime = LocalDateTime.now().plusMinutes(15);

            // 存储验证码
            verificationCodes.put(email, new VerificationCode(code, expireTime, email));

            // 发送邮件 - 根据配置选择邮件服务
            boolean emailSent;
            if ("bt".equals(mailServiceType)) {
                emailSent = btEmailService.sendPasswordResetEmail(email, teacher.getXm(), code);
            } else {
                emailSent = emailService.sendPasswordResetEmail(email, teacher.getXm(), code);
            }

            if (emailSent) {
                log.info("密码重置验证码发送成功: {} -> {}", account, email);
                return true;
            } else {
                // 发送失败，清除验证码
                verificationCodes.remove(email);
                log.error("密码重置验证码邮件发送失败: {}", email);
                return false;
            }

        } catch (Exception e) {
            log.error("发送密码重置验证码失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证验证码并重置密码
     * 
     * @param account     账号
     * @param code        验证码
     * @param newPassword 新密码
     * @return 重置结果
     */
    public boolean resetPassword(String account, String code, String newPassword) {
        try {
            // 查找用户
            RchxJzgjbxx teacher = rchxJzgjbxxMapper.findByAccount(account);
            if (teacher == null) {
                log.warn("密码重置失败，用户不存在: {}", account);
                return false;
            }

            String email = teacher.getDzxx();
            if (email == null || email.trim().isEmpty()) {
                log.warn("密码重置失败，用户未设置邮箱: {}", account);
                return false;
            }

            email = email.trim();

            // 验证验证码
            VerificationCode verificationCode = verificationCodes.get(email);
            if (verificationCode == null) {
                log.warn("密码重置失败，验证码不存在: {}", email);
                return false;
            }

            if (verificationCode.isExpired()) {
                verificationCodes.remove(email);
                log.warn("密码重置失败，验证码已过期: {}", email);
                return false;
            }

            if (!verificationCode.getCode().equals(code)) {
                log.warn("密码重置失败，验证码错误: {}", email);
                return false;
            }

            // 更新密码
            teacher.setPass(newPassword);
            int result = rchxJzgjbxxMapper.updateById(teacher);

            if (result > 0) {
                // 密码重置成功，清除验证码
                verificationCodes.remove(email);
                log.info("密码重置成功: {}", account);
                return true;
            } else {
                log.error("密码重置失败，数据库更新失败: {}", account);
                return false;
            }

        } catch (Exception e) {
            log.error("密码重置失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 生成6位随机验证码
     */
    private String generateVerificationCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    /**
     * 清理过期的验证码
     */
    public void cleanExpiredCodes() {
        verificationCodes.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }
}
