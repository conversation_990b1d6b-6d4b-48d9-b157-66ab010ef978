package com.gl.gl_lg_java.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 分析参数DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisParams {
    
    /**
     * 时间范围（年）
     */
    private String timeRange;
    
    /**
     * 最低匹配分数
     */
    private Double minMatchScore;
    
    /**
     * 最大结果数量
     */
    private Integer maxResults;
    
    /**
     * 是否包含详细信息
     */
    private Boolean includeDetails;
    
    /**
     * 分析创建人
     */
    private String createdBy;
    
    /**
     * 获取时间范围的数值
     */
    public int getTimeRangeValue() {
        if ("all".equals(timeRange)) {
            return 100; // 表示所有时间
        }
        try {
            return Integer.parseInt(timeRange);
        } catch (NumberFormatException e) {
            return 5; // 默认5年
        }
    }
    
    /**
     * 获取默认分析参数
     */
    public static AnalysisParams getDefault() {
        return AnalysisParams.builder()
            .timeRange("5")
            .minMatchScore(60.0)
            .maxResults(100)
            .includeDetails(true)
            .build();
    }
}
