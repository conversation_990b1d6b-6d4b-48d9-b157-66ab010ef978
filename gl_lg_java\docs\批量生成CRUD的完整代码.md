# 批量生成CRUD的完整代码

## 实体类主键配置

### 1. RchxHjcgjbxx - 获奖成果基本信息
```java
// 主键：hjcgbh (获奖成果编号)
@TableId(value = "hjcgbh", type = IdType.INPUT)
private String hjcgbh;
```

### 2. RchxKjlwjbxx - 科技论文基本信息
```java
// 需要查看实体类确定主键字段
```

### 3. RchxJzggzjlxx - 教职工工作简历信息
```java
// 需要查看实体类确定主键字段
```

### 4. RchxXmysxx - 项目预算信息
```java
// 需要查看实体类确定主键字段
```

## 快速生成方案

由于每个实体类字段都很多（50+字段），我采用以下策略：

### 1. 核心字段CRUD
只为核心业务字段创建查询方法，包括：
- 主键查询
- 名称模糊查询
- 状态查询
- 负责人/完成人查询
- 分页查询

### 2. 标准CRUD操作
- 新增、更新、删除、批量操作
- 权限控制
- 异常处理

### 3. 统一接口规范
- RESTful API设计
- 统一响应格式
- 统一权限注解

## 获奖成果基本信息完整CRUD

### Mapper更新
```java
@Mapper
public interface RchxHjcgjbxxMapper extends BaseMapper<RchxHjcgjbxx> {
    
    // 根据获奖成果编号查询
    @Select("SELECT * FROM t_rchx_hjcgjbxx WHERE hjcgbh = #{hjcgbh}")
    RchxHjcgjbxx findByHjcgbh(@Param("hjcgbh") String hjcgbh);
    
    // 根据获奖成果名称模糊查询
    @Select("SELECT * FROM t_rchx_hjcgjbxx WHERE hjcgmc LIKE CONCAT('%', #{hjcgmc}, '%')")
    List<RchxHjcgjbxx> findByHjcgmcLike(@Param("hjcgmc") String hjcgmc);
    
    // 根据第一完成人职工号查询
    @Select("SELECT * FROM t_rchx_hjcgjbxx WHERE dywcrzgh = #{dywcrzgh}")
    List<RchxHjcgjbxx> findByDywcrzgh(@Param("dywcrzgh") String dywcrzgh);
    
    // 根据第一完成人姓名模糊查询
    @Select("SELECT * FROM t_rchx_hjcgjbxx WHERE dywcrxm LIKE CONCAT('%', #{dywcrxm}, '%')")
    List<RchxHjcgjbxx> findByDywcrxmLike(@Param("dywcrxm") String dywcrxm);
    
    // 根据奖励名称模糊查询
    @Select("SELECT * FROM t_rchx_hjcgjbxx WHERE jlmc LIKE CONCAT('%', #{jlmc}, '%')")
    List<RchxHjcgjbxx> findByJlmcLike(@Param("jlmc") String jlmc);
    
    // 根据审核状态查询
    @Select("SELECT * FROM t_rchx_hjcgjbxx WHERE shzt = #{shzt}")
    List<RchxHjcgjbxx> findByShzt(@Param("shzt") String shzt);
    
    // 根据获奖级别查询
    @Select("SELECT * FROM t_rchx_hjcgjbxx WHERE hjjb = #{hjjb}")
    List<RchxHjcgjbxx> findByHjjb(@Param("hjjb") String hjjb);
    
    // 根据获奖日期范围查询
    @Select("SELECT * FROM t_rchx_hjcgjbxx WHERE hjrq >= #{startDate} AND hjrq <= #{endDate}")
    List<RchxHjcgjbxx> findByHjrqBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    // 多条件查询
    @Select("<script>" +
            "SELECT * FROM t_rchx_hjcgjbxx WHERE 1=1 " +
            "<if test='hjcgbh != null and hjcgbh != \"\"'> AND hjcgbh LIKE CONCAT('%', #{hjcgbh}, '%') </if>" +
            "<if test='hjcgmc != null and hjcgmc != \"\"'> AND hjcgmc LIKE CONCAT('%', #{hjcgmc}, '%') </if>" +
            "<if test='jlmc != null and jlmc != \"\"'> AND jlmc LIKE CONCAT('%', #{jlmc}, '%') </if>" +
            "<if test='dywcrxm != null and dywcrxm != \"\"'> AND dywcrxm LIKE CONCAT('%', #{dywcrxm}, '%') </if>" +
            "<if test='dywcrzgh != null and dywcrzgh != \"\"'> AND dywcrzgh = #{dywcrzgh} </if>" +
            "<if test='hjjb != null and hjjb != \"\"'> AND hjjb = #{hjjb} </if>" +
            "<if test='shzt != null and shzt != \"\"'> AND shzt = #{shzt} </if>" +
            "<if test='dwmc != null and dwmc != \"\"'> AND dwmc LIKE CONCAT('%', #{dwmc}, '%') </if>" +
            "<if test='startDate != null and startDate != \"\"'> AND hjrq >= #{startDate} </if>" +
            "<if test='endDate != null and endDate != \"\"'> AND hjrq <= #{endDate} </if>" +
            "ORDER BY hjrq DESC" +
            "</script>")
    List<RchxHjcgjbxx> findByMultiConditions(@Param("hjcgbh") String hjcgbh,
                                             @Param("hjcgmc") String hjcgmc,
                                             @Param("jlmc") String jlmc,
                                             @Param("dywcrxm") String dywcrxm,
                                             @Param("dywcrzgh") String dywcrzgh,
                                             @Param("hjjb") String hjjb,
                                             @Param("shzt") String shzt,
                                             @Param("dwmc") String dwmc,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);
    
    // 分页查询
    @Select("<script>" +
            "SELECT * FROM t_rchx_hjcgjbxx WHERE 1=1 " +
            "<if test='hjcgbh != null and hjcgbh != \"\"'> AND hjcgbh LIKE CONCAT('%', #{hjcgbh}, '%') </if>" +
            "<if test='hjcgmc != null and hjcgmc != \"\"'> AND hjcgmc LIKE CONCAT('%', #{hjcgmc}, '%') </if>" +
            "<if test='jlmc != null and jlmc != \"\"'> AND jlmc LIKE CONCAT('%', #{jlmc}, '%') </if>" +
            "<if test='dywcrxm != null and dywcrxm != \"\"'> AND dywcrxm LIKE CONCAT('%', #{dywcrxm}, '%') </if>" +
            "<if test='dywcrzgh != null and dywcrzgh != \"\"'> AND dywcrzgh = #{dywcrzgh} </if>" +
            "<if test='hjjb != null and hjjb != \"\"'> AND hjjb = #{hjjb} </if>" +
            "<if test='shzt != null and shzt != \"\"'> AND shzt = #{shzt} </if>" +
            "<if test='dwmc != null and dwmc != \"\"'> AND dwmc LIKE CONCAT('%', #{dwmc}, '%') </if>" +
            "<if test='startDate != null and startDate != \"\"'> AND hjrq >= #{startDate} </if>" +
            "<if test='endDate != null and endDate != \"\"'> AND hjrq <= #{endDate} </if>" +
            "ORDER BY hjrq DESC" +
            "</script>")
    IPage<RchxHjcgjbxx> findPageByMultiConditions(Page<RchxHjcgjbxx> page,
                                                   @Param("hjcgbh") String hjcgbh,
                                                   @Param("hjcgmc") String hjcgmc,
                                                   @Param("jlmc") String jlmc,
                                                   @Param("dywcrxm") String dywcrxm,
                                                   @Param("dywcrzgh") String dywcrzgh,
                                                   @Param("hjjb") String hjjb,
                                                   @Param("shzt") String shzt,
                                                   @Param("dwmc") String dwmc,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);
}
```

### 查询DTO
```java
@Data
public class HjcgjbxxQueryDTO {
    private String hjcgbh;        // 获奖成果编号
    private String hjcgmc;        // 获奖成果名称
    private String jlmc;          // 奖励名称
    private String dywcrxm;       // 第一完成人姓名
    private String dywcrzgh;      // 第一完成人职工号
    private String hjjb;          // 获奖级别
    private String shzt;          // 审核状态
    private String dwmc;          // 单位名称
    private String startDate;     // 开始日期
    private String endDate;       // 结束日期
    private Integer pageNum = 1;
    private Integer pageSize = 10;
    private String orderBy = "hjrq";
    private String orderDirection = "desc";
}
```

### Service接口
```java
public interface RchxHjcgjbxxService extends IService<RchxHjcgjbxx> {
    RchxHjcgjbxx getByHjcgbh(String hjcgbh);
    List<RchxHjcgjbxx> listByHjcgmcLike(String hjcgmc);
    List<RchxHjcgjbxx> listByDywcrzgh(String dywcrzgh);
    List<RchxHjcgjbxx> listByShzt(String shzt);
    List<RchxHjcgjbxx> listByMultiConditions(HjcgjbxxQueryDTO queryDTO);
    IPage<RchxHjcgjbxx> pageByMultiConditions(HjcgjbxxQueryDTO queryDTO);
    boolean saveHjcgjbxx(RchxHjcgjbxx hjcgjbxx);
    boolean updateByHjcgbh(RchxHjcgjbxx hjcgjbxx);
    boolean removeByHjcgbh(String hjcgbh);
    boolean removeBatchByHjcgbhs(List<String> hjcgbhs);
    boolean saveBatchHjcgjbxx(List<RchxHjcgjbxx> hjcgjbxxList);
    boolean updateBatchHjcgjbxx(List<RchxHjcgjbxx> hjcgjbxxList);
}
```

### Controller接口
```java
@RestController
@RequestMapping("/api/hjcgjbxx")
@Slf4j
public class HjcgjbxxController {
    
    @Autowired
    private RchxHjcgjbxxService hjcgjbxxService;
    
    // GET /{hjcgbh} - 根据编号查询
    @RequirePermission("教师")
    
    // POST /page - 分页查询
    @RequirePermission("教师")
    
    // POST /list - 多条件查询
    @RequirePermission("教师")
    
    // GET /name/{hjcgmc} - 根据名称模糊查询
    @RequirePermission("学院管理员")
    
    // GET /winner/{dywcrzgh} - 根据完成人查询
    @RequirePermission("教师")
    
    // GET /audit/{shzt} - 根据审核状态查询
    @RequirePermission("评审")
    
    // POST / - 新增
    @RequirePermission("学院管理员")
    
    // PUT / - 更新
    @RequirePermission("学院管理员")
    
    // DELETE /{hjcgbh} - 删除
    @RequirePermission("系统管理员")
    
    // 批量操作接口...
}
```

## 权限控制规则

### 获奖成果管理
- **教师**: 查看自己的获奖成果
- **评审**: 查看和评审获奖成果
- **学院管理员**: 管理本学院的获奖成果
- **系统管理员**: 管理所有获奖成果

### API路径规范
- `/api/hjcgjbxx` - 获奖成果基本信息
- `/api/kjlwjbxx` - 科技论文基本信息
- `/api/jzggzjlxx` - 教职工工作简历信息
- `/api/xmysxx` - 项目预算信息

## 实施计划

1. **立即完成**: 获奖成果基本信息完整CRUD
2. **今天完成**: 科技论文基本信息完整CRUD
3. **明天完成**: 工作简历和项目预算完整CRUD

这样可以快速完成所有实体类的CRUD接口，同时保持代码质量和一致性。
