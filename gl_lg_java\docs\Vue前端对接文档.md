# Vue前端对接文档

## 1. Axios配置和API服务封装

### 1.1 Axios请求配置
```javascript
// utils/request.js - Axios配置
import axios from 'axios'
import { Message } from 'element-ui'

// 创建axios实例
const service = axios.create({
    baseURL: 'http://localhost:8080',
    timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        // 从localStorage获取token
        const token = localStorage.getItem('token')
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`
        }
        return config
    },
    error => {
        console.error('请求错误:', error)
        return Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        const res = response.data
        
        // 如果返回的状态码不是200，说明接口有问题
        if (res.code !== 200) {
            Message({
                message: res.message || '请求失败',
                type: 'error',
                duration: 5 * 1000
            })
            
            // 401表示token过期或无效
            if (res.code === 401) {
                // 清除token并跳转到登录页
                localStorage.removeItem('token')
                localStorage.removeItem('userInfo')
                window.location.href = '/login'
            }
            
            return Promise.reject(new Error(res.message || '请求失败'))
        } else {
            return response
        }
    },
    error => {
        console.error('响应错误:', error)
        Message({
            message: error.message || '网络错误',
            type: 'error',
            duration: 5 * 1000
        })
        return Promise.reject(error)
    }
)

export default service
```

### 1.2 认证API服务
```javascript
// api/authApi.js - 认证相关API
import request from '@/utils/request'

export default {
    // 登录（支持职工号、手机号、身份证号、邮箱）
    login(loginData) {
        return request.post('/api/auth/login', loginData)
    },
    
    // 获取当前用户信息
    getCurrentUser() {
        return request.get('/api/auth/me')
    },
    
    // 刷新Token
    refreshToken() {
        return request.post('/api/auth/refresh')
    },
    
    // 登出
    logout() {
        return request.post('/api/auth/logout')
    }
}
```

## 2. 登录组件示例

```vue
<template>
  <div class="login-container">
    <div class="login-form-wrapper">
      <div class="login-header">
        <h2>教职工管理系统</h2>
        <p>请使用职工号、手机号、身份证号或邮箱登录</p>
      </div>
      
      <el-form 
        :model="loginForm" 
        :rules="loginRules" 
        ref="loginForm" 
        class="login-form"
        @submit.native.prevent="handleLogin"
      >
        <el-form-item prop="account">
          <el-input
            v-model="loginForm.account"
            placeholder="请输入职工号/手机号/身份证号/邮箱"
            prefix-icon="el-icon-user"
            size="large"
            clearable
          >
            <template slot="prepend">账号</template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock"
            size="large"
            show-password
            @keyup.enter.native="handleLogin"
          >
            <template slot="prepend">密码</template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-tips">
        <h4>测试账号：</h4>
        <p>职工号：admin，密码：admin</p>
        <p>支持使用手机号、身份证号或邮箱登录</p>
      </div>
    </div>
  </div>
</template>

<script>
import authApi from '@/api/authApi'

export default {
  name: 'Login',
  data() {
    // 账号验证规则
    const validateAccount = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入账号'))
      } else if (value.length < 3) {
        callback(new Error('账号长度不能少于3位'))
      } else {
        callback()
      }
    }
    
    return {
      loginForm: {
        account: '',
        password: ''
      },
      loginRules: {
        account: [
          { validator: validateAccount, trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 1, message: '密码不能为空', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    async handleLogin() {
      try {
        // 表单验证
        await this.$refs.loginForm.validate()
        
        this.loading = true
        
        // 调用登录API
        const response = await authApi.login(this.loginForm)
        
        if (response && response.data.code === 200) {
          // 保存token和用户信息
          localStorage.setItem('token', response.data.data.token)
          localStorage.setItem('userInfo', JSON.stringify(response.data.data.userInfo))
          
          this.$message.success('登录成功')
          
          // 跳转到主页
          this.$router.push('/')
        }
      } catch (error) {
        console.error('登录失败:', error)
        this.$message.error('登录失败，请检查账号密码')
      } finally {
        this.loading = false
      }
    },
    
    // 快速填充测试账号
    fillTestAccount() {
      this.loginForm.account = 'admin'
      this.loginForm.password = 'admin'
    }
  },
  
  mounted() {
    // 如果已经登录，直接跳转
    const token = localStorage.getItem('token')
    if (token) {
      this.$router.push('/')
    }
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-form-wrapper {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-header {
  text-align: center;
  padding: 40px 30px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.login-header h2 {
  margin: 0 0 10px;
  color: #333;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.login-form {
  padding: 30px;
}

.login-tips {
  padding: 20px 30px 30px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.login-tips h4 {
  margin: 0 0 10px;
  color: #333;
  font-size: 14px;
}

.login-tips p {
  margin: 5px 0;
  color: #666;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-form-wrapper {
    max-width: 100%;
  }
  
  .login-header,
  .login-form,
  .login-tips {
    padding-left: 20px;
    padding-right: 20px;
  }
}
</style>
```

## 3. 路由守卫示例

```javascript
// router/index.js
import Vue from 'vue'
import VueRouter from 'vue-router'
import { Message } from 'element-ui'

Vue.use(VueRouter)

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { requiresAuth: true }
  }
]

const router = new VueRouter({
  mode: 'history',
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  
  if (to.meta.requiresAuth) {
    // 需要登录的页面
    if (token) {
      next()
    } else {
      Message.warning('请先登录')
      next('/login')
    }
  } else {
    // 不需要登录的页面
    if (to.path === '/login' && token) {
      // 已登录用户访问登录页，重定向到首页
      next('/')
    } else {
      next()
    }
  }
})

export default router
```

## 4. 用户信息管理

```javascript
// store/modules/user.js - Vuex用户模块
import authApi from '@/api/authApi'

const state = {
  token: localStorage.getItem('token') || '',
  userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}')
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    localStorage.setItem('token', token)
  },
  
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
  },
  
  CLEAR_USER_DATA(state) {
    state.token = ''
    state.userInfo = {}
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }
}

const actions = {
  // 登录
  async login({ commit }, loginData) {
    try {
      const response = await authApi.login(loginData)
      if (response.data.code === 200) {
        const { token, userInfo } = response.data.data
        commit('SET_TOKEN', token)
        commit('SET_USER_INFO', userInfo)
        return response.data
      }
      return null
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  },
  
  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      const response = await authApi.getCurrentUser()
      if (response.data.code === 200) {
        commit('SET_USER_INFO', response.data.data)
        return response.data.data
      }
      return null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  },
  
  // 登出
  async logout({ commit }) {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      commit('CLEAR_USER_DATA')
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
```

## 5. 使用示例

### 5.1 在组件中使用
```javascript
// 在Vue组件中使用
export default {
  methods: {
    // 登录
    async handleLogin() {
      try {
        const result = await this.$store.dispatch('user/login', {
          account: 'admin',
          password: 'admin'
        })
        
        if (result) {
          this.$message.success('登录成功')
          this.$router.push('/')
        }
      } catch (error) {
        this.$message.error('登录失败')
      }
    },
    
    // 登出
    async handleLogout() {
      await this.$store.dispatch('user/logout')
      this.$router.push('/login')
    },
    
    // 获取当前用户信息
    async getCurrentUser() {
      try {
        const userInfo = await this.$store.dispatch('user/getUserInfo')
        console.log('当前用户:', userInfo)
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    }
  }
}
```

### 5.2 权限判断
```javascript
// 在模板中判断权限
<template>
  <div>
    <!-- 只有系统管理员可以看到 -->
    <el-button v-if="isSystemAdmin" type="danger">删除用户</el-button>

    <!-- 学院管理员和系统管理员可以看到 -->
    <el-button v-if="isAdmin" type="primary">编辑用户</el-button>

    <!-- 评审及以上权限可以看到 -->
    <el-button v-if="canReview" type="success">开始评审</el-button>

    <!-- 所有用户都可以看到 -->
    <el-button>查看个人信息</el-button>
  </div>
</template>

<script>
export default {
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo
    },

    // 是否是教师
    isTeacher() {
      return this.userInfo.qx === '教师'
    },

    // 是否是评审
    isReviewer() {
      return this.userInfo.qx === '评审'
    },

    // 是否是学院管理员
    isCollegeAdmin() {
      return this.userInfo.qx === '学院管理员'
    },

    // 是否是系统管理员
    isSystemAdmin() {
      return this.userInfo.qx === '系统管理员'
    },

    // 是否是管理员（学院管理员或系统管理员）
    isAdmin() {
      return ['学院管理员', '系统管理员'].includes(this.userInfo.qx)
    },

    // 是否可以管理用户
    canManageUsers() {
      return ['学院管理员', '系统管理员'].includes(this.userInfo.qx)
    },

    // 是否可以评审
    canReview() {
      return ['评审', '学院管理员', '系统管理员'].includes(this.userInfo.qx)
    },

    // 权限级别检查（高权限包含低权限功能）
    hasTeacherPermission() {
      return ['教师', '评审', '学院管理员', '系统管理员'].includes(this.userInfo.qx)
    },

    hasReviewerPermission() {
      return ['评审', '学院管理员', '系统管理员'].includes(this.userInfo.qx)
    },

    hasCollegeAdminPermission() {
      return ['学院管理员', '系统管理员'].includes(this.userInfo.qx)
    },

    hasSystemAdminPermission() {
      return this.userInfo.qx === '系统管理员'
    }
  }
}
</script>
```

## 6. 注意事项

1. **Token管理**：
   - 登录成功后自动保存Token到localStorage
   - 每次请求自动在请求头中添加Token
   - Token过期时自动跳转到登录页

2. **错误处理**：
   - 统一的错误提示
   - 网络错误和业务错误分别处理
   - 401错误自动清除Token并跳转登录

3. **安全考虑**：
   - 敏感信息不要存储在localStorage
   - 生产环境使用HTTPS
   - 定期刷新Token

4. **用户体验**：
   - 登录状态持久化
   - 自动跳转逻辑
   - 友好的错误提示
