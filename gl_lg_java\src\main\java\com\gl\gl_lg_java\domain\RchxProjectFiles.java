package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目文件表实体类
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_rchx_project_files")
public class RchxProjectFiles {

    /**
     * 文件ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务类型(COLLECTION征集,APPLICATION申报,INSPECTION中检,COMPLETION结项)
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 业务ID
     */
    @TableField("business_id")
    private Long businessId;

    /**
     * 项目编号
     */
    @TableField("project_code")
    private String projectCode;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 原始文件名
     */
    @TableField("file_original_name")
    private String fileOriginalName;

    /**
     * 文件路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件大小(字节)
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件类型
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 文件扩展名
     */
    @TableField("file_extension")
    private String fileExtension;

    /**
     * MinIO存储桶名称
     */
    @TableField("bucket_name")
    private String bucketName;

    /**
     * MinIO对象键
     */
    @TableField("object_key")
    private String objectKey;

    /**
     * 内容类型
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 文件分类(GUIDE指南,MATERIAL材料,REPORT报告,OTHER其他)
     */
    @TableField("file_category")
    private String fileCategory;

    /**
     * 文件描述
     */
    @TableField("description")
    private String description;

    /**
     * 上传时间
     */
    @TableField(value = "upload_time", fill = FieldFill.INSERT)
    private LocalDateTime uploadTime;

    /**
     * 上传人职工号
     */
    @TableField("upload_by")
    private String uploadBy;

    /**
     * 是否删除(0未删除,1已删除)
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private LocalDateTime deleteTime;

    /**
     * 删除人职工号
     */
    @TableField("delete_by")
    private String deleteBy;
}
