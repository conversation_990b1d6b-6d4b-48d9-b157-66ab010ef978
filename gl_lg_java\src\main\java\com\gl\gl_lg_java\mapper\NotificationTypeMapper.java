package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gl.gl_lg_java.domain.RchxNotificationTypes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_notification_types(通知类型表)】的数据库操作Mapper
 * @createDate 2025-07-24 13:00:00
 * @Entity com.gl.gl_lg_java.domain.RchxNotificationTypes
 */
@Mapper
public interface NotificationTypeMapper extends BaseMapper<RchxNotificationTypes> {

    /**
     * 查询所有启用的通知类型
     * @return 通知类型列表
     */
    @Select("SELECT id, type_code, type_name, icon, color FROM t_rchx_notification_types ORDER BY id")
    List<RchxNotificationTypes> selectAllTypes();

    /**
     * 根据类型编码查询通知类型
     * @param typeCode 类型编码
     * @return 通知类型
     */
    @Select("SELECT id, type_code, type_name, icon, color FROM t_rchx_notification_types WHERE type_code = #{typeCode}")
    RchxNotificationTypes selectByTypeCode(String typeCode);
}
