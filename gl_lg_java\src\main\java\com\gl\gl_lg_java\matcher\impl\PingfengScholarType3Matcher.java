package com.gl.gl_lg_java.matcher.impl;

import com.gl.gl_lg_java.dto.AnalysisParams;
import com.gl.gl_lg_java.dto.MatchResult;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.matcher.ProjectMatcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;

/**
 * 屏风学者引育计划项目-Ⅲ类匹配算法（简化版）
 */
@Component
@Slf4j
public class PingfengScholarType3Matcher implements ProjectMatcher {
    
    @Override
    public String getProjectCode() {
        return "PFXZ3";
    }
    
    @Override
    public String getProjectName() {
        return "屏风学者引育计划项目-Ⅲ类";
    }
    
    @Override
    public MatchResult matchTeacher(RchxJzgjbxx teacher, AnalysisParams params) {
        log.info("开始分析教师{}的屏风学者Ⅲ类匹配度", teacher.getZgh());
        
        MatchResult result = MatchResult.builder()
            .teacherZgh(teacher.getZgh())
            .teacherName(teacher.getXm())
            .department(teacher.getBm())
            .title(teacher.getZc())
            .createdTime(LocalDateTime.now())
            .strengths(new ArrayList<>())
            .weaknesses(new ArrayList<>())
            .recommendations(new ArrayList<>())
            .build();
        
        try {
            // 简化的匹配逻辑（基于成果条件）
            double score = 55.0; // 基础分
            
            // 基于职称给分
            if (teacher.getZc() != null) {
                if (teacher.getZc().contains("教授")) {
                    score += 20.0;
                    result.getStrengths().add("具有教授职称");
                } else if (teacher.getZc().contains("副教授")) {
                    score += 15.0;
                    result.getStrengths().add("具有副教授职称");
                } else if (teacher.getZc().contains("讲师")) {
                    score += 10.0;
                    result.getStrengths().add("具有讲师职称");
                }
            }
            
            // 成果条件推断（简化）
            if (teacher.getBm() != null && !teacher.getBm().isEmpty()) {
                score += 10.0;
                result.getStrengths().add("有明确的学科归属");
            }
            
            // 社会服务推断
            if (teacher.getZc() != null && (teacher.getZc().contains("主任") || teacher.getZc().contains("副"))) {
                score += 5.0;
                result.getStrengths().add("具有管理服务经验");
            }
            
            result.setMatchScore(score);
            result.setIsQualified(score >= params.getMinMatchScore());
            result.calculateMatchLevel();
            
            // 生成建议
            if (result.getIsQualified()) {
                result.getRecommendations().add("建议准备资格类或成果类条件证明材料");
                result.getRecommendations().add("重点展示近五年的重要成果");
            } else {
                result.getRecommendations().add("建议加强学术成果积累");
                result.getRecommendations().add("争取满足资格类或成果类条件（7项中任选3项）");
            }
            
            log.info("教师{}屏风学者Ⅲ类匹配分析完成，得分: {}", teacher.getZgh(), score);
            
        } catch (Exception e) {
            log.error("教师{}屏风学者Ⅲ类匹配分析失败", teacher.getZgh(), e);
            result.setIsQualified(false);
            result.setReason("分析过程中发生错误: " + e.getMessage());
            result.setMatchScore(0.0);
            result.calculateMatchLevel();
        }
        
        return result;
    }
    
    @Override
    public boolean validateBasicConditions(RchxJzgjbxx teacher) {
        return teacher.getXm() != null && !teacher.getXm().isEmpty() &&
               teacher.getZgh() != null && !teacher.getZgh().isEmpty();
    }
}
