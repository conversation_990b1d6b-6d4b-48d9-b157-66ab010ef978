package com.gl.gl_lg_java.controller;

import com.gl.gl_lg_java.common.Result;
import com.gl.gl_lg_java.dto.LoginDTO;
import com.gl.gl_lg_java.dto.LoginResponseDTO;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
@Slf4j
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    /**
     * 用户登录（支持职工号、手机号、身份证号、邮箱）
     */
    @PostMapping("/login")
    public Result<LoginResponseDTO> login(@RequestBody LoginDTO loginDTO) {
        try {
            // 基本校验
            if (loginDTO.getAccount() == null || loginDTO.getAccount().trim().isEmpty()) {
                return Result.error("账号不能为空");
            }
            if (loginDTO.getPassword() == null || loginDTO.getPassword().trim().isEmpty()) {
                return Result.error("密码不能为空");
            }
            
            // 执行登录
            LoginResponseDTO loginResponse = authService.login(loginDTO);
            if (loginResponse == null) {
                return Result.error("账号或密码错误");
            }
            
            return Result.success("登录成功", loginResponse);
            
        } catch (Exception e) {
            log.error("登录失败: {}", e.getMessage());
            return Result.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public Result<RchxJzgjbxx> getCurrentUser(@RequestHeader("Authorization") String token) {
        try {
            RchxJzgjbxx teacher = authService.validateTokenAndGetUser(token);
            if (teacher == null) {
                return Result.error(401, "Token无效或已过期");
            }
            
            return Result.success(teacher);
            
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage());
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    public Result<LoginResponseDTO> refreshToken(@RequestHeader("Authorization") String token) {
        try {
            LoginResponseDTO refreshResponse = authService.refreshToken(token);
            if (refreshResponse == null) {
                return Result.error(401, "Token刷新失败");
            }
            
            return Result.success("Token刷新成功", refreshResponse);
            
        } catch (Exception e) {
            log.error("Token刷新失败: {}", e.getMessage());
            return Result.error("Token刷新失败: " + e.getMessage());
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<String> logout(@RequestHeader("Authorization") String token) {
        try {
            // 这里可以将Token加入黑名单，简单起见直接返回成功
            // 实际项目中可以考虑使用Redis存储Token黑名单
            
            log.info("用户登出成功");
            return Result.success("登出成功");
            
        } catch (Exception e) {
            log.error("登出失败: {}", e.getMessage());
            return Result.error("登出失败: " + e.getMessage());
        }
    }
}
