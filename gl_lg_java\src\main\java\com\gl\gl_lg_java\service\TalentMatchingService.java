package com.gl.gl_lg_java.service;

import com.gl.gl_lg_java.dto.ProjectRequirementDTO;
import com.gl.gl_lg_java.dto.TalentMatchingResultDTO;
import com.gl.gl_lg_java.dto.TeacherCapabilityDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 人才匹配分析服务接口
 */
public interface TalentMatchingService {
    
    /**
     * 执行人才项目适配度分析
     * @param requirement 项目需求配置
     * @return 匹配结果列表，按适配度降序排列
     */
    List<TalentMatchingResultDTO> analyzeTalentMatching(ProjectRequirementDTO requirement);
    
    /**
     * 获取教师详细能力分析
     * @param teacherId 教师职工号
     * @return 教师能力详细分析
     */
    TeacherCapabilityDTO getTeacherCapabilityAnalysis(String teacherId);
    
    /**
     * 计算学术影响力得分
     * @param teacherId 教师职工号
     * @return 学术影响力得分 (0-100)
     */
    BigDecimal calculateAcademicInfluenceScore(String teacherId);
    
    /**
     * 计算领域深耕度得分
     * @param teacherId 教师职工号
     * @param projectFields 项目研究领域
     * @param projectKeywords 项目关键词
     * @return 领域深耕度得分 (0-100)
     */
    BigDecimal calculateFieldExpertiseScore(String teacherId, List<String> projectFields, List<String> projectKeywords);
    
    /**
     * 计算团队适配值得分
     * @param teacherId 教师职工号
     * @return 团队适配值得分 (0-100)
     */
    BigDecimal calculateTeamCompatibilityScore(String teacherId);
    
    /**
     * 计算成果落地性得分
     * @param teacherId 教师职工号
     * @return 成果落地性得分 (0-100)
     */
    BigDecimal calculateAchievementPracticalityScore(String teacherId);
    
    /**
     * 计算综合适配度得分
     * @param teacherId 教师职工号
     * @param requirement 项目需求配置
     * @return 综合适配度得分 (0-100)
     */
    BigDecimal calculateTotalMatchingScore(String teacherId, ProjectRequirementDTO requirement);
    
    /**
     * 生成推荐理由
     * @param teacherId 教师职工号
     * @param requirement 项目需求配置
     * @return 推荐理由列表
     */
    List<String> generateRecommendationReasons(String teacherId, ProjectRequirementDTO requirement);
    
    /**
     * 生成风险预警
     * @param teacherId 教师职工号
     * @param requirement 项目需求配置
     * @return 风险预警列表
     */
    List<String> generateRiskWarnings(String teacherId, ProjectRequirementDTO requirement);
    
    /**
     * 获取预设项目模板
     * @return 预设模板列表
     */
    List<ProjectRequirementDTO> getPresetTemplates();
}
