package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目大类表实体类
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_rchx_project_categories")
public class RchxProjectCategories {

    /**
     * 大类ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 大类编码
     */
    @TableField("category_code")
    private String categoryCode;

    /**
     * 大类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 大类描述
     */
    @TableField("description")
    private String description;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否启用(0禁用,1启用)
     */
    @TableField("is_enabled")
    private Boolean isEnabled;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
