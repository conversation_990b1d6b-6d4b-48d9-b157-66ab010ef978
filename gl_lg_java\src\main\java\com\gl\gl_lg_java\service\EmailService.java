package com.gl.gl_lg_java.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

/**
 * 邮件服务类
 */
@Service
@Slf4j
public class EmailService {

    /**
     * xn
     */
    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

    /**
     * 发送简单文本邮件
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送结果
     */
    public boolean sendSimpleEmail(String to, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(from);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);

            mailSender.send(message);
            log.info("邮件发送成功: {} -> {}, 主题: {}", from, to, subject);
            return true;
        } catch (Exception e) {
            log.error("邮件发送失败: {} -> {}, 错误: {}", from, to, e.getMessage());
            return false;
        }
    }



    /**
     * 发送密码重置邮件
     * @param email 收件人邮箱
     * @param userName 用户姓名
     * @param verificationCode 验证码
     * @return 发送结果
     */
    public boolean sendPasswordResetEmail(String email, String userName, String verificationCode) {
        String subject = "科研管理系统 - 密码重置验证码";
        String content = buildPasswordResetEmailContent(userName, verificationCode);

        return sendSimpleEmail(email, subject, content);
    }

    /**
     * 构建密码重置邮件内容
     */
    private String buildPasswordResetEmailContent(String userName, String verificationCode) {
        StringBuilder content = new StringBuilder();
        content.append("尊敬的 ").append(userName != null ? userName : "用户").append("：\n\n");
        content.append("您好！您正在申请重置科研管理系统的登录密码。\n\n");
        content.append("您的密码重置验证码是：").append(verificationCode).append("\n\n");
        content.append("验证码有效期为15分钟，请及时使用。\n\n");
        content.append("安全提示：\n");
        content.append("• 请勿将验证码告诉他人\n");
        content.append("• 如果这不是您本人的操作，请忽略此邮件\n");
        content.append("• 验证码仅可使用一次\n\n");
        content.append("如有疑问，请联系系统管理员。\n\n");
        content.append("科研管理系统\n");
        content.append(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")));

        return content.toString();
    }
}
