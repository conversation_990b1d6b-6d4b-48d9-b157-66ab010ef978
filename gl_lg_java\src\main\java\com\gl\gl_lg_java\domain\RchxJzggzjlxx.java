package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 教职工工作简历信息
 * @TableName t_rchx_jzggzjlxx
 */
@TableName(value ="t_rchx_jzggzjlxx")
@Data
public class RchxJzggzjlxx implements Serializable {
    /**
     * 职工号
     */
    @TableId(value = "zgh", type = IdType.INPUT)
    private String zgh;

    /**
     * 工作起始日期
     */
    @TableField(value = "gzqsrq")
    private String gzqsrq;

    /**
     * 工作终止日期
     */
    @TableField(value = "gzzzrq")
    private String gzzzrq;

    /**
     * 工作单位
     */
    @TableField(value = "gzdw")
    private String gzdw;

    /**
     * 工作内容
     */
    @TableField(value = "gznr")
    private String gznr;

    /**
     * 曾任职务
     */
    @TableField(value = "crzw")
    private String crzw;

    /**
     * 从事专业
     */
    @TableField(value = "cszy")
    private String cszy;

    /**
     * 工作证明人
     */
    @TableField(value = "gzzmr")
    private String gzzmr;

    /**
     * 工作所在国家或地区
     */
    @TableField(value = "gzszd")
    private String gzszd;

    /**
     * 备注
     */
    @TableField(value = "bz")
    private String bz;

    /**
     * 时间戳
     */
    @TableField(value = "tstamp")
    private String tstamp;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RchxJzggzjlxx other = (RchxJzggzjlxx) that;
        return (this.getZgh() == null ? other.getZgh() == null : this.getZgh().equals(other.getZgh()))
            && (this.getGzqsrq() == null ? other.getGzqsrq() == null : this.getGzqsrq().equals(other.getGzqsrq()))
            && (this.getGzzzrq() == null ? other.getGzzzrq() == null : this.getGzzzrq().equals(other.getGzzzrq()))
            && (this.getGzdw() == null ? other.getGzdw() == null : this.getGzdw().equals(other.getGzdw()))
            && (this.getGznr() == null ? other.getGznr() == null : this.getGznr().equals(other.getGznr()))
            && (this.getCrzw() == null ? other.getCrzw() == null : this.getCrzw().equals(other.getCrzw()))
            && (this.getCszy() == null ? other.getCszy() == null : this.getCszy().equals(other.getCszy()))
            && (this.getGzzmr() == null ? other.getGzzmr() == null : this.getGzzmr().equals(other.getGzzmr()))
            && (this.getGzszd() == null ? other.getGzszd() == null : this.getGzszd().equals(other.getGzszd()))
            && (this.getBz() == null ? other.getBz() == null : this.getBz().equals(other.getBz()))
            && (this.getTstamp() == null ? other.getTstamp() == null : this.getTstamp().equals(other.getTstamp()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getZgh() == null) ? 0 : getZgh().hashCode());
        result = prime * result + ((getGzqsrq() == null) ? 0 : getGzqsrq().hashCode());
        result = prime * result + ((getGzzzrq() == null) ? 0 : getGzzzrq().hashCode());
        result = prime * result + ((getGzdw() == null) ? 0 : getGzdw().hashCode());
        result = prime * result + ((getGznr() == null) ? 0 : getGznr().hashCode());
        result = prime * result + ((getCrzw() == null) ? 0 : getCrzw().hashCode());
        result = prime * result + ((getCszy() == null) ? 0 : getCszy().hashCode());
        result = prime * result + ((getGzzmr() == null) ? 0 : getGzzmr().hashCode());
        result = prime * result + ((getGzszd() == null) ? 0 : getGzszd().hashCode());
        result = prime * result + ((getBz() == null) ? 0 : getBz().hashCode());
        result = prime * result + ((getTstamp() == null) ? 0 : getTstamp().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", zgh=").append(zgh);
        sb.append(", gzqsrq=").append(gzqsrq);
        sb.append(", gzzzrq=").append(gzzzrq);
        sb.append(", gzdw=").append(gzdw);
        sb.append(", gznr=").append(gznr);
        sb.append(", crzw=").append(crzw);
        sb.append(", cszy=").append(cszy);
        sb.append(", gzzmr=").append(gzzmr);
        sb.append(", gzszd=").append(gzszd);
        sb.append(", bz=").append(bz);
        sb.append(", tstamp=").append(tstamp);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}