package com.gl.gl_lg_java.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 分析摘要DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisSummary {

    /**
     * 总教师数
     */
    private Integer totalTeachers;

    /**
     * 已分析教师数
     */
    private Integer analyzedTeachers;

    /**
     * 符合条件教师数
     */
    private Integer qualifiedTeachers;

    /**
     * 平均匹配分数
     */
    private Double averageScore;

    /**
     * 高匹配数量
     */
    private Integer highMatchCount;

    /**
     * 中匹配数量
     */
    private Integer mediumMatchCount;

    /**
     * 低匹配数量
     */
    private Integer lowMatchCount;
}
