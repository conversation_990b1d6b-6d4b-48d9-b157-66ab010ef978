package com.gl.gl_lg_java.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gl.gl_lg_java.domain.RchxNotifications;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_rchx_notifications(教师通知表)】的数据库操作Mapper
 * @createDate 2025-07-24 13:00:00
 * @Entity com.gl.gl_lg_java.domain.RchxNotifications
 */
@Mapper
public interface NotificationMapper extends BaseMapper<RchxNotifications> {

    /**
     * 核心字段列表 - 用于列表查询优化
     */
    String CORE_FIELDS = "id, zgh, title, type, is_read, priority, create_time, sender_name";

    /**
     * 分页查询教师通知列表（包含类型信息）
     * @param page 分页对象
     * @param zgh 职工号
     * @param isRead 是否已读（null表示查询所有）
     * @param type 通知类型（null表示查询所有）
     * @return 通知列表
     */
    @Select({
        "<script>",
        "SELECT n.id, n.zgh, n.title, n.content, n.type, n.is_read, n.priority, ",
        "       n.create_time, n.read_time, n.sender_zgh, n.sender_name,",
        "       nt.type_name, nt.icon, nt.color,",
        "       t.xm as receiver_name",
        "FROM t_rchx_notifications n",
        "LEFT JOIN t_rchx_notification_types nt ON n.type = nt.type_code",
        "LEFT JOIN t_rchx_jzgjbxx t ON n.zgh = t.zgh",
        "WHERE n.zgh = #{zgh}",
        "<if test='isRead != null'>",
        "  AND n.is_read = #{isRead}",
        "</if>",
        "<if test='type != null and type != \"\"'>",
        "  AND n.type = #{type}",
        "</if>",
        "ORDER BY n.priority DESC, n.create_time DESC",
        "</script>"
    })
    IPage<RchxNotifications> selectNotificationPage(Page<RchxNotifications> page,
                                               @Param("zgh") String zgh,
                                               @Param("isRead") Integer isRead,
                                               @Param("type") String type);

    /**
     * 查询教师未读通知数量
     * @param zgh 职工号
     * @return 未读通知数量
     */
    @Select("SELECT COUNT(*) FROM t_rchx_notifications WHERE zgh = #{zgh} AND is_read = 0")
    long countUnreadByZgh(@Param("zgh") String zgh);

    /**
     * 查询教师最新的几条通知
     * @param zgh 职工号
     * @param limit 数量限制
     * @return 通知列表
     */
    @Select({
        "SELECT n.id, n.zgh, n.title, n.content, n.type, n.is_read, n.priority, ",
        "       n.create_time, n.read_time, n.sender_zgh, n.sender_name,",
        "       nt.type_name, nt.icon, nt.color",
        "FROM t_rchx_notifications n",
        "LEFT JOIN t_rchx_notification_types nt ON n.type = nt.type_code",
        "WHERE n.zgh = #{zgh}",
        "ORDER BY n.priority DESC, n.create_time DESC",
        "LIMIT #{limit}"
    })
    List<RchxNotifications> selectLatestNotifications(@Param("zgh") String zgh, @Param("limit") int limit);

    /**
     * 批量标记通知为已读
     * @param zgh 职工号
     * @param ids 通知ID列表
     * @return 更新数量
     */
    @Update({
        "<script>",
        "UPDATE t_rchx_notifications SET is_read = 1, read_time = NOW()",
        "WHERE zgh = #{zgh} AND id IN",
        "<foreach collection='ids' item='id' open='(' separator=',' close=')'>",
        "  #{id}",
        "</foreach>",
        "</script>"
    })
    int batchMarkAsRead(@Param("zgh") String zgh, @Param("ids") List<Long> ids);

    /**
     * 标记所有通知为已读
     * @param zgh 职工号
     * @return 更新数量
     */
    @Update("UPDATE t_rchx_notifications SET is_read = 1, read_time = NOW() WHERE zgh = #{zgh} AND is_read = 0")
    int markAllAsRead(@Param("zgh") String zgh);

    /**
     * 根据类型查询通知数量
     * @param zgh 职工号
     * @param type 通知类型
     * @return 通知数量
     */
    @Select("SELECT COUNT(*) FROM t_rchx_notifications WHERE zgh = #{zgh} AND type = #{type}")
    long countByZghAndType(@Param("zgh") String zgh, @Param("type") String type);

    /**
     * 查询系统统计信息
     * @return 统计信息
     */
    @Select({
        "SELECT ",
        "  COUNT(*) as total_notifications,",
        "  SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count,",
        "  COUNT(DISTINCT zgh) as user_count,",
        "  COUNT(DISTINCT type) as type_count",
        "FROM t_rchx_notifications",
        "WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
    })
    Map<String, Object> getSystemStats();
}
