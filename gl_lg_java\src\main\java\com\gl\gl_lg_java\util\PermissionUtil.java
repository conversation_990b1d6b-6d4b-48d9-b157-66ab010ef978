package com.gl.gl_lg_java.util;

import com.gl.gl_lg_java.enums.PermissionEnum;
import org.springframework.stereotype.Component;

/**
 * 权限工具类
 */
@Component
public class PermissionUtil {
    
    /**
     * 检查用户是否有指定权限
     * 只有系统管理员拥有所有权限，其他权限只能访问自己对应的功能
     *
     * @param userPermission 用户权限
     * @param requiredPermission 需要的权限
     * @return 是否有权限
     */
    public boolean hasPermission(String userPermission, String requiredPermission) {
        // 系统管理员拥有所有权限
        if (PermissionEnum.SYSTEM_ADMIN.getName().equals(userPermission)) {
            return true;
        }
        // 其他权限只能访问自己对应的功能
        return userPermission.equals(requiredPermission);
    }
    
    /**
     * 检查用户是否是指定权限
     * 
     * @param userPermission 用户权限
     * @param targetPermission 目标权限
     * @return 是否是指定权限
     */
    public boolean isPermission(String userPermission, String targetPermission) {
        PermissionEnum userPerm = PermissionEnum.getByName(userPermission);
        PermissionEnum targetPerm = PermissionEnum.getByName(targetPermission);
        return userPerm.isPermission(targetPerm);
    }
    
    /**
     * 检查用户是否是教师
     */
    public boolean isTeacher(String userPermission) {
        return isPermission(userPermission, PermissionEnum.TEACHER.getName());
    }
    
    /**
     * 检查用户是否是评审
     */
    public boolean isReviewer(String userPermission) {
        return isPermission(userPermission, PermissionEnum.REVIEWER.getName());
    }
    
    /**
     * 检查用户是否是学院管理员
     */
    public boolean isCollegeAdmin(String userPermission) {
        return isPermission(userPermission, PermissionEnum.COLLEGE_ADMIN.getName());
    }
    
    /**
     * 检查用户是否是系统管理员
     */
    public boolean isSystemAdmin(String userPermission) {
        return isPermission(userPermission, PermissionEnum.SYSTEM_ADMIN.getName());
    }
    
    /**
     * 检查用户是否是管理员（学院管理员或系统管理员）
     */
    public boolean isAdmin(String userPermission) {
        return isCollegeAdmin(userPermission) || isSystemAdmin(userPermission);
    }

    /**
     * 检查用户是否可以管理其他用户
     */
    public boolean canManageUsers(String userPermission) {
        return isCollegeAdmin(userPermission) || isSystemAdmin(userPermission);
    }

    /**
     * 检查用户是否可以进行评审操作
     */
    public boolean canReview(String userPermission) {
        return isReviewer(userPermission) || isSystemAdmin(userPermission);
    }
    
    /**
     * 获取权限级别
     */
    public int getPermissionLevel(String userPermission) {
        return PermissionEnum.getByName(userPermission).getLevel();
    }
    
    /**
     * 获取权限描述
     */
    public String getPermissionDescription(String userPermission) {
        return PermissionEnum.getByName(userPermission).getDescription();
    }
}
