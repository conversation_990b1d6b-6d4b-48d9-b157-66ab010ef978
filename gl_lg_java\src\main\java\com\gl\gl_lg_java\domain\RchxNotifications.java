package com.gl.gl_lg_java.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 教师通知表
 * @TableName t_rchx_notifications
 */
@TableName(value = "t_rchx_notifications")
@Data
public class RchxNotifications implements Serializable {
    
    /**
     * 通知ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 接收教师职工号
     */
    @TableField(value = "zgh")
    private String zgh;

    /**
     * 通知标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 通知内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 通知类型
     */
    @TableField(value = "type")
    private String type;

    /**
     * 是否已读(0未读,1已读)
     */
    @TableField(value = "is_read")
    private Integer isRead;

    /**
     * 优先级(1普通,2重要,3紧急)
     */
    @TableField(value = "priority")
    private Integer priority;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 阅读时间
     */
    @TableField(value = "read_time")
    private LocalDateTime readTime;

    /**
     * 发送者职工号
     */
    @TableField(value = "sender_zgh")
    private String senderZgh;

    /**
     * 发送者姓名
     */
    @TableField(value = "sender_name")
    private String senderName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    // 扩展字段 - 不映射到数据库
    /**
     * 通知类型名称（关联查询获得）
     */
    @TableField(exist = false)
    private String typeName;

    /**
     * 通知类型图标（关联查询获得）
     */
    @TableField(exist = false)
    private String icon;

    /**
     * 通知类型颜色（关联查询获得）
     */
    @TableField(exist = false)
    private String color;

    /**
     * 接收者姓名（关联查询获得）
     */
    @TableField(exist = false)
    private String receiverName;

    /**
     * 是否为未读通知
     */
    public boolean isUnread() {
        return this.isRead == null || this.isRead == 0;
    }

    /**
     * 是否为重要通知
     */
    public boolean isImportant() {
        return this.priority != null && this.priority >= 2;
    }

    /**
     * 是否为紧急通知
     */
    public boolean isUrgent() {
        return this.priority != null && this.priority == 3;
    }

    /**
     * 获取优先级文本
     */
    public String getPriorityText() {
        if (priority == null) return "普通";
        switch (priority) {
            case 1: return "普通";
            case 2: return "重要";
            case 3: return "紧急";
            default: return "普通";
        }
    }

    /**
     * 获取优先级颜色
     */
    public String getPriorityColor() {
        if (priority == null) return "#1890ff";
        switch (priority) {
            case 1: return "#1890ff";  // 蓝色
            case 2: return "#fa8c16";  // 橙色
            case 3: return "#f5222d";  // 红色
            default: return "#1890ff";
        }
    }
}
