package com.gl.gl_lg_java.service;

import com.gl.gl_lg_java.dto.LoginDTO;
import com.gl.gl_lg_java.dto.LoginResponseDTO;
import com.gl.gl_lg_java.domain.RchxJzgjbxx;
import com.gl.gl_lg_java.mapper.RchxJzgjbxxMapper;
import com.gl.gl_lg_java.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

/**
 * 认证服务
 */
@Service
@Slf4j
public class AuthService {
    
    @Autowired
    private RchxJzgjbxxMapper rchxJzgjbxxMapper;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 用户登录（支持职工号、手机号、身份证号、邮箱）
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 2000))
    public LoginResponseDTO login(LoginDTO loginDTO) {
        try {
            // 根据账号查询用户（支持职工号、手机号、身份证号、邮箱）
            RchxJzgjbxx teacher = rchxJzgjbxxMapper.findByAccount(loginDTO.getAccount());
            if (teacher == null) {
                log.warn("登录失败，用户不存在: {}", loginDTO.getAccount());
                return null;
            }
            
            // 验证密码
            if (teacher.getPass() == null || !teacher.getPass().equals(loginDTO.getPassword())) {
                log.warn("登录失败，密码错误: {}", loginDTO.getAccount());
                return null;
            }
            
            // 生成JWT Token
            String token = jwtUtil.generateToken(teacher.getZgh(), teacher.getQx());
            
            // 获取Token过期时间
            long expiresIn = jwtUtil.getExpirationTime();
            
            log.info("用户登录成功: 账号={}, 职工号={}, 权限={}", 
                    loginDTO.getAccount(), teacher.getZgh(), teacher.getQx());
            
            return new LoginResponseDTO(token, expiresIn, teacher);
            
        } catch (Exception e) {
            log.error("登录处理失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证Token并获取用户信息
     */
    public RchxJzgjbxx validateTokenAndGetUser(String token) {
        try {
            // 验证Token
            if (!jwtUtil.validateToken(token)) {
                return null;
            }
            
            // 从Token中获取职工号
            String zgh = jwtUtil.getZghFromToken(token);
            if (zgh == null) {
                return null;
            }
            
            // 查询用户信息
            return rchxJzgjbxxMapper.findByZgh(zgh);
            
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 刷新Token
     */
    public LoginResponseDTO refreshToken(String oldToken) {
        try {
            // 验证旧Token
            if (!jwtUtil.validateToken(oldToken)) {
                log.warn("刷新Token失败，旧Token无效");
                return null;
            }
            
            // 从旧Token中获取用户信息
            String zgh = jwtUtil.getZghFromToken(oldToken);
            RchxJzgjbxx teacher = rchxJzgjbxxMapper.findByZgh(zgh);
            
            if (teacher == null) {
                log.warn("刷新Token失败，用户不存在: {}", zgh);
                return null;
            }
            
            // 生成新Token
            String newToken = jwtUtil.generateToken(teacher.getZgh(), teacher.getQx());
            long expiresIn = jwtUtil.getExpirationTime();
            
            log.info("Token刷新成功: {}", zgh);
            
            return new LoginResponseDTO(newToken, expiresIn, teacher);
            
        } catch (Exception e) {
            log.error("Token刷新失败: {}", e.getMessage());
            return null;
        }
    }
}
